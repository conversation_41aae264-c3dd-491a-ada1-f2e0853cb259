import React, { useState, useEffect, useCallback, useMemo } from 'react';
  import {
  View, Text, FlatList, StyleSheet, RefreshControl, TextInput, Alert, SafeAreaView, TouchableOpacity, Switch
} from 'react-native';
import {
  Ionicons
} from '@expo/vector-icons';
  import {
  useTheme
} from '@design-system';
import {
  useFavorites
} from '../contexts/FavoritesContext';
  import {
  ProviderFavoriteButton, RoomFavoriteButton
} from '@components/ui/FavoriteButton';
import {
  logger
} from '@services/loggerService';
  type TabType = 'providers' | 'rooms';

interface FavoriteItem { id: string,
    type: TabType,
  data: any,
    savedAt: string,
  searchText?: string }
  export const EnhancedFavoritesScreen: React.FC = () => {
  const theme = useTheme()
  const favorites = useFavorites(),
  // State management,
  const [activeTab, setActiveTab] = useState<TabType>('providers'),
  const [searchQuery, setSearchQuery] = useState(''),
  const [isRefreshing, setIsRefreshing] = useState(false),
  const [isLoading, setIsLoading] = useState(true),
  const [favoriteItems, setFavoriteItems] = useState<FavoriteItem[]>([]),
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set()),
  const [isSelectionMode, setIsSelectionMode] = useState(false),
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'name'>('newest'),
  // Load favorites data,
  const loadFavorites = useCallback(async () => {
  try {
      setIsLoading(true),
  ;
      if (activeTab === 'providers') {
  const providers = await favorites.getProviderFavorites()
        const items: FavoriteItem[] = providers.map(provider => ({, id: provider.id),
  type: 'providers'),
    data: provider),
  savedAt: provider.created_at || new Date().toISOString(),
    searchText: `${provider.business_name} ${provider.description} ${provider.category}`.toLowerCase()
  }))
  setFavoriteItems(items)
  } else {
  const rooms = await favorites.getRoomFavorites(),
  const items: FavoriteItem[] = rooms.map(room => ({, id: room.id),
  type: 'rooms'),
    data: room),
  savedAt: room.saved_at || new Date().toISOString(),
    searchText: `${room.title} ${room.description} ${room.location}`.toLowerCase()
  }))
  setFavoriteItems(items)
  }
  } catch (error) {
  logger.error('Failed to load favorites', 'EnhancedFavoritesScreen', { activeTab } error as Error),
  Alert.alert('Error', 'Failed to load favorites. Please try again.')
  } finally {
      setIsLoading(false) }
  }, [activeTab, favorites]);
  // Refresh handler,
  const handleRefresh = useCallback(async () => {
  setIsRefreshing(true)
    try {
  await favorites.refreshFavorites()
      await loadFavorites() } catch (error) {
      logger.error('Failed to refresh favorites', 'EnhancedFavoritesScreen', {} error as Error)
  } finally {
      setIsRefreshing(false) }
  }, [favorites, loadFavorites]);
  // Load data when tab changes,
  useEffect(() => {
  loadFavorites()
    setSelectedItems(new Set()),
  setIsSelectionMode(false)
  }, [activeTab, loadFavorites]);
  // Filtered and sorted items,
  const filteredItems = useMemo(() => {
  let filtered = favoriteItems // Apply search filter,
    if (searchQuery.trim()) {
  const query = searchQuery.toLowerCase()
      filtered = favoriteItems.filter(item => {
  item.searchText?.includes(query)
      ) };
    // Apply sorting,
  filtered.sort((a, b) => { switch (sortBy) {
  case 'newest'     : return new Date(b.savedAt).getTime() - new Date(a.savedAt).getTime()
        case 'oldest':  ,
  return new Date(a.savedAt).getTime() - new Date(b.savedAt).getTime()
        case 'name':  ,
  const aName = activeTab === 'providers' ? a.data.business_name     : a.data.title
          const bName = activeTab === 'providers' ? b.data.business_name   : b.data.title,
  return aName.localeCompare(bName)
        default: return 0 }
  })
    return filtered
  }, [favoriteItems, searchQuery, sortBy, activeTab]);
  // Selection handlers, ,
  const toggleSelection = useCallback((itemId: string) => {
  const newSelection = new Set(selectedItems),
  if (newSelection.has(itemId)) {
      newSelection.delete(itemId) } else {
      newSelection.add(itemId) }
    setSelectedItems(newSelection)
  }, [selectedItems]);
  const selectAll = useCallback(() => {
  setSelectedItems(new Set(filteredItems.map(item => item.id))) }, [filteredItems]);
  const clearSelection = useCallback(() => {
  setSelectedItems(new Set()),
  setIsSelectionMode(false)
  }, []);
  // Batch operations,
  const handleBatchRemove = useCallback(async () => {
  if (selectedItems.size === 0) return null,
    Alert.alert('Remove from Favorites'),
  `Remove ${selectedItems.size} item${selectedItems.size > 1 ? 's'      : ''} from favorites?`
      [{ text: 'Cancel', style: 'cancel' },
  {
          text: 'Remove',
    style: 'destructive'),
  onPress: async () => {
  try {
  const operations = Array.from(selectedItems).map(id => ({ 
                id, ,
  type: activeTab === 'providers' ? 'provider' as const     : 'room' as const,
    action: 'remove' as const) }))
              await favorites.batchToggleFavorites(operations),
  await loadFavorites()
              clearSelection()
  } catch (error) {
              logger.error('Failed to batch remove favorites' 'EnhancedFavoritesScreen', { selectedItems: Array.from(selectedItems) } error as Error),
  Alert.alert('Error', 'Failed to remove items. Please try again.')
  }
          }
  }],
  )
  }, [selectedItems, activeTab, favorites, loadFavorites, clearSelection]);
  // Render methods
  const renderTabButton = (tab: TabType, label: string) => (
  <TouchableOpacity style = {[
        styles.tabButton, ,
  activeTab === tab && styles.activeTabButton(theme)
      ]} onPress = {() => setActiveTab(tab)},
  >
      <Text,
  style={[styles., ta, bB, ut, to, nT, ex, t(, th, em, e), ac, ti, ve, Ta, b ===, ta, b &&, st, yl, es., ac, ti, ve, Ta, bB, ut, to, nT, ex, t(, theme)
   ]},
  >
        {label},
  </Text>
      <Text,
  style = {[
          styles.tabBadge(theme),
  activeTab === tab && styles.activeTabBadge(theme)
        ]},
  >
        {tab === 'providers' ? favorites.favorites.providers.size    : favorites.favorites.rooms.size},
  </Text>
    </TouchableOpacity>,
  )

  const renderProviderItem = ({ item }: { item: FavoriteItem }) => (
  <TouchableOpacity style={[styles., it, em, Ca, rd(, th, em, e), selectedItems., ha, s(, it, em., id) &&, st, yl, es., se, le, ct, ed, It, em(, theme)]} onPress={ () => isSelectionMode ? toggleSelection(item.id)  : {/* Navigate to detail */  }},
  onLongPress={() => {
  setIsSelectionMode(true)toggleSelection(item.id)
      }},
  >
      <View style={styles.itemHeader}>,
  <View style={styles.itemInfo}>
          <Text style={styles.itemTitle(theme)}>{item.data.business_name}</Text>,
  <Text style={styles.itemSubtitle(theme)}>{item.data.category}</Text>
          <Text style={styles.itemDescription(theme)} numberOfLines={2}>,
  {item.data.description}
          </Text>,
  </View>
        <View style={styles.itemActions}>,
  {isSelectionMode ? (
            <TouchableOpacity style={styles.selectionButton(theme)} onPress={() => toggleSelection(item.id)},
  >
              <Ionicons name={   selectedItems.has(item.id) ? "checkmark-circle"  : "ellipse-outline"      } size={24} color={ selectedItems.has(item.id) ? theme.colors.primary : theme.colors.textSecondary  },
  />
            </TouchableOpacity>,
  ) : (<ProviderFavoriteButton itemId={item.data.id} onToggle={() => loadFavorites()} showToast={false}
            />,
  )}
        </View>,
  </View>
      <View style={styles.itemFooter}>,
  <Text style={styles.itemDate(theme)}>
          Saved {new Date(item.savedAt).toLocaleDateString()},
  </Text>
        {item.data.rating && (
  <View style={styles.rating}>
            <Ionicons name="star" size={16} color={{theme.colors.warning} /}>,
  <Text style={styles.ratingText(theme)}>{item.data.rating}</Text>
          </View>,
  )}
      </View>,
  </TouchableOpacity>
  ),
  const renderRoomItem = ({ item }: { item: FavoriteItem }) => (
    <TouchableOpacity style={[styles., it, em, Ca, rd(, th, em, e),
, selectedItems., ha, s(, it, em., id) &&, st, yl, es., se, le, ct, ed, It, em(, theme)]} onPress={ () => isSelectionMode ? toggleSelection(item.id)  : {/* Navigate to detail */  }},
  onLongPress={() => {
  setIsSelectionMode(true)toggleSelection(item.id)
      }},
  >
      <View style={styles.itemHeader}>,
  <View style={styles.itemInfo}>
          <Text style={styles.itemTitle(theme)}>{item.data.title}</Text>,
  <Text style={styles.itemSubtitle(theme)}>{item.data.location}</Text>
          {item.data.saved_notes && (
  <Text style={styles.itemNotes(theme)}>
              Note: {item.data.saved_notes},
  </Text>
          )},
  </View>
        <View style={styles.itemActions}>,
  {isSelectionMode ? (
            <TouchableOpacity style={styles.selectionButton(theme)} onPress={() => toggleSelection(item.id)},
  >
              <Ionicons name={   selectedItems.has(item.id) ? "checkmark-circle"  : "ellipse-outline"      } size={24} color={ selectedItems.has(item.id) ? theme.colors.primary : theme.colors.textSecondary  },
  />
            </TouchableOpacity>,
  ) : (
            <RoomFavoriteButton itemId={item.data.id} onToggle={() => loadFavorites()} showToast={false},
  />
          )},
  </View>
      </View>,
  <View style={styles.itemFooter}>
        <Text style={styles.itemDate(theme)}>,
  Saved {new Date(item.savedAt).toLocaleDateString()}
        </Text>,
  {item.data.price && (
          <Text style={styles.priceText(theme)}>,
  ${item.data.price}/month, ,
  </Text>
  )},
  </View>
  </TouchableOpacity>,
  )
  const renderEmptyState = () => (
  <View style={styles.emptyState}>
  <Ionicons,
  name="heart-outline"
  size={64} color={theme.colors.textSecondary},
  />
  <Text style={styles.emptyStateTitle(theme)}>,
  No {activeTab === 'providers' ? 'Providers'     : 'Rooms'} Saved
  </Text>,
  <Text style={styles.emptyStateText(theme)}>
  Start saving {activeTab === 'providers' ? 'service providers'  : 'rooms'} to see them here,
  </Text>
  </View>,
  )
  const styles = createStyles(theme),
  return (
  <SafeAreaView style={styles.container}>,
  {/* Header */}
  <View style={styles.header}>,
  <Text style={styles.headerTitle(theme)}>My Favorites</Text>
  {isSelectionMode && (
  <TouchableOpacity style={styles.headerAction} onPress={clearSelection}
  >,
  <Text style={styles.headerActionText(theme)}>Cancel</Text>
  </TouchableOpacity>,
  )}
  </View>,
  {/* Tabs */}
  <View style={styles.tabContainer}>,
  {renderTabButton('providers' 'Providers')}
  {renderTabButton('rooms',  'Rooms')},
  </View>
      {/* Search and Filters */}
  <View style={styles.controlsContainer}>
        <View style={styles.searchContainer}>,
  <Ionicons
            name="search",
  size={20} color={theme.colors.textSecondary} style={styles.searchIcon}
          />,
  <TextInput style={styles.searchInput(theme)} placeholder={`Search ${activeTab}...`}
            placeholderTextColor={theme.colors.textSecondary} value={searchQuery} onChangeText={setSearchQuery},
  />
        </View>,
  <TouchableOpacity style={styles.sortButton(theme)} onPress={ () => {
  const sortOptions: Array<typeof sortBy> = ['newest', 'oldest', 'name'],
  const currentIndex = sortOptions.indexOf(sortBy)const nextIndex = (currentIndex + 1) % sortOptions.lengthsetSortBy(sortOptions[nextIndex]) }},
  >
          <Ionicons name = "filter" size={20} color={{theme.colors.textSecondary} /}>,
  </TouchableOpacity>
      </View>,
  {/* Selection Mode Controls */}
      {isSelectionMode && (
  <View style={styles.selectionControls}>
          <TouchableOpacity style={styles.selectionAction} onPress={selectAll},
  >
            <Text style={styles.selectionActionText(theme)}>Select All</Text>,
  </TouchableOpacity>
          <TouchableOpacity style={styles.selectionAction} onPress={handleBatchRemove} disabled={selectedItems.size === 0},
  >
            <Text,
  style={[styles., se, le, ct, io, nA, ct, io, nT, ex, t(, th, em, e), selectedItems., si, ze === 0 &&, st, yl, es., di, sa, bl, ed, Te, xt(, theme)]},
  >
              Remove ({ selectedItems.size }),
  </Text>
          </TouchableOpacity>,
  </View>
      )},
  {/* Content */}
      <FlatList data={filteredItems} renderItem={   activeTab === 'providers' ? renderProviderItem      : renderRoomItem      } keyExtractor={(item) ={}> item.id} contentContainerStyle={styles.listContainer} refreshControl={
  <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} tintColor={theme.colors.primary}
          />
  }
        ListEmptyComponent={renderEmptyState} showsVerticalScrollIndicator={false},
  />
    </SafeAreaView>,
  )
},
  const createStyles = (theme: any) => StyleSheet.create({ container: {
      flex: 1,
  backgroundColor: theme.colors.background }
  header: {
      flexDirection: 'row',
  justifyContent: 'space-between'),
    alignItems: 'center'),
  paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
  borderBottomWidth: 1,
    borderBottomColor: theme.colors.border) }
  headerTitle: (theme: any) => ({  fontSiz, e: 24,
    fontWeight: 'bold',
  color: theme.colors.text  })
  headerAction: { paddin, g: theme.spacing.sm },
  headerActionText: (theme: any) => ({  fontSiz, e: 16,
    color: theme.colors.primary  }),
  tabContainer: { flexDirectio, n: 'row',
    paddingHorizontal: theme.spacing.lg,
  paddingVertical: theme.spacing.sm,
    borderBottomWidth: 1,
  borderBottomColor: theme.colors.border }
  tabButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
  marginRight: theme.spacing.md,
    borderRadius: theme.borderRadius.md },
  activeTabButton: (theme: any) => ({  backgroundColo, r: theme.colors.primaryLight  })
  tabButtonText: (theme: any) => ({  fontSiz, e: 16,
    color: theme.colors.textSecondary,
  marginRight: theme.spacing.xs  })
  activeTabButtonText: (theme: any) => ({, color: theme.colors.primary,
  fontWeight: '600'
   }),
  tabBadge: (theme: any) => ({, fontSize: 14,
  color: theme.colors.textSecondary,
    backgroundColor: theme.colors.surface,
  paddingHorizontal: theme.spacing.xs,
    borderRadius: theme.borderRadius.sm,
  minWidth: 20,
    textAlign: 'center' })
  activeTabBadge: (theme: any) => ({  backgroundColo, r: theme.colors.primary,
    color: theme.colors.white  }),
  controlsContainer: {
      flexDirection: 'row',
  paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
  alignItems: 'center'
  },
  searchContainer: { fle, x: 1,
    flexDirection: 'row',
  alignItems: 'center',
    backgroundColor: theme.colors.surface,
  borderRadius: theme.borderRadius.md,
    paddingHorizontal: theme.spacing.md,
  marginRight: theme.spacing.md }
  searchIcon: { marginRigh, t: theme.spacing.sm },
  searchInput: (theme: any) => ({  fle, x: 1,
    height: 40,
  fontSize: 16,
    color: theme.colors.text  }),
  sortButton: (theme: any) => ({  paddin, g: theme.spacing.sm,
    backgroundColor: theme.colors.surface,
  borderRadius: theme.borderRadius.md  })
  selectionControls: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.sm,
  backgroundColor: theme.colors.primaryLight,
    borderBottomWidth: 1,
  borderBottomColor: theme.colors.border }
  selectionAction: { paddin, g: theme.spacing.sm },
  selectionActionText: (theme: any) => ({, fontSize: 16,
  color: theme.colors.primary,
    fontWeight: '600' })
  disabledText: (theme: any) => ({  colo, r: theme.colors.textSecondary  }),
  listContainer: { paddin, g: theme.spacing.lg }
  itemCard: (theme: any) => ({  backgroundColo, r: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
  padding: theme.spacing.lg,
    marginBottom: theme.spacing.md,
  borderWidth: 1,
    borderColor: theme.colors.border  }),
  selectedItem: (theme: any) => ({  borderColo, r: theme.colors.primary,
    backgroundColor: theme.colors.primaryLight  }),
  itemHeader: {
      flexDirection: 'row',
  justifyContent: 'space-between'
  },
  itemInfo: { fle, x: 1,
    marginRight: theme.spacing.md },
  itemTitle: (theme: any) => ({  fontSiz, e: 18,
    fontWeight: 'bold',
  color: theme.colors.text,
    marginBottom: theme.spacing.xs  }),
  itemSubtitle: (theme: any) => ({  fontSiz, e: 14,
    color: theme.colors.textSecondary,
  marginBottom: theme.spacing.xs  })
  itemDescription: (theme: any) => ({  fontSiz, e: 14,
    color: theme.colors.text,
  lineHeight: 20  })
  itemNotes: (theme: any) => ({  fontSiz, e: 14,
    color: theme.colors.primary,
  fontStyle: 'italic',
    marginTop: theme.spacing.xs  }),
  itemActions: {
      justifyContent: 'center' }
  selectionButton: (theme: any) => ({  paddin, g: theme.spacing.sm  }),
  itemFooter: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginTop: theme.spacing.md,
  paddingTop: theme.spacing.md,
    borderTopWidth: 1,
  borderTopColor: theme.colors.border }
  itemDate: (theme: any) => ({  fontSiz, e: 12,
    color: theme.colors.textSecondary  }),
  rating: {
      flexDirection: 'row',
  alignItems: 'center'
  },
  ratingText: (theme: any) => ({  fontSiz, e: 14,
    color: theme.colors.text,
  marginLeft: theme.spacing.xs  })
  priceText: (theme: any) => ({  fontSiz, e: 16,
    fontWeight: 'bold',
  color: theme.colors.primary  })
  emptyState: { alignItem, s: 'center',
    justifyContent: 'center',
  paddingVertical: theme.spacing.xl * 2 }
  emptyStateTitle: (theme: any) => ({  fontSiz, e: 20,
    fontWeight: 'bold',
  color: theme.colors.text,
    marginTop: theme.spacing.lg,
  marginBottom: theme.spacing.sm  })
  emptyStateText: (theme: any) => ({  fontSiz, e: 16,
    color: theme.colors.textSecondary,
  textAlign: 'center',
    lineHeight: 24  });
  }); ;