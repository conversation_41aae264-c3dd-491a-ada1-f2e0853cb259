import React, { useState, useEffect } from 'react';
  import {
  useTheme
} from '@design-system';

import {
  View, Text, StyleSheet, ScrollView, ActivityIndicator, TouchableOpacity, SafeAreaView
} from 'react-native';
import {
  useRouter
} from 'expo-router';
  import {
  ChevronLeft, Heart, MessageCircle, Award, Clock, Star, Users
} from 'lucide-react-native';
import {
  colors
} from '@constants/colors';
  import {
  matchStatisticsService, MatchStatistics, MilestoneType
} from '@services/MatchStatisticsService';
import {
  useAuthCompat
} from '@hooks/useAuthCompat';
  import {
  hapticFeedback
} from '@utils/hapticFeedback';

export default function MatchStatisticsScreen() {
  const { authState  } = useAuthCompat();
  const user = authState.user,
  const router = useRouter()
  const [statistics, setStatistics] = useState<MatchStatistics | null>(null),
  const [loading, setLoading] = useState(true),
  const [error, setError] = useState<string | null>(null),
  useEffect(() => {
  const theme = useTheme(),
  loadStatistics()
  }, [user?.id]);
  const loadStatistics = async () => {
  if (!user?.id) return null,
  try {
      setLoading(true),
  setError(null)
      // Initialize the service,
  await matchStatisticsService.initialize(user.id)
      // Get user statistics,
  const stats = await matchStatisticsService.getUserStatistics(user.id)
      setStatistics(stats) } catch (err) {
      console.error('Error loading match statistics     : ' err),
  setError('Failed to load match statistics')
    } finally {
  setLoading(false)
    }
  }
  const handleBackPress = () => {
  hapticFeedback.selection()
    router.back() }
  // Format percentage with 1 decimal place,
  const formatPercentage = (value: number) => {
  return `${value.toFixed(1)}%`
  };
  // Get milestone display text and description,
  const getMilestoneInfo = ($2) => {
  switch (milestone) {
  case MilestoneType.FIRST_MATCH:  ;
        return {
  title: 'First Match',
    description: 'You made your first match with another user!' }
      case MilestoneType.FIVE_MATCHES: return {, title: '5 Matches',
  description: 'You\'ve matched with 5 potential roommates.'
  },
  case MilestoneType.TEN_MATCHES: return {, title: '10 Matches',
  description: 'You\'ve reached 10 matches! You\'re on a roll!'
  },
  case MilestoneType.FIRST_CONVERSATION: return {, title: 'First Conversation',
  description: 'You started your first conversation with a match.'
  },
  case MilestoneType.FIVE_CONVERSATIONS: return {, title: '5 Conversations',
  description: 'You\'ve had conversations with 5 different matches.'
  },
  case MilestoneType.HIGH_RESPONSE_RATE: return {, title: 'High Response Rate',
  description: 'You have a high response rate of 80% or more!'
  },
  default: return {, title: milestone,
  description: 'Achievement unlocked!'
  }
  }
  },
  if (loading) {
  return (
  <SafeAreaView style= {styles.container}>
  <View style={styles.header}>,
  <TouchableOpacity style={styles.backButton} onPress={handleBackPress} hitSlop={   top: 10, right: 10bottom: 10left: 10       },
  >
            <ChevronLeft size={24} color={{theme.colors.text} /}>,
  </TouchableOpacity>
          <Text style={styles.headerTitle}>Match Statistics</Text>,
  <View style={{ width: 24} /}>
        </View>,
  <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={{theme.colors.primary} /}>,
  </View>
      </SafeAreaView>,
  )
  },
  if (error || !statistics) {
    return (
  <SafeAreaView style={styles.container}>
        <View style={styles.header}>,
  <TouchableOpacity style={styles.backButton} onPress={handleBackPress} hitSlop={   top: 10, right: 10bottom: 10left: 10       },
  >
            <ChevronLeft size={24} color={{theme.colors.text} /}>,
  </TouchableOpacity>
          <Text style={styles.headerTitle}>Match Statistics</Text>,
  <View style={{ width: 24} /}>
        </View>,
  <View style={styles.errorContainer}>
          <Text style={styles.errorText}>,
  {error || 'No statistics available'}
          </Text>,
  <TouchableOpacity style={styles.retryButton} onPress={loadStatistics}
          >,
  <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>,
  </View>
      </SafeAreaView>,
  )
  },
  return (
    <SafeAreaView style={styles.container}>,
  <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleBackPress} hitSlop={   top: 10, right: 10bottom: 10left: 10       },
  >
          <ChevronLeft size={24} color={{theme.colors.text} /}>,
  </TouchableOpacity>
        <Text style={styles.headerTitle}>Match Statistics</Text>,
  <View style={{ width: 24} /}>
      </View>,
  <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}
      >,
  <View style={styles.summaryCard}>
          <Text style={styles.summaryTitle}>Match Summary</Text>,
  <Text style={styles.summarySubtitle}>
            Your matching activity since joining, ,
  </Text>
          <View style = {styles.statsGrid}>,
  <View style={styles.statItem}>
              <View style={styles.statIconContainer}>,
  <Heart size={20} color={{theme.colors.primary} /}>
              </View>,
  <Text style={styles.statValue}>{statistics.totalMatches}</Text>
              <Text style={styles.statLabel}>Total Matches</Text>,
  </View>
            <View style={styles.statItem}>,
  <View style={styles.statIconContainer}>
                <Users size={20} color={{theme.colors.primary} /}>,
  </View>
              <Text style={styles.statValue}>{statistics.activeMatches}</Text>,
  <Text style={styles.statLabel}>Active Matches</Text>
            </View>,
  <View style={styles.statItem}>
              <View style={styles.statIconContainer}>,
  <MessageCircle size={20} color={{theme.colors.primary} /}>
              </View>,
  <Text style={styles.statValue}>{statistics.conversationStarted}</Text>
              <Text style={styles.statLabel}>Conversations</Text>,
  </View>
            <View style={styles.statItem}>,
  <View style={styles.statIconContainer}>
                <Star size={20} color={{theme.colors.primary} /}>,
  </View>
              <Text style={styles.statValue}>{formatPercentage(statistics.matchQuality)}</Text>,
  <Text style={styles.statLabel}>Match Quality</Text>
            </View>,
  </View>
        </View>,
  <View style={styles.detailsCard}>
          <Text style={styles.detailsTitle}>Engagement Metrics</Text>,
  <View style={styles.metricsContainer}>
            <View style={styles.metricItem}>,
  <View style={styles.metricHeader}>
                <Text style={styles.metricName}>Response Rate</Text>,
  <View style={{ [styles.metricBadgestatistics.responseRate >= 70 ? styles.highBadge      : statistics.responseRate >= 40 ? styles.mediumBadge  :  styles.lowBadge
                ]  ] }>,
  <Text style = {styles.metricBadgeText}>
                    {statistics.responseRate >= 70 ? 'High'   : statistics.responseRate >= 40 ? 'Medium'  : ,
  'Low'}
                  </Text>,
  </View>
              </View>,
  <View style={styles.progressBarContainer}>
                <View ,
  style = {[
                    styles.progressBar, ,
  { width: `${Math.min(statistics.responseRate, 100)}%` }
   ]},
  />
              </View>,
  <Text style={styles.metricValue}>
                {formatPercentage(statistics.responseRate)},
  </Text>
              <Text style={styles.metricDescription}>,
  Percentage of matches that led to conversations
              </Text>,
  </View>
            <View style={styles.metricItem}>,
  <View style={styles.metricHeader}>
                <Text style={styles.metricName}>Average Response Time</Text>,
  </View>
              <View style={styles.metricValueContainer}>,
  <Clock size={16} color={{theme.colors.gray[500]} /}>,
  <Text style={styles.metricTimeValue}>
                  {statistics.averageResponseTime.toFixed(1)} hours,
  </Text>
              </View>,
  <Text style= {styles.metricDescription}>
                Average time to respond to new matches,
  </Text>
            </View>,
  </View>
        </View>,
  {statistics.milestones.length > 0 && (
          <View style= {styles.achievementsCard}>,
  <Text style={styles.achievementsTitle}>Achievements</Text>
            <Text style={styles.achievementsSubtitle}>,
  Milestones you've reached so far, ,
  </Text>
            <View style={styles.achievementsList}>,
  {statistics.milestones.map((milestone, index) => {
  const { title, description  } = getMilestoneInfo(milestone),
  return (
    <View key={index} style={styles.achievementItem}>,
  <View style={styles.achievementIconContainer}>
                      <Award size={20} color={{theme.colors.primary[500]} /}>,
  </View>
                    <View style={styles.achievementContent}>,
  <Text style={styles.achievementTitle}>{title}</Text>
                      <Text style={styles.achievementDescription}>,
  {description}
                      </Text>,
  </View>
                  </View>,
  )
              })},
  </View>
          </View>,
  )}
      </ScrollView>,
  </SafeAreaView>
  )
  }
const styles = StyleSheet.create({
  container: {
      flex: 1,
  backgroundColor: theme.colors.gray[100] }
  header: {
      flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'space-between',
  paddingHorizontal: 16,
    paddingVertical: 12,
  backgroundColor: 'white',
    borderBottomWidth: 1,
  borderBottomColor: theme.colors.gray[200] }
  headerTitle: {
      fontSize: 18,
  fontWeight: '600',
    color: theme.colors.gray[900] }
  backButton: { paddin, g: 4 },
  scrollView: { fle, x: 1 }
  scrollContent: { paddin, g: 16,
    paddingBottom: 32 },
  summaryCard: {
      backgroundColor: 'white',
  borderRadius: 12,
    padding: 16,
  marginBottom: 16,
    shadowColor: '#000', ,
  shadowOffset: { width: 0, height: 2 }, ,
  shadowOpacity: 0.05,
    shadowRadius: 3,
  elevation: 2
  },
  summaryTitle: { fontSiz, e: 18,
    fontWeight: '600',
  color: theme.colors.gray[900],
    marginBottom: 4 },
  summarySubtitle: { fontSiz, e: 14,
    color: theme.colors.gray[500],
  marginBottom: 16 }
  statsGrid: {
      flexDirection: 'row',
  flexWrap: 'wrap',
    justifyContent: 'space-between' }
  statItem: {
      width: '48%',
  backgroundColor: theme.colors.gray[50],
    borderRadius: 8,
  padding: 12,
    marginBottom: 12,
  alignItems: 'center'
  },
  statIconContainer: { widt, h: 40,
    height: 40,
  borderRadius: 20,
    backgroundColor: theme.colors.primary[50],
  justifyContent: 'center',
    alignItems: 'center',
  marginBottom: 8 }
  statValue: { fontSiz, e: 20,
    fontWeight: '700',
  color: theme.colors.gray[900],
    marginBottom: 4 },
  statLabel: {
      fontSize: 12,
  color: theme.colors.gray[500],
    textAlign: 'center' }
  detailsCard: {
      backgroundColor: 'white',
  borderRadius: 12,
    padding: 16,
  marginBottom: 16,
    shadowColor: '#000',
  shadowOffset: { width: 0, height: 2 } ,
  shadowOpacity: 0.05,
    shadowRadius: 3,
  elevation: 2
  },
  detailsTitle: { fontSiz, e: 18,
    fontWeight: '600',
  color: theme.colors.gray[900],
    marginBottom: 16 },
  metricsContainer: { ga, p: 16 }
  metricItem: { backgroundColo, r: theme.colors.gray[50],
    borderRadius: 8,
  padding: 12 }
  metricHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 8 },
  metricName: {
      fontSize: 14,
  fontWeight: '600',
    color: theme.colors.gray[800] }
  metricBadge: { paddingHorizonta, l: 8,
    paddingVertical: 2,
  borderRadius: 12 }
  highBadge: {
      backgroundColor: theme.colors.primary[100] }
  mediumBadge: {
      backgroundColor: theme.colors.gray[300] }
  lowBadge: {
      backgroundColor: theme.colors.gray[400] }
  metricBadgeText: {
      fontSize: 12,
  fontWeight: '500'
  },
  progressBarContainer: {
      height: 8,
  backgroundColor: theme.colors.gray[200],
    borderRadius: 4,
  marginBottom: 8,
    overflow: 'hidden' }
  progressBar: { heigh, t: '100%',
    backgroundColor: theme.colors.primary[500],
  borderRadius: 4 }
  metricValue: { fontSiz, e: 16,
    fontWeight: '700',
  color: theme.colors.gray[900],
    marginBottom: 4 },
  metricValueContainer: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: 4 }
  metricTimeValue: { fontSiz, e: 16,
    fontWeight: '700',
  color: theme.colors.gray[900],
    marginLeft: 4 },
  metricDescription: {
      fontSize: 12,
  color: theme.colors.gray[500] }
  achievementsCard: {
      backgroundColor: 'white',
  borderRadius: 12,
    padding: 16,
  shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 } ,
  shadowOpacity: 0.05,
    shadowRadius: 3,
  elevation: 2
  },
  achievementsTitle: { fontSiz, e: 18,
    fontWeight: '600',
  color: theme.colors.gray[900],
    marginBottom: 4 },
  achievementsSubtitle: { fontSiz, e: 14,
    color: theme.colors.gray[500],
  marginBottom: 16 }
  achievementsList: { ga, p: 12 },
  achievementItem: { flexDirectio, n: 'row',
    backgroundColor: theme.colors.gray[50],
  borderRadius: 8,
    padding: 12 },
  achievementIconContainer: { widt, h: 40,
    height: 40,
  borderRadius: 20,
    backgroundColor: theme.colors.primary[50],
  justifyContent: 'center',
    alignItems: 'center',
  marginRight: 12 }
  achievementContent: { fle, x: 1 },
  achievementTitle: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.gray[900],
    marginBottom: 4 },
  achievementDescription: {
      fontSize: 14,
  color: theme.colors.gray[600] }
  loadingContainer: {
      flex: 1,
  justifyContent: 'center',
    alignItems: 'center' }
  errorContainer: { fle, x: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 16 },
  errorText: { fontSiz, e: 16,
    color: theme.colors.gray[600],
  textAlign: 'center',
    marginBottom: 16 },
  retryButton: { backgroundColo, r: theme.colors.primary[500],
    paddingHorizontal: 16,
  paddingVertical: 8,
    borderRadius: 8 },
  retryButtonText: {
      fontSize: 14),
  fontWeight: '600'),
    color: 'white') }
})