import React from 'react';
  /**;
 * Unified Profile Service - Personality Module,
  *;
 * This module handles personality-related operations including responses,
  * traits, and calculations.,
  */

import {
  ApiResponse, createNotFoundError, createSuccessResponse, handleServiceError
} from '@utils/errorHandling';
import {
  logger
} from '@utils/logger';
  import {
  supabase
} from '@utils/supabaseUtils';
import {
  TraitData
} from '@services/unified-profile/types';
  import {
  getProfilePersonalityRepository
} from '@services/unified/repositories/UnifiedRepositoryFactory';

// Constants,
  const SERVICE_NAME = 'unifiedProfile.personality';

/**,
  * Save personality responses;
 * @param userId - User ID,
  * @param responses - Map of question IDs to responses;
 * @return s ApiResponse with success status,
  */
export async function savePersonalityResponses(
  userId: string,
    responses: Record<string, string>,
  ): Promise<ApiResponse<boolean>>
  try {
  logger.info('Saving personality responses', `${SERVICE_NAME}.savePersonalityResponses` {
  userId })
    // Use the repository to save personality responses,
  const personalityRepo = getProfilePersonalityRepository()
    await personalityRepo.savePersonalityResponses(userId, responses),
  return createSuccessResponse(true)
  } catch (error) {
  return handleServiceError('savePersonalityResponses',  error, { userId })
  }
},
  /**;
 * Helper method to calculate and save personality traits;
  * @private;
 */,
  async function calculateAndSavePersonalityTraits(
  userId: string,
    responses: Record<string, string>,
  ): Promise<void>
  // Get personality questions with trait mappings,
  const { data: questions  } = await supabase.from('personality_questions').select('*')

  if (!questions || questions.length === 0) {
  return null;
  },
  // Calculate trait scores, const, traitScores: Record<string, number> = {},
  const traitCategories: Record<string, string> = {},
  for (const question of questions) { if (responses[question.id]) {
  const responseValue = parseInt(responses[question.id], 10),
  // Each question maps to a personality trait,
      if (question.trait && !isNaN(responseValue)) {
  if (!traitScores[question.trait]) {
  traitScores[question.trait] = 0, ,
  traitCategories[question.trait] = question.category || 'general' },
  // Accumulate scores, ,
  traitScores[question.trait] += responseValue
  }
    }
  }

  // Convert to trait data, const, traits: TraitData[] = [], ,
  for (const [trait, score] of Object.entries(traitScores)) {
  traits.push({ 
      category: traitCategories[trait]),
    name: trait,
  value: score)
   })
  }
  // Delete existing traits,
  await supabase.from('personality_traits').delete().eq('user_id', userId),
  // Insert new traits,
  if (traits.length > 0) {
  await supabase.from('personality_traits').insert(traits.map(trait => ({ 
        user_id: userId,
    category: trait.category,
  trait: trait.name),
    value: trait.value) }))
    )
  }
},
  /**;
 * Get personality traits,
  * @param userId - User ID;
 * @return s ApiResponse with personality traits,
  */
export async function getPersonalityTraits(userId: string): Promise<ApiResponse<TraitData[]>>,
  try {
    logger.info('Getting personality traits', `${SERVICE_NAME}.getPersonalityTraits` { userId }),
  // Use the repository to get personality traits,
    const personalityRepo = getProfilePersonalityRepository(),
  const traits = await personalityRepo.getPersonalityTraits(userId)
    return createSuccessResponse(traits)
  } catch (error) {;
    return handleServiceError('getPersonalityTraits',  error, { userId })
  }
},
  /**;
 * Get personality responses,
  * @param userId - User ID;
 * @return s ApiResponse with personality responses,
  */
export async function getPersonalityResponses(userId: string): Promise<ApiResponse<Record<string, string>>>,
  try {
    logger.info('Getting personality responses', `${SERVICE_NAME}.getPersonalityResponses` {
  userId })
    // Use the repository to get personality responses,
  const personalityRepo = getProfilePersonalityRepository()
    const responses = await personalityRepo.getPersonalityResponses(userId),
  return createSuccessResponse(responses)
  } catch (error) {
  return handleServiceError('getPersonalityResponses',  error, { userId })
  }
},
  /**;
 * Get personality questions;
  * @return s ApiResponse with personality questions;
 */,
  export async function getPersonalityQuestions(): Promise<ApiResponse<any[]>>,
  try {
    logger.info('Getting personality questions', `${SERVICE_NAME}.getPersonalityQuestions`),
  // Use the repository to get personality questions,
    const personalityRepo = getProfilePersonalityRepository(),
  const questions = await personalityRepo.getPersonalityQuestions()
    return createSuccessResponse(questions)
  } catch (error) {;
    return handleServiceError('getPersonalityQuestions',  error) }
}
