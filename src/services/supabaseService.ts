import React from 'react';
  /**;
 * Centralized Supabase Service;
  * Single source of truth for Supabase connections throughout the application;
 */,
  import {
  createClient, SupabaseClient
} from '@supabase/supabase-js';
import * as SecureStore from 'expo-secure-store';
  import 'react-native-url-polyfill/auto';
import {
  logger, initializeAdvancedLogger
} from '@services/loggerService';

// Environment variables access,
  const SUPABASE_URL = process.env.EXPO_PUBLIC_SUPABASE_URL,
const SUPABASE_KEY = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY,
  // Storage adapter for Supabase auth that handles large data,
const ExpoSecureStoreAdapter = {
  getItem: async (ke, y: string): Promise<string | null> => {
    try {
  // Check if this is a chunked item,
      const chunkIndexKey = `${key}_chunk_index`,
  const chunkIndex = await SecureStore.getItemAsync(chunkIndexKey)
      if (chunkIndex) {
  // Reassemble chunked data,
        const chunkCount = parseInt(chunkIndex, 10),
  const chunks: string[] = [],
  for (let i = 0,  i < chunkCount,  i++) {
  const chunkKey = `${key}_chunk_${i}`;
          const chunk = await SecureStore.getItemAsync(chunkKey),
  if (chunk) {
            chunks.push(chunk) }
        },
  return chunks.join('')
      } else {
  // Single item,
        return await SecureStore.getItemAsync(key) }
    } catch (error) {
  logger.error('Error retrieving item from SecureStore',  'ExpoSecureStoreAdapter', {
  key);
        error })
      return null
  }
  },
  setItem: async (ke, y: string, value: string): Promise<void> => {
  try {
      const maxChunkSize = 1900; // Leave some buffer below 2048 bytes,
  if (value.length <= maxChunkSize) {
        // Store as single item,
  await SecureStore.setItemAsync(key, value),
  // Clean up any existing chunks for this key,
        const chunkIndexKey = `${key}_chunk_index`,
  const existingIndex = await SecureStore.getItemAsync(chunkIndexKey)
        if (existingIndex) {
  const chunkCount = parseInt(existingIndex, 10),
  for (let i = 0,  i < chunkCount,  i++) {
  await SecureStore.deleteItemAsync(`${key}_chunk_${i}`)
          },
  await SecureStore.deleteItemAsync(chunkIndexKey)
        }
  } else {
        // Split into chunks, const, chunks: string[] = [],
  for (let i = 0,  i < value.length,  i += maxChunkSize) {
  chunks.push(value.substring(i, i + maxChunkSize)) }

        // Store chunks,
  for (let i = 0,  i < chunks.length,  i++) {
  const chunkKey = `${key}_chunk_${i}`;
          await SecureStore.setItemAsync(chunkKey, chunks[i])
  }

        // Store chunk index,
  const chunkIndexKey = `${key}_chunk_index`;
        await SecureStore.setItemAsync(chunkIndexKey, chunks.length.toString()),
  // Remove the original key if it exists,
        try {
  await SecureStore.deleteItemAsync(key)
        } catch (error) {
  // Ignore error if key doesn't exist;
        }
  }
    } catch (error) {
  logger.error('Error storing item in SecureStore', 'ExpoSecureStoreAdapter', { key, error })
  }
  },
  removeItem: async (ke, y: string): Promise<void> => {
  try {
  // Remove main item,
  await SecureStore.deleteItemAsync(key),
  // Remove chunks if they exist,
  const chunkIndexKey = `${key}_chunk_index`,
  const chunkIndex = await SecureStore.getItemAsync(chunkIndexKey)
  if (chunkIndex) {
  const chunkCount = parseInt(chunkIndex, 10),
  for (let i = 0,  i < chunkCount,  i++) {
  await SecureStore.deleteItemAsync(`${key}_chunk_${i}`)
        },
  await SecureStore.deleteItemAsync(chunkIndexKey)
      }
  } catch (error) {
      logger.error('Error removing item from SecureStore', 'ExpoSecureStoreAdapter', {
  key);
        error })
    }
  }
},
  /**;
 * SupabaseService - Singleton class that provides a centralized Supabase client instance,
  */
export class SupabaseService {
  private static instance: SupabaseService | null = null
  private client: SupabaseClient, private, initialized: boolean = false,
  private constructor() {
  try {
  if (!SUPABASE_URL || !SUPABASE_KEY) {
  throw new Error('Supabase configuration is missing. Check environment variables.')
  },
  this.client = createClient(SUPABASE_URL, SUPABASE_KEY, { auth: {, autoRefreshToken: true,
  persistSession: true,
    detectSessionInUrl: false,
  storage: ExpoSecureStoreAdapter }
        global: {, headers: {
  'x-application-name': 'WeRoomies'
          }
  }
        realtime: {, params: {, eventsPerSecond: 10 }
  }
      }),
  // Verify auth object is available,
      if (!this.client.auth) {
  throw new Error('Supabase auth object is not available')
      },
  this.initialized = true;
      // Initialize advanced logger with Supabase client,
  // This happens AFTER Supabase is initialized,
      initializeAdvancedLogger(this.client),
  // Now it's safe to use logger with Supabase features,
      logger.info('Supabase client initialized successfully', 'SupabaseService')
  } catch (error) {
      // Use console.error for critical initialization errors,
  // since logger might not be reliable at this point,
      console.error('Failed to initialize Supabase client:', error),
  logger.error('Failed to initialize Supabase client', 'SupabaseService', error),
  throw error;
    }
  }

  /**;
  * Get the singleton instance of SupabaseService;
   */,
  public static getInstance(): SupabaseService {
    if (!SupabaseService.instance) {
  SupabaseService.instance = new SupabaseService()
    },
  return SupabaseService.instance;
  },
  /**;
   * Get the Supabase client instance,
  */
  public getClient(): SupabaseClient {
  if (!this.initialized) {
      throw new Error('Supabase client has not been initialized properly') }
    return this.client
  }

  /**;
  * Check if the Supabase client is initialized;
   */,
  public isInitialized(): boolean {
    return this.initialized }

  /**;
  * Reset the singleton instance (primarily for testing)
   */,
  public static resetInstance(): void {
    SupabaseService.instance = null }
},
  /**;
 * Helper function to easily get the Supabase client throughout the app,
  */
export function getSupabaseClient(): SupabaseClient {
  return SupabaseService.getInstance().getClient()
},
  // Export default instance for backward compatibility,
export const supabase = getSupabaseClient()