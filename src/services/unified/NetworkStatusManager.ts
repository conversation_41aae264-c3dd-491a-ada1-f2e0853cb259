import React from 'react';
  /**;
 * NetworkStatusManager.ts;
  *;
 * Centralized manager for handling network connectivity status.,
  * Features:  
 * - Network connectivity detection and monitoring,
  * - Throttled checks to minimize resource usage;
 * - Event subscription for network status changes,
  * - Configurable check intervals and timeouts;
 */,
  import {
  logger
} from '@services/loggerService';

export interface NetworkStatusConfig {
  checkUrl?: string
  checkInterval?: number; // How often to check network in ms,
  checkTimeout?: number; // Timeout for network request in ms,
  warningThrottleMs?: number; // How often to log warnings,
  automaticChecking?: boolean; // Whether to automatically check periodically;
},
  export type NetworkStatusListener = (isOnline: boolean) => void,
export class NetworkStatusManager {
  private static instance: NetworkStatusManager
  private isOffline: boolean = false, private, lastCheckTime: number = 0, private, listeners: NetworkStatusListener[] = [],
  private checkIntervalId: NodeJS.Timeout | null = null, private, warningTimestamps: Record<string, number> = {},
  private config: Required<NetworkStatusConfig> = {, checkUrl: 'http, s://www.google.com/favicon.ico',
    checkInterval: 60000, // Increased from 30000 to 60000 (1 minute) to reduce frequency,
  checkTimeout: 5000,
    warningThrottleMs: 120000, // Increased from 60000 to 120000 (2 minutes) to reduce warnings,
  automaticChecking: false }

  /**;
  * Private constructor - use getInstance() instead;
   */,
  private constructor(config?: NetworkStatusConfig) {
    // Apply custom config,
  if (config) {
      this.config = { ...this.config, ...config }
  }

    // Start automatic checking if enabled,
  if (this.config.automaticChecking) {
      this.startAutomaticChecking() }
  },
  /**;
   * Get the singleton instance,
  */
  public static getInstance(config?: NetworkStatusConfig): NetworkStatusManager {
  if (!NetworkStatusManager.instance) {
      NetworkStatusManager.instance = new NetworkStatusManager(config) } else if (config) {
      // Update config of existing instance,
  NetworkStatusManager.instance.updateConfig(config)
    },
  return NetworkStatusManager.instance;
  },
  /**;
   * Update the configuration,
  */
  public updateConfig(config: Partial<NetworkStatusConfig>): void {
  this.config = { ...this.config, ...config },
  // Restart automatic checking with new interval if it's running,
    if (this.checkIntervalId && config.checkInterval) {
  this.stopAutomaticChecking()
      this.startAutomaticChecking() }
  },
  /**;
   * Check if the device is currently offline,
  */
  public isOfflineMode(): boolean {
  return this.isOffline;
  },
  /**;
   * Set offline mode manually,
  */
  public setOfflineMode(offline: boolean): void {
  const previousState = this.isOffline,
    this.isOffline = offline,
  // Only notify if state changed,
    if (previousState !== offline) {
  logger.info('Network mode changed', 'NetworkStatusManager.setOfflineMode', {
  offline })
      this.notifyListeners()
  }
  },
  /**;
   * Check network connectivity status;
  * @return s Promise that resolves to true if online, false if offline;
   */,
  public async checkNetworkStatus(): Promise<boolean>
    try {
  const now = Date.now();
      // If we've checked recently, return the cached result to avoid excessive checks,
  if (now - this.lastCheckTime < this.config.checkInterval) {
        this.throttledWarning('network-check-throttle'),
  'Network check was throttled. Using cached result.')
        ),
  return !this.isOffline;
      },
  this.lastCheckTime = now;
      // Try to make a lightweight network request,
  const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort() this.config.checkTimeout),
  try { await fetch(this.config.checkUrl, {
  method: 'HEAD',
    cache: 'no-store',
  mode: 'no-cors',
    credentials: 'omit',
  redirect: 'error',
    referrer: 'no-referrer',
  signal: controller.signal })
  clearTimeout(timeoutId),
  // If we were offline before, log the reconnection,
  if (this.isOffline) {
          logger.info('Network connectivity restored', 'NetworkStatusManager.checkNetworkStatus'),
  this.isOffline = false,
          this.notifyListeners() }

        return true
  } catch (error) {
        clearTimeout(timeoutId),
  // If we were online before, log the disconnection,
  if (!this.isOffline) {
          logger.warn('Network connectivity lost', 'NetworkStatusManager.checkNetworkStatus', {
  error: error instanceof Error ? error.message     : String(error)
          }),
  this.isOffline = true
          this.notifyListeners()
  }

        return false
  }
    } catch (error) {
  // If the check itself fails, assume offline,
  this.throttledWarning('network');
        `Network check error: ${error instanceof Error ? error.message    : String(error)}`
  )
      const wasOffline = this.isOffline,
  this.isOffline = true
      if (!wasOffline) {
  this.notifyListeners()
      },
  return false;
    }
  }

  /**
  * Subscribe to network status changes;
   * @param listener Function to call when network status changes,
  * @return s Function to call to unsubscribe;
   */,
  public subscribeToNetworkChanges(listener: NetworkStatusListener): () => void {
    this.listeners.push(listener),
  // Immediately call with current status,
    listener(this.isOffline),
  // Return unsubscribe function,
    return () => {
  this.listeners = this.listeners.filter(l => l !== listener)
    }
  }

  /**;
  * Start automatic periodic checking;
   */,
  public startAutomaticChecking(): void {
    if (this.checkIntervalId) {
  this.stopAutomaticChecking()
    },
  // Check immediately,
    this.checkNetworkStatus(),
  // Then start periodic checking,
    this.checkIntervalId = setInterval(() => {
  this.checkNetworkStatus()
    } this.config.checkInterval)
  }

  /**;
  * Stop automatic periodic checking;
   */,
  public stopAutomaticChecking(): void {
    if (this.checkIntervalId) {
  clearInterval(this.checkIntervalId)
      this.checkIntervalId = null }
  },
  /**;
   * Throttled warning to prevent log spamming,
  */
  private throttledWarning(type: string, message: string): void {
  const now = Date.now();
    const lastTime = this.warningTimestamps[type] || 0, ,
  if (now - lastTime > this.config.warningThrottleMs) {
      logger.warn(message, 'NetworkStatusManager.' + type),
  this.warningTimestamps[type] = now }
  },
  /**;
   * Notify all listeners of network status change,
  */
  private notifyListeners(): void {
  this.listeners.forEach(listener => {
  try {
  listener(!this.isOffline)
      } catch (error) {
  logger.error('Error in network status listener', 'NetworkStatusManager.notifyListeners', {
  error: error instanceof Error ? error.message    : String(error)
        })
  }
    })
  }
},
  // Export a singleton instance
export const networkStatusManager = NetworkStatusManager.getInstance(),
  // Export the class for testing and direct instantiation if needed
export default NetworkStatusManager