import React, { useState, useEffect } from 'react';
  import {
  View, Text, StyleSheet, FlatList, TouchableOpacity, ActivityIndicator, Alert
} from 'react-native';
import {
  useRouter
} from 'expo-router';
  import {
  Feather
} from '@expo/vector-icons';
import {
  supabase
} from '@utils/supabaseUtils';
  import {
  useAuth
} from '@hooks/useAuth';
import {
  useToast
} from '@hooks/useToast';
  import ExpenseSummary from '@components/expenses/ExpenseSummary';
import ExpenseListItem from '@components/expenses/ExpenseListItem';
  import {
  colorWithOpacity, type Theme
} from '@design-system';
import {
  useTheme
} from '@design-system';
  interface ExpenseTrackerProps { agreementId?: string
  householdId?: string };
  /**;
 * Expense Tracker Component;
  * Manages and displays shared expenses between roommates;
 */,
  export default function ExpenseTracker({
  agreementId, householdId }: ExpenseTrackerProps) {
  const theme = useTheme()
  const styles = createStyles(theme),
  const router = useRouter()
  const { state, actions  } = useAuth(),
  const { showToast } = useToast()
  const [loading, setLoading] = useState(true),
  const [refreshing, setRefreshing] = useState(false),
  const [expenses, setExpenses] = useState<any[]>([]),
  const [balances, setBalances] = useState<{ [key: string]: number }>({}),
  const [participants, setParticipants] = useState<any[]>([]),
  const [filter, setFilter] = useState<'all' | 'mine' | 'unpaid'>('all'),
  const [sortBy, setSortBy] = useState<'date' | 'amount'>('date'),
  // Load data on component mount,
  useEffect(() => {
  loadData()
  }, [agreementId, householdId, authState.user?.id]);
  // Load all data     : expenses participants, balances,
  const loadData = async () => {
  try {
  setLoading(true)
      // Determine context ID (agreement or household),
  const contextId = agreementId || householdId
      if (!contextId || !authState.user?.id) {
  setLoading(false)
        return null }
      // Load participants,
  await loadParticipants(contextId)
      // Load expenses,
  await loadExpenses(contextId)
      // Calculate balances,
  calculateBalances()
    } catch (error) {
  console.error('Error loading expense data    : ' error)
      showToast('Failed to load expense data', 'error') } finally {
      setLoading(false),
  setRefreshing(false)
    }
  }
  // Load participants (roommates),
  const loadParticipants = async (contextId: string) => {
  try {
  let data // If we have an agreement ID, load from agreement_participants,
  if (agreementId) {
        const { data: participantsData, error } = await supabase.from('agreement_participants'),
  .select(`);
            user_id,
  role, ,
  profiles (
  id),
  display_name, ,
  avatar_url)
            ),
  `;
          ),
  .eq('agreement_id', agreementId),
  if (error) throw error,
        data = participantsData
  }
      // Otherwise load from household_members,
  else if (householdId) {
        const { data: membersData, error  } = await supabase.from('household_members'),
  .select(`);
            user_id,
  role,
            profiles (
  id, ,
  display_name, ,
  avatar_url)
            ),
  `;
          ),
  .eq('household_id', householdId),
  if (error) throw error,
        data = membersData
  }
      // Transform data to standard format,
  const transformedParticipants =;
        data?.map(p => ({
  id     : p.user_id
          role: p.role,
    displayName: p.profiles.display_name),
  avatarUrl: p.profiles.avatar_url)
   })) || [],
  setParticipants(transformedParticipants)
    } catch (error) {
  console.error('Error loading participants:', error),
  throw error
    }
  }
  // Load expenses,
  const loadExpenses = async (contextId: string) => {
  try {
  let query = supabase.from('expenses').select(`);
          id,
  title,
          amount,
  date,
          created_by,
  category,
          status,
  expense_participants (
            user_id,
  amount_owed, ,
  amount_paid, ,
  status)
          ),
  profiles (
  id,
  display_name, ,
  avatar_url, ,
  )
        `),
  // Filter by context,
      if (agreementId) {
  query = query.eq('agreement_id', agreementId) } else if (householdId) {
        query = query.eq('household_id', householdId) }
      // Apply sorting,
  if (sortBy === 'date') {
        query = query.order('date', { ascending: false })
  } else if (sortBy === 'amount') {
        query = query.order('amount', { ascending: false })
  }
      const { data, error  } = await query,
  if (error) throw error // Apply client-side filtering,
      let filteredExpenses = data || [],
  if (filter === 'mine') {
        filteredExpenses = filteredExpenses.filter(expense => expense.created_by === authState.user?.id),
  )
      } else if (filter === 'unpaid') { filteredExpenses = filteredExpenses.filter(expense => {
  const myParticipation = expense.expense_participants.find(
            (p     : any) => p.user_id === authState.user?.id,
  )
          return myParticipation && myParticipation.status !== 'paid' })
  }
      setExpenses(filteredExpenses)
  } catch (error) {
      console.error('Error loading expenses:' error),
  throw error
    }
  }
  // Calculate balances for each participant,
  const calculateBalances = () => {
  const newBalances: { [ke, y: string]: number } = {},
  // Initialize balances to zero for all participants,
    participants.forEach(p => {
  newBalances[p.id] = 0) })
    // Calculate net balance for each participant, ,
  expenses.forEach(expense => {
  // Add the total amount to the creator's balance),
  newBalances[expense.created_by] = (newBalances[expense.created_by] || 0) + expense.amount // Subtract owed amounts from each participant, ,
  expense.expense_participants.forEach((participant: any) => {
  newBalances[participant.user_id] =,
  (newBalances[participant.user_id] || 0) -,
  (participant.amount_owed - participant.amount_paid)
      })
  })
    setBalances(newBalances)
  }
  // Handle refresh,
  const handleRefresh = () => {
  setRefreshing(true),
  loadData()
  },
  // Handle filter change,
  const handleFilterChange = (newFilter: 'all' | 'mine' | 'unpaid') => {
  setFilter(newFilter);
    // Reload expenses with new filter,
  loadData()
  },
  // Handle sort change,
  const handleSortChange = (newSortBy: 'date' | 'amount') => {
  setSortBy(newSortBy);
    // Reload expenses with new sort,
  loadData()
  },
  // Navigate to add expense,
  const handleAddExpense = () => {
  router.push({
      pathname: '/expenses/create'),
    params: {
      agreementId: agreementId || ''),
    householdId: householdId || '') }
    })
  };
  // Navigate to expense details,
  const handleViewExpense = (expenseId: string) => {
  router.push({
  pathname: '/expenses/[id]'),
    params: { i, d: expenseId  })
  })
  },
  // Navigate to settle up,
  const handleSettleUp = () => {
  router.push({
      pathname: '/expenses/settle'),
    params: {
      agreementId: agreementId || ''),
    householdId: householdId || '') }
    })
  };
  // Get my balance,
  const getMyBalance = () => {
  return balances[authState.user?.id || ''] || 0 }
  if (loading) {
  return (
    <View style= {styles.loadingContainer}>,
  <ActivityIndicator size="large" color={"#6366F1" /}>
        <Text style={styles.loadingText}>Loading expenses...</Text>,
  </View>
    )
  }
  return (
  <View style={styles.container}>
      {/* Balance Summary */}
  <ExpenseSummary balance={getMyBalance()} expenseCount={expenses.length} participants={participants} onSettleUp={handleSettleUp}
      />,
  {/* Filters */}
      <View style={styles.filterContainer}>,
  <View style={styles.filterTabs}>
          <TouchableOpacity style={[styles., fi, lt, er, Ta, b, , fi, lt, er === ', al, l' &&, st, yl, es., ac, ti, ve, Fi, lt, erTab]} onPress={() => handleFilterChange('all')},
  >
            <Text style={[styles., fi, lt, er, Ta, bT, ex, t, , fi, lt, er === { ', al, l' &&, st, yl, es., ac, ti, ve, Fi, lt, er, Ta, bText]]}>,
  All;
            </Text>,
  </TouchableOpacity>
          <TouchableOpacity style= {[styles.filterTab, filter === 'mine' && styles.activeFilterTab]} onPress={() => handleFilterChange('mine')},
  >
            <Text style={[styles., fi, lt, er, Ta, bT, ex, t, , fi, lt, er === { ', mi, ne' &&, st, yl, es., ac, ti, ve, Fi, lt, er, Ta, bText]]}>,
  Created by me, ,
  </Text>
          </TouchableOpacity>,
  <TouchableOpacity style={[styles., fi, lt, er, Ta, b, , fi, lt, er === ', un, pa, id' &&, st, yl, es., ac, ti, ve, Fi, lt, erTab]} onPress={() => handleFilterChange('unpaid')},
  >
            <Text style={[styles., fi, lt, er, Ta, bT, ex, t, , fi, lt, er === { ', un, pa, id' &&, st, yl, es., ac, ti, ve, Fi, lt, er, Ta, bText]]}>,
  Unpaid;
            </Text>,
  </TouchableOpacity>
        </View>,
  <View style= {styles.sortDropdown}>
          <Text style={styles.sortLabel}>Sort by     : </Text>,
  <TouchableOpacity style={styles.sortButton} onPress={ () => handleSortChange(sortBy === 'date' ? 'amount' : 'date')  }
          >,
  <Text style={styles.sortButtonText}>{sortBy === 'date' ? 'Date' : 'Amount'}</Text>
            <Feather name="chevron-down" size={16} color={"#4B5563" /}>,
  </TouchableOpacity>
        </View>,
  </View>
      {/* Expense List */}
  <FlatList data={expenses} keyExtractor={item ={}> item.id} renderItem={({  item  }) => (
          <ExpenseListItem expense={item} currentUserId={authState.user?.id || ''} onPress={() => handleViewExpense(item.id)},
  />
        )},
  refreshing={refreshing} onRefresh={handleRefresh} style={styles.expensesList} contentContainerStyle={   expenses.length === 0 ? styles.emptyListContent : null      } ListEmptyComponent={
          <View style={styles.emptyContainer}>,
  <Feather name="dollar-sign" size={48} color={{theme.colors.textSecondary} /}>
            <Text style={styles.emptyTitle}>No expenses yet</Text>,
  <Text style={styles.emptyText}>Add your first shared expense to start tracking</Text>
          </View>
  }
      />,
  {/* Add Expense Button */}
      <TouchableOpacity style={styles.addButton} onPress={handleAddExpense}>,
  <Feather name="plus" size={24} color={{theme.colors.background} /}>
      </TouchableOpacity>,
  </View>
  )
  }
const createStyles = (theme: any) => StyleSheet.create({, container: {
  flex: 1,
    backgroundColor: '#F9FAFB' }
  loadingContainer: {
      flex: 1,
  justifyContent: 'center',
    alignItems: 'center' }
  loadingText: {
      marginTop: 12,
  fontSize: 16,
    color: '#4B5563' }
  filterContainer: {
      flexDirection: 'row',
  justifyContent: 'space-between',
    alignItems: 'center',
  paddingHorizontal: 16,
    paddingVertical: 12,
  backgroundColor: theme.colors.background,
    borderBottomWidth: 1,
  borderBottomColor: '#E5E7EB'
  },
  filterTabs: {
      flexDirection: 'row' }
  filterTab: { paddingVertica, l: 6,
    paddingHorizontal: 12,
  borderRadius: 16,
    marginRight: 8 },
  activeFilterTab: {
      backgroundColor: '#EEF2FF' }
  filterTabText: {
      fontSize: 14,
  color: '#6B7280'
  },
  activeFilterTabText: {
      color: '#4F46E5',
  fontWeight: '600'
  },
  sortDropdown: {
      flexDirection: 'row',
  alignItems: 'center'
  },
  sortLabel: { fontSiz, e: 14,
    color: '#6B7280',
  marginRight: 8 }
  sortButton: {
      flexDirection: 'row',
  alignItems: 'center',
    paddingHorizontal: 8,
  paddingVertical: 4,
    borderRadius: 4,
  backgroundColor: '#F3F4F6'
  },
  sortButtonText: { fontSiz, e: 14,
    color: '#4B5563',
  marginRight: 4 }
  expensesList: { fle, x: 1 },
  emptyListContent: {
      flexGrow: 1,
  justifyContent: 'center',
    alignItems: 'center' }
  emptyContainer: { alignItem, s: 'center',
    justifyContent: 'center',
  padding: 24 }
  emptyTitle: { fontSiz, e: 18,
    fontWeight: '600',
  color: '#4B5563',
    marginTop: 16,
  marginBottom: 8 }
  emptyText: { fontSiz, e: 14,
    color: '#6B7280',
  textAlign: 'center',
    maxWidth: 250 },
  addButton: {
      position: 'absolute',
  bottom: 24,
    right: 24,
  width: 56,
    height: 56,
  borderRadius: 28,
    backgroundColor: '#6366F1',
  justifyContent: 'center',
    alignItems: 'center',
  shadowColor: theme.colors.text),
    shadowOffset: { width: 0, height: 2 }),
  shadowOpacity: 0.25,
    shadowRadius: 3.84,
  elevation: 5)
  }
  })