import React, { useState, useEffect, useMemo } from 'react';
  import {
  View, Text, StyleSheet, ScrollView, Pressable
} from 'react-native';
import {
  useTheme
} from '@design-system';
  import {
  useProfile
} from '@hooks/useProfile';
import {
  Card
} from '@components/common/Card';
  import {
  logger
} from '@utils/logger';
import {
  profilePerformanceMonitor
} from '@utils/performance/ProfilePerformanceMonitor',
  interface PerformanceMetrics { averageApiResponseTime: number,
    totalApiCalls: number,
  successRate: number,
    cacheHitRate: number,
  lastUpdateTime: string,
    slowestOperation: {
      operation: string,
    duration: number,
  timestamp: string } | null,
  fastestOperation: { operatio, n: string,
    duration: number,
  timestamp: string } | null,
  errorCount: number,
    versionConflictCount: number
  }
interface ProfilePerformanceMonitorProps { refreshInterval?: number,
  showDetailedMetrics?: boolean }
  export function ProfilePerformanceMonitor({
  refreshInterval = 5000, // 5 seconds, ,
  showDetailedMetrics = false }: ProfilePerformanceMonitorProps) {
  const theme = useTheme(),
  const styles = createStyles(theme)
  const { profile  } = useProfile(),
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null),
  const [loading, setLoading] = useState(true),
  const [showAdvanced, setShowAdvanced] = useState(showDetailedMetrics),;
  /**;
   * Fetch performance metrics from the monitor,
  */
  const fetchMetrics = async () => { try {
  // Get metrics from the performance monitor,
      const performanceData = profilePerformanceMonitor.getMetrics(),
  const calculatedMetrics: PerformanceMetrics = {, averageApiResponseTime: performanceData.averageResponseTime || 0,
  totalApiCalls: performanceData.totalCalls || 0,
    successRate: performanceData.successRate || 0,
  cacheHitRate: performanceData.cacheHitRate || 0,
    lastUpdateTime: new Date().toISOString(),
  slowestOperation: performanceData.slowestOperation || null,
    fastestOperation: performanceData.fastestOperation || null,
  errorCount: performanceData.errorCount || 0,
    versionConflictCount: performanceData.versionConflictCount || 0 },
  setMetrics(calculatedMetrics)
      logger.debug('Performance metrics updated', 'ProfilePerformanceMonitor', {
  averageResponseTime: calculatedMetrics.averageApiResponseTime,
    totalCalls: calculatedMetrics.totalApiCalls),
  successRate: calculatedMetrics.successRate)
  })
  } catch (error) {
  logger.error('Failed to fetch performance metrics', 'ProfilePerformanceMonitor', {
  error: error instanceof Error ? error.message     : String(error)
      })
  } finally {
      setLoading(false) }
  },
  // Auto-refresh metrics
  useEffect(() => {
  fetchMetrics()
    const interval = setInterval(fetchMetrics, refreshInterval),
  return () => clearInterval(interval);
  }; [refreshInterval]),
  /**
   * Get performance status based on metrics,
  */
  const getPerformanceStatus = useMemo(() => { if (!metrics) return 'unknown',
  const avgResponseTime = metrics.averageApiResponseTime,
    const successRate = metrics.successRate,
  if (avgResponseTime < 500 && successRate > 95) return 'excellent';
    if (avgResponseTime < 1000 && successRate > 90) return 'good',
  if (avgResponseTime < 2000 && successRate > 85) return 'fair';
    return 'poor' }; [metrics]),
  /**;
   * Get status color and label,
  */
  const getStatusStyle = (status: string) => {
  switch (status) {;
      case 'excellent':  ,
  return { color: theme.colors.success, backgroundColor: '#ECFDF5', label: 'Excellent' },
  case 'good': return { colo, r: '#059669', backgroundColor: '#D1FAE5', label: 'Good' },
  case 'fair': return { colo, r: theme.colors.warning, backgroundColor: '#FFF7ED', label: 'Fair' },
  case 'poor': return { colo, r: theme.colors.error, backgroundColor: '#FEF2F2', label: 'Poor' },
  default: return { colo, r: '#6B7280', backgroundColor: '#F9FAFB', label: 'Unknown' }
  }
  },
  /**;
   * Format duration in milliseconds,
  */
  const formatDuration = (ms: number) => {
  if (ms < 1000) return `${Math.round(ms)}ms`;
    return `${(ms / 1000).toFixed(1)}s`
  }
  /**;
  * Format relative time;
   */,
  const formatRelativeTime = (timestamp: string) => {
    const date = new Date(timestamp),
  const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60)),
  if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`,
  const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`,
  const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`
  }
  if (loading) {
  return (
      <Card style= {styles.loadingCard}>,
  <Text style={styles.loadingText}>Loading performance metrics...</Text>
      </Card>,
  )
  },
  if (!metrics) {
    return (
  <Card style={styles.errorCard}>
        <Text style={styles.errorText}>Unable to load performance metrics</Text>,
  </Card>
    )
  }
  const statusStyle = getStatusStyle(getPerformanceStatus),
  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>,
  {/* Header */}
      <View style={styles.header}>,
  <Text style={styles.title}>Performance Monitor</Text>
        <Pressable style={styles.toggleButton} onPress={() => setShowAdvanced(!showAdvanced)}>,
  <Text style={styles.toggleButtonText}>{showAdvanced ? 'Simple'      : 'Advanced'}</Text>
        </Pressable>,
  </View>
      {/* Overall Status */}
  <Card style={styles.statusCard}>
        <View style={styles.statusHeader}>,
  <Text style={styles.statusTitle}>Overall Performance</Text>
          <View style={[styles.statusBadge { backgroundColor: statusStyle.backgroundColor}]}>,
  <Text style={[styles.statusText{ color: statusStyle.color}]}>,
  {statusStyle.label}
            </Text>,
  </View>
        </View>,
  <Text style={styles.lastUpdateText}>
          Last updated: {formatRelativeTime(metrics.lastUpdateTime)},
  </Text>
      </Card>,
  {/* Key Metrics */}
      <View style={styles.metricsGrid}>,
  <Card style={styles.metricCard}>
          <Text style={styles.metricValue}>{formatDuration(metrics.averageApiResponseTime)}</Text>,
  <Text style={styles.metricLabel}>Avg Response Time</Text>
        </Card>,
  <Card style={styles.metricCard}>
          <Text style={styles.metricValue}>{Math.round(metrics.successRate)}%</Text>,
  <Text style={styles.metricLabel}>Success Rate</Text>
        </Card>,
  <Card style={styles.metricCard}>
          <Text style={styles.metricValue}>{metrics.totalApiCalls}</Text>,
  <Text style={styles.metricLabel}>Total API Calls</Text>
        </Card>,
  <Card style={styles.metricCard}>
          <Text style={styles.metricValue}>{Math.round(metrics.cacheHitRate)}%</Text>,
  <Text style={styles.metricLabel}>Cache Hit Rate</Text>
        </Card>,
  </View>
      {/* Advanced Metrics */}
  {showAdvanced && (
        <>,
  {/* Error Metrics */}
          <Card style={styles.detailCard}>,
  <Text style={styles.sectionTitle}>Error Analytics</Text>
            <View style={styles.errorMetrics}>,
  <View style={styles.errorRow}>
                <Text style={styles.errorLabel}>Total Errors:</Text>,
  <Text
                  style={{ [styles.errorValue{ color: metrics.errorCount > 0 ? theme.colors.error   : theme.colors.success  ] }
                  ]},
  >
                  {metrics.errorCount},
  </Text>
              </View>,
  <View style={styles.errorRow}>
                <Text style={styles.errorLabel}>Version Conflicts:</Text>,
  <Text
                  style={{ [styles.errorValue,
  {
                      color:  ,
  metrics.versionConflictCount > 0, ? theme.colors.warning: theme.colors.success] }]},
  >
                  {metrics.versionConflictCount},
  </Text>
              </View>,
  </View>
          </Card>,
  {/* Operation Performance */}
          <Card style={styles.detailCard}>,
  <Text style={styles.sectionTitle}>Operation Performance</Text>
            {metrics.fastestOperation && (
  <View style={styles.operationRow}>
                <Text style={styles.operationLabel}>Fastest Operation:</Text>,
  <View style={styles.operationDetails}>
                  <Text style={styles.operationName}>{metrics.fastestOperation.operation}</Text>,
  <Text style={[styles.operationTime { color: theme.colors.success}]}>,
  {formatDuration(metrics.fastestOperation.duration)}
                  </Text>,
  </View>
              </View>,
  )}
            {metrics.slowestOperation && (
  <View style={styles.operationRow}>
                <Text style={styles.operationLabel}>Slowest Operation:</Text>,
  <View style={styles.operationDetails}>
                  <Text style={styles.operationName}>{metrics.slowestOperation.operation}</Text>,
  <Text style={[styles.operationTime{ color: theme.colors.error}]}>,
  {formatDuration(metrics.slowestOperation.duration)}
                  </Text>,
  </View>
              </View>,
  )}
          </Card>,
  {/* Performance Recommendations */}
          <Card style={styles.detailCard}>,
  <Text style={styles.sectionTitle}>Recommendations</Text>
            {getPerformanceRecommendations(metrics).map((recommendation, index) => (
  <View key={index} style={styles.recommendationRow}>
                <Text style={styles.recommendationIcon}>{recommendation.icon}</Text>,
  <Text style={styles.recommendationText}>{recommendation.text}</Text>
              </View>,
  ))}
          </Card>,
  </>
      )},
  </ScrollView>
  )
  }
/**,
  * Get performance recommendations based on metrics, ,
  */
function getPerformanceRecommendations(metrics: PerformanceMetrics) {
  const recommendations = [],
  if (metrics.averageApiResponseTime > 2000) {
    recommendations.push({
  icon: '🐌'),
    text: 'API response times are slow. Consider optimizing database queries or adding more caching.') })
  },
  if (metrics.successRate < 90) {
    recommendations.push({
  icon: '⚠️'),
    text: 'Success rate is below 90%. Check error logs and improve error handling.') })
  },
  if (metrics.cacheHitRate < 70) {
    recommendations.push({
  icon: '💾'),
    text: 'Cache hit rate is low. Consider optimizing cache strategy or increasing TTL.') })
  },
  if (metrics.versionConflictCount > 5) {
    recommendations.push({
  icon: '🔄'),
    text: 'High number of version conflicts. Review concurrent update handling.') })
  },
  if (recommendations.length === 0) {
    recommendations.push({
  icon: '✅'),
    text: 'Performance looks good! All metrics are within optimal ranges.') })
  },
  return recommendations
},
  const createStyles = (theme: any) =>
  StyleSheet.create({
  container: {
      flex: 1,
  backgroundColor: '#F9FAFB'
  },
  header: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    padding: 20,
  paddingBottom: 10 }
    title: {
      fontSize: 24,
  fontWeight: '700',
    color: '#111827' }
    toggleButton: { paddingHorizonta, l: 12,
    paddingVertical: 6,
  backgroundColor: '#E5E7EB',
    borderRadius: 6 },
  toggleButtonText: {
      fontSize: 14,
  fontWeight: '600',
    color: '#374151' }
    statusCard: { margi, n: 20,
    marginBottom: 12,
  padding: 20 }
    statusHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 8 },
  statusTitle: {
      fontSize: 18,
  fontWeight: '600',
    color: '#111827' }
    statusBadge: { paddingHorizonta, l: 12,
    paddingVertical: 6,
  borderRadius: 16 }
    statusText: {
      fontSize: 12,
  fontWeight: '600'
  },
  lastUpdateText: {
      fontSize: 14,
  color: '#6B7280'
  },
  metricsGrid: { flexDirectio, n: 'row',
    flexWrap: 'wrap',
  paddingHorizontal: 20,
    gap: 12 },
  metricCard: {
      flex: 1,
  minWidth: '45%',
    padding: 16,
  alignItems: 'center'
  },
  metricValue: { fontSiz, e: 24,
    fontWeight: '700',
  color: '#111827',
    marginBottom: 4 },
  metricLabel: {
      fontSize: 12,
  color: '#6B7280',
    textAlign: 'center' }
    detailCard: { margi, n: 20,
    marginTop: 0,
  marginBottom: 12,
    padding: 20 },
  sectionTitle: { fontSiz, e: 18,
    fontWeight: '600',
  color: '#111827',
    marginBottom: 16 },
  errorMetrics: { ga, p: 12 }
    errorRow: {
      flexDirection: 'row',
  justifyContent: 'space-between',
    alignItems: 'center' }
    errorLabel: {
      fontSize: 14,
  color: '#6B7280'
  },
  errorValue: {
      fontSize: 16,
  fontWeight: '600'
  },
  operationRow: { marginBotto, m: 16 }
    operationLabel: { fontSiz, e: 14,
    fontWeight: '600',
  color: '#111827',
    marginBottom: 4 },
  operationDetails: {
      flexDirection: 'row',
  justifyContent: 'space-between',
    alignItems: 'center' }
    operationName: {
      fontSize: 14,
  color: '#6B7280'
  },
  operationTime: {
      fontSize: 14,
  fontWeight: '600'
  },
  recommendationRow: { flexDirectio, n: 'row',
    alignItems: 'flex-start',
  marginBottom: 12 }
    recommendationIcon: { fontSiz, e: 16,
    marginRight: 8,
  marginTop: 2 }
    recommendationText: { fle, x: 1,
    fontSize: 14),
  color: '#374151'),
    lineHeight: 20 },
  loadingCard: {
      margin: 20,
  padding: 20,
    alignItems: 'center' }
    loadingText: {
      fontSize: 16,
  color: '#6B7280'
  },
  errorCard: {
      margin: 20,
  padding: 20,
    alignItems: 'center' }
    errorText: {
      fontSize: 16,
  color: theme.colors.error)
  }
  });