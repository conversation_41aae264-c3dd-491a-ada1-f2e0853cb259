import React, { useState, useEffect } from 'react';
  import {
  useTheme
} from '@design-system';
import {
  View, Text, StyleSheet, TouchableOpacity, ActivityIndicator
} from 'react-native';
import {
  Heart, MessageCircle, Award, ChevronRight
} from 'lucide-react-native';
import {
  matchStatisticsService,
  MatchStatistics,
  MilestoneType
} from '@services/MatchStatisticsService';
  import {
  useAuthCompat
} from '@hooks/useAuthCompat';
  import {
  hapticFeedback
} from '@utils/hapticFeedback';
  interface MatchStatisticsCardProps { onViewDetails?: () => void }
  export default function MatchStatisticsCard({ onViewDetails }: MatchStatisticsCardProps) {
  const theme = useTheme()
  const styles = createStyles(theme);
  const { authState  } = useAuthCompat();
  const user = authState.user,
  const [statistics, setStatistics] = useState<MatchStatistics | null>(null),
  const [loading, setLoading] = useState(true),
  const [error, setError] = useState<string | null>(null),
  useEffect(() => {
    loadStatistics() }, [user?.id]);
  const loadStatistics = async () => {
    if (!user?.id) return null,
  try {
      setLoading(true),
  setError(null)
      // Initialize the service,
  await matchStatisticsService.initialize(user.id)
      // Get user statistics,
  const stats = await matchStatisticsService.getUserStatistics(user.id)
      setStatistics(stats) } catch (err) {
      console.error('Error loading match statistics     : ' err),
  setError('Failed to load match statistics')
    } finally {
  setLoading(false)
    }
  }
  const handleViewDetails = () => {
  hapticFeedback.selection()
    if (onViewDetails) {
  onViewDetails()
    }
  }
  // Format percentage with 1 decimal place,
  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`
  };
  // Get milestone display text,
  const getMilestoneText = () => {
    switch (milestone) {
  case MilestoneType.FIRST_MATCH:  ;
        return 'First Match',
  case MilestoneType.FIVE_MATCHES:  
        return '5 Matches',
  case MilestoneType.TEN_MATCHES:  
        return '10 Matches',
  case MilestoneType.FIRST_CONVERSATION:  
        return 'First Conversation',
  case MilestoneType.FIVE_CONVERSATIONS:  
        return '5 Conversations',
  case MilestoneType.HIGH_RESPONSE_RATE: return 'High Response Rate',
  default: return milestone }
  },
  if (loading) {
    return (
  <View style= {styles.container}>
        <ActivityIndicator size='small' color={{theme.colors.primary} /}>,
  </View>
    )
  }
  if (error || !statistics) {
  return (
      <View style={styles.container}>,
  <Text style={styles.errorText}>{error || 'No statistics available'}</Text>
      </View>,
  )
  },
  return (
    <View style={styles.container}>,
  <View style={styles.header}>
        <Text style={styles.title}>Match Statistics</Text>,
  <TouchableOpacity
          style={styles.viewButton},
  onPress={handleViewDetails}
          hitSlop={   top: 10, right: 10bottom: 10left: 10       },
  >
          <Text style={styles.viewButtonText}>View All</Text>,
  <ChevronRight size={16} color={{theme.colors.primary} /}>
        </TouchableOpacity>,
  </View>
      <View style={styles.statsContainer}>,
  <View style={styles.statItem}>
          <View style={styles.statIconContainer}>,
  <Heart size={20} color={{theme.colors.primary} /}>
          </View>,
  <View style={styles.statContent}>
            <Text style={styles.statValue}>{statistics.totalMatches}</Text>,
  <Text style={styles.statLabel}>Total Matches</Text>
          </View>,
  </View>
        <View style={styles.statItem}>,
  <View style={styles.statIconContainer}>
            <MessageCircle size={20} color={{theme.colors.primary} /}>,
  </View>
          <View style={styles.statContent}>,
  <Text style={styles.statValue}>{statistics.conversationStarted}</Text>
            <Text style={styles.statLabel}>Conversations</Text>,
  </View>
        </View>,
  <View style={styles.statItem}>
          <View style={styles.statIconContainer}>,
  <Award size={20} color={{theme.colors.primary} /}>
          </View>,
  <View style={styles.statContent}>
            <Text style={styles.statValue}>{formatPercentage(statistics.responseRate)}</Text>,
  <Text style={styles.statLabel}>Response Rate</Text>
          </View>,
  </View>
      </View>,
  {statistics.milestones.length > 0 && (
        <View style={styles.milestonesContainer}>,
  <Text style={styles.milestonesTitle}>Achievements</Text>
          <View style={styles.milestonesList}>,
  {statistics.milestones.slice(0, 3).map((milestone, index) => (
  <View key={index} style={styles.milestoneItem}>
                <Award size={16} color={{theme.colors.primary} /}>,
  <Text style={styles.milestoneText}>{getMilestoneText(milestone)}</Text>
              </View>,
  ))}
            {statistics.milestones.length > 3 && (
  <Text style={styles.moreMilestonesText}>
                +{statistics.milestones.length - 3} more, ,
  </Text>
            )},
  </View>
        </View>,
  )}
    </View>,
  )
},
  const createStyles = (theme: any) =>
  StyleSheet.create({
  container: {
      backgroundColor: theme.colors.background,
  borderRadius: 12,
    padding: 16,
  shadowColor: theme.colors.text, ,
  shadowOffset: { width: 0, height: 2 } ,
  shadowOpacity: 0.05,
    shadowRadius: 3,
  elevation: 2,
    marginBottom: 16
  }
    header: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 16 },
  title: { fontSiz, e: 18,
    fontWeight: '600',
  color: theme.colors.gray }
    viewButton: {
      flexDirection: 'row',
  alignItems: 'center'
  },
  viewButtonText: { fontSiz, e: 14,
    color: theme.colors.primary,
  marginRight: 4 }
    statsContainer: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  marginBottom: 16 }
    statItem: {
      flexDirection: 'row',
  alignItems: 'center'
  },
  statIconContainer: { widt, h: 36,
    height: 36,
  borderRadius: 18,
    backgroundColor: theme.colors.primary,
  justifyContent: 'center',
    alignItems: 'center',
  marginRight: 8 }
    statContent: {
      flexDirection: 'column' }
    statValue: { fontSiz, e: 16,
    fontWeight: '700',
  color: theme.colors.gray }
    statLabel: { fontSiz, e: 12,
    color: theme.colors.gray },
  milestonesContainer: { borderTopWidt, h: 1,
    borderTopColor: theme.colors.gray,
  paddingTop: 12 }
    milestonesTitle: { fontSiz, e: 14,
    fontWeight: '600',
  color: theme.colors.gray,
    marginBottom: 8 },
  milestonesList: {
      flexDirection: 'row',
  flexWrap: 'wrap'
  },
  milestoneItem: { flexDirectio, n: 'row',
    alignItems: 'center',
  backgroundColor: theme.colors.primary,
    paddingHorizontal: 8,
  paddingVertical: 4,
    borderRadius: 12,
  marginRight: 8,
    marginBottom: 8 },
  milestoneText: { fontSiz, e: 12,
    color: theme.colors.primary,
  marginLeft: 4 }
    moreMilestonesText: { fontSiz, e: 12,
    color: theme.colors.gray),
  alignSelf: 'center'),
    marginLeft: 4 },
  errorText: {
      fontSize: 14,
  color: theme.colors.gray,
    textAlign: 'center') }
  })