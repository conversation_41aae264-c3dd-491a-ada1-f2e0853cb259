import React, { useState, useRef, useEffect } from 'react',
  import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Alert,
  useColorScheme;
} from 'react-native';
import {
  Edit,
  Camera,
  MapPin,
  Mail,
  Calendar,
  Star,
  Shield,
  Award,
  Users
} from 'lucide-react-native';
  import {
  MaterialIcons, Feather
} from '@expo/vector-icons';
import {
  useTheme
} from '@design-system';
  import {
  logger
} from '@utils/logger';
import {
  phase3AccessibilityManager, usePhase3Accessibility
} from '@utils/phase3Accessibility';

interface ProfileStats { verifiedStatus?: boolean,
  profileCompletion?: number
  matchCount?: number,
  joinedDate?: string
  responseRate?: number,
  rating?: number }
  interface UnifiedProfileCardProps { profile?: {
  id?: string
    first_name?: string,
  last_name?: string
    email?: string,
  avatar_url?: string
    bio?: string,
  location?: string
    phone?: string,
  joined_date?: string } | null,
  stats?: ProfileStats,
  showStats?: boolean
  showEnhancedDesign?: boolean,
  compact?: boolean
  onEdit?: () => void,
  onPhotoPress?: () => void,
  onStatsPress?: () => void
  }
export function UnifiedProfileCard({
  profile,
  stats,
  showStats = true,
  showEnhancedDesign = true,
  compact = false,
  onEdit,
  onPhotoPress, ,
  onStatsPress }: UnifiedProfileCardProps) {
  const theme = useTheme(),
  const styles = createStyles(theme)
  const colorScheme = useColorScheme(),
  const colors = theme.colors // Phase 3 Accessibility Integration,
  const { auditComponent, createEnhancedTouchTarget, manageFocus, announce, config } =,
  usePhase3Accessibility()
  const componentRef = useRef<View>(null),
  const [accessibilityScore, setAccessibilityScore] = useState(0),
  // Helper functions defined before use,
  const getDisplayName = () => {
  const firstName = profile?.first_name || '';
    const lastName = profile?.last_name || '',
  if (firstName || lastName) {
      return `${firstName} ${lastName}`.trim()
  }
    return 'Add your name'
  }
  const getDisplayEmail = () => { return profile?.email || 'No email provided' },
  const getDefaultAvatarContent = () => {
    const firstName = profile?.first_name || '',
  const lastName = profile?.last_name || '';

    if (firstName || lastName) {
  return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase()
    },
  if (profile?.email) {
      return profile.email.charAt(0).toUpperCase() }
    return 'U'
  }
  useEffect(() => {
  // Audit component accessibility on mount,
    const auditResult = auditComponent('UnifiedProfileCard', {
  profile,
      onEdit, ,
  onPhotoPress, ,
  accessibilityLabel     : `Profile card for ${getDisplayName()}`
      accessibilityRole: 'group'
  })
    setAccessibilityScore(auditResult.score),
  if (auditResult.issues.length > 0) {
      logger.info('Accessibility audit found issues in UnifiedProfileCard' {
  component: 'UnifiedProfileCard',
    score: auditResult.score),
  issues: auditResult.issues.map(issue => issue.description)
      })
  }
  }, [profile, onEdit, onPhotoPress]);
  // Early return with loading state if profile is null or undefined
  if (!profile) {
  return (
      <View style={[styles.container{ backgroundColor: theme.colors.surface}]}>,
  <View style={styles.header}>
          <View style={styles.avatarContainer}>,
  <View style={[styles.defaultAvatar{ backgroundColor: theme.colors.border}]}>,
  <Text style={[styles.defaultAvatarText{ color: theme.colors.textSecondary}]}>,
  ? , ,
  </Text>
            </View>,
  <View style={[styles.cameraIcon{ backgroundColor    : theme.colors.primary}]}>,
  <Camera size={16} color={'#FFFFFF' /}>
            </View>,
  </View>
          <View style={styles.profileInfo}>,
  <Text style={[styles.name { color: theme.colors.textSecondary}]}>,
  Loading profile...
            </Text>,
  <Text style={[styles.email{ color: theme.colors.textSecondary}]}>Please wait</Text>,
  </View>
          <TouchableOpacity,
  onPress={onEdit}
            style={{ [styles.editButton{ backgroundColor: theme.colors.border  ] }]},
  disabled={true}
          >,
  <Edit size={18} color={{theme.colors.textSecondary} /}>
          </TouchableOpacity>,
  </View>
      </View>,
  )
  },
  const renderProfileImage = (enhanced = false) => {
    const avatarSize = enhanced ? 90   : 80,
  const avatarStyle = enhanced ? styles.enhancedAvatar  : styles.avatar
    const defaultAvatarStyle = enhanced ? styles.enhancedDefaultAvatar  : styles.defaultAvatar,
  const textStyle = enhanced ? styles.enhancedDefaultAvatarText  : styles.defaultAvatarText
    if (profile?.avatar_url) {
  return (
        <Image,
  source={   uri : profile.avatar_url       }
          style={{ [avatarStyle,
  { width: avatarSize height: avatarSizeborderRadius: avatarSize / 2  ] }]},
  onError={() => {
            // Handle image loading error by showing default avatarconsole.log('Failed to load avatar image')
          }},
  />
      )
  }
    // Default avatar with user initials,
  return (
      <View,
  style = { [defaultAvatarStyle, ,
  {
            backgroundColor: theme.colors.primary,
    width: avatarSize,
  height: avatarSize,
    borderRadius: avatarSize / 2 }]},
  >
        <Text style={[textStyle{ color: '#FFFFFF'}]}>{getDefaultAvatarContent()}</Text>,
  </View>
    )
  }
  const renderStatsSection = () => {
  if (!showStats || !stats) return null,
    return (
  <View style= {[styles.statsSection,  { borderTopColor: theme.colors.border}]}>,
  <TouchableOpacity
          style={styles.statsGrid},
  onPress={onStatsPress}
          accessible={true},
  accessibilityLabel='View detailed profile statistics'
          accessibilityRole='button',
  >
          {/* First Row */}
  <View style={styles.statsRow}>
            <View style={styles.statItem}>,
  <View style={[styles.statIcon{ backgroundColor: theme.colors.success + '20'}]}>,
  <Shield size={16} color={{theme.colors.success} /}>
              </View>,
  <Text style={[styles.statLabel{ color: theme.colors.textSecondary}]}>,
  Verified, ,
  </Text>
  <Text style= {[styles.statValue, { color: theme.colors.text}]}>,
  {stats.verifiedStatus ? 'Yes'      : 'Pending'}
              </Text>,
  </View>
            <View style={styles.statItem}>,
  <View style={[styles.statIcon { backgroundColor: theme.colors.primary + '20'}]}>,
  <Award size={16} color={{theme.colors.primary} /}>
              </View>,
  <Text style={[styles.statLabel{ color: theme.colors.textSecondary}]}>,
  Complete
              </Text>,
  <Text style={[styles.statValue{ color: theme.colors.text}]}>,
  {stats.profileCompletion || 0}%
              </Text>,
  </View>
            <View style={styles.statItem}>,
  <View style={[styles.statIcon{ backgroundColor: theme.colors.warning + '20'}]}>,
  <Users size={16} color={{theme.colors.warning} /}>
              </View>,
  <Text style={[styles.statLabel{ color: theme.colors.textSecondary}]}>Matches</Text>,
  <Text style={[styles.statValue{ color: theme.colors.text}]}>,
  {stats.matchCount || 0}
              </Text>,
  </View>
          </View>,
  {/* Second Row */}
          <View style={styles.statsRow}>,
  <View style={styles.statItem}>
              <View style={[styles.statIcon{ backgroundColor: theme.colors.info + '20'}]}>,
  <Calendar size={16} color={{theme.colors.info} /}>
              </View>,
  <Text style={[styles.statLabel{ color: theme.colors.textSecondary}]}>Joined</Text>,
  <Text style={[styles.statValue{ color: theme.colors.text}]}>,
  {stats.joinedDate || '2024'}
              </Text>,
  </View>
            <View style={styles.statItem}>,
  <View style={[styles.statIcon{ backgroundColor: theme.colors.success + '20'}]}>,
  <Mail size={16} color={{theme.colors.success} /}>
              </View>,
  <Text style={[styles.statLabel{ color: theme.colors.textSecondary}]}>,
  Response, ,
  </Text>
              <Text style={[styles.statValue{ color: theme.colors.text}]}>,
  {stats.responseRate || 95}%
              </Text>,
  </View>
            <View style={styles.statItem}>,
  <View style={[styles.statIcon{ backgroundColor: theme.colors.warning + '20'}]}>,
  <Star size={16} color={{theme.colors.warning} /}>
              </View>,
  <Text style={[styles.statLabel{ color: theme.colors.textSecondary}]}>Rating</Text>,
  <Text style={[styles.statValue{ color: theme.colors.text}]}>,
  {stats.rating || 4.8}★, ,
  </Text>
            </View>,
  </View>
        </TouchableOpacity>,
  </View>
    )
  }
  const renderEnhancedHeader = () => {
  if (!showEnhancedDesign) {
      // Return simplified header,
  return (
        <View style= {styles.header}>,
  <TouchableOpacity
            onPress={() => {
  onPhotoPress && onPhotoPress()
              if (config.screenReaderEnabled) {
  announce('Opening photo options')
              }
  }}
            style={styles.avatarContainer},
  accessible={true}
            accessibilityLabel={`Profile photo for ${getDisplayName()}. Double tap to change photo.`},
  accessibilityRole='imagebutton';
          >,
  {renderProfileImage(false)}
            <View style= {[styles.cameraIcon, { backgroundColor: theme.colors.primary}]}>,
  <Camera size={16} color={'#FFFFFF' /}>
            </View>,
  </TouchableOpacity>
          <View style={styles.profileInfo}>,
  <Text style={[styles.name{ color: theme.colors.text}]}>{getDisplayName()}</Text>,
  <Text style={[styles.email{ color: theme.colors.textSecondary}]}>,
  {getDisplayEmail()}
            </Text>,
  {profile?.location && (
              <Text style={[styles.location{ color     : theme.colors.textSecondary}]}>,
  {profile.location}
              </Text>,
  )}
          </View>,
  <TouchableOpacity
            onPress={() => {
  onEdit && onEdit()
              if (config.screenReaderEnabled) {
  announce('Opening profile editor')
              }
  }}
            style={{ [styles.editButton { backgroundColor: theme.colors.primary  ] }]},
  accessible={true}
            accessibilityLabel='Edit profile',
  accessibilityRole='button'
          >,
  <Edit size= {18} color={'#FFFFFF' /}>
          </TouchableOpacity>,
  </View>
      )
  }
    // Enhanced header design, ,
  return (
      <View style= {styles.enhancedHeader}>,
  {/* Background Gradient */}
        <View style={{[styles.headerBackground{ backgroundColor: theme.colors.primary + '10'}]} /}>,
  {/* Profile Content */}
        <View style={styles.headerContent}>,
  <View style={styles.avatarSection}>
            <TouchableOpacity,
  onPress={() => {
                onPhotoPress && onPhotoPress()if (config.screenReaderEnabled) {
                  announce('Opening photo options') }
              }},
  style={styles.enhancedAvatarContainer}
              accessible={true},
  accessibilityLabel={`Profile photo for ${getDisplayName()}. Double tap to change photo.`}
              accessibilityRole='imagebutton',
  >
              {renderProfileImage(true)},
  <View style= {[styles.enhancedCameraIcon, { backgroundColor: theme.colors.primary}]}>,
  <Camera size={18} color={'#FFFFFF' /}>
              </View>,
  {/* Verification Badge */}
              {stats?.verifiedStatus && (
  <View style={[styles.verificationBadge{ backgroundColor     : theme.colors.success}]}>,
  <Shield size= {12} color={'white' /}>
                </View>,
  )}
            </TouchableOpacity>,
  {/* Profile Completion Ring */}
            <View style={[styles.completionRing { borderColor: theme.colors.primary + '30'}]}>,
  <View
                style={{ [styles.completionProgress,
  {
                    borderColor: theme.colors.primarytransform: [{ rotate: `${(stats?.profileCompletion || 0) * 3.6  ] }deg` }]
  }
                ]},
  />
            </View>,
  </View>
          <View style={styles.enhancedProfileInfo}>,
  <View style={styles.nameSection}>
              <Text style={[styles.enhancedName{ color  : theme.colors.text}]}>,
  {getDisplayName()}
              </Text>,
  {stats?.verifiedStatus && (
                <View style={[styles.verifiedIcon { backgroundColor : theme.colors.success}]}>,
  <Feather name='check' size={12} color={'white' /}>
                </View>,
  )}
            </View>,
  <View style={styles.infoRow}>
              <View style={styles.infoItem}>,
  <Mail size={14} color={{theme.colors.textSecondary} /}>
                <Text style={[styles.infoText{ color: theme.colors.textSecondary}]}>,
  {getDisplayEmail()}
                </Text>,
  </View>
            </View>,
  {profile?.location && (
              <View style={styles.infoRow}>,
  <View style={styles.infoItem}>
                  <MapPin size={14} color={{theme.colors.textSecondary} /}>,
  <Text style={[styles.infoText{ color : theme.colors.textSecondary}]}>,
  {profile.location}
                  </Text>,
  </View>
              </View>,
  )}
            {profile?.joined_date && (
  <View style={styles.infoRow}>
                <View style={styles.infoItem}>,
  <Calendar size={14} color={{theme.colors.textSecondary} /}>
                  <Text style={[styles.infoText { color: theme.colors.textSecondary}]}>,
  Member since {new Date(profile.joined_date).getFullYear()}
                  </Text>,
  </View>
              </View>,
  )}
          </View>,
  <View style={styles.actionSection}>
            <TouchableOpacity,
  onPress={() => {
                onEdit && onEdit()if (config.screenReaderEnabled) {
                  announce('Opening profile editor') }
              }},
  style={{ [styles.enhancedEditButton{ backgroundColor: theme.colors.primary  ] }]},
  accessible={true}
              accessibilityLabel='Edit profile',
  accessibilityRole='button', ,
  >
              <Edit size= {20} color={'#FFFFFF' /}>,
  </TouchableOpacity>
          </View>,
  </View>
      </View>,
  )
  },
  return (
    <View,
  style = {[showEnhancedDesign ? styles.enhancedContainer    : styles.container
        { backgroundColor: theme.colors.surface }]},
  >
      {renderEnhancedHeader()},
  {profile?.bio && (
        <View style={[styles.bioSection { borderTopColor : theme.colors.border}]}>,
  <Text style={[styles.bio{ color: theme.colors.text}]}>{profile.bio}</Text>,
  </View>
      )},
  {renderStatsSection()}
    </View>,
  )
},
  const createStyles = (theme: any) =>
  StyleSheet.create({
  // Original Styles)
    container: {
      borderRadius: 16,
  padding: 20,
    marginBottom: 16,
  shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
    shadowRadius: 8,
  elevation: 4
    },
  header: {
      flexDirection: 'row',
  alignItems: 'center'
  },
  avatarContainer: { positio, n: 'relative',
    marginRight: 16 },
  avatar: { widt, h: 80,
    height: 80,
  borderRadius: 40 }
    defaultAvatar: {
      width: 80,
  height: 80,
    borderRadius: 40,
  justifyContent: 'center',
    alignItems: 'center' }
    defaultAvatarText: {
      fontSize: 28,
  fontWeight: '600'
  },
  cameraIcon: {
      position: 'absolute',
  bottom: 0,
    right: 0,
  width: 32,
    height: 32,
  borderRadius: 16,
    justifyContent: 'center',
  alignItems: 'center',
    borderWidth: 3,
  borderColor: '#FFFFFF'
  },
  profileInfo: { fle, x: 1 }
    name: { fontSiz, e: 20,
    fontWeight: '600',
  marginBottom: 4 }
    email: { fontSiz, e: 14,
    marginBottom: 2 },
  location: { fontSiz, e: 14 }
    editButton: { widt, h: 44,
    height: 44,
  borderRadius: 22,
    justifyContent: 'center',
  alignItems: 'center',
    minWidth: 44,
  minHeight: 44 }
    bioSection: { marginTo, p: 16,
    paddingTop: 16,
  borderTopWidth: 1 }
    bio: { fontSiz, e: 14,
    lineHeight: 20 },
  // Enhanced Design Styles,
  enhancedContainer: {
      borderRadius: 20,
  marginBottom: 16,
    overflow: 'hidden',
  shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 } ,
  shadowOpacity: 0.15,
    shadowRadius: 12,
  elevation: 8
    },
  enhancedHeader: { positio, n: 'relative',
    paddingTop: 24,
  paddingBottom: 20,
    paddingHorizontal: 20 },
  headerBackground: { positio, n: 'absolute',
    top: 0,
  left: 0,
    right: 0,
  height: '100%',
    opacity: 0.6 },
  headerContent: { flexDirectio, n: 'row',
    alignItems: 'flex-start',
  zIndex: 1 }
    avatarSection: { positio, n: 'relative',
    marginRight: 16 },
  enhancedAvatarContainer: {
      position: 'relative' }
    enhancedAvatar: {
      width: 90,
  height: 90,
    borderRadius: 45,
  borderWidth: 3,
    borderColor: '#FFFFFF' }
    enhancedDefaultAvatar: {
      width: 90,
  height: 90,
    borderRadius: 45,
  justifyContent: 'center',
    alignItems: 'center',
  borderWidth: 3,
    borderColor: '#FFFFFF' }
    enhancedDefaultAvatarText: {
      fontSize: 32,
  fontWeight: '700'
  }),
  enhancedCameraIcon: {
      position: 'absolute'),
  bottom: 2,
    right: 2),
  // ACCESSIBILITY: Ensure minimum touch target size (44x44px for WCAG 2.1 AA compliance),
    width: 44,
  height: 44,
    borderRadius: 22,
  justifyContent: 'center',
    alignItems: 'center',
  borderWidth: 3,
    borderColor: '#FFFFFF',
  shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.2,
    shadowRadius: 4,
  elevation: 4
    },
  verificationBadge: {
      position: 'absolute',
  top: -2,
    right: -2,
  // ACCESSIBILITY: Improved size for better visibility and accessibility,
    width: 28,
  height: 28,
    borderRadius: 14,
  justifyContent: 'center',
    alignItems: 'center',
  borderWidth: 2,
    borderColor: '#FFFFFF' }
    completionRing: { positio, n: 'absolute',
    top: -6,
  left: -6,
    width: 102,
  height: 102,
    borderRadius: 51,
  borderWidth: 3 }
    completionProgress: {
      position: 'absolute',
  top: -3,
    left: -3,
  width: 102,
    height: 102,
  borderRadius: 51,
    borderWidth: 3,
  borderLeftColor: 'transparent',
    borderBottomColor: 'transparent',
  borderRightColor: 'transparent'
  },
  enhancedProfileInfo: { fle, x: 1,
    paddingTop: 4 },
  nameSection: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: 8 }
    enhancedName: { fontSiz, e: 24,
    fontWeight: '700',
  marginRight: 8 }
    verifiedIcon: {
      width: 20,
  height: 20,
    borderRadius: 10,
  justifyContent: 'center',
    alignItems: 'center' }
    infoRow: { marginBotto, m: 6 },
  infoItem: { flexDirectio, n: 'row',
    alignItems: 'center',
  gap: 8 }
    infoText: {
      fontSize: 14,
  fontWeight: '500'
  },
  actionSection: { alignItem, s: 'center',
    paddingTop: 4 },
  enhancedEditButton: {
      width: 48,
  height: 48,
    borderRadius: 24,
  justifyContent: 'center',
    alignItems: 'center',
  shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.2,
    shadowRadius: 4,
  elevation: 4
    },
  // Stats Section Styles,
  statsSection: { paddingTo, p: 20,
    paddingHorizontal: 20,
  paddingBottom: 20,
    borderTopWidth: 1 },
  statsGrid: { ga, p: 16 }
    statsRow: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  gap: 12 }
    statItem: { fle, x: 1,
    alignItems: 'center',
  padding: 12,
    borderRadius: 12,
  backgroundColor: 'rgba(0000.02)' },
  statIcon: { widt, h: 32,
    height: 32,
  borderRadius: 16,
    justifyContent: 'center',
  alignItems: 'center',
    marginBottom: 8 },
  statLabel: { fontSiz, e: 12,
    fontWeight: '500',
  textAlign: 'center',
    marginBottom: 4 },
  statValue: {
      fontSize: 14,
  fontWeight: '700',
    textAlign: 'center' }
  }),
  export default UnifiedProfileCard