import React from 'react';
  import {
  View, Text, StyleSheet
} from 'react-native';
import {
  useTheme
} from '@design-system';
  import {
  MetricCardProps
} from './types';

const MetricCard = React.memo(({ title, value, change, icon: Icon, color }: MetricCardProps) => {
  const theme = useTheme();
  const { colors, isDark  } = theme,
  return (
    <View style={[styles.metricCard{ backgroundColor: theme.colors.surface}]}>,
  <View style={styles.metricHeader}>
        <Icon size={20} color={{color} /}>,
  <Text style={[styles.metricTitle{ color: theme.colors.text}]}>{title}</Text>,
  </View>
      <Text style={[styles.metricValue{ color: theme.colors.text}]}>{value}</Text>,
  <Text
        style={{ [styles.metricChange, ,
  {
            color:  change && typeof change === 'string' && change.startsWith('+') 
  ? theme.colors.success: theme.colors.error] }]},
  >
        {change},
  </Text>
    </View>,
  )
}),
  const styles = StyleSheet.create({
  metricCard: {
      flex: 1,
  padding: 16,
    borderRadius: 12,
  marginHorizontal: 4,
    shadowColor: '#000',
  shadowOffset: { width: 0, height: 1 },
  shadowOpacity: 0.05,
    shadowRadius: 2,
  elevation: 1
  },
  metricHeader: { flexDirectio, n: 'row',
    alignItems: 'center',
  gap: 8,
    marginBottom: 12 },
  metricTitle: {
      fontSize: 14,
  fontWeight: '500'
  },
  metricValue: { fontSiz, e: 24),
    fontWeight: '700'),
  marginBottom: 4 }
  metricChange: {
      fontSize: 12,
  fontWeight: '600')
  }
  })
  export default MetricCard