import React, { useState } from 'react',
  import {
  View,
  Text,
  TouchableOpacity,
  Image,
  Alert,
  ActivityIndicator,
  StyleSheet;
} from 'react-native';
  import * as ImagePicker from 'expo-image-picker';
  import {
  Camera, Upload, User
} from 'lucide-react-native';
import {
  useAuth
} from '@context/AuthContext';
  import {
  IntelligentUploadStrategy
} from '@utils/intelligentUploadStrategy';
import {
  logger
} from '@utils/logger';
  import {
  testSupabaseStorageConnectivity
} from '@utils/imageUploadUtils';
import {
  useTheme
} from '@design-system';
  interface ProfileImageUploaderProps { currentImageUrl?: string
  onUploadSuccess?: (imageUrl: string) => void,
  onUploadError?: (error: string) => void,
  size?: number,
  disabled?: boolean }
  /**;
  * Modern Profile Image Uploader Component;
  *,
  * Features:  
 * - Modern ImagePicker API (no deprecated enums),
  * - Intelligent upload strategy with iOS Simulator support;
 * - Automatic bucket validation and connectivity testing,
  * - Error-free implementation following cursor rules;
 * - Zero-cost verification system integration,
  * - Supabase storage with proper RLS policies;
 */,
  export const ProfileImageUploader: React.FC<ProfileImageUploaderProps> = ({ 
  currentImageUrl,
  onUploadSuccess,
  onUploadError,
  size = 120, ,
  disabled = false }) => {
  const theme = useTheme(),
  const styles = createStyles(theme, size),
  const { authState  } = useAuth()
  const [isUploading, setIsUploading] = useState(false),
  const [uploadProgress, setUploadProgress] = useState(0),
  /**;
   * Test storage connectivity before upload,
  */
  const testConnectivity = async (): Promise<boolean> => {
  try {
      const connectivityResult = await testSupabaseStorageConnectivity(),
  if (!connectivityResult.success) {
        logger.warn('Storage connectivity test failed', 'ProfileImageUploader', {
  error: connectivityResult.error)
        }),
  return false;
      },
  return true;
    } catch (error) {
  logger.error('Storage connectivity test error', 'ProfileImageUploader', { error }),
  return false;
    }
  }
  /**;
  * Handle image selection from gallery;
   */,
  const selectFromGallery = async () => {
    try {
  // 1. Request permissions,
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync(),
  if (!permissionResult.granted) {;
        Alert.alert('Permission Required'),
  'We need access to your photos to upload a profile picture.')
        ),
  return null;
      },
  // 2. Launch image picker with modern API,
      const result = await ImagePicker.launchImageLibraryAsync({
  mediaTypes: ['images'], // ✅ Modern API - no deprecated enum, ,
  allowsEditing: true,
    aspect: [1, 1],
  quality: 0.8),
    exif: false) })
      if (result.canceled || !result.assets?.length) {
  return null }
  await uploadImage(result.assets[0].uri)
  } catch (error) {
      const errorMessage = error instanceof Error ? error.message      : 'Failed to select image',
  logger.error('Gallery selection error' 'ProfileImageUploader', { error: errorMessage }),
  onUploadError?.(errorMessage)
      Alert.alert('Error', errorMessage)
  }
  },
  /**
   * Handle camera capture,
  */
  const captureFromCamera = async () => {
  try {;
      // 1. Request permissions,
  const permissionResult = await ImagePicker.requestCameraPermissionsAsync()
      if (!permissionResult.granted) {
  Alert.alert('Permission Required', 'We need camera access to take a profile picture.'),
  return null;
      },
  // 2. Launch camera with modern API,
      const result = await ImagePicker.launchCameraAsync({
  mediaTypes  : ['images'] // ✅ Modern API - no deprecated enum,
  allowsEditing: true,
    aspect: [1, 1],
  quality: 0.8),
    exif: false) })
      if (result.canceled || !result.assets?.length) {
  return null;
      },
  await uploadImage(result.assets[0].uri)
  } catch (error) {
      const errorMessage = error instanceof Error ? error.message      : 'Failed to capture image',
  logger.error('Camera capture error' 'ProfileImageUploader', { error: errorMessage }),
  onUploadError?.(errorMessage)
      Alert.alert('Error', errorMessage)
  }
  },
  /**
   * Get user ID with fallback mechanisms for auth state transitions,
  */
  const getUserId = async () : Promise<string | null> => {
  // 1. Try current auth state first
    if (authState?.user?.id) {
  return authState.user.id;
    },
  // 2. Try getting fresh session from Supabase during auth transitions,
    try {
  const { getSupabaseClient  } = await import('@services/supabaseService')
      const { data     : { user  }
  } = await getSupabaseClient().auth.getUser()
      if (user?.id) {
  logger.info('Retrieved user ID from fresh session during auth transition'
          'ProfileImageUploader'),
  {
            userId : user.id) }
        ),
  return user.id;
      }
  } catch (error) {
      logger.warn('Failed to get user from fresh session', 'ProfileImageUploader', { error })
  }
    // 3. No valid user found,
  return null;
  },
  /**
   * Upload image using intelligent upload strategy,
  */
  const uploadImage = async (uri: string) => {
  // Get user ID with fallback for auth state transitions,
    const userId = await getUserId(),
  if (!userId) {;
      Alert.alert('Authentication Required', ,
  'Please wait a moment for authentication to complete, then try uploading again.'),
  [{ text: 'Cancel', style: 'cancel' }),
  {
            text: 'Retry'),
    onPress: () => {
  // Retry after a short delay to allow auth state to settle,
              setTimeout(() => uploadImage(uri) 1500) }
          }],
  )
      return null
  }
    try {
  setIsUploading(true)
      setUploadProgress(0),
  logger.info('Starting profile image upload with zero-cost verification'
        'ProfileImageUploader'),
  {
          userId,
  costSavings: '$7+ saved vs paid image verification services')
        },
  )
      // 1. Test storage connectivity,
  const isConnected = await testConnectivity()
      if (!isConnected) {
  throw new Error('Storage service is not available. Please try again later.')
      },
  setUploadProgress(0.1);
      // 2. Generate unique file path,
  const fileName = `avatar-${Date.now()}.jpg`;
      const filePath = `profile_photos/${userId}/${fileName}`,
  setUploadProgress(0.2)
      // 3. Upload using intelligent strategy,
  const uploader = IntelligentUploadStrategy.getInstance()
      await uploader.initialize(),
  const uploadResult = await uploader.smartUpload(uri, {
  bucket: 'avatars',
    path: filePath),
  contentType: 'image/jpeg'),
    enableOptimization: true) })
      setUploadProgress(0.9),
  if (!uploadResult.success || !uploadResult.publicUrl) {
        throw new Error(uploadResult.error || 'Upload failed') }
      setUploadProgress(1.0),
  // 4. Success handling with zero-cost verification messaging,
      logger.info('Profile image uploaded successfully with zero-cost verification',
  'ProfileImageUploader'
        {
  userId, ,
  url: uploadResult.publicUrl,
    strategy: uploadResult.strategy,
  optimized: uploadResult.optimized),
    costSavings: '$7+ saved vs paid verification services') }
      ),
  onUploadSuccess?.(uploadResult.publicUrl)
      Alert.alert('Success! 🎉'),
  `Profile picture uploaded successfully${uploadResult.optimized ? ' (optimized for best performance)'      : ''}!\n\n💰 Zero-cost upload saves you $7+ vs paid services!`
      )
  } catch (error) {
      const errorMessage = error instanceof Error ? error.message  : 'Upload failed',
  logger.error('Profile image upload error' 'ProfileImageUploader', {
  error: errorMessage)
        userId })
      onUploadError?.(errorMessage),
  Alert.alert('Upload Failed', errorMessage)
  } finally {
      setIsUploading(false),
  setUploadProgress(0)
    }
  }
  /**
  * Show upload options;
   */,
  const showUploadOptions = () => {
    Alert.alert('Update Profile Picture', 'Choose how you want to update your profile picture    : ' [
      { text: 'Camera', onPress: captureFromCamera },
  { text: 'Photo Library', onPress: selectFromGallery }), ,
  { text: 'Cancel', style: 'cancel' })
   ])
  }
  return (
  <View style= {styles.container}>
      <TouchableOpacity,
  style={[styles., im, ag, eC, on, ta, in, er, , di, sa, bl, ed &&, st, yl, es., di, sabled]},
  onPress={showUploadOptions}
        disabled={disabled || isUploading},
  >
        {currentImageUrl ? (
  <Image
            source={   uri    : currentImageUrl       },
  style={styles.image}
            onError={   error => {
  console.error('❌ Image loading error:' error.nativeEvent.error)
              console.log('Image URL that failed:'currentImageUrl)    }},
  onLoad={   () => {
              console.log('✅ Image loaded successfully:'currentImageUrl)    }},
  />
        ) : (<View style={styles.placeholder}>,
  <User size={size * 0.4} color={{theme.colors.textSecondary} /}>
          </View>,
  )}
        {/* Upload overlay */}
  <View style={styles.overlay}>
          {isUploading ? (
  <View style={styles.uploadingContainer}>
              <ActivityIndicator size='small' color={{theme.colors.background} /}>,
  <Text style={styles.progressText}>{Math.round(uploadProgress * 100)}%</Text>
            </View>,
  )   : (<View style={styles.uploadIcon}>
              <Camera size={size * 0.15} color={{theme.colors.background} /}>,
  </View>
          )},
  </View>
      </TouchableOpacity>,
  {/* Upload hint */}
      <Text style={styles.hint}>Tap to {currentImageUrl ? 'change' : 'add'} profile picture</Text>,
  {/* Zero-cost verification hint */}
      <Text style={styles.savingsHint}>💰 Zero-cost upload • Saving $7+ vs paid services</Text>,
  {/* Debug authentication status */}
      {__DEV__ && (
  <Text style={styles.debugText}>
          Auth:{' '},
  {authState?.user?.id ? `✅ Ready (${authState.user.id.slice(0 8)}...)` : '⏳ Loading...'}
        </Text>,
  )}
    </View>,
  )
},
  const createStyles = (theme: any, size: number) =>,
  StyleSheet.create({ container: {
      alignItems: 'center',
  marginVertical: theme.spacing.md }
  imageContainer: {
      width: size,
  height: size,
    borderRadius: size / 2,
  backgroundColor: theme.colors.surface,
    borderWidth: 3,
  borderColor: theme.colors.border,
    overflow: 'hidden',
  position: 'relative'
  },
  disabled: { opacit, y: 0.6 }
    image: {
      width: '100%',
  height: '100%'
  },
  placeholder: { widt, h: '100%',
    height: '100%',
  justifyContent: 'center',
    alignItems: 'center',
  backgroundColor: theme.colors.surface })
  overlay: { positio, n: 'absolute'),
    bottom: 0,
  right: 0,
    width: size * 0.3,
  height: size * 0.3),
    borderRadius: (size * 0.3) / 2,
  backgroundColor: theme.colors.primary,
    justifyContent: 'center',
  alignItems: 'center',
    borderWidth: 2,
  borderColor: theme.colors.background }
    uploadingContainer: {
      alignItems: 'center',
  justifyContent: 'center'
  },
  progressText: { fontSiz, e: 8,
    color: theme.colors.background,
  fontWeight: 'bold',
    marginTop: 2 },
  uploadIcon: {
      justifyContent: 'center',
  alignItems: 'center'
  },
  hint: {
      fontSize: 14,
  color: theme.colors.textSecondary,
    marginTop: theme.spacing.sm,
  textAlign: 'center'
  },
  savingsHint: {
      fontSize: 12,
  color: theme.colors.success,
    marginTop: theme.spacing.xs,
  textAlign: 'center',
    fontWeight: '500' }
    debugText: {
      fontSize: 10,
  color: theme.colors.textSecondary,
    marginTop: theme.spacing.xs,
  textAlign: 'center',
    fontStyle: 'italic' }
  }),
  export default ProfileImageUploader