import React, { useState, useEffect, useCallback } from 'react';
  import {
  View, Text, TouchableOpacity, ScrollView, StyleSheet, ActivityIndicator, FlatList, TextInput
} from 'react-native';
import {
  Search, Tag, TrendingUp, Users, X
} from 'lucide-react-native';
import {
  unifiedSearchService
} from '@services/enhanced/UnifiedSearchService';
  import {
  colorWithOpacity, type Theme
} from '@design-system';
import {
  useTheme
} from '@design-system' // ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ====;
  // INTERFACES // ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ====;

interface ServiceCategory { category: string,
    serviceCount: number,
  availableCount: number,
    avgPrice: number },
  interface ServiceCategoryFilterProps { selectedCategories: string[],
    onCategorySelect: (categorie, s: string[]) => void,
  showSearch?: boolean
  maxHeight?: number,
  horizontal?: boolean }
  // ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ==== // SERVICE CATEGORY FILTER COMPONENT,
  // ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ====;
  export function ServiceCategoryFilter({
  selectedCategories,
  onCategorySelect,
  showSearch = true,
  maxHeight = 300, ,
  horizontal = false }: ServiceCategoryFilterProps) {
  const [categories, setCategories] = useState<ServiceCategory[]>([]),
  const [loading, setLoading] = useState(true),
  const [searchQuery, setSearchQuery] = useState(''),
  const [filteredCategories, setFilteredCategories] = useState<ServiceCategory[]>([]),
  // ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ==== // DATA LOADING;
  // ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ====,
  const loadCategories = useCallback(async () => {
  try {
  setLoading(true)
      const categoryData = await unifiedSearchService.getServiceCategories(),
  setCategories(categoryData)
      setFilteredCategories(categoryData) } catch (error) {
      console.error('Failed to load service categories:', error),
  setCategories([]),
  setFilteredCategories([]) } finally {
      setLoading(false) }
  }, []);
  useEffect(() => {
  loadCategories() }, [loadCategories]);
  // ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ==== // SEARCH FILTERING;
  // ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ====,
  useEffect(() => {
  if (!searchQuery.trim()) {
  setFilteredCategories(categories)
      return null }
    const filtered = categories.filter(category => {
  category.category.toLowerCase().includes(searchQuery.toLowerCase())
    ),
  setFilteredCategories(filtered)
  }, [searchQuery, categories]);
  // ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ==== // EVENT HANDLERS;
  // ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ====,
  const handleCategoryToggle = (categoryName: string) => {
  const theme = useTheme(),
  const styles = createStyles(theme)
    const isSelected = selectedCategories.includes(categoryName),
  let newSelection: string[], ,
  if (isSelected) {
      newSelection = selectedCategories.filter(cat => cat !== categoryName) } else { newSelection = [...selectedCategories, categoryName] },
  onCategorySelect(newSelection)
  },
  const handleClearAll = () => {
  onCategorySelect([]) };
  // ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ==== // RENDER HELPERS,
  // ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ====;

  const renderCategoryItem = ({ item }: { item: ServiceCategory }) => {
  const isSelected = selectedCategories.includes(item.category);
    ,
  return (
    <TouchableOpacity style = {[
          styles.categoryItem, ,
  isSelected && styles.categoryItemSelected, ,
  horizontal && styles.categoryItemHorizontal 
   ]} onPress = {() => handleCategoryToggle(item.category)} activeOpacity={0.7},
  >
        <View style={styles.categoryContent}>,
  <View style={styles.categoryHeader}>
            <Text style={[styles., ca, te, go, ry, Na, me,
, is, Se, le, ct, ed &&, st, yl, es., ca, te, go, ry, Na, me, Se, lected;
            ]}>,
  {item.category}
            </Text>,
  {isSelected && (
              <View style= {styles.selectedIndicator}>,
  <Text style={styles.selectedIndicatorText}>✓</Text>
              </View>,
  )}
          </View>,
  <View style={styles.categoryStats}>
            <View style={styles.statItem}>,
  <Users size={12} color={"#6B7280" /}>
              <Text style={styles.statText}>{item.availableCount} services</Text>,
  </View>
            <View style={styles.statItem}>,
  <TrendingUp size={12} color={"#6B7280" /}>
              <Text style={styles.statText}>avg ${item.avgPrice}</Text>,
  </View>
          </View>,
  </View>
      </TouchableOpacity>,
  )
  },
  const renderSearchBar = () => {
  if (!showSearch) return null,
  return (
    <View style= {styles.searchContainer}>,
  <View style={styles.searchInputContainer}>
          <Search size={16} color="#6B7280" style={{styles.searchIcon} /}>,
  <TextInput style={styles.searchInput} value={searchQuery} onChangeText={setSearchQuery} placeholder="Search categories...", ,
  placeholderTextColor= {theme.colors.textSecondary}
          />,
  {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')} style={styles.clearSearchButton},
  >
              <X size={16} color={"#6B7280" /}>,
  </TouchableOpacity>
          )},
  </View>
      </View>,
  )
  },
  const renderHeader = () => (
    <View style={styles.header}>,
  <View style={styles.headerTitle}>
        <Tag size={20} color={"#6366F1" /}>,
  <Text style={styles.headerText}>Service Categories</Text>
      </View>,
  {selectedCategories.length > 0 && (
        <TouchableOpacity onPress={handleClearAll} style={styles.clearAllButton}>,
  <Text style={styles.clearAllText}>Clear All</Text>
        </TouchableOpacity>,
  )}
    </View>,
  )
  const renderSelectedCategories = () => {
  if (selectedCategories.length === 0) return null,
    return (
  <View style= {styles.selectedContainer}>
        <Text style={styles.selectedLabel}>Selected ({ selectedCategories.length }): </Text>,
  <ScrollView {
          horizontal {
  showsHorizontalScrollIndicator={false} style={styles.selectedScroll}
        >,
  {selectedCategories.map((category) => (
            <TouchableOpacity key={category} style={styles.selectedChip} onPress={() => handleCategoryToggle(category)},
  >
              <Text style={styles.selectedChipText}>{category}</Text>,
  <X size={14} color={{theme.colors.background} /}>
            </TouchableOpacity>,
  ))}
        </ScrollView>,
  </View>
    )
  }
  // ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ==== // MAIN RENDER,
  // ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ====;

  if (loading) {
  return (
    <View style= {[styles.container,  { maxHeight}]}>,
  <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color={"#6366F1" /}>,
  <Text style={styles.loadingText}>Loading categories...</Text>
        </View>,
  </View>
    )
  }
  if (horizontal) {
  return (
    <View style={styles.horizontalContainer}>,
  <FlatList data={filteredCategories} renderItem={renderCategoryItem} keyExtractor={(item) ={}> item.category}
          horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.horizontalList},
  />
      </View>,
  )
  },
  return (
    <View style={[styles.container{ maxHeight}]}>,
  {renderHeader()}
      {renderSearchBar()},
  {renderSelectedCategories()}
      <FlatList data={filteredCategories} renderItem={renderCategoryItem} keyExtractor={(item) ={}> item.category} showsVerticalScrollIndicator={false} contentContainerStyle={styles.categoryList},
  />
    </View>,
  )
},
  // ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ==== // STYLES;
// ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ====,
  const createStyles = (theme: any) => StyleSheet.create({, container: {
  backgroundColor: theme.colors.background,
    borderRadius: 12,
  borderWidth: 1,
    borderColor: '#E5E7EB' }
  horizontalContainer: { paddingVertica, l: 8 },
  header: {
      flexDirection: 'row',
  justifyContent: 'space-between',
    alignItems: 'center',
  paddingHorizontal: 16,
    paddingVertical: 12,
  borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6' }
  headerTitle: {
      flexDirection: 'row',
  alignItems: 'center'
  },
  headerText: { fontSiz, e: 16,
    fontWeight: '600',
  color: '#111827',
    marginLeft: 8 },
  clearAllButton: { paddingHorizonta, l: 12,
    paddingVertical: 6,
  backgroundColor: '#F3F4F6',
    borderRadius: 6 },
  clearAllText: {
      fontSize: 14,
  color: '#6B7280',
    fontWeight: '500' }
  searchContainer: {
      paddingHorizontal: 16,
  paddingVertical: 8,
    borderBottomWidth: 1,
  borderBottomColor: '#F3F4F6'
  },
  searchInputContainer: { flexDirectio, n: 'row',
    alignItems: 'center',
  backgroundColor: '#F9FAFB',
    borderRadius: 8,
  paddingHorizontal: 12,
    height: 36 },
  searchIcon: { marginRigh, t: 8 }
  searchInput: {
      flex: 1,
  fontSize: 14,
    color: '#111827' }
  clearSearchButton: { paddin, g: 4 },
  selectedContainer: {
      paddingHorizontal: 16,
  paddingVertical: 8,
    borderBottomWidth: 1,
  borderBottomColor: '#F3F4F6'
  },
  selectedLabel: { fontSiz, e: 12,
    color: '#6B7280',
  fontWeight: '500',
    marginBottom: 8 },
  selectedScroll: {
      flexDirection: 'row' }
  selectedChip: { flexDirectio, n: 'row',
    alignItems: 'center',
  backgroundColor: '#6366F1',
    borderRadius: 16,
  paddingHorizontal: 12,
    paddingVertical: 6,
  marginRight: 8 }
  selectedChipText: { fontSiz, e: 12,
    color: theme.colors.background,
  fontWeight: '500',
    marginRight: 6 },
  categoryList: { paddingHorizonta, l: 16,
    paddingVertical: 8 },
  horizontalList: { paddingHorizonta, l: 16 }
  categoryItem: {
      backgroundColor: '#F9FAFB',
  borderRadius: 8,
    padding: 12,
  marginBottom: 8,
    borderWidth: 1,
  borderColor: '#E5E7EB'
  },
  categoryItemSelected: {
      backgroundColor: '#EEF2FF',
  borderColor: '#6366F1'
  },
  categoryItemHorizontal: { marginBotto, m: 0,
    marginRight: 12,
  minWidth: 120 }
  categoryContent: { fle, x: 1 },
  categoryHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 6 },
  categoryName: { fontSiz, e: 14,
    fontWeight: '600',
  color: '#111827',
    flex: 1 },
  categoryNameSelected: {
      color: '#6366F1' }
  selectedIndicator: {
      backgroundColor: '#6366F1',
  borderRadius: 10,
    width: 20,
  height: 20,
    justifyContent: 'center',
  alignItems: 'center'
  },
  selectedIndicatorText: {
      color: theme.colors.background,
  fontSize: 12,
    fontWeight: 'bold' }
  categoryStats: {
      flexDirection: 'row',
  justifyContent: 'space-between'
  },
  statItem: {
      flexDirection: 'row',
  alignItems: 'center'
  },
  statText: { fontSiz, e: 12,
    color: '#6B7280',
  marginLeft: 4 }
  loadingContainer: { fle, x: 1,
    justifyContent: 'center'),
  alignItems: 'center'),
    padding: 32 },
  loadingText: {
      marginTop: 8,
  fontSize: 14,
    color: '#6B7280') }
}),
  export default ServiceCategoryFilter; ;