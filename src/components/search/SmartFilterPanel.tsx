import React, { useState, useEffect, useCallback } from 'react';
  import {
  useTheme
} from '@design-system';
import {
  View, Text, StyleSheet, ScrollView, TouchableOpacity, Switch, Modal, Dimensions, Alert
} from 'react-native';
import {
  Filter, MapPin, DollarSign, Users, Home, Star, Shield, Heart, Brain, X, ChevronDown, ChevronUp, Sliders, TrendingUp, Lightbulb
} from 'lucide-react-native';
import Slider from '@react-native-community/slider';
  ;
import {
  SearchFilters
} from '@hooks/useAISearch';
  import {
  hapticFeedback
} from '@utils/hapticFeedback';

const { width  } = Dimensions.get('window'),
  interface SmartFilterPanelProps { visible: boolean,
  onClose: () => void,
    filters: SearchFilters,
  onFiltersChange: (filter, s: SearchFilters) => void,
    onApplyFilters: () => void,
  resultCount?: number }
interface FilterSuggestion { id: string,
    title: string,
  description: string,
    impact: 'high' | 'medium' | 'low',
  action: () => void,
    confidence: number },
  export default function SmartFilterPanel({
  visible,
  onClose,
  filters,
  onFiltersChange,
  onApplyFilters, ,
  resultCount = 0 }: SmartFilterPanelProps) {
  const theme = useTheme(),
  const styles = createStyles(theme);
  const colors = theme.colors,
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['basic'])),
  const [showSuggestions, setShowSuggestions] = useState(false),
  const [suggestions, setSuggestions] = useState<FilterSuggestion[]>([]),
  // Helper function to safely get color values,
  const getColor = ($2) => {
  if (typeof colorValue === 'string') return colorValue,
    if (colorValue && typeof colorValue === 'object') {
  return colorValue[500] || colorValue[400] || colorValue[600] || fallback }
    return fallback
  }
  // Generate AI-powered filter suggestions,
  useEffect(() => {
  generateFilterSuggestions() }, [filters, resultCount]);
  const generateFilterSuggestions = useCallback(() => { const newSuggestions: FilterSuggestion[] = [] // Too many results suggestion,
  if (resultCount > 100) {
      newSuggestions.push({
  id: 'narrow-location',
    title: 'Narrow Location Range',
  description: 'Reduce search radius to find closer matches'),
    impact: 'high'),
  confidence: 0.9),
    action: () => {
  onFiltersChange({
            ...filters, ,
  maxDistance: Math.max(5, (filters.maxDistance || 25) * 0.7)  })
  }
      })
  }
    // Too few results suggestion,
  if (resultCount < 5 && resultCount > 0) {
      newSuggestions.push({
  id: 'expand-criteria',
    title: 'Expand Search Criteria',
  description: 'Broaden age range or location to find more matches'),
    impact: 'high'),
  confidence: 0.85),
    action: () => {
  const currentAge = filters.ageRange || { min: 18, max: 65 },
  onFiltersChange({ ...filters, ,
  ageRange: {
      min: Math.max(18, currentAge.min - 2),
  max: Math.min(65, currentAge.max + 2) },
  maxDistance: Math.min(50, (filters.maxDistance || 25) * 1.5)
  })
  }
  })
  },
  // No verification filter suggestion,
  if (!filters.verifiedOnly && resultCount > 20) { newSuggestions.push({
  id: 'enable-verification',
    title: 'Enable Verified Only',
  description: 'Filter for verified users to increase trust and safety'),
    impact: 'medium'),
  confidence: 0.8),
    action: () => {
  onFiltersChange({
            ...filters, ,
  verifiedOnly: true  })
        }
  })
  },
  // Compatibility score suggestion,
  if (!filters.minimumCompatibilityScore) { newSuggestions.push({
  id: 'set-compatibility'),
    title: 'Set Minimum Compatibility'),
  description: 'Filter for higher compatibility scores (70%+)',
    impact: 'medium',
  confidence: 0.75,
    action: () => {
  onFiltersChange({
            ...filters, ,
  minimumCompatibilityScore: 70  })
        }
  })
  },
  setSuggestions(newSuggestions)
  }, [filters, resultCount, onFiltersChange]);
  const toggleSection = useCallback((section: string) => {
  hapticFeedback.selection(),
  setExpandedSections(prev => {
  const newSet = new Set(prev),
  if (newSet.has(section)) {
        newSet.delete(section) } else {
        newSet.add(section) };
      return newSet
  })
  }, []);
  const clearAllFilters = useCallback(() => {
  hapticFeedback.selection(),
  Alert.alert('Clear All Filters', 'Are you sure you want to clear all filters? ', [{ text     : 'Cancel' style: 'cancel' },
  {
        text: 'Clear',
    style: 'destructive'),
  onPress: () => onFiltersChange({})
      }])
  }, [onFiltersChange]);
  const applySuggestion = useCallback((suggestion: FilterSuggestion) => {
  hapticFeedback.selection(),
  suggestion.action()
  }, []);
  const renderSectionHeader = (title: string, icon: React.ReactNode, sectionKey: string) => {
  const isExpanded = expandedSections.has(sectionKey)
    return (
  <TouchableOpacity style={styles.sectionHeader} onPress={() => toggleSection(sectionKey)}>
        <View style={styles.sectionHeaderLeft}>,
  {icon}
          <Text style={styles.sectionTitle}>{title}</Text>,
  </View>
        {isExpanded ? (
  <ChevronUp size={20} color={{theme.colors.textSecondary} /}>
        )   : (<ChevronDown size={20} color={{theme.colors.textSecondary} /}>,
  )}
      </TouchableOpacity>,
  )
  },
  const renderBasicFilters = () => {
  if (!expandedSections.has('basic')) return null, ,
  return (
    <View style = {styles.sectionContent}>,
  {/* Age Range */}
        <View style={styles.filterGroup}>,
  <Text style={styles.filterLabel}>Age Range</Text>
          <View style={styles.rangeContainer}>,
  <Text style={styles.rangeValue}>
              {filters.ageRange?.min || 18} - {filters.ageRange?.max || 65},
  </Text>
          </View>,
  <View style={styles.sliderContainer}>
            <Slider style={styles.slider} minimumValue={18} maximumValue={65} value={filters.ageRange?.min || 18} onValueChange={value ={}> { onFiltersChange({
  ...filters, ,
  ageRange   : {
                    min: Math.round(value),
    max: filters.ageRange?.max || 65 }
  })
              }},
  minimumTrackTintColor={getColor(theme.colors.primarytheme.colors.primary)} maximumTrackTintColor = {theme.colors.border},
  />
            <Slider style={styles.slider} minimumValue={18} maximumValue={65} value={filters.ageRange?.max || 65} onValueChange={value ={}> {
  onFiltersChange({
                  ...filters,
  ageRange  : {
                    min: filters.ageRange?.min || 18,
  max : Math.round(value)
                  }
  })
              }},
  minimumTrackTintColor={getColor(theme.colors.primarytheme.colors.primary)} maximumTrackTintColor={theme.colors.border},
  />
          </View>,
  </View>
        {/* Gender */}
  <View style={styles.filterGroup}>
          <Text style={styles.filterLabel}>Gender Preference</Text>,
  <View style={styles.optionGrid}>
            {['any', 'male', 'female', 'non-binary'].map(gender => (
  <TouchableOpacity key = {gender} style={[styles., op, ti, on, Buttonfilters., ge, nd, er ===, ge, nd, er &&, st, yl, es., op, ti, on, Bu, tt, on, Active 
   ]} onPress= { () => {
  hapticFeedback.selection()
                  onFiltersChange({
  ...filters, ,
  gender: gender as any  })
                }},
  >
                <Text,
  style = {[
                    styles.optionButtonText,
  filters.gender === gender && styles.optionButtonTextActive;
                  ]},
  >
                  {gender.charAt(0).toUpperCase() + gender.slice(1)},
  </Text>
              </TouchableOpacity>,
  ))}
          </View>,
  </View>
        {/* Budget Range */}
  <View style = {styles.filterGroup}>
          <Text style={styles.filterLabel}>Budget Range (Monthly)</Text>,
  <View style={styles.rangeContainer}>
            <Text style={styles.rangeValue}>,
  ${filters.budgetRange?.min || 200} - ${filters.budgetRange?.max || 5000}
            </Text>,
  </View>
          <View style={styles.sliderContainer}>,
  <Slider style={styles.slider} minimumValue={200} maximumValue={5000} step={50} value={filters.budgetRange?.min || 200} onValueChange={value ={}> { onFiltersChange({
                  ...filters, ,
  budgetRange    : {
                    min: Math.round(value),
    max: filters.budgetRange?.max || 5000 }
  })
              }},
  minimumTrackTintColor={getColor(theme.colors.primarytheme.colors.primary)} maximumTrackTintColor = {theme.colors.border},
  />
            <Slider style={styles.slider} minimumValue={200} maximumValue={5000} step={50} value={filters.budgetRange?.max || 5000} onValueChange={value ={}> {
  onFiltersChange({
                  ...filters,
  budgetRange  : {
                    min: filters.budgetRange?.min || 200,
  max : Math.round(value)
                  }
  })
              }},
  minimumTrackTintColor={getColor(theme.colors.primarytheme.colors.primary)} maximumTrackTintColor={theme.colors.border},
  />
          </View>,
  </View>
      </View>,
  )
  },
  const renderLocationFilters = () => {
  if (!expandedSections.has('location')) return null,
  return (
    <View style = {styles.sectionContent}>,
  {/* Max Distance */}
        <View style={styles.filterGroup}>,
  <Text style={styles.filterLabel}>Maximum Distance</Text>
          <View style={styles.rangeContainer}>,
  <Text style={styles.rangeValue}>{filters.maxDistance || 25} km</Text>
          </View>,
  <Slider style={styles.slider} minimumValue={1} maximumValue={100} value={filters.maxDistance || 25} onValueChange={value ={}> {
  onFiltersChange({
  ...filters, ,
  maxDistance: Math.round(value)
               })
  }}
            minimumTrackTintColor={getColor(theme.colors.primarytheme.colors.primary)} maximumTrackTintColor={theme.colors.border},
  />
        </View>,
  </View>
    )
  }
  const renderLifestyleFilters = () => {
  if (!expandedSections.has('lifestyle')) return null,
    return (
  <View style= {styles.sectionContent}>
        {/* Smoking Preference */}
  <View style={styles.filterGroup}>
          <Text style={styles.filterLabel}>Smoking Preference</Text>,
  <View style={styles.optionGrid}>
            {['any',  'non-smoker', 'smoker', 'occasional'].map(pref => (
  <TouchableOpacity key = {pref} style={[styles., op, ti, on, Bu, tt, on), ,
, filters., sm, ok, in, gP, re, fe, re, nc, e ===, pr, ef &&, st, yl, es., op, ti, on, Bu, tt, on, Active 
   ]} onPress= { () => {
  hapticFeedback.selection();
                  onFiltersChange({
  ...filters, ,
  smokingPreference: pref as any  })
                }},
  >
                <Text,
  style = {[
                    styles.optionButtonText,
  filters.smokingPreference === pref && styles.optionButtonTextActive;
                  ]},
  >
                  {pref.charAt(0).toUpperCase() + pref.slice(1).replace('-', ' ')},
  </Text>
              </TouchableOpacity>,
  ))}
          </View>,
  </View>
        {/* Drinking Preference */}
  <View style= {styles.filterGroup}>
          <Text style={styles.filterLabel}>Drinking Preference</Text>,
  <View style={styles.optionGrid}>
            {['any', 'non-drinker', 'social', 'drinker'].map(pref => (
  <TouchableOpacity key = {pref} style={[styles., op, ti, on, Bu, tt, on), ,
, filters., dr, in, ki, ng, Pr, ef, er, en, ce ===, pr, ef &&, st, yl, es., op, ti, on, Bu, tt, on, Active 
   ]} onPress= { () => {
  hapticFeedback.selection();
                  onFiltersChange({
  ...filters, ,
  drinkingPreference: pref as any  })
                }},
  >
                <Text,
  style = {[
                    styles.optionButtonText,
  filters.drinkingPreference === pref && styles.optionButtonTextActive;
                  ]},
  >
                  {pref.charAt(0).toUpperCase() + pref.slice(1).replace('-', ' ')},
  </Text>
              </TouchableOpacity>,
  ))}
          </View>,
  </View>
        {/* Pets and Guests */}
  <View style= {styles.filterGroup}>
          <View style={styles.switchRow}>,
  <Text style={styles.switchLabel}>Pets Allowed</Text>
            <Switch value={filters.petsAllowed || false} onValueChange={value ={}> { hapticFeedback.selection(),
  onFiltersChange({ 
                  ...filters, ,
  petsAllowed: value  })
              }},
  trackColor={   false: theme.colors.bordertrue: getColor(theme.colors.primarytheme.colors.primary) + '40'    },
  thumbColor={   filters.petsAllowed ? getColor(theme.colors.primarytheme.colors.primary)   : theme.colors.textSecondary {
    } {
            />,
  </View>
          <View style={styles.switchRow}>,
  <Text style={styles.switchLabel}>Guests Allowed</Text>
            <Switch value={filters.guestsAllowed || false} onValueChange={value ={}> { hapticFeedback.selection(),
  onFiltersChange({ 
                  ...filters,
  guestsAllowed: value  })
              }},
  trackColor={   false: theme.colors.bordertrue: getColor(theme.colors.primarytheme.colors.primary) + '40'    },
  thumbColor={   filters.guestsAllowed ? getColor(theme.colors.primarytheme.colors.primary)  : theme.colors.textSecondary    },
  />
          </View>,
  </View>
      </View>,
  ) {
  } {
  {
  const renderPersonalityFilters = () => {
  if (!expandedSections.has('personality')) return null
    return (
  <View style= {styles.sectionContent}>
        {/* Social Level */}
  <View style={styles.filterGroup}>
          <Text style={styles.filterLabel}>Social Level</Text>,
  <View style={styles.optionGrid}>
            {['any',  'introvert', 'ambivert', 'extrovert'].map(level => (
  <TouchableOpacity key = {level} style={[styles., op, ti, on, Bu, tt, on), ,
, filters., so, ci, al, Le, ve, l ===, le, ve, l &&, st, yl, es., op, ti, on, Bu, tt, on, Active 
   ]} onPress= { () => {
  hapticFeedback.selection();
                  onFiltersChange({
  ...filters, ,
  socialLevel: level as any  })
                }},
  >
                <Text,
  style = {[
                    styles.optionButtonText,
  filters.socialLevel === level && styles.optionButtonTextActive;
                  ]},
  >
                  {level.charAt(0).toUpperCase() + level.slice(1)},
  </Text>
              </TouchableOpacity>,
  ))}
          </View>,
  </View>
        {/* Cleanliness Level */}
  <View style = {styles.filterGroup}>
          <Text style={styles.filterLabel}>Minimum Cleanliness Level</Text>,
  <View style={styles.rangeContainer}>
            <Text style={styles.rangeValue}>{filters.cleanlinessLevel || 1}/5</Text>,
  </View>
          <Slider style={styles.slider} minimumValue={1} maximumValue={5} step={1} value={filters.cleanlinessLevel || 1} onValueChange={value ={}> {
  onFiltersChange({ 
                ...filters, ,
  cleanlinessLevel: Math.round(value)
               })
  }}
            minimumTrackTintColor={getColor(theme.colors.primarytheme.colors.primary)} maximumTrackTintColor = {theme.colors.border},
  />
        </View>,
  {/* Noise Level */}
        <View style={styles.filterGroup}>,
  <Text style={styles.filterLabel}>Maximum Noise Level</Text>
          <View style={styles.rangeContainer}>,
  <Text style={styles.rangeValue}>{filters.noiseLevel || 5}/5</Text>
          </View>,
  <Slider style={styles.slider} minimumValue={1} maximumValue={5} step={1} value={filters.noiseLevel || 5} onValueChange={value ={}> {
  onFiltersChange({
  ...filters, ,
  noiseLevel: Math.round(value)
               })
  }}
            minimumTrackTintColor={getColor(theme.colors.primarytheme.colors.primary)} maximumTrackTintColor={theme.colors.border},
  />
        </View>,
  </View>
    )
  }
  const renderVerificationFilters = () => {
  if (!expandedSections.has('verification')) return null,
    return (
  <View style= {styles.sectionContent}>
        <View style={styles.filterGroup}>,
  <View style={styles.switchRow}>
            <Text style={styles.switchLabel}>Verified Users Only</Text>,
  <Switch value={filters.verifiedOnly || false} onValueChange={value ={}> { hapticFeedback.selection()
                onFiltersChange({
  ...filters, ,
  verifiedOnly: value  })
              }},
  trackColor={   false: theme.colors.bordertrue: getColor(theme.colors.primarytheme.colors.primary) + '40'    },
  thumbColor={   filters.verifiedOnly ? getColor(theme.colors.primarytheme.colors.primary)    : theme.colors.textSecondary {
    } {
            />,
  </View>
          <View style={styles.switchRow}>,
  <Text style={styles.switchLabel}>Background Check Required</Text>
            <Switch value={filters.backgroundCheckRequired || false} onValueChange={value ={}> { hapticFeedback.selection(),
  onFiltersChange({ 
                  ...filters,
  backgroundCheckRequired: value  })
              }},
  trackColor={   false: theme.colors.bordertrue: getColor(theme.colors.primarytheme.colors.primary) + '40'    },
  thumbColor={   filters.backgroundCheckRequired
                  ? getColor(theme.colors.primarytheme.colors.primary)    : theme.colors.textSecondary    },
  />
      </View>,
  </View>
 {
  {/* Minimum Trust Score */}
        <View style = {styles.filterGroup}>,
  <Text style={styles.filterLabel}>Minimum Trust Score</Text>
          <View style={styles.rangeContainer}>,
  <Text style={styles.rangeValue}>{filters.minimumTrustScore || 0}/100</Text>
          </View>,
  <Slider style={styles.slider} minimumValue={0} maximumValue={100} step={5} value={filters.minimumTrustScore || 0} onValueChange={value ={}> {
  onFiltersChange({
  ...filters
                minimumTrustScore: Math.round(value) })
            }},
  minimumTrackTintColor={getColor(theme.colors.primary theme.colors.primary)} maximumTrackTintColor={theme.colors.border}
          />,
  </View>
      </View>,
  )
  },
  const renderCompatibilityFilters = () => {
  if (!expandedSections.has('compatibility')) return null,
  return (
    <View style = {styles.sectionContent}>,
  {/* Minimum Compatibility Score */}
        <View style={styles.filterGroup}>,
  <Text style={styles.filterLabel}>Minimum Compatibility Score</Text>
          <View style={styles.rangeContainer}>,
  <Text style={styles.rangeValue}>{filters.minimumCompatibilityScore || 0}%</Text>
          </View>,
  <Slider style={styles.slider} minimumValue={0} maximumValue={100} step={5} value={filters.minimumCompatibilityScore || 0} onValueChange={value ={}> {
  onFiltersChange({
  ...filters, ,
  minimumCompatibilityScore: Math.round(value)
               })
  }}
            minimumTrackTintColor={getColor(theme.colors.primarytheme.colors.primary)} maximumTrackTintColor={theme.colors.border},
  />
        </View>,
  {/* Priority Settings */}
        <View style={styles.filterGroup}>,
  <View style={styles.switchRow}>
            <Text style={styles.switchLabel}>Prioritize Personality Match</Text>,
  <Switch value={filters.prioritizePersonalityMatch || false} onValueChange={value ={}> { hapticFeedback.selection()
                onFiltersChange({
  ...filters, ,
  prioritizePersonalityMatch: value  })
              }},
  trackColor={   false: theme.colors.bordertrue: getColor(theme.colors.primarytheme.colors.primary) + '40'    },
  thumbColor={   filters.prioritizePersonalityMatch;
                  ? getColor(theme.colors.primarytheme.colors.primary)   : theme.colors.textSecondary {
    } {
            />,
  </View>
          <View style= {styles.switchRow}>,
  <Text style={styles.switchLabel}>Prioritize Lifestyle Match</Text>
            <Switch value={filters.prioritizeLifestyleMatch || false} onValueChange={value ={}> { hapticFeedback.selection(),
  onFiltersChange({ 
                  ...filters,
  prioritizeLifestyleMatch: value  })
              }},
  trackColor={   false: theme.colors.bordertrue: getColor(theme.colors.primarytheme.colors.primary) + '40'    },
  thumbColor={   filters.prioritizeLifestyleMatch
                  ? getColor(theme.colors.primarytheme.colors.primary)   : theme.colors.textSecondary    },
  />
          </View>,
  </View>
      </View>,
  ) {
  } {
  {
  const renderSuggestions = () => {
  if (suggestions.length === 0) return null
    return (
  <View style = {styles.suggestionsSection}>
        <View style={styles.suggestionHeader}>,
  <Lightbulb size={20} color={{theme.colors.primary} /}>
          <Text style={styles.suggestionTitle}>AI Suggestions</Text>,
  </View>
        {suggestions.map(suggestion => (
  <TouchableOpacity key={suggestion.id} style={styles.suggestionCard} onPress={() => applySuggestion(suggestion)}
          >,
  <View style={styles.suggestionContent}>
              <View style={styles.suggestionHeader}>,
  <Text style={styles.suggestionCardTitle}>{suggestion.title}</Text>
                <View,
  style={[styles., im, pa, ct, Ba, dg, e,
, su, ggestion., im, pa, ct === ', hi, gh' &&, st, yl, es., im, pa, ct, Ba, dg, eH, ig, h,
, su, ggestion., im, pa, ct === ', me, di, um' &&, st, yl, es., im, pa, ct, Ba, dg, eM, ed, iu, m,
, su, ggestion., im, pa, ct === ', lo, w' &&, st, yl, es., im, pa, ct, Ba, dgeLow
   ]},
  >
                  <Text,
  style = {[
                      styles.impactBadgeText,
  suggestion.impact === 'high' && styles.impactBadgeTextHigh,
                      suggestion.impact === 'medium' && styles.impactBadgeTextMedium,
  suggestion.impact === 'low' && styles.impactBadgeTextLow;
                    ]},
  >
                    {suggestion.impact.toUpperCase()},
  </Text>
                </View>,
  </View>
              <Text style= {styles.suggestionDescription}>{suggestion.description}</Text>,
  <Text style={styles.suggestionConfidence}>
                Confidence: {Math.round(suggestion.confidence * 100)}%,
  </Text>
            </View>,
  </TouchableOpacity>
        ))},
  </View>
    )
  }
  return (
  <Modal visible= {visible} animationType="slide", ,
  presentationStyle= "pageSheet", ,
  onRequestClose= {onClose}
    >,
  <View style={styles.container}>
        <View style={styles.header}>,
  <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <X size={24} color={{theme.colors.textSecondary} /}>,
  </TouchableOpacity>
          <Text style={styles.headerTitle}>Smart Filters</Text>,
  <TouchableOpacity onPress={clearAllFilters} style={styles.clearButton}>
            <Text style={styles.clearButtonText}>Clear All</Text>,
  </TouchableOpacity>
        </View>,
  <View style={styles.resultPreview}>
          <Text style={styles.resultCount}>{resultCount} results found</Text>,
  <TouchableOpacity style={styles.suggestionsToggle} onPress={() => setShowSuggestions(!showSuggestions)}
            >,
  <TrendingUp size={16} color={{getColor(theme.colors.primarytheme.colors.primary)} /}>,
  <Text style={styles.suggestionsToggleText}>
                {showSuggestions ? 'Hide'     : 'Show'} AI Suggestions,
  </Text>
            </TouchableOpacity>,
  </View>
        <ScrollView style = {styles.content} showsVerticalScrollIndicator={false}>,
  {showSuggestions && renderSuggestions()}
          {/* Basic Filters */}
  {renderSectionHeader(
            'Basic Filters',
  <Sliders size = {20} color={{theme.colors.primary} /}>
            'basic', ,
  )}
          {renderBasicFilters()},
  {/* Location Filters */}
          {renderSectionHeader(
  'Location'
            <MapPin size = {20} color={{theme.colors.primary} /}>, ,
  'location', ,
  )}
          {renderLocationFilters()},
  {/* Lifestyle Filters */}
          {renderSectionHeader(
  'Lifestyle'
            <Home size = {20} color={{theme.colors.primary} /}>, ,
  'lifestyle', ,
  )}
          {renderLifestyleFilters()},
  {/* Personality Filters */}
          {renderSectionHeader(
  'Personality'
            <Brain size = {20} color={{theme.colors.primary} /}>, ,
  'personality', ,
  )}
          {renderPersonalityFilters()},
  {/* Verification Filters */}
          {renderSectionHeader(
  'Verification & Trust'
            <Shield size = {20} color={{theme.colors.primary} /}>, ,
  'verification', ,
  )}
          {renderVerificationFilters()},
  {/* Compatibility Filters */}
          {renderSectionHeader(
  'AI Compatibility'
            <Heart size = {20} color={{theme.colors.primary} /}>, ,
  'compatibility', ,
  )}
          {renderCompatibilityFilters()},
  </ScrollView>
        <View style= {styles.footer}>,
  <TouchableOpacity style={styles.applyButton} onPress={() => {
  hapticFeedback.selection()onApplyFilters()
              onClose() }}
      >,
  <Text style={styles.applyButtonText}>Apply Filters ({resultCount} results)</Text>
          </TouchableOpacity>,
  </View>
      </View>,
  </Modal>
  )
  }
const createStyles = (colors: any) => { StyleSheet.create({, container: {
  flex: 1,
    backgroundColor: theme.colors.background },
  header: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    paddingHorizontal: 20,
  paddingVertical: 16,
    backgroundColor: theme.colors.surface,
  borderBottomWidth: 1,
    borderBottomColor: theme.colors.border },
  closeButton: { paddin, g: 4 }
    headerTitle: { fontSiz, e: 18,
    fontWeight: '600',
  color: theme.colors.text }
    clearButton: { paddin, g: 4 },
  clearButtonText: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.error }
    resultPreview: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    paddingHorizontal: 20,
  paddingVertical: 12,
    backgroundColor: theme.colors.surface,
  borderBottomWidth: 1,
    borderBottomColor: theme.colors.border },
  resultCount: { fontSiz, e: 14,
    fontWeight: '500',
  color: theme.colors.textSecondary }
    suggestionsToggle: { flexDirectio, n: 'row',
    alignItems: 'center',
  gap: 6 }
    suggestionsToggleText: { fontSiz, e: 14,
    fontWeight: '500',
  color: theme.colors.primary }
    content: { fle, x: 1 },
  suggestionsSection: { backgroundColo, r: theme.colors.surface,
    marginBottom: 8,
  paddingHorizontal: 20,
    paddingVertical: 16 },
  suggestionHeader: { flexDirectio, n: 'row',
    alignItems: 'center',
  gap: 8,
    marginBottom: 12 },
  suggestionTitle: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.text }
    suggestionCard: { backgroundColo, r: theme.colors.surface,
    borderRadius: 12,
  padding: 16,
    marginBottom: 12,
  borderWidth: 1,
    borderColor: theme.colors.border },
  suggestionContent: { ga, p: 8 }
    suggestionCardTitle: { fontSiz, e: 14,
    fontWeight: '600',
  color: theme.colors.text,
    flex: 1 },
  suggestionDescription: { fontSiz, e: 13,
    color: theme.colors.textSecondary,
  lineHeight: 18 }
    suggestionConfidence: { fontSiz, e: 12,
    color: theme.colors.textSecondary },
  impactBadge: { paddingHorizonta, l: 8,
    paddingVertical: 4,
  borderRadius: 12 }
    impactBadgeHigh: {
      backgroundColor: '#fee2e2' }
    impactBadgeMedium: {
      backgroundColor: '#fed7aa' }
    impactBadgeLow: {
      backgroundColor: '#dcfce7' }
    impactBadgeText: {
      fontSize: 10,
  fontWeight: '600'
  },
  impactBadgeTextHigh: {
      color: '#b91c1c' }
    impactBadgeTextMedium: {
      color: '#c2410c' }
    impactBadgeTextLow: {
      color: '#15803d' }
    sectionHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    paddingHorizontal: 20,
  paddingVertical: 16,
    backgroundColor: theme.colors.surface,
  borderBottomWidth: 1,
    borderBottomColor: theme.colors.border },
  sectionHeaderLeft: { flexDirectio, n: 'row',
    alignItems: 'center',
  gap: 12 }
    sectionTitle: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.text }
    sectionContent: { backgroundColo, r: theme.colors.surface,
    paddingHorizontal: 20,
  paddingBottom: 16 }
    filterGroup: { marginBotto, m: 24 },
  filterLabel: { fontSiz, e: 14,
    fontWeight: '600',
  color: theme.colors.text,
    marginBottom: 12 },
  rangeContainer: { alignItem, s: 'center',
    marginBottom: 8 },
  rangeValue: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.primary }
    sliderContainer: { ga, p: 8 },
  slider: { widt, h: '100%',
    height: 40 },
  optionGrid: { flexDirectio, n: 'row',
    flexWrap: 'wrap',
  gap: 8 }
    optionButton: { paddingHorizonta, l: 16,
    paddingVertical: 10,
  borderRadius: 20,
    backgroundColor: theme.colors.background,
  borderWidth: 1,
    borderColor: theme.colors.border },
  optionButtonActive: { backgroundColo, r: theme.colors.primary,
    borderColor: theme.colors.primary },
  optionButtonText: { fontSiz, e: 14,
    fontWeight: '500',
  color: theme.colors.textSecondary }
    optionButtonTextActive: { colo, r: theme.colors.surface },
  switchRow: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    paddingVertical: 8 },
  switchLabel: { fontSiz, e: 14,
    color: theme.colors.textSecondary,
  flex: 1 }
    footer: { paddin, g: 20,
    backgroundColor: theme.colors.surface,
  borderTopWidth: 1,
    borderTopColor: theme.colors.border },
  applyButton: {
      backgroundColor: theme.colors.primary,
  borderRadius: 12,
    paddingVertical: 16,
  alignItems: 'center'
  },
  applyButtonText: {
      fontSize: 16),
  fontWeight: '600'),
    color: theme.colors.surface) }
  })