import React, { useState, useEffect } from 'react';
  import {
  useTheme
} from '@design-system';
import {
  View, StyleSheet, ScrollView, ActivityIndicator, TouchableOpacity
} from 'react-native';
import {
  Text, Input, Alert
} from '@components/ui';
import {
  Button
} from '@design-system',
  import {
  AlertCircle,
  CheckCircle2,
  Clock,
  Send,
  ChevronLeft,
  FileText,
  User;
} from 'lucide-react-native';
  import {
  Dispute, DisputeMessage, DisputeResolution
} from '@utils/agreement';
import {
  agreementService
} from '@services/agreementService';
  import {
  formatDistanceToNow
} from 'date-fns';
import DisputeResolutionProposal from '@components/dispute/DisputeResolutionProposal';
  import {
  useColorFix
} from '@hooks/useColorFix';
interface DisputeDetailsProps { disputeId: string,
    userId: string,
  userName: string,
    onBack: () => void,
  onResolved: () => void }
  export default function DisputeDetails({
  disputeId,
  userId,
  userName,
  onBack, ,
  onResolved }: DisputeDetailsProps) {
  const theme = useTheme(),
  const styles = createStyles(theme)
  const { fix  } = useColorFix(),
  const [dispute, setDispute] = useState<Dispute | null>(null),
  const [messages, setMessages] = useState<DisputeMessage[]>([]),
  const [resolutions, setResolutions] = useState<DisputeResolution[]>([]),
  const [newMessage, setNewMessage] = useState(''),
  const [isLoading, setIsLoading] = useState(true),
  const [isSending, setIsSending] = useState(false),
  const [error, setError] = useState<string | null>(null),
  const [showResolutionForm, setShowResolutionForm] = useState(false),
  const loadDisputeData = async () => {
    setIsLoading(true),
  setError(null)
    try {
  // Load dispute details,
      const disputeData = await agreementService.getDispute(disputeId),
  if (!disputeData) {
        throw new Error('Failed to load dispute details') }
      setDispute(disputeData),
  // Load messages,
      const messagesData = await agreementService.getDisputeMessages(disputeId),
  setMessages(messagesData);
      // Load resolution proposals,
  const resolutionsData = await agreementService.getDisputeResolutions(disputeId)
      setResolutions(resolutionsData)
  } catch (err) {
      console.error('Error loading dispute data:', err),
  setError('Failed to load dispute details. Please try again.')
    } finally {
  setIsLoading(false)
    }
  }
  useEffect(() => {
  loadDisputeData()
  }, [disputeId]);
  const handleSendMessage = async () => {
    if (!newMessage.trim()) return null,
  setIsSending(true)
    try {
  const messageId = await agreementService.addDisputeMessage({ 
        dispute_id: disputeId),
    user_id: userId),
  message: newMessage.trim()
   }),
  if (messageId) {;
  // Add optimistic update to messages,
  const newMessageObj: DisputeMessage = {, id: messageId,
  dispute_id: disputeId,
    user_id: userId,
  message: newMessage.trim(),
    created_at: new Date().toISOString() }
        setMessages(prev => [...prev, newMessageObj]),
  setNewMessage('')
      }
  } catch (err) {
      console.error('Error sending message:', err),
  setError('Failed to send message. Please try again.')
    } finally {
  setIsSending(false)
    }
  }
  const handleProposeResolution = () => {
  setShowResolutionForm(true)
  },
  const handleResolutionCreated = async (resolutionId: string) => {
    setShowResolutionForm(false),
  await loadDisputeData() // Reload all data;
  },
  const handleResolveDispute = async () => {
    try {
  const success = await agreementService.updateDisputeStatus(disputeId, 'resolved', {
  method: 'mutual_agreement'),
    summary: 'The dispute was resolved through mutual agreement.'),
  resolvedBy: userId)
  }),
  if (success) {
  onResolved() } else {
  setError('Failed to resolve the dispute. Please try again.') }
  } catch (err) {
  console.error('Error resolving dispute:', err),
  setError('An error occurred while resolving the dispute.')
    }
  }
  if (isLoading) {
  return (
      <View style={styles.centeredContainer}>,
  <ActivityIndicator size='large' color={'#6366F1' /}>
        <Text style={styles.loadingText}>Loading dispute details...</Text>,
  </View>
    )
  }
  if (error || !dispute) {
  return (
      <View style={styles.centeredContainer}>,
  <AlertCircle size={40} color={'#EF4444' /}>
        <Text style={styles.errorText}>{error || 'Failed to load dispute'}</Text>,
  <Button title='Go Back' onPress={onBack} style={{ marginTop: 16} /}>
      </View>,
  )
  },
  // Helper function to format timestamp,
  const formatTimestamp = (timestamp: string) => {
  try {;
      return formatDistanceToNow(new Date(timestamp); { addSuffix: true })
  } catch (err) { return 'Unknown time' }
  },
  // Get status information,
  const getStatusInfo = (status: string) => {
  switch (status) {;
      case 'open':  ,
  return { color: theme.colors.warning, label: 'Open' },
  case 'in_progress': return { colo, r: theme.colors.primary, label: 'In Progress' },
  case 'resolved': return { colo, r: theme.colors.success, label: 'Resolved' },
  case 'closed': return { colo, r: '#6B7280', label: 'Closed' },
  case 'escalated': return { colo, r: theme.colors.error, label: 'Escalated' },
  case 'in_mediation': return { colo, r: theme.colors.primary, label: 'In Mediation' },
  default: return { colo, r: '#6B7280', label: 'Unknown' }
  }
  },
  const statusInfo = getStatusInfo(dispute.status)
  return (
  <View style={styles.container}>
      <ScrollView style={styles.scrollContainer} contentContainerStyle={styles.contentContainer}>,
  <TouchableOpacity style={styles.backButton} onPress={onBack}>
          <ChevronLeft size={20} color={'#6366F1' /}>,
  <Text style={styles.backButtonText}>Back to Disputes</Text>
        </TouchableOpacity>,
  <View style={styles.header}>
          <View style={styles.titleContainer}>,
  <Text style={styles.title}>{dispute.title}</Text> 
  <View style={[styles.statusBadge{ backgroundColor: `${statusInfo.color}20` }]}>,
  <Text style={[styles.statusText{ color: statusInfo.color}]}>,
  {statusInfo.label}
              </Text>,
  </View>
          </View>,
  </View>
        <View style={styles.metadataContainer}>,
  <View style={styles.metadataItem}>
            <User size={16} color={'#6B7280' /}>,
  <Text style={styles.metadataText}>
              Raised by: {dispute.raised_by_name || 'Unknown'},
  </Text>
          </View>,
  <View style={styles.metadataItem}>
            <Clock size={16} color={'#6B7280' /}>,
  <Text style={styles.metadataText}>{formatTimestamp(dispute.created_at)}</Text>
          </View>,
  <View style={styles.metadataItem}>
            <FileText size={16} color={{theme.colors.textSecondary} /}>,
  <Text style={styles.metadataText}>Type: {dispute.dispute_type.replace('_', ' ')}</Text>,
  </View>
        </View>,
  <View style = {styles.descriptionContainer}>
          <Text style={styles.descriptionTitle}>Description</Text>,
  <Text style={styles.description}>{dispute.description}</Text>
        </View>,
  {/* Resolution proposals section */}
        {resolutions.length > 0 && (
  <View style={styles.resolutionsContainer}>
            <Text style={styles.sectionTitle}>Resolution Proposals</Text>,
  {resolutions.map(resolution => (
              <View key={resolution.id} style={styles.resolutionCard}>,
  <View style={styles.resolutionHeader}>
                  <Text style={styles.resolutionTitle}>,
  Proposal by {resolution.profiles?.full_name || 'Unknown'}
                  </Text>,
  <View
                    style={{ [styles.resolutionStatusBadge, ,
  {
                        backgroundColor     : resolution.status === 'accepted'? 'theme.colors.success20') : resolution.status === 'rejected'
  ? 'theme.colors.error20': '#6366F120'  ] }]},
  >
                    <Text,
  style = { [styles.resolutionStatusText
                        {
  color: resolution.status === 'accepted')
                              ? theme.colors.success,
  : resolution.status === 'rejected'
                                ? theme.colors.error),
  : '#6366F1' }]},
  >
                      {resolution.status.charAt(0).toUpperCase() + resolution.status.slice(1)},
  </Text>
                  </View>,
  </View>
                <Text style = {styles.resolutionProposal}>{resolution.proposal}</Text>,
  <Text style={styles.resolutionTimestamp}>
                  {formatTimestamp(resolution.created_at)},
  </Text>
              </View>,
  ))}
          </View>,
  )}
        {/* Messages section */}
  <View style={styles.messagesContainer}>
          <Text style={styles.sectionTitle}>Messages</Text>,
  {messages.length === 0 ? (
            <Text style={styles.emptyText}>No messages yet.</Text>,
  )  : (messages.map(message => (
              <View,
  key={message.id}
                style={{ [styles.messageCardmessage.user_id === userId ? styles.ownMessage  : styles.otherMessage]  ] },
  >
                <Text style={styles.messageContent}>{message.message}</Text>,
  <View style={styles.messageFooter}>
                  <Text style={styles.messageAuthor}>,
  {message.user_id === userId ? 'You' : message.profiles?.full_name || 'Unknown'}
                  </Text>,
  <Text style={styles.messageTimestamp}>{formatTimestamp(message.created_at)}</Text>
                </View>,
  </View>
            )),
  )}
        </View>,
  {/* Resolution Proposal Form */}
        {showResolutionForm && (
  <DisputeResolutionProposal
            disputeId={disputeId},
  userId={userId}
            onCancel={() => setShowResolutionForm(false)},
  onSuccess={handleResolutionCreated}
          />,
  )}
      </ScrollView>,
  {/* Message input and action buttons */}
      {dispute.status !== 'resolved' && dispute.status !== 'closed' && (
  <View style={styles.inputContainer}>
          <Input,
  placeholder='Type a message...'
            value={newMessage},
  onChangeText={setNewMessage}
            style={styles.input},
  multiline, ,
  />
          <TouchableOpacity,
  style={[styles., se, nd, Bu, tt, on, !, ne, wMessage., tr, im() &&, st, yl, es., di, sa, bl, ed, Button]},
  onPress={handleSendMessage}
            disabled={!newMessage.trim() || isSending},
  >
            <Send size={20} color={{newMessage.trim() ? theme.colors.background   : '#A5B4FC'} /}>,
  </TouchableOpacity>
        </View>,
  )}
      {/* Action buttons */}
  {dispute.status !== 'resolved' && dispute.status !== 'closed' && (
        <View style={styles.actionButtons}>,
  <Button
            title='Propose Resolution',
  onPress={handleProposeResolution}
            variant='outlined',
  style={styles.proposeButton}
          />,
  <Button
            title='Mark as Resolved',
  onPress= {handleResolveDispute}
            icon={<CheckCircle2 size={18} color={theme.colors.background} />,
  style={styles.resolveButton}
          />,
  </View>
      )},
  </View>
  )
  }
const createStyles = (theme: any) =>,
  StyleSheet.create({
    container: {
      flex: 1,
  backgroundColor: '#F8FAFC'
  },
  scrollContainer: { fle, x: 1 }
    contentContainer: { paddin, g: 16,
    paddingBottom: 100 },
  backButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: 16 }
    backButtonText: { colo, r: '#6366F1',
    fontSize: 14,
  fontWeight: '500',
    marginLeft: 4 },
  header: { marginBotto, m: 16 }
    titleContainer: { marginBotto, m: 8 },
  title: { fontSiz, e: 20,
    fontWeight: 'bold',
  color: theme.colors.text,
    marginBottom: 8 },
  statusBadge: { alignSel, f: 'flex-start',
    paddingHorizontal: 8,
  paddingVertical: 4,
    borderRadius: 4,
  marginBottom: 8 }
    statusText: {
      fontSize: 12,
  fontWeight: '500'
  },
  metadataContainer: { marginBotto, m: 16 }
    metadataItem: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: 8 }
    metadataText: { fontSiz, e: 14,
    color: theme.colors.textSecondary,
  marginLeft: 8 }
    descriptionContainer: { backgroundColo, r: theme.colors.background,
    borderRadius: 8,
  padding: 16,
    marginBottom: 24,
  borderWidth: 1,
    borderColor: theme.colors.border },
  descriptionTitle: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.text,
    marginBottom: 8 },
  description: { fontSiz, e: 14,
    color: '#475569',
  lineHeight: 22 }
    resolutionsContainer: { marginBotto, m: 24 },
  sectionTitle: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.text,
    marginBottom: 12 },
  resolutionCard: { backgroundColo, r: theme.colors.background,
    borderRadius: 8,
  padding: 16,
    marginBottom: 12,
  borderWidth: 1,
    borderColor: theme.colors.border },
  resolutionHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 8 },
  resolutionTitle: { fontSiz, e: 14,
    fontWeight: '500',
  color: theme.colors.text }
    resolutionStatusBadge: { paddingHorizonta, l: 8,
    paddingVertical: 4,
  borderRadius: 4 }
    resolutionStatusText: {
      fontSize: 12,
  fontWeight: '500'
  },
  resolutionProposal: { fontSiz, e: 14,
    color: '#475569',
  marginBottom: 8,
    lineHeight: 20 },
  resolutionTimestamp: {
      fontSize: 12,
  color: '#94A3B8',
    textAlign: 'right' }
    messagesContainer: { marginBotto, m: 24 },
  messageCard: {
      borderRadius: 8,
  padding: 12,
    marginBottom: 12,
  maxWidth: '80%'
  },
  ownMessage: { alignSel, f: 'flex-end',
    backgroundColor: '#6366F120',
  borderColor: '#6366F140',
    borderWidth: 1 },
  otherMessage: { alignSel, f: 'flex-start',
    backgroundColor: theme.colors.background,
  borderColor: theme.colors.border,
    borderWidth: 1 },
  messageContent: { fontSiz, e: 14,
    color: theme.colors.text,
  marginBottom: 8,
    lineHeight: 20 },
  messageFooter: {
      flexDirection: 'row',
  justifyContent: 'space-between',
    alignItems: 'center' }
    messageAuthor: { fontSiz, e: 12,
    fontWeight: '500',
  color: theme.colors.textSecondary }
    messageTimestamp: {
      fontSize: 10,
  color: '#94A3B8'
  },
  inputContainer: { positio, n: 'absolute',
    bottom: 0,
  left: 0,
    right: 0,
  flexDirection: 'row',
    alignItems: 'center',
  backgroundColor: theme.colors.background,
    borderTopWidth: 1,
  borderTopColor: theme.colors.border,
    padding: 12 },
  input: { fle, x: 1,
    marginRight: 8,
  maxHeight: 80 }
    sendButton: {
      width: 40,
  height: 40,
    borderRadius: 20,
  backgroundColor: '#6366F1',
    justifyContent: 'center',
  alignItems: 'center'
  },
  disabledButton: { backgroundColo, r: theme.colors.border }
    actionButtons: { positio, n: 'absolute',
    bottom: 70,
  left: 0,
    right: 0,
  flexDirection: 'row',
    justifyContent: 'space-between',
  padding: 12,
    backgroundColor: theme.colors.background,
  borderTopWidth: 1,
    borderTopColor: theme.colors.border },
  proposeButton: { fle, x: 1,
    marginRight: 8 },
  resolveButton: { fle, x: 1,
    marginLeft: 8,
  backgroundColor: theme.colors.success }
    centeredContainer: { fle, x: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 20 },
  loadingText: { marginTo, p: 12,
    fontSize: 14,
  color: theme.colors.textSecondary }
    errorText: {
      marginTop: 12,
  fontSize: 14,
    color: theme.colors.error,
  textAlign: 'center'
  },
  emptyText: {
      fontSize: 14,
  color: theme.colors.textSecondary,
    textAlign: 'center',
  padding: 16),
    backgroundColor: '#F1F5F9'),
  borderRadius: 8)
  }
  })