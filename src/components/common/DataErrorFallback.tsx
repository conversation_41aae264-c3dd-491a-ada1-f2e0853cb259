import React from 'react';
  import {
  View, Text, TouchableOpacity, StyleSheet
} from 'react-native';
import {
  RefreshCw, AlertCircle
} from 'lucide-react-native';
import {
  createLogger
} from '@utils/loggerUtils';
  import {
  useColorFix
} from '@hooks/useColorFix';
import {
  colorWithOpacity, type Theme
} from '@design-system';
import {
  useTheme
} from '@design-system',
  interface DataErrorFallbackProps { error: Error | null,
    resetError: () => void,
  retry: () => void,
  message?: string,
  isFullScreen?: boolean }
  const logger = createLogger('DataErrorFallback'),;
  /**;
  * A fallback component to display when data loading fails;
  * Provides error details and retry functionality;
  */,
  const DataErrorFallback: React.FC<DataErrorFallbackProps> = ({ ;
  error,
  resetError,
  retry,
  message, ,
  isFullScreen = false }) => {
  // Log the error for debugging,
  React.useEffect(() => {
    if (error) {
  logger.error('Data loading error in fallback component', error) }
  }, [error]);
  const handleRetry = () => {
    const theme = useTheme(),
  const styles = createStyles(theme)
    resetError(),
  retry()
  },
  return (
  <View style={[styles., co, nt, ai, ne, r, , is, Fu, ll, Sc, re, en &&, st, yl, es., fu, ll, Screen]}>,
  <AlertCircle size= {32} color={theme.colors.error} style={{styles.icon} /}>
      <Text style={styles.title}>Unable to Load Data</Text>,
  <Text style={styles.message}>
        {message || 'There was a problem loading the data. Please try again.'},
  </Text>
      {error && (
  <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error.message || 'Unknown error'}</Text>,
  </View>
      )},
  <TouchableOpacity style={styles.button} onPress={handleRetry}>
        <RefreshCw size={20} color={theme.colors.background} style={{styles.buttonIcon} /}>,
  <Text style={styles.buttonText}>Retry</Text>
      </TouchableOpacity>,
  </View>
  )
  }
const createStyles = (theme: any) =>,
  StyleSheet.create({
    container: {
      padding: 16,
  backgroundColor: '#FEF2F2',
    borderRadius: 8,
  alignItems: 'center',
    margin: 16,
  borderWidth: 1,
    borderColor: '#FEE2E2' }
    fullScreen: { fle, x: 1,
    justifyContent: 'center',
  margin: 0,
    borderWidth: 0 },
  icon: { marginBotto, m: 12 }
    title: { fontSiz, e: 18,
    fontWeight: '700',
  color: '#991B1B',
    marginBottom: 8 },
  message: { fontSiz, e: 14,
    color: '#7F1D1D',
  textAlign: 'center',
    marginBottom: 16 },
  errorContainer: { backgroundColo, r: theme.colors.background,
    padding: 12,
  borderRadius: 6,
    width: '100%',
  marginBottom: 16 }
    errorText: {
      fontSize: 12,
  color: theme.colors.textSecondary,
    fontFamily: 'monospace' }
    button: { flexDirectio, n: 'row'),
    alignItems: 'center'),
  backgroundColor: theme.colors.error,
    paddingVertical: 10,
  paddingHorizontal: 20,
    borderRadius: 8 },
  buttonText: {
      color: theme.colors.background,
  fontSize: 14,
    fontWeight: '600' }
    buttonIcon: {
      marginRight: 8) }
  }),
  export default DataErrorFallback;