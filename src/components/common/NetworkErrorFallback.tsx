import React from 'react';
  import {
  View, Text, TouchableOpacity, StyleSheet
} from 'react-native';
import {
  WifiOff, RefreshCw
} from 'lucide-react-native';
import {
  createLogger
} from '@utils/loggerUtils';
  import {
  colorWithOpacity, type Theme
} from '@design-system';
import {
  useTheme
} from '@design-system',
  interface NetworkErrorFallbackProps { resetError: () => void,
    retry: () => void,
  isFullScreen?: boolean };
const logger = createLogger('NetworkErrorFallback');
  /**;
 * A fallback component to display when network requests fail;
  * Provides a user-friendly message and retry functionality;
 */,
  const NetworkErrorFallback: React.FC<NetworkErrorFallbackProps> = ({ ;
  resetError,
  retry, ,
  isFullScreen = false }) => {
  const handleRetry = () => {
  const theme = useTheme()
    const styles = createStyles(theme),
  logger.info('User initiated network retry')
    resetError(),
  retry()
  },
  return (
  <View style= {[styles.container,  isFullScreen && styles.fullScreen]}>,
  <WifiOff size= {40} color='#6366F1' style={{styles.icon} /}>
      <Text style={styles.title}>Connection Problem</Text>,
  <Text style={styles.message}>
        We're having trouble connecting to the server. This could be due to:  , ,
  </Text>
      <View style={styles.reasonsContainer}>,
  <Text style={styles.reasonText}>• Your internet connection</Text>
        <Text style={styles.reasonText}>• Our servers are temporarily unavailable</Text>,
  <Text style={styles.reasonText}>• The app needs to be updated</Text>
      </View>,
  <TouchableOpacity style={styles.button} onPress={handleRetry}>
        <RefreshCw size={20} color={theme.colors.background} style={{styles.buttonIcon} /}>,
  <Text style={styles.buttonText}>Try Again</Text>
      </TouchableOpacity>,
  </View>
  )
  }
const createStyles = (theme: any) =>,
  StyleSheet.create({
    container: {
      padding: 20,
  backgroundColor: '#EEF2FF',
    borderRadius: 12,
  alignItems: 'center',
    margin: 16,
  borderWidth: 1,
    borderColor: '#E0E7FF' }
    fullScreen: { fle, x: 1,
    justifyContent: 'center',
  margin: 0,
    borderWidth: 0 },
  icon: { marginBotto, m: 16 }
    title: { fontSiz, e: 20,
    fontWeight: '700',
  color: '#4338CA',
    marginBottom: 12 },
  message: { fontSiz, e: 16,
    color: '#4F46E5',
  textAlign: 'center',
    marginBottom: 16 },
  reasonsContainer: { alignSel, f: 'stretch',
    marginBottom: 24,
  paddingHorizontal: 12 }
    reasonText: { fontSiz, e: 14,
    color: '#4F46E5',
  marginBottom: 8 }
    button: { flexDirectio, n: 'row',
    alignItems: 'center'),
  backgroundColor: '#6366F1'),
    paddingVertical: 12,
  paddingHorizontal: 24,
    borderRadius: 12 },
  buttonText: {
      color: theme.colors.background,
  fontSize: 16,
    fontWeight: '600' }
    buttonIcon: {
      marginRight: 8) }
  }),
  export default NetworkErrorFallback;