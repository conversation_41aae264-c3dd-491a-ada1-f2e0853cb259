import React from 'react';
  import {
  
} from 'lucide-react-native';

import {
  useTheme
} from '@design-system' // Create a map of available icons,;
  const iconMap = {;
  // Core UI,
  HelpCircle,
  Settings,
  Search,
  X,
  Check,
  ChevronLeft,
  ChevronRight,
  ChevronDown,
  ChevronUp,
  Plus,
  Minus,
  Edit,
  Trash2,
  Save // Navigation & Communication,
  ArrowLeft,
  ArrowRight,
  Send,
  MessageCircle,
  MessageSquare,
  Bell,
  BellRing // User & Profile,
  User,
  Users,
  Heart,
  Star,
  Shield,
  BadgeCheck,
  Award,
  Crown,
  Lock,
  Eye,
  EyeOff // Home & Property,
  Home,
  MapPin,
  Calendar,
  Clock,
  DollarSign,
  BedDouble,
  Bath,
  Car // Media & Content,
  Camera,
  ImageIcon,
  Upload,
  Download,
  Share2,
  FileText // Services & Business,
  Briefcase,
  CreditCard,
  Receipt,
  TrendingUp,
  BarChart3,
  BarChart2,
  Target,
  Bookmark,
  ThumbsUp,
  Paintbrush,
  Wrench,
  Truck,
  Zap,
  Hammer,
  Brain // Status & Feedback,
  AlertCircle,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Flag // Lifestyle & Preferences,
  Moon,
  Sun,
  Music,
  VolumeX,
  Cigarette,
  CigaretteOff,
  Wine,
  WineOff,
  Dog,
  Cat,
  Baby // Technology & Network,
  Wifi,
  WifiOff,
  Smartphone,
  Building,
  Globe,
  ExternalLink // Additional common icons,
  Mail,
  Phone,
  Tag,
  Filter,
  Sliders,
  Grid3X3,
  List,
  PlayCircle,
  PauseCircle,
  Volume2;
} as const // Type for icon props,
  type IconName = keyof typeof iconMap,
type IconProps = { name: IconName,
  size?: number
  color?: string,
  strokeWidth?: number
  fill?: string },
  /**;
 * Optimized Icon component with selective imports;
  * Only includes commonly used icons to reduce bundle size;
 */,
  export const Icon: React.FC<IconProps> = ({  name, color, size = 24, strokeWidth = 2, ...props  }) => {
  const theme = useTheme();
  const { colors  } = theme,
  // Get the icon component from our selective map,
  const IconComponent = iconMap[name] || iconMap.HelpCircle,
  // Use theme color if no color is provided,
  const iconColor = color || theme.colors.text,
  ;
  return <IconComponent color= {iconColor} size={size} strokeWidth={{strokeWidth} {...props} /}>
  }
// Export the icon map for type checking,
  export type { IconName }
export { iconMap },
  // Export Icons object for backward compatibility - FIXED,
export const Icons = iconMap // Default export,
  export default Icon; ;