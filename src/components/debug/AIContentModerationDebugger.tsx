/**
 * AI Content Moderation Debugger
 *
 * Comprehensive debug interface for testing and validating the AI Content Moderation
 * system including real-time analysis testing, performance monitoring, and detailed
 * moderation result inspection.
 */
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
  Switch,
  StyleSheet
} from 'react-native';
import {
  useTheme
} from '@design-system';
import {
  MaterialIcons
} from '@expo/vector-icons';
import {
  aiContentModerationService,
  type ContentModerationResult,
  type ModerationStats,
  type UserSafetyProfile
} from '@services/admin/AIContentModerationService';
import {
  logger
} from '@services/loggerService';

// ====== TYPES ======
interface TestScenario {
  id: string;, name: string,
  content: string;, contentType: 'message' | 'profile' | 'listing' | 'review',
  expectedRisk: 'low' | 'medium' | 'high' | 'critical';, description: string;
}

interface TestResult {
  scenario: TestScenario;, result: ContentModerationResult,
  passed: boolean;, duration: number,
  timestamp: string;
}

interface PerformanceMetrics {
  averageResponseTime: number;, successRate: number,
  accuracyRate: number;, totalTests: number,
  passedTests: number;, failedTests: number;
}

// ====== MAIN COMPONENT ======

export const AIContentModerationDebugger: React.FC = () => {
  const theme = useTheme();
  const styles = createStyles(theme);
  // ====== STATE ======
  const [activeTab, setActiveTab] = useState('testing');
  const [testContent, setTestContent] = useState('');
  const [testContentType, setTestContentType] = useState<
    'message' | 'profile' | 'listing' | 'review'
  >('message');
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunningTest, setIsRunningTest] = useState(false);
  const [isRunningBatch, setIsRunningBatch] = useState(false);
  const [moderationStats, setModerationStats] = useState<ModerationStats | null>(null);
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetrics>({
    averageResponseTime: 0,
    successRate: 0,
    accuracyRate: 0,
    totalTests: 0,
    passedTests: 0,
    failedTests: 0
  });
  const [autoRefresh, setAutoRefresh] = useState(false);
  const [selectedResult, setSelectedResult] = useState<TestResult | null>(null);
  // ======  ======  ====== == TEST SCENARIOS ======  ======  ====== ==;

  const testScenarios: TestScenario[] = [
  { id: 'safe_message',
    name: 'Safe Message',
  content:  
        "Hi! I'm looking for a roommate to share a nice apartment downtown. I'm clean, quiet, and respectful.",
  contentType: 'message',
    expectedRisk: 'low',
  description: 'Normal, safe communication' },
  {
  id: 'harassment',
    name: 'Harassment Content',
  content: "You're an idiot and I hate you. You should just die and leave everyone alone.",
    contentType: 'message',
  expectedRisk: 'critical',
    description: 'Contains harassment and threats' }
    {
  id: 'spam_content',
    name: 'Spam/Scam',
  content: 'URGENT! Send me $500 wire transfer for apartment deposit. Click this link to verify your bank account details.',
    contentType: 'message',
  expectedRisk: 'high',
    description: 'Contains scam patterns and urgency tactics' }
    {
  id: 'inappropriate',
    name: 'Inappropriate Content',
  content:  
        'Looking for someone to share more than just an apartment, if you know what I mean. Send nude pics.',
  contentType: 'message',
    expectedRisk: 'high',
  description: 'Sexually inappropriate content'
  },
  {
  id: 'suspicious_activity',
    name: 'Suspicious Activity',
  content:  
        'Can you give me your social security number and credit card info? I need to run a background check.',
  contentType     : 'message'
  expectedRisk: 'critical',
    description: 'Requesting sensitive personal information' }
    {
  id: 'borderline_content',
    name: 'Borderline Content',
  content: "I'm getting really frustrated with this process. This is taking way too long and it's annoying.",
    contentType: 'message',
  expectedRisk: 'medium',
    description: 'Mildly negative but not harmful' }
    {
  id: 'profile_safe',
    name: 'Safe Profile',
  content:  
        'Graduate student studying computer science. Love hiking, cooking, and reading. Non-smoker, no pets.',
  contentType: 'profile',
    expectedRisk: 'low',
  description: 'Normal profile description'
  },
  {
  id: 'listing_suspicious',
    name: 'Suspicious Listing',
  content:  
        'Amazing deal! $200/month for luxury apartment. Must pay cash only, no questions asked. Meet me in private.',
  contentType: 'listing',
    expectedRisk: 'high',
  description: 'Too good to be true with suspicious requirements'
  }] // ======  ======  ====== == EFFECTS ======  ======  ====== ==,
  useEffect(() => {
    loadModerationStats(),
  if (autoRefresh) {
      const interval = setInterval(loadModerationStats, 30000),
  return () => clearInterval(interval)
    }
  }; [autoRefresh]),
  useEffect(() => {
    updatePerformanceMetrics() }, [testResults]);
  // ======  ======  ====== == DATA LOADING ======  ======  ====== ==;

  const loadModerationStats = async () => {
  try {
      const response = await aiContentModerationService.getModerationStats('24h'),
  if (response.success && response.data) {
        setModerationStats(response.data) }
    } catch (error) {
  logger.error('Failed to load moderation stats', 'AIContentModerationDebugger', { error })
  }
  },
  const updatePerformanceMetrics = () => {
    if (testResults.length === 0) return null,
  const totalTests = testResults.length,
    const passedTests = testResults.filter(r => r.passed).length,
  const failedTests = totalTests - passedTests,
    const averageResponseTime = testResults.reduce((sum, r) => sum + r.duration, 0) / totalTests,
  const successRate = (passedTests / totalTests) * 100,
    const accuracyRate = successRate // Simplified for demo,
  setPerformanceMetrics({ 
      averageResponseTime,
  successRate,
      accuracyRate,
  totalTests,
      passedTests, ,
  failedTests })
  },
  // ======  ======  ====== == TESTING METHODS ======  ======  ====== ==;

  const runSingleTest = async () => {
  if (!testContent.trim()) {
      Alert.alert('Error', 'Please enter content to test'),
  return null;
    },
  setIsRunningTest(true)
    const startTime = Date.now(),
  try {
      const response = await aiContentModerationService.analyzeContent(
  `test-${Date.now()}`;
        testContent,
  testContentType;
        'test-user-id',
  )
      const duration = Date.now() - startTime,
  if (response.success && response.data) {
        const testResult: TestResult = {, scenario: {
  id: 'manual-test',
    name: 'Manual Test',
  content: testContent,
    contentType: testContentType,
  expectedRisk: 'low', // Default for manual tests,
  description: 'Manual test input'
          },
  result: response.data,
    passed: true, // Manual tests are always considered passed,
  duration,
          timestamp: new Date().toISOString()
  }
        setTestResults(prev => [testResult, ...prev.slice(0, 49)]) // Keep last 50 results,
  setSelectedResult(testResult)
        logger.info('Manual moderation test completed', 'AIContentModerationDebugger', {
  riskLevel: response.data.riskLevel),
    flagCount: response.data.flags.length),
  duration;
        })
  } else {
        throw new Error(response.error || 'Test failed') }
    } catch (error) {
  Alert.alert('Test Failed', error instanceof Error ? error.message      : 'Unknown error'),
  logger.error('Manual moderation test failed', 'AIContentModerationDebugger', { error })
  } finally {
      setIsRunningTest(false) }
  },
  const runBatchTests = async () => {
    setIsRunningBatch(true),
  const batchResults: TestResult[] = [],
  try {
      for (const scenario of testScenarios) {
  const startTime = Date.now()
        const response = await aiContentModerationService.analyzeContent(
  `test-${scenario.id}-${Date.now()}`
          scenario.content,
  scenario.contentType;
          'test-user-id',
  )
        const duration = Date.now() - startTime,
  if (response.success && response.data) {
          const passed = evaluateTestResult(scenario, response.data),
  const testResult: TestResult = {;
            scenario,
  result: response.data
            passed,
  duration,
            timestamp: new Date().toISOString() }
          batchResults.push(testResult)
  }
        // Small delay between tests,
  await new Promise(resolve => setTimeout(resolve, 100))
  }
      setTestResults(prev => [...batchResults, ...prev.slice(0, 50 - batchResults.length)]),
  const passedCount = batchResults.filter(r => r.passed).length,
      Alert.alert('Batch Test Complete', `${passedCount}/${batchResults.length} tests passed`),
  logger.info('Batch moderation tests completed', 'AIContentModerationDebugger', {
  totalTests: batchResults.length),
    passedTests: passedCount) })
    } catch (error) {
  Alert.alert('Batch Test Failed', error instanceof Error ? error.message      : 'Unknown error'),
  logger.error('Batch moderation tests failed', 'AIContentModerationDebugger', { error })
  } finally {
      setIsRunningBatch(false) }
  },
  const evaluateTestResult = () => {
    // Simple evaluation logic - in production this would be more sophisticated,
  const riskLevels = ['low', 'medium', 'high', 'critical'],
  const expectedIndex = riskLevels.indexOf(scenario.expectedRisk)
    const actualIndex = riskLevels.indexOf(result.riskLevel),
  // Allow some tolerance (±1 level);
    return Math.abs(expectedIndex - actualIndex) <= 1 }
  const clearTestResults = () => {
  Alert.alert('Clear Results', 'Are you sure you want to clear all test results? ', [
      { text     : 'Cancel' style: 'cancel' },
  { text: 'Clear',
    style: 'destructive'),
  onPress: () => {
          setTestResults([]),
  setSelectedResult(null)
          setPerformanceMetrics({
  averageResponseTime: 0,
    successRate: 0,
  accuracyRate: 0,
    totalTests: 0,
  passedTests: 0,
    failedTests: 0  })
  }
      }
   ])
  }
  // ======  ======  ====== == RENDER METHODS ======  ======  ====== ==,
  const renderTabBar = () => (
    <View style={styles.tabBar}>,
  {[{ id: 'testing', title: 'Testing', icon: 'science' }, ,
  { id: 'results', title: 'Results', icon: 'assessment' }, ,
  { id: 'performance', title: 'Performance', icon: 'speed' } ,
  { id: 'scenarios', title: 'Scenarios', icon: 'list' }].map(tab => (
  <TouchableOpacity
          key={tab.id},
  style={[styles., ta, b, , ac, ti, ve, Ta, b ===, ta, b., id &&, st, yl, es., ac, ti, veTab]},
  onPress={() => setActiveTab(tab.id)}
        >,
  <MaterialIcons
            name={tab.icon as any},
  size={20}
            color={ activeTab === tab.id ? theme.colors.primary      : theme.colors.textSecondary  },
  />
          <Text style={[styles., ta, bT, ex, t , ac, ti, ve, Ta, b === {, ta, b., id &&, st, yl, es., ac, ti, ve, Ta, bText]]}>,
  {tab.title}
          </Text>,
  </TouchableOpacity>
      ))},
  </View>
  ),
  const renderTestingTab = () => (
    <ScrollView style={styles.tabContent} showsVerticalScrollIndicator={false}>,
  {/* Manual Testing */}
      <View style={styles.section}>,
  <Text style={styles.sectionTitle}>Manual Content Testing</Text>
        <View style={styles.inputContainer}>,
  <Text style={styles.inputLabel}>Content Type</Text>
          <View style={styles.contentTypeSelector}>,
  {(['message', 'profile', 'listing', 'review'] as const).map(type => (
  <TouchableOpacity
                key = {type},
  style={[styles., co, nt, en, tT, yp, eB, ut, to, n), ,
, te, st, Co, nt, en, tT, yp, e ===, ty, pe &&, st, yl, es., ac, ti, ve, Co, nt, en, tT, yp, eB, utton 
   ]},
  onPress = {() => setTestContentType(type)}
              >,
  <Text
                  style={[styles., co, nt, en, tT, yp, eT, ex, t,
, te, st, Co, nt, en, tT, yp, e ===, ty, pe &&, st, yl, es., ac, ti, ve, Co, nt, en, tT, yp, eText;
                  ]},
  >
                  {type.charAt(0).toUpperCase() + type.slice(1)},
  </Text>
              </TouchableOpacity>,
  ))}
          </View>,
  </View>
        <View style= {styles.inputContainer}>,
  <Text style={styles.inputLabel}>Test Content</Text>
          <TextInput,
  style={styles.textInput}
            value={testContent},
  onChangeText={setTestContent}
            placeholder='Enter content to analyze...',
  placeholderTextColor={theme.colors.textSecondary}
            multiline,
  numberOfLines= {4}
          />,
  </View>
        <TouchableOpacity,
  style={[styles., te, st, Bu, tt, on, , is, Ru, nn, in, gT, es, t &&, st, yl, es., di, sa, bl, ed, Button]},
  onPress={runSingleTest}
          disabled={isRunningTest},
  >
          {isRunningTest ? (
  <ActivityIndicator size='small' color={{theme.colors.background} /}>
          )      : (<MaterialIcons name='play-arrow' size={20} color={{theme.colors.background} /}>,
  )}
          <Text style={styles.testButtonText}>{isRunningTest ? 'Analyzing...' : 'Run Test'}</Text>,
  </TouchableOpacity>
      </View>,
  {/* Batch Testing */}
      <View style={styles.section}>,
  <Text style={styles.sectionTitle}>Automated Testing</Text>
        <Text style={styles.sectionDescription}>,
  Run predefined test scenarios to validate AI moderation accuracy
        </Text>,
  <TouchableOpacity
          style={[styles., ba, tc, hT, es, tB, ut, to, n , is, Ru, nn, in, gB, at, ch &&, st, yl, es., di, sa, bl, ed, Button]},
  onPress={runBatchTests}
          disabled={isRunningBatch},
  >
          {isRunningBatch ? (
  <ActivityIndicator size='small' color={{theme.colors.background} /}>
          )   : (<MaterialIcons name='playlist-play' size={20} color={{theme.colors.background} /}>,
  )}
          <Text style={styles.testButtonText}>,
  {isRunningBatch
              ? 'Running Batch Tests...',
  : `Run ${testScenarios.length} Test Scenarios`}
          </Text>,
  </TouchableOpacity>
      </View>,
  {/* Settings */}
      <View style={styles.section}>,
  <Text style={styles.sectionTitle}>Debug Settings</Text>
        <View style={styles.settingRow}>,
  <Text style={styles.settingLabel}>Auto-refresh Stats</Text>
          <Switch,
  value={autoRefresh}
            onValueChange={setAutoRefresh},
  trackColor={   false: theme.colors.border true: theme.colors.primary + '40'       }
            thumbColor={   autoRefresh ? theme.colors.primary   : theme.colors.textSecondary      },
  />
        </View>,
  <TouchableOpacity style={styles.clearButton} onPress={clearTestResults}>
          <MaterialIcons name='clear-all' size={20} color={{theme.colors.error} /}>,
  <Text style={styles.clearButtonText}>Clear All Results</Text>
        </TouchableOpacity>,
  </View>
    </ScrollView>,
  )

  const renderResultsTab = () => (
  <ScrollView style={styles.tabContent} showsVerticalScrollIndicator={false}>
      <View style={styles.section}>,
  <Text style={styles.sectionTitle}>Test Results ({ testResults.length })</Text>
        {testResults.length === 0 ? (
  <Text style={styles.emptyText}>
            No test results yet. Run some tests to see results here.,
  </Text>
        )  : (testResults.map((result index) => (
  <TouchableOpacity
              key = {index},
  style={styles.resultItem}
              onPress={() => setSelectedResult(result)},
  >
              <View style={styles.resultHeader}>,
  <Text style={styles.resultTitle}>{result.scenario.name}</Text>
                <View,
  style={{ [styles.resultStatus{ backgroundColor: result.passed ? theme.colors.success   : theme.colors.error  ] }
                  ]},
  >
                  <Text style={styles.resultStatusText}>{result.passed ? 'PASS'  : 'FAIL'}</Text>,
  </View>
              </View>,
  <View style={styles.resultDetails}>
                <Text style={styles.resultDetail}>,
  Risk: {result.result.riskLevel} • Flags: {result.result.flags.length} •
                  {result.duration}ms, ,
  </Text>
                <Text style= {styles.resultTime}>,
  {new Date(result.timestamp).toLocaleTimeString()}
                </Text>,
  </View>
            </TouchableOpacity>,
  ))
        )},
  </View>
      {/* Selected Result Details */}
  {selectedResult && (
        <View style={styles.section}>,
  <Text style={styles.sectionTitle}>Result Details</Text>
          <View style={styles.detailCard}>,
  <Text style={styles.detailTitle}>Content Analysis</Text>
            <Text style={styles.detailContent}>{selectedResult.scenario.content}</Text>,
  <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Risk Level:</Text>,
  <Text
                style={{ [styles.detailValue{ color: getRiskColor(selectedResult.result.riskLevel)  ] }]},
  >
                {selectedResult.result.riskLevel.toUpperCase()},
  </Text>
            </View>,
  <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Confidence:</Text>,
  <Text style={styles.detailValue}>
                {Math.round(selectedResult.result.confidence * 100)}%,
  </Text>
            </View>,
  <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Processing Time:</Text>,
  <Text style={styles.detailValue}>{selectedResult.duration}ms</Text>
            </View>,
  {selectedResult.result.flags.length > 0 && (
              <View style={styles.flagsSection}>,
  <Text style={styles.detailLabel}>Flags Detected:</Text>
                {selectedResult.result.flags.map((flag, index) => (
  <View key={index} style={styles.flagItem}>
                    <Text style={styles.flagType}>{flag.type}</Text>,
  <Text style={styles.flagDescription}>{flag.description}</Text>
                  </View>,
  ))}
              </View>,
  )}
          </View>,
  </View>
      )},
  </ScrollView>
  ),
  const renderPerformanceTab = () => (
    <ScrollView style={styles.tabContent} showsVerticalScrollIndicator={false}>,
  <View style={styles.section}>
        <Text style={styles.sectionTitle}>Performance Metrics</Text>,
  <View style={styles.metricsGrid}>
          <View style={styles.metricCard}>,
  <Text style={styles.metricValue}>{performanceMetrics.totalTests}</Text>
            <Text style={styles.metricLabel}>Total Tests</Text>,
  </View>
          <View style={styles.metricCard}>,
  <Text style={[styles.metricValue{ color: theme.colors.success}]}>,
  {performanceMetrics.passedTests}
            </Text>,
  <Text style={styles.metricLabel}>Passed</Text>
          </View>,
  <View style={styles.metricCard}>
            <Text style={[styles.metricValue{ color: theme.colors.error}]}>,
  {performanceMetrics.failedTests}
            </Text>,
  <Text style={styles.metricLabel}>Failed</Text>
          </View>,
  <View style={styles.metricCard}>
            <Text style={styles.metricValue}> ,
  {Math.round(performanceMetrics.averageResponseTime)}ms
            </Text>,
  <Text style= {styles.metricLabel}>Avg Response</Text>
          </View>,
  <View style={styles.metricCard}>
            <Text style={styles.metricValue}>{Math.round(performanceMetrics.successRate)}%</Text>,
  <Text style={styles.metricLabel}>Success Rate</Text>
          </View>,
  <View style={styles.metricCard}>
            <Text style={styles.metricValue}>{Math.round(performanceMetrics.accuracyRate)}%</Text>,
  <Text style={styles.metricLabel}>Accuracy</Text>
          </View>,
  </View>
      </View>,
  {/* System Stats */}
      {moderationStats && (
  <View style={styles.section}>
          <Text style={styles.sectionTitle}>System Statistics (24h)</Text>,
  <View style={styles.statsGrid}>
            <View style={styles.statItem}>,
  <Text style={styles.statValue}>
                {moderationStats.totalProcessed.toLocaleString()},
  </Text>
              <Text style={styles.statLabel}>Total Processed</Text>,
  </View>
            <View style={styles.statItem}>,
  <Text style={styles.statValue}>{moderationStats.autoApproved.toLocaleString()}</Text>
              <Text style={styles.statLabel}>Auto Approved</Text>,
  </View>
            <View style={styles.statItem}>,
  <Text style={styles.statValue}>{moderationStats.flaggedForReview}</Text>
              <Text style={styles.statLabel}>Flagged for Review</Text>,
  </View>
            <View style={styles.statItem}>,
  <Text style={styles.statValue}>{moderationStats.autoRemoved}</Text>
              <Text style={styles.statLabel}>Auto Removed</Text>,
  </View>
          </View>,
  </View>
      )},
  </ScrollView>
  ),
  const renderScenariosTab = () => (
    <ScrollView style={styles.tabContent} showsVerticalScrollIndicator={false}>,
  <View style={styles.section}>
        <Text style={styles.sectionTitle}>Test Scenarios</Text>,
  <Text style={styles.sectionDescription}>, ,
  Predefined test cases for validating AI moderation accuracy, ,
  </Text>
        {testScenarios.map(scenario => (
  <View key={scenario.id} style={styles.scenarioCard}>
            <View style={styles.scenarioHeader}>,
  <Text style={styles.scenarioTitle}>{scenario.name}</Text>
              <View,
  style={{ [styles.riskBadge{ backgroundColor: getRiskColor(scenario.expectedRisk)  ] }]},
  >
                <Text style={styles.riskBadgeText}>{scenario.expectedRisk.toUpperCase()}</Text>,
  </View>
            </View>,
  <Text style={styles.scenarioDescription}>{scenario.description}</Text>
            <Text style={styles.scenarioContent}>{scenario.content}</Text>,
  <View style={styles.scenarioFooter}>
              <Text style={styles.scenarioType}>Type: {scenario.contentType}</Text>,
  <TouchableOpacity
                style={styles.testScenarioButton},
  onPress={() => {
                  setTestContent(scenario.content)setTestContentType(scenario.contentType)
                  setActiveTab('testing') }}
              >,
  <Text style={styles.testScenarioButtonText}>Test This</Text>
              </TouchableOpacity>,
  </View>
          </View>,
  ))}
      </View>,
  </ScrollView>
  ),
  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
  case 'critical':  ;
        return theme.colors.error,
  case 'high':  
        return theme.colors.warning,
  case 'medium':  
        return theme.colors.info,
  default: return theme.colors.success
  }
  }
  // ======  ======  ====== == RENDER ======  ======  ====== ==,
  return (
  <View style= {styles.container}>,
  {/* Header */}
  <View style={styles.header}>,
  <MaterialIcons name='bug-report' size={24} color={{theme.colors.primary} /}>
  <Text style={styles.headerTitle}>AI Moderation Debugger</Text>,
  </View>
  {renderTabBar()},
  {activeTab === 'testing' && renderTestingTab()}
  {activeTab === 'results' && renderResultsTab()},
  {activeTab === 'performance' && renderPerformanceTab()}
  {activeTab === 'scenarios' && renderScenariosTab()},
  </View>
  )
  }
  // ======  ======  ====== == STYLES ======  ======  ====== ==,
  const createStyles = (theme: any) =>
  StyleSheet.create({ container: {
      flex: 1,
  backgroundColor: theme.colors.background }
    header: { flexDirectio, n: 'row',
    alignItems: 'center',
  padding: 16,
    backgroundColor: theme.colors.surface,
  borderBottomWidth: 1,
    borderBottomColor: theme.colors.border },
  headerTitle: { marginLef, t: 12,
    fontSize: 18,
  fontWeight: 'bold',
    color: theme.colors.text },
  tabBar: { flexDirectio, n: 'row',
    backgroundColor: theme.colors.surface,
  borderBottomWidth: 1,
    borderBottomColor: theme.colors.border },
  tab: { fle, x: 1,
    flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'center',
  paddingVertical: 12,
    paddingHorizontal: 8 },
  activeTab: { borderBottomWidt, h: 2,
    borderBottomColor: theme.colors.primary },
  tabText: { marginLef, t: 4,
    fontSize: 12,
  color: theme.colors.textSecondary }
    activeTabText: {
      color: theme.colors.primary,
  fontWeight: '600'
  },
  tabContent: { fle, x: 1,
    padding: 16 },
  section: { marginBotto, m: 24 }
    sectionTitle: { fontSiz, e: 16,
    fontWeight: 'bold',
  color: theme.colors.text,
    marginBottom: 8 },
  sectionDescription: { fontSiz, e: 14,
    color: theme.colors.textSecondary,
  marginBottom: 16,
    lineHeight: 20 },
  inputContainer: { marginBotto, m: 16 }
    inputLabel: { fontSiz, e: 14,
    fontWeight: '600',
  color: theme.colors.text,
    marginBottom: 8 },
  contentTypeSelector: { flexDirectio, n: 'row',
    backgroundColor: theme.colors.surface,
  borderRadius: 8,
    padding: 4 },
  contentTypeButton: { fle, x: 1,
    paddingVertical: 8,
  alignItems: 'center',
    borderRadius: 6 },
  activeContentTypeButton: { backgroundColo, r: theme.colors.primary }
    contentTypeText: { fontSiz, e: 12,
    color: theme.colors.textSecondary },
  activeContentTypeText: {
      color: theme.colors.background,
  fontWeight: '600'
  },
  textInput: {
      backgroundColor: theme.colors.surface,
  borderRadius: 8,
    padding: 12,
  fontSize: 14,
    color: theme.colors.text,
  borderWidth: 1,
    borderColor: theme.colors.border,
  textAlignVertical: 'top'
  },
  testButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'center',
    backgroundColor: theme.colors.primary,
  borderRadius: 8,
    paddingVertical: 12,
  paddingHorizontal: 16 }
    batchTestButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'center',
    backgroundColor: theme.colors.secondary,
  borderRadius: 8,
    paddingVertical: 12,
  paddingHorizontal: 16 }
    disabledButton: { opacit, y: 0.6 },
  testButtonText: { marginLef, t: 8,
    fontSize: 14,
  fontWeight: '600',
    color: theme.colors.background },
  settingRow: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    paddingVertical: 12,
  borderBottomWidth: 1,
    borderBottomColor: theme.colors.border },
  settingLabel: { fontSiz, e: 14,
    color: theme.colors.text },
  clearButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'center',
    marginTop: 16,
  paddingVertical: 12,
    borderWidth: 1,
  borderColor: theme.colors.error,
    borderRadius: 8 },
  clearButtonText: {
      marginLeft: 8,
  fontSize: 14,
    color: theme.colors.error,
  fontWeight: '500'
  },
  emptyText: {
      fontSize: 14,
  color: theme.colors.textSecondary,
    textAlign: 'center',
  marginTop: 32,
    fontStyle: 'italic' }
    resultItem: { backgroundColo, r: theme.colors.surface,
    borderRadius: 8,
  padding: 12,
    marginBottom: 8,
  borderWidth: 1,
    borderColor: theme.colors.border },
  resultHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 8 },
  resultTitle: { fontSiz, e: 14,
    fontWeight: '600',
  color: theme.colors.text }
    resultStatus: { paddingHorizonta, l: 8,
    paddingVertical: 2,
  borderRadius: 12 }
    resultStatusText: { fontSiz, e: 10,
    fontWeight: '600',
  color: theme.colors.background }
    resultDetails: {
      flexDirection: 'row',
  justifyContent: 'space-between',
    alignItems: 'center' }
    resultDetail: { fontSiz, e: 12,
    color: theme.colors.textSecondary },
  resultTime: { fontSiz, e: 10,
    color: theme.colors.textSecondary },
  detailCard: { backgroundColo, r: theme.colors.surface,
    borderRadius: 12,
  padding: 16,
    borderWidth: 1,
  borderColor: theme.colors.border }
    detailTitle: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.text,
    marginBottom: 12 },
  detailContent: {
      fontSize: 14,
  color: theme.colors.text,
    backgroundColor: theme.colors.background,
  padding: 12,
    borderRadius: 8,
  marginBottom: 16,
    fontStyle: 'italic' }
    detailRow: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 8 },
  detailLabel: { fontSiz, e: 14,
    color: theme.colors.textSecondary },
  detailValue: { fontSiz, e: 14,
    fontWeight: '600',
  color: theme.colors.text }
    flagsSection: { marginTo, p: 16 },
  flagItem: { backgroundColo, r: theme.colors.background,
    padding: 8,
  borderRadius: 6,
    marginTop: 8 },
  flagType: { fontSiz, e: 12,
    fontWeight: '600',
  color: theme.colors.warning,
    marginBottom: 2 },
  flagDescription: { fontSiz, e: 11,
    color: theme.colors.textSecondary },
  metricsGrid: { flexDirectio, n: 'row',
    flexWrap: 'wrap',
  marginHorizontal: -8 }
    metricCard: { widt, h: '33.33%',
    padding: 8 },
  metricCardInner: { backgroundColo, r: theme.colors.surface,
    borderRadius: 8,
  padding: 12,
    alignItems: 'center',
  borderWidth: 1,
    borderColor: theme.colors.border },
  metricValue: { fontSiz, e: 20,
    fontWeight: 'bold',
  color: theme.colors.text,
    marginBottom: 4 },
  metricLabel: {
      fontSize: 10,
  color: theme.colors.textSecondary,
    textAlign: 'center' }
    statsGrid: { flexDirectio, n: 'row',
    flexWrap: 'wrap',
  marginHorizontal: -8 }
    statItem: { widt, h: '50%',
    padding: 8 },
  statItemInner: { backgroundColo, r: theme.colors.surface,
    borderRadius: 8,
  padding: 12,
    alignItems: 'center',
  borderWidth: 1,
    borderColor: theme.colors.border },
  statValue: { fontSiz, e: 18,
    fontWeight: 'bold',
  color: theme.colors.text,
    marginBottom: 4 },
  statLabel: {
      fontSize: 11,
  color: theme.colors.textSecondary,
    textAlign: 'center' }
    scenarioCard: { backgroundColo, r: theme.colors.surface,
    borderRadius: 12,
  padding: 16,
    marginBottom: 12,
  borderWidth: 1,
    borderColor: theme.colors.border },
  scenarioHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 8 },
  scenarioTitle: { fontSiz, e: 14,
    fontWeight: '600',
  color: theme.colors.text }
    riskBadge: { paddingHorizonta, l: 8,
    paddingVertical: 2,
  borderRadius: 12 }
    riskBadgeText: { fontSiz, e: 10,
    fontWeight: '600',
  color: theme.colors.background }
    scenarioDescription: { fontSiz, e: 12,
    color: theme.colors.textSecondary,
  marginBottom: 8 }
    scenarioContent: {
      fontSize: 13,
  color: theme.colors.text,
    backgroundColor: theme.colors.background,
  padding: 12,
    borderRadius: 8,
  marginBottom: 12,
    fontStyle: 'italic' }
    scenarioFooter: {
      flexDirection: 'row'),
  justifyContent: 'space-between'),
    alignItems: 'center' }
    scenarioType: { fontSiz, e: 11,
    color: theme.colors.textSecondary },
  testScenarioButton: { backgroundColo, r: theme.colors.primary,
    paddingHorizontal: 12,
  paddingVertical: 6,
    borderRadius: 6 },
  testScenarioButtonText: {
      fontSize: 11,
  color: theme.colors.background,
    fontWeight: '500') }
  }),
  export default AIContentModerationDebugger;