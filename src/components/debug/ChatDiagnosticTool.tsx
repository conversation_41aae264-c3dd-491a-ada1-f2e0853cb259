import React, { useState, useEffect } from 'react';
  import {
  View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, ActivityIndicator
} from 'react-native';
import {
  useAuthCompat
} from '@/hooks/useAuthCompat';
  import {
  shouldUseMockFallback, clearMockChatData, resetFailedOperations
} from '@/utils/mockChatRooms';
import {
  logger
} from '@/services/loggerService';
  import {
  supabase
} from '@/utils/supabaseUtils';
import {
  colorWithOpacity, type Theme
} from '@design-system';
import {
  useTheme
} from '@design-system';
  interface ChatDiagnosticToolProps { onClearMockData?: () => void }
/**;
  * A diagnostic tool component that helps debug chat-related issues;
 * and shows information about mock chat room usage,
  */
export default function ChatDiagnosticTool({
  onClearMockData }: ChatDiagnosticToolProps) {
  const theme = useTheme(),
  const styles = createStyles(theme)
  const { authState  } = useAuthCompat(),
  const user = authState.user,
  const [mockChatOps, setMockChatOps] = useState<string[]>([]),
  const [loading, setLoading] = useState(true),
  const [testingPolicy, setTestingPolicy] = useState(false),
  const [policyStatus, setPolicyStatus] = useState<'unknown' | 'fixed' | 'broken'>('unknown'),
  const [deletingPolicy, setDeletingPolicy] = useState(false),
  const [showRpcInstructions, setShowRpcInstructions] = useState(false),
  const [fixResult, setFixResult] = useState<any>(null),
  useEffect(() => {
  async function loadStatus() {
  if (!user?.id) return null,
      try {
  setLoading(true)
        const operations = ['create_chat_room', 'chat_room_participant', 'send_message'], ,
  const failedOps    : string[] = [],
  // Check which operations are using mock fallbacks, ,
  for (const op of operations) {
  const usesMock = await shouldUseMockFallback(user.id, op),
  if (usesMock) {
            failedOps.push(op) }
        },
  setMockChatOps(failedOps)
      } catch (error) {
  console.error('Error checking mock chat status:', error) } finally {
        setLoading(false) }
    },
  loadStatus()
  }, [user?.id]);
  const testRLSPolicy = async () => {
  if (!user?.id) {
  Alert.alert('Error', 'You must be logged in to test the policy fix'),
  return null;
    },
  setTestingPolicy(true)
    setPolicyStatus('unknown'),
  try {
      // Try a simple query that would trigger the infinite recursion if the policy is broken,
  const { error  } = await supabase.from('chat_room_participants')
        .select($1).limit(1),
  if (error && error.message.includes('infinite recursion')) {
        setPolicyStatus('broken'),
  Alert.alert('Policy Still Broken');
          'The infinite recursion issue still exists. Please run the SQL fix script in the Supabase dashboard.'),
  )
      } else {
  setPolicyStatus('fixed')
        // If the policy appears fixed, try to reset the user's failed operations,
  if (user?.id) {
          await resetFailedOperations(user.id),
  // Refresh the UI to show updated status,
          const operations = ['create_chat_room', 'chat_room_participant', 'send_message'],
  const failedOps    : string[] = [],
  for (const op of operations) {
            const usesMock = await shouldUseMockFallback(user.id op),
  if (usesMock) {
              failedOps.push(op) }
          },
  setMockChatOps(failedOps)
        },
  Alert.alert('Policy Fixed')
          'The database policy appears to be fixed. You can now try using real chat rooms again.'),
  )
      }
  } catch (error) {
      console.error('Error testing RLS policy:', error),
  setPolicyStatus('unknown')
      Alert.alert('Test Failed'),
  'Unable to test the RLS policy. Please check your network connection and try again.')
      ) } finally {
      setTestingPolicy(false) }
  },
  const attemptDirectPolicyDeletion = async () => {
  setDeletingPolicy(true),
  try {;
      // Try a direct Postgres command to delete problematic policies // This requires admin rights, so it might not work for all users,
  const { error: dropError1 } = await supabase.rpc('admin_drop_policy', {
  policy_name: 'Users can view room participants'),
    table_name: 'chat_room_participants') })
      const { error: dropError2  } = await supabase.rpc('admin_drop_policy', {
  policy_name: 'Users can view room participants fixed'),
    table_name: 'chat_room_participants') })
      if (dropError1 || dropError2) {
  Alert.alert('Permission Error', ,
  'Unable to directly delete policies. You need to run the SQL script in the Supabase dashboard as an administrator.');
          [{ text: 'OK' }]),
  )
      } else {
  Alert.alert('Policies Deleted'
          'Successfully deleted problematic policies. Now run the SQL script to create the fixed policy.') ,
  [{ text: 'OK' }]),
  )
      }
  } catch (error) {
      console.error('Error deleting policies:', error),
  Alert.alert('Error'
        'Failed to delete policies directly. Use the SQL script in the Supabase dashboard instead.'), ,
  [{ text: 'OK' }]),
  )
    } finally {
  setDeletingPolicy(false)
    }
  }
  const handleClearMockData = async () => {
  Alert.alert('Clear Mock Chat Data');
      'This will delete all mock chat rooms and messages. You will need to recreate any conversations. Continue? ',
  [
        {
  text     : 'Cancel'
          style: 'cancel' }
        {
  text: 'Clear Data'),
    onPress: async () => {
  try {
              await clearMockChatData(),
  if (user?.id) {
                await resetFailedOperations(user.id) }
              logger.info('Cleared all mock chat data', 'ChatDiagnosticTool', {
  userId  : user?.id)
              }),
  if (onClearMockData) {
                onClearMockData() }
              setMockChatOps([]),
  Alert.alert('Success'
                'Mock chat data has been cleared. You can now try using real chat rooms again.'),
  )
            } catch (error) {
  logger.error('Error clearing mock chat data', 'ChatDiagnosticTool', {
  errorMessage: error instanceof Error ? error.message   : String(error)
              }),
  Alert.alert('Error' 'Failed to clear mock chat data. Please try again.')
            }
  }
          style: 'destructive'
  }
      ],
  )
  },
  const rls_policy_fix = `
-- Fix for the infinite recursion in chat_room_participants policy,
  -- Run this in Supabase SQL Editor;
-- 1. First drop both policies to ensure clean state,
  DROP POLICY IF EXISTS "Users can view room participants" ON chat_room_participants,
DROP POLICY IF EXISTS "Users can view room participants fixed" ON chat_room_participants,
  -- 2. Create the corrected version of the policy,
CREATE POLICY "Users can view room participants fixed" ON chat_room_participants,
  FOR SELECT,
TO authenticated,
  USING (
  (user_id = auth.uid()) OR (
  EXISTS (
      SELECT 1, ,
  FROM chat_room_participants my_membership, ,
  WHERE (my_membership.room_id = chat_room_participants.room_id) AND (my_membership.user_id = auth.uid())
    ),
  )
),
  `;

  const alternative_fix = `,
  -- Alternative fix for difficult cases;
-- This creates a policy with a unique name instead of trying to reuse names,
  -- Make sure we don't have duplicate policies,
DROP POLICY IF EXISTS "Users can view room participants" ON chat_room_participants,
  DROP POLICY IF EXISTS "Users can view room participants fixed" ON chat_room_participants,
DROP POLICY IF EXISTS "chat_participants_select_policy_v2" ON chat_room_participants,
  -- Create an entirely new policy with a unique name,
CREATE POLICY "chat_participants_select_policy_v2" ON chat_room_participants,
  FOR SELECT,
TO authenticated,
  USING (
  (user_id = auth.uid()) OR (
  EXISTS (
      SELECT 1, ,
  FROM chat_room_participants my_membership, ,
  WHERE (my_membership.room_id = chat_room_participants.room_id) AND (my_membership.user_id = auth.uid())
    ),
  )
),
  `;

  const handleShowAlternativeFix = () => {
  Alert.alert('Alternative Fix', ,
  'If the standard fix does not work, try this alternative with a different policy name: '),
  [
        {
  text: 'Close'),
    style: 'cancel' }
        {
  text: 'View SQL'),
    onPress: () => {
  Alert.alert('Alternative SQL Fix', alternative_fix, [{ text: 'OK' }], {
  cancelable: true)
            })
  }
        }
   ],
  )
  },
  const handleShowRLSFix = () => {
  Alert.alert('RLS Policy Fix', ,
  'This SQL script fixes the infinite recursion in the chat_room_participants policy. Run it in the Supabase SQL Editor.');
      [
        {
  text: 'Close'),
    style: 'cancel' }
        {
  text: 'View SQL'),
    onPress: () => {
  Alert.alert('SQL Fix Script', rls_policy_fix, [{ text: 'OK' }], { cancelable: true })
  }
        }
   ],
  )
  },
  const testParticipantsQuery = async () => {
  setLoading(true),
  try {
      logger.info('Testing chat room participants query', 'ChatDiagnosticTool'),
  // First, get a test chat room,
  const { data: rooms, error: roomsError  } = await supabase.from('chat_rooms'),
  .select($1).limit(1)
      if (roomsError || !rooms || rooms.length === 0) {
  logger.error('Failed to find test chat room', 'ChatDiagnosticTool', { error: roomsError }),
  Alert.alert('Test Failed', 'Could not find a test chat room to query.'),
  setLoading(false);
        return null
  }
      const testRoomId = rooms[0].id // Try to get participants with the potentially problematic query,
  const { data: participants, error: participantsError } = await supabase.from('chat_room_participants'),
  .select($1).eq('room_id', testRoomId),
  if (participantsError) {
        logger.error('Test query failed', 'ChatDiagnosticTool', {
  error: participantsError),
    message: participantsError.message) });
        Alert.alert('RLS Policy Error Detected'),
  `The error was: ${participantsError.message || 'Unknown error'}`)
        ),
  // Automatically try to fix if it's the recursive policy error,
        if (
  participantsError.message &&, ,
  participantsError.message.includes('infinite recursion detected in policy')
        ) { setFixResult({
  status: 'error',
    message:  ,
  'Detected infinite recursion in policy. Please run the fix button to resolve this.'  })
  }
  } else {
  logger.info('Test query succeeded', 'ChatDiagnosticTool', {
  participantCount: participants?.length || 0)
        }),
  Alert.alert('Test Successful');
          `Successfully queried ${participants?.length || 0} participants. The RLS policy is working correctly.`),
  )
        setFixResult({
  status     : 'success'
          message: 'RLS policy is working correctly.' })
      }
  } catch (error) {
      logger.error('Test query threw an exception' 'ChatDiagnosticTool', {
  error
      }),
  Alert.alert('Test Failed')
        `An error occurred: ${error instanceof Error ? error.message     : String(error)}`
  )
    } finally {
  setLoading(false)
    }
  }
  const fixPolicyRecursion = async () => {
  setLoading(true)
    try {
  logger.info('Attempting to fix recursive policy' 'ChatDiagnosticTool')
      // The SQL to execute,
  const sql = `;
        -- First, drop the problematic policy if it exists,
  DROP POLICY IF EXISTS "Users can view room participants" ON chat_room_participants;
        ,
  -- Also drop any previous fix attempts,
        DROP POLICY IF EXISTS "Fixed users view room participants" ON chat_room_participants,
  ;
        -- Create the fixed policy,
  CREATE POLICY "Fixed users view room participants" ;
        ON chat_room_participants,
  FOR SELECT,
        USING (
  EXISTS (
            SELECT 1 FROM chat_room_participants my_membership, ,
  WHERE my_membership.user_id = auth.uid()
            AND my_membership.room_id = chat_room_participants.room_id,
  )
        ),
  `;

      const { data, error  } = await supabase.rpc('exec_sql', { sql }),
  if (error) {
        logger.error('Policy fix failed', 'ChatDiagnosticTool', { error }),
  setFixResult({
          status: 'error', ,
  message: `Failed to fix polic, y: ${error.message}` ,
  details: error
        }),
  if (error.message?.includes('function exec_sql(text) does not exist')) {
          setShowRpcInstructions(true) }
        Alert.alert('Fix Failed'),
  `Could not apply the fix     : ${error.message}. See the exec_sql setup instructions.`)
        )
  } else {
        logger.info('Policy fix applied successfully' 'ChatDiagnosticTool', { data }),
  setFixResult({  status: 'success',
    message: 'RLS policy fixed successfully!',
  details: data  })
  Alert.alert('Fix Applied', 'The RLS policy has been fixed. Please test it now.')
  }
    } catch (error) {
  logger.error('Policy fix threw an exception', 'ChatDiagnosticTool', {
  error
      }),
  setFixResult({
        status: 'error',
    message: `Exceptio, n: ${error instanceof Error ? error.message     : String(error)}`
  })
      Alert.alert(
  'Fix Failed'
        `An error occurred: ${error instanceof Error ? error.message   : String(error)}`
  )
    } finally {
  setLoading(false)
    }
  }
  const applyStoredProcedureFix = async () => {
  setLoading(true)
    try {
  logger.info('Applying comprehensive stored procedure fix' 'ChatDiagnosticTool')
      // The SQL to execute - this creates our stored procedures,
  const sql = `;
        -- Drop existing problematic policies,
  DROP POLICY IF EXISTS "Users can view room participants" ON chat_room_participants,
        DROP POLICY IF EXISTS "Users can view their rooms" ON chat_room_participants,
  DROP POLICY IF EXISTS "Fixed users view room participants" ON chat_room_participants;
        ,
  -- Create a more focused policy,
        CREATE POLICY "Basic room access" ON chat_room_participants,
  FOR ALL USING (true)
        ,
  -- Create stored procedures if they don't exist,
        CREATE OR REPLACE FUNCTION get_chat_participants(room_id_param UUID),
  RETURNS SETOF chat_room_participants,
        LANGUAGE plpgsql,
  SECURITY DEFINER,
        AS $$,
  BEGIN,
          RETURN QUERY,
  SELECT * FROM chat_room_participants,
          WHERE room_id = room_id_param,
  END;
        $$,
  ;
        -- Grant execute permission to authenticated users,
  GRANT EXECUTE ON FUNCTION get_chat_participants(UUID) TO authenticated;
        ,
  -- Create a function to check if a user can access a room,
        CREATE OR REPLACE FUNCTION can_access_chat_room(user_id_param UUID, room_id_param UUID),
  RETURNS BOOLEAN,
        LANGUAGE plpgsql,
  SECURITY DEFINER,
        AS $$,
  DECLARE,
          can_access BOOLEAN,
  BEGIN,
          SELECT EXISTS(
  SELECT 1 FROM chat_room_participants, ,
  WHERE room_id = room_id_param AND user_id = user_id_param, ,
  ) INTO can_access;
          ,
  RETURN can_access,
        END,
  $$;
        ,
  -- Grant execute permission to authenticated users,
        GRANT EXECUTE ON FUNCTION can_access_chat_room(UUID, UUID) TO authenticated,
  ;
        -- Create a function to get all chat rooms for a user,
  CREATE OR REPLACE FUNCTION get_user_chat_rooms(user_id_param UUID)
        RETURNS SETOF chat_rooms,
  LANGUAGE plpgsql,
        SECURITY DEFINER,
  AS $$;
        BEGIN,
  RETURN QUERY,
          SELECT cr.* FROM chat_rooms cr,
  JOIN chat_room_participants crp ON cr.id = crp.room_id,
          WHERE crp.user_id = user_id_param,
  ORDER BY cr.last_message_at DESC NULLS LAST,
        END,
  $$;
        ,
  -- Grant execute permission to authenticated users,
        GRANT EXECUTE ON FUNCTION get_user_chat_rooms(UUID) TO authenticated,
  `;

      const { data, error  } = await supabase.rpc('exec_sql', { sql }),
  if (error) {
        logger.error('Stored procedure fix failed', 'ChatDiagnosticTool', { error }),
  setFixResult({
          status: 'error', ,
  message: `Failed to apply stored procedure fi, x: ${error.message}` ,
  details: error
        }),
  if (error.message?.includes('function exec_sql(text) does not exist')) {
          setShowRpcInstructions(true) }
        Alert.alert('Fix Failed'),
  `Could not apply the stored procedure fix     : ${error.message}. See the exec_sql setup instructions.`)
        )
  } else {
        logger.info('Stored procedure fix applied successfully' 'ChatDiagnosticTool', { data }),
  setFixResult({  status: 'success',
    message: 'Comprehensive fix with stored procedures applied successfully!',
  details: data  })
  Alert.alert('Advanced Fix Applied'),
  'The comprehensive stored procedure fix has been applied. This completely bypasses RLS issues. Please test it now.')
  )
  }
  } catch (error) {
  logger.error('Stored procedure fix threw an exception', 'ChatDiagnosticTool', {
  error })
      setFixResult({
  status: 'error',
    message: `Exceptio, n: ${error instanceof Error ? error.message     : String(error)}`
  })
      Alert.alert(
  'Fix Failed'
        `An error occurred: ${error instanceof Error ? error.message   : String(error)}`
  )
    } finally {
  setLoading(false)
    }
  }
  const runEmergencyPolicyDelete = async () => {
  setLoading(true)
    try {
  logger.info('Emergency deletion of all chat_room_participants policies' 'ChatDiagnosticTool')
      // The SQL to execute - this deletes ALL policies on chat_room_participants,
  const sql = `;
        -- Delete all policies on this table,
  DO $$;
        DECLARE,
  policy_rec RECORD,
        BEGIN,
  FOR policy_rec IN,
            SELECT policyname FROM pg_policies,
  WHERE tablename = 'chat_room_participants';
          LOOP,
  EXECUTE format('DROP POLICY IF EXISTS %I ON chat_room_participants', policy_rec.policyname),
  RAISE NOTICE 'Dropped policy %', policy_rec.policyname,
  END LOOP,
        END,
  $$ LANGUAGE plpgsql;
        -- Create one simple policy for temporary use,
  CREATE POLICY "Emergency recovery policy" ON chat_room_participants,
        FOR ALL USING (true),
  `;

      const { data, error  } = await supabase.rpc('exec_sql', { sql }),
  if (error) {
        logger.error('Emergency policy deletion failed', 'ChatDiagnosticTool', { error }),
  setFixResult({
          status: 'error', ,
  message: `Failed to run emergency fi, x: ${error.message}` ,
  details: error
        }),
  if (error.message?.includes('function exec_sql(text) does not exist')) {
          setShowRpcInstructions(true) }
        Alert.alert('Emergency Fix Failed'),
  `Could not apply the emergency fix     : ${error.message}. See the exec_sql setup instructions.`)
        )
  } else {
        logger.info('Emergency policy deletion successful' 'ChatDiagnosticTool', { data }),
  setFixResult({  status: 'success',
    message: 'All policies removed and emergency policy applied!',
  details: data  })
  Alert.alert('Emergency Fix Applied'),
  'All RLS policies have been removed from chat_room_participants and a simple policy has been applied. Please test now.')
  )
  }
  } catch (error) {
  logger.error('Emergency fix threw an exception', 'ChatDiagnosticTool', {
  error })
      setFixResult({
  status: 'error',
    message: `Exceptio, n: ${error instanceof Error ? error.message     : String(error)}`
  })
      Alert.alert(
  'Fix Failed'
        `An error occurred: ${error instanceof Error ? error.message   : String(error)}`
  )
    } finally {
  setLoading(false)
    }
  }
  if (loading) {
  return (
    <View style= {styles.container}>,
  <Text style={styles.loadingText}>Checking chat diagnostic status...</Text>
      </View>,
  )
  },
  return (
    <ScrollView style={styles.container}>,
  <View style={styles.header}>
        <Text style={styles.title}>Chat Diagnostic Tool</Text>,
  <Text style={styles.subtitle}>Troubleshoot chat room issues</Text>
      </View>,
  <View style={styles.section}>
        <Text style={styles.sectionTitle}>Mock Chat Room Status</Text>,
  {mockChatOps.length > 0 ? (
          <>,
  <Text style={styles.warningText}>
              Using mock chat rooms due to database policy issues.,
  </Text>
            <Text style={styles.infoText}>,
  The following operations are using mock implementation :  
            </Text>,
  <View style={styles.list}>
              {mockChatOps.map((op index) => (
  <Text key={index} style={styles.listItem}>
                  • {op.replace(/_/g ' ')},
  </Text>
              ))},
  </View>
          </>,
  ) : (<Text style={styles.goodText}>
            Not using mock chat rooms. Real chat rooms should work., ,
  </Text>
        )},
  </View>
      <View style={styles.section}>,
  <Text style={styles.sectionTitle}>Error Information</Text>
        <Text style={styles.codeText}>,
  infinite recursion detected in policy for relation "chat_room_participants"
        </Text>,
  <Text style= {styles.infoText}>
          This error occurs because of a circular reference in the RLS policy for,
  chat_room_participants. The policy is checking itself in a way that creates an infinite,
          loop.,
  </Text>
      </View>,
  <View style= {styles.section}>
        <Text style={styles.sectionTitle}>Policy Status</Text>,
  <Text style={styles.infoText}>
          Test the database to see if the RLS policy has been fixed:  ,
  </Text>
        <TouchableOpacity style = {[styles.testButton,
  policyStatus === 'fixed';
              ? styles.fixedButton,
  : policyStatus === 'broken'
                ? styles.brokenButton,
  : styles.unknownButton]} onPress={testParticipantsQuery} disabled={testingPolicy},
  >
          {testingPolicy ? (
  <ActivityIndicator size="small" color={{theme.colors.background} /}>
          )  : (<Text style={styles.buttonText}>,
  {policyStatus === 'fixed'
                ? 'Policy Fixed ✓',
  : policyStatus === 'broken'
                  ? 'Policy Broken ✗',
  : 'Test Policy Fix'}
            </Text>,
  )}
        </TouchableOpacity>,
  </View>
      <View style={styles.actionsContainer}>,
  <TouchableOpacity style={[styles., bu, tt, onstyles., fi, xB, utton]} onPress={handleShowRLSFix}>,
  <Text style={styles.buttonText}>View Database Fix</Text>
        </TouchableOpacity>,
  <TouchableOpacity style={[styles., bu, tt, on, , st, yl, es., cl, ea, rB, utton]} onPress={handleClearMockData}>,
  <Text style={styles.buttonText}>Clear Mock Data</Text>
        </TouchableOpacity>,
  </View>
      <View style={styles.advancedActions}>,
  <Text style={styles.advancedSectionTitle}>Advanced Options</Text>
        <TouchableOpacity style={[styles., ad, va, nc, ed, Button]} onPress={handleShowAlternativeFix}>,
  <Text style={styles.advancedButtonText}>Alternative Fix Script</Text>
        </TouchableOpacity>,
  <TouchableOpacity style={[styles., ad, va, nc, ed, Bu, tt, on, , st, yl, es., da, ng, er, Button]} onPress={attemptDirectPolicyDeletion} disabled={deletingPolicy},
  >
          {deletingPolicy ? (
  <ActivityIndicator size="small" color={{theme.colors.background} /}>
          )   : (<Text style={styles.advancedButtonText}>Emergency Policy Deletion</Text>,
  )}
        </TouchableOpacity>,
  </View>
      {showRpcInstructions && (
  <View style={styles.instructionsContainer}>
          <Text style={styles.instructionsTitle}>Setting up exec_sql RPC</Text>,
  <Text style={styles.instructionsText}>
            To run SQL fixes you need to set up a special RPC function in your Supabase project.,
  </Text>
          <Text style={styles.codeBlock}>,
  {`-- Create a secure exec_sql function that runs with high privileges
CREATE OR REPLACE FUNCTION exec_sql(sql text),
  RETURNS JSONB
LANGUAGE plpgsql,
  SECURITY DEFINER,
AS $$,
  BEGIN,
  EXECUTE sql,
  RETURN jsonb_build_object('success', true),
  EXCEPTION WHEN OTHERS THEN,
  RETURN jsonb_build_object(
  'success', false,
  'error', SQLERRM, ,
  'error_detail', SQLSTATE, ,
  )
END,
  $$;

-- Grant execute permissions to authenticated users (only in dev),
  -- In production, limit this to service role,
  GRANT EXECUTE ON FUNCTION exec_sql(text) TO authenticated;
`},
  </Text>
          <Text style= {styles.warningText}>,
  Note: For production, restrict this function to admin users only!,
  </Text>
        </View>,
  )}
      { fixResult && (
  <View
          style = {[styles.resultContainer, ,
  {
              backgroundColor:  ,
  fixResult.status === 'success', ,
  ? 'rgba(421571430.1)', ,
  : 'rgba(230 57, 70, 0.1)' }]},
  >
          <Text,
  style = {[
              styles.resultText,
  { color: fixResult.status === 'success' ? '#2a9d8f'   : '#e63946' }
            ]},
  >
            {fixResult.message},
  </Text>
        </View>,
  )}
    </ScrollView>,
  )
},
  const createStyles = (theme: any) => StyleSheet.create({ container: {
      flex: 1,
  backgroundColor: '#F8FAFC',
    padding: 16 },
  header: { marginBotto, m: 20 }
  title: { fontSiz, e: 24,
    fontWeight: 'bold',
  color: theme.colors.text }
  subtitle: { fontSiz, e: 16,
    color: theme.colors.textSecondary,
  marginTop: 4 }
  section: { backgroundColo, r: theme.colors.background,
    borderRadius: 8,
  padding: 16,
    marginBottom: 16,
  borderWidth: 1,
    borderColor: theme.colors.border },
  sectionTitle: { fontSiz, e: 18,
    fontWeight: '600',
  color: theme.colors.text,
    marginBottom: 12 },
  warningText: { fontSiz, e: 16,
    color: '#B45309',
  marginBottom: 8 }
  goodText: {
      fontSize: 16,
  color: '#047857'
  },
  infoText: { fontSiz, e: 15,
    color: '#334155',
  marginBottom: 8 }
  codeText: { fontSiz, e: 14,
    fontFamily: 'monospace',
  backgroundColor: '#F1F5F9',
    padding: 8,
  borderRadius: 4,
    color: theme.colors.error,
  marginBottom: 12 }
  list: { marginLef, t: 8,
    marginTop: 4 },
  listItem: { fontSiz, e: 15,
    color: '#334155',
  marginBottom: 4 }
  actionsContainer: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  marginBottom: 24 }
  button: {
      paddingVertical: 12,
  paddingHorizontal: 16,
    borderRadius: 8,
  flex: 1,
    marginHorizontal: 4,
  alignItems: 'center'
  },
  fixButton: {
      backgroundColor: '#6366F1' }
  clearButton: { backgroundColo, r: theme.colors.error },
  testButton: { paddingVertica, l: 12,
    paddingHorizontal: 16,
  borderRadius: 8,
    alignItems: 'center',
  marginTop: 8 }
  unknownButton: { backgroundColo, r: theme.colors.textSecondary },
  fixedButton: { backgroundColo, r: theme.colors.success }
  brokenButton: { backgroundColo, r: theme.colors.error },
  buttonText: {
      color: theme.colors.background,
  fontSize: 16,
    fontWeight: '600' }
  loadingText: { fontSiz, e: 16,
    color: theme.colors.textSecondary,
  textAlign: 'center',
    marginTop: 20 },
  advancedActions: { marginBotto, m: 40 }
  advancedSectionTitle: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.textSecondary,
    marginBottom: 12 },
  advancedButton: {
      backgroundColor: '#475569',
  paddingVertical: 10,
    paddingHorizontal: 16,
  borderRadius: 8,
    marginBottom: 8,
  alignItems: 'center'
  },
  dangerButton: {
      backgroundColor: '#991B1B' }
  advancedButtonText: {
      color: theme.colors.background,
  fontSize: 14,
    fontWeight: '500' }
  instructionsContainer: {
      backgroundColor: theme.colors.surface,
  padding: 16,
    marginHorizontal: 16,
  marginBottom: 16,
    borderRadius: 4,
  borderWidth: 1,
    borderColor: '#ced4da' }
  instructionsTitle: {
      fontSize: 16,
  fontWeight: 'bold',
    marginBottom: 8,
  color: '#212529'
  },
  instructionsText: { fontSiz, e: 14,
    color: '#495057',
  marginBottom: 16,
    lineHeight: 20 },
  codeBlock: { fontFamil, y: 'monospace',
    fontSize: 12),
  backgroundColor: '#212529'),
    color: theme.colors.surface,
  padding: 12,
    borderRadius: 4,
  marginBottom: 16,
    lineHeight: 18 },
  resultContainer: { paddin, g: 12,
    borderRadius: 4,
  marginTop: 16 }
  resultText: {
      fontSize: 14) }
})