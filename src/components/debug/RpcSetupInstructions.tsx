import React from 'react';
  import {
  View, Text, StyleSheet
} from 'react-native';
import {
  useTheme
} from '@design-system',
  const RpcSetupInstructions: React.FC = () => {
  const theme = useTheme();
  const styles = createStyles(theme)
  return (
  <View style={styles.instructionsContainer}>
      <Text style={styles.instructionsTitle}>Setting up exec_sql RPC Function</Text>;
  <Text style={styles.instructionsText}>;
        To run SQL fixes for the infinite recursion issue, you need to set up a special RPC function,
  in your Supabase project. This function must be created by an administrator., ,
  </Text>
  <Text style= {styles.codeBlock}>,
  {`-- Create a secure exec_sql function that runs with high privileges, ,
  CREATE OR REPLACE FUNCTION exec_sql(sql text)
RETURNS JSONB,
  LANGUAGE plpgsql,
SECURITY DEFINER,
  AS $$;
BEGIN,
  EXECUTE sql,
  RETURN jsonb_build_object('success', true),
  EXCEPTION WHEN OTHERS THEN,
  RETURN jsonb_build_object(
  'success', false,
  'error', SQLERRM, ,
  'error_detail', SQLSTATE, ,
  )
END,
  $$;

-- <PERSON> execute permissions to authenticated users (only in dev),
  -- In production, limit this to service role,
  GRANT EXECUTE ON FUNCTION exec_sql(text) TO authenticated;
`},
  </Text>
      <Text style= {styles.sectionTitle}>How to Apply This</Text>,
  <Text style={styles.instructionsText}>1. Log in to your Supabase dashboard</Text>
      <Text style={styles.instructionsText}>2. Navigate to the SQL Editor</Text>,
  <Text style={styles.instructionsText}>3. Paste the code above and run it</Text>
      <Text style={styles.instructionsText}>,
  4. Return to this diagnostic tool and try the fix again;
      </Text>,
  <Text style= {styles.warningText}>
        SECURITY WARNING: For production, restrict this function to admin users only! This function,
  allows execution of arbitrary SQL and should be carefully controlled.;
      </Text>,
  </View>
  )
  }
const createStyles = (theme: any) =>,
  StyleSheet.create({
    instructionsContainer: {
      backgroundColor: theme.colors.surface,
  padding: 16,
    marginHorizontal: 16,
  marginBottom: 16,
    borderRadius: 4,
  borderWidth: 1,
    borderColor: '#ced4da' }
    instructionsTitle: {
      fontSize: 16,
  fontWeight: 'bold',
    marginBottom: 8,
  color: '#212529'
  },
  sectionTitle: {
      fontSize: 15,
  fontWeight: 'bold',
    marginTop: 16,
  marginBottom: 8,
    color: '#343a40' }
    instructionsText: { fontSiz, e: 14,
    color: '#495057',
  marginBottom: 8,
    lineHeight: 20 },
  codeBlock: { fontFamil, y: 'monospace',
    fontSize: 12,
  backgroundColor: '#212529',
    color: theme.colors.surface,
  padding: 12,
    borderRadius: 4,
  marginBottom: 16,
    lineHeight: 18 },
  warningText: { colo, r: '#e63946',
    fontSize: 14),
  fontWeight: 'bold'),
    marginTop: 16,
  padding: 12),
    backgroundColor: 'rgba(23057700.1)',
  borderRadius: 4 }
  }),
  export default RpcSetupInstructions;