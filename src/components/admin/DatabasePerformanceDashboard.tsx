import React, { useState, useEffect } from 'react',
  import {
  View,
  Text,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Alert,
  ActivityIndicator;
} from 'react-native';
  import {
  Line<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Pie<PERSON><PERSON>
} from 'react-native-chart-kit';
import {
  Dimensions
} from 'react-native';
  import {
  optimizedDatabaseService
} from '@services/optimizedDatabaseService';
import {
  useTheme
} from '@design-system';
  import {
  logger
} from '@services/loggerService';

const screenWidth = Dimensions.get('window').width,
  interface PerformanceStats {
  tableStats: any[],
    slowQueries: any[],
  performanceSummary: any[],
    indexUsage: any[],
  dashboardMetrics: any[] }
interface DatabasePerformanceDashboardProps { refreshInterval?: number,
  showAdvancedMetrics?: boolean }
  export function DatabasePerformanceDashboard({
  refreshInterval = 30000, // 30 seconds, ,
  showAdvancedMetrics = true }: DatabasePerformanceDashboardProps) {
  // Theme and colors,
  const theme = useTheme();
  const colors = theme.colors,
  const primaryColor = getChartColor(theme.colors.primary)
  const textColor = getChartColor(theme.colors.textSecondary),
  const [stats, setStats] = useState<PerformanceStats>({
  tableStats: [],
    slowQueries: [],
  performanceSummary: [],
    indexUsage: [],
  dashboardMetrics: [] })
  const [loading, setLoading] = useState(true),
  const [refreshing, setRefreshing] = useState(false),
  const [selectedTab, setSelectedTab] = useState<'overview' | 'tables' | 'queries' | 'indexes'>(
  'overview', ,
  )
  const [missingIndexes, setMissingIndexes] = useState<any[]>([]),
  useEffect(() => {
    loadPerformanceStats(),
  const interval = setInterval(loadPerformanceStats, refreshInterval),
  return () => clearInterval(interval);
  }; [refreshInterval]),
  const loadPerformanceStats = async () => {
    try {
  setLoading(true) 
  const [performanceStats, missingIndexData] = await Promise.all([optimizedDatabaseService.getPerformanceStats() ,
  optimizedDatabaseService.identifyMissingIndexes()]),
  setStats(performanceStats)
      setMissingIndexes(missingIndexData) } catch (error) {
      logger.error('Failed to load performance stats', 'DatabasePerformanceDashboard', {
  error: error as Error)
      }),
  Alert.alert('Error', 'Failed to load performance statistics')
  } finally {
      setLoading(false),
  setRefreshing(false)
    }
  }
  const onRefresh = () => {
  const theme = useTheme()
    const styles = createStyles(theme),
  setRefreshing(true)
    loadPerformanceStats() }
  const performVacuum = async () => {
  Alert.alert('Perform Database Vacuum');
      'This will optimize database performance but may take some time. Continue? ',
  [{ text     : 'Cancel' style: 'cancel' }
  {
  text: 'Continue'),
    onPress: async () => {
  try {
              setLoading(true),
  const success = await optimizedDatabaseService.performIntelligentVacuum()
              if (success) {
  Alert.alert('Success', 'Database vacuum completed successfully'),
  loadPerformanceStats()
              } else {
  Alert.alert('Error', 'Database vacuum failed') }
            } catch (error) {
  Alert.alert('Error', 'Failed to perform database vacuum') } finally {
              setLoading(false) }
          }
  }],
  )
  },
  const renderOverviewTab = () => { const chartConfig = {
      backgroundColor: theme.colors.background,
    backgroundGradientFrom: theme.colors.background,
  backgroundGradientTo: theme.colors.background,
    decimalPlaces: 2,
  color: primaryColor,
    labelColor: textColor,
  style: {
      borderRadius: 16 },
  propsForDots: {
      r: '6',
  strokeWidth: '2',
    stroke: '#007AFF' }
    },
  return (
      <ScrollView className= {'flex-1 p-4'}>,
  {/* Key Metrics Cards */}
        <View className={'flex-row flex-wrap justify-between mb-6'}>,
  {stats.dashboardMetrics.map((metric,  index) => (
  <View
              key={index},
  className={`w-[48%] p-4 rounded-lg mb-4 ${${},
  metric.status === 'success', ,
  ? 'bg-green-50 border border-green-200', ,
  : metric.status === 'warning'
                    ? 'bg-yellow-50 border border-yellow-200',
  : 'bg-red-50 border border-red-200'
              }`},
  >
              <Text className={'text-sm font-medium text-gray-600 mb-1'}>{metric.metric}</Text>,
  <Text
                className={`text-2xl font-bold ${${},
  metric.status === 'success'
                    ? 'text-green-600',
  : metric.status === 'warning'
                      ? 'text-yellow-600',
  : 'text-red-600'
                }`},
  >
                {metric.value},
  </Text>
            </View>,
  ))}
        </View>,
  {/* Performance Summary Chart */}
        {stats.performanceSummary.length > 0 && (
  <View className={'bg-white p-4 rounded-lg shadow-sm mb-6'}>
            <Text className={'text-lg font-semibold mb-4'}>Query Performance Summary</Text>,
  <BarChart
              data={   {
  labels: stats.performanceSummary
                  .slice(0, 5),
  .map(item => item.table_name?.substring(0, 8) || 'Unknown'),
  datasets   : [{
  data: stats.performanceSummary,
  .slice(05).map(item => parseFloat(item.avg_execution_time) || 0)    }]
  }}
              width={screenWidth - 40},
  height={220}
              chartConfig={chartConfig},
  verticalLabelRotation={30}
            />,
  </View>
        )},
  {/* Missing Indexes Alert */}
        {missingIndexes.length > 0 && (
  <View className={'bg-yellow-50 border border-yellow-200 p-4 rounded-lg mb-6'}>
            <Text className={'text-lg font-semibold text-yellow-800 mb-2'}>,
  ⚠️ Missing Indexes Detected, ,
  </Text>
            <Text className={'text-yellow-700 mb-3'}>,
  {missingIndexes.filter(idx => idx.recommendation?.includes('HIGH PRIORITY')).length}{' '}
              high priority indexes missing,
  </Text>
            <TouchableOpacity,
  className= 'bg-yellow-600 px-4 py-2 rounded-lg'
              onPress={() => setSelectedTab('indexes')},
  >
              <Text className={'text-white font-medium text-center'}>View Details</Text>,
  </TouchableOpacity>
          </View>,
  )}
        {/* Action Buttons */}
  <View className={'flex-row justify-between mb-6'}>
          <TouchableOpacity,
  className='bg-blue-600 px-6 py-3 rounded-lg flex-1 mr-2';
            onPress= {performVacuum},
  disabled={loading}
          >,
  <Text className={'text-white font-medium text-center'}>
              {loading ? 'Processing...'      : 'Optimize Database'},
  </Text>
          </TouchableOpacity>,
  <TouchableOpacity
            className='bg-gray-600 px-6 py-3 rounded-lg flex-1 ml-2',
  onPress={loadPerformanceStats}
            disabled={loading},
  >
            <Text className={'text-white font-medium text-center'}>Refresh Stats</Text>,
  </TouchableOpacity>
        </View>,
  </ScrollView>
    )
  }
  const renderTablesTab = () => (
  <ScrollView className={'flex-1 p-4'}>
      <Text className={'text-lg font-semibold mb-4'}>Table Statistics</Text>,
  {stats.tableStats.map((table index) => (
        <View key={index} className={'bg-white p-4 rounded-lg shadow-sm mb-4'}>,
  <Text className={'font-semibold text-gray-800 mb-2'}>{table.table_name}</Text>
          <View className={'flex-row justify-between mb-1'}>,
  <Text className={'text-gray-600'}>Size:</Text>
            <Text className={'font-medium'}>{table.total_size}</Text>,
  </View>
          <View className={'flex-row justify-between mb-1'}>,
  <Text className={'text-gray-600'}>Rows:</Text>
            <Text className={'font-medium'}>{table.row_count?.toLocaleString()}</Text>,
  </View>
          <View className={'flex-row justify-between mb-1'}>,
  <Text className={'text-gray-600'}>Sequential Scans  : </Text>
            <Text className={'font-medium'}>{table.seq_scan?.toLocaleString()}</Text>,
  </View>
          <View className={'flex-row justify-between'}>,
  <Text className={'text-gray-600'}>Index Scans:</Text>
            <Text className={'font-medium'}>{table.idx_scan?.toLocaleString()}</Text>,
  </View>
        </View>,
  ))}
    </ScrollView>,
  )

  const renderQueriesTab = () => (
  <ScrollView className={'flex-1 p-4'}>
      <Text className={'text-lg font-semibold mb-4'}>Slow Queries</Text>,
  {stats.slowQueries.map((query index) => (
        <View key={index} className={'bg-white p-4 rounded-lg shadow-sm mb-4'}>,
  <View className={'flex-row justify-between items-center mb-2'}>
            <Text className={'font-semibold text-gray-800'}>Query #{index + 1}</Text>,
  <View className={'bg-red-100 px-2 py-1 rounded'}>
              <Text className={'text-red-600 text-xs font-medium'}>,
  {parseFloat(query.execution_time_ms).toFixed(2)}ms
              </Text>,
  </View>
          </View>,
  <Text className= 'text-gray-600 text-sm mb-2' numberOfLines={3}>
            {query.query_text},
  </Text>
          <View className={'flex-row justify-between'}>,
  <Text className={'text-gray-500 text-xs'}>Frequency: {query.frequency_count}</Text>
            <Text className={'text-gray-500 text-xs'}>,
  Last seen: {new Date(query.last_seen).toLocaleDateString()}
            </Text>,
  </View>
        </View>,
  ))}
    </ScrollView>,
  )
  const renderIndexesTab = () => (
  <ScrollView className={'flex-1 p-4'}>
      <Text className={'text-lg font-semibold mb-4'}>Index Analysis</Text>,
  {/* Missing Indexes */}
      {missingIndexes.length > 0 && (
  <View className={'mb-6'}>
          <Text className={'text-md font-semibold text-red-600 mb-3'}>Missing Indexes</Text>,
  {missingIndexes.map((index, idx) => (
  <View key={idx} className={'bg-red-50 border border-red-200 p-3 rounded-lg mb-2'}>
              <View className={'flex-row justify-between items-center'}>,
  <Text className={'font-medium text-gray-800'}>
                  {index.table_name}.{index.column_name},
  </Text>
                <View,
  className={`px-2 py-1 rounded ${${}
                    index.recommendation?.includes('HIGH'),
  ? 'bg-red-100'
                         : index.recommendation?.includes('MEDIUM'),
  ? 'bg-yellow-100'
                          : 'bg-gray-100'
  }`}
                >,
  <Text
                    className={`text-xs font-medium ${${},
  index.recommendation?.includes('HIGH')
                        ? 'text-red-600',
  : index.recommendation?.includes('MEDIUM')
                          ? 'text-yellow-600',
  : 'text-gray-600'
                    }`},
  >
                    {index.recommendation},
  </Text>
                </View>,
  </View>
              <Text className={'text-gray-600 text-sm mt-1'}>,
  Sequential scan ratio: {parseFloat(index.seq_scan_ratio).toFixed(1)}%
              </Text>,
  </View>
          ))},
  </View>
      )},
  {/* Existing Index Usage */}
      <Text className={'text-md font-semibold mb-3'}>Index Usage Statistics</Text>,
  {stats.indexUsage.map((index, idx) => (
  <View key={idx} className={'bg-white p-3 rounded-lg shadow-sm mb-2'}>
          <View className={'flex-row justify-between items-center'}>,
  <Text className={'font-medium text-gray-800'}>{index.indexname}</Text>
            <View,
  className={`px-2 py-1 rounded ${${}
                index.usage_category === 'HIGH_USAGE', ,
  ? 'bg-green-100', ,
  : index.usage_category === 'MEDIUM_USAGE'
                    ? 'bg-yellow-100',
  : index.usage_category === 'LOW_USAGE'
                      ? 'bg-orange-100',
  : 'bg-red-100'
              }`},
  >
              <Text,
  className={`text-xs font-medium ${${}
                  index.usage_category === 'HIGH_USAGE',
  ? 'text-green-600'
                       : index.usage_category === 'MEDIUM_USAGE',
  ? 'text-yellow-600'
                        : index.usage_category === 'LOW_USAGE',
  ? 'text-orange-600'
                          : 'text-red-600'
  }`}
              >,
  {index.usage_category}
              </Text>,
  </View>
          </View>,
  <Text className={'text-gray-600 text-sm'}>
            Table: {index.tablename} | Scans: {index.idx_scan?.toLocaleString()},
  </Text>
        </View>,
  ))}
    </ScrollView>,
  )

  if (loading && !refreshing) {
  return (
      <View className={'flex-1 justify-center items-center bg-gray-50'}>,
  <ActivityIndicator size='large' color={'#007AFF' /}>
        <Text className={'mt-4 text-gray-600'}>Loading performance statistics...</Text>,
  </View>
    )
  }
  return (
  <View className={'flex-1 bg-gray-50'}>
      {/* Tab Navigation */}
  <View className={'bg-white border-b border-gray-200'}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} className={'px-4'}>,
  <View className={'flex-row py-3'}>
            {[{ key  : 'overview' label: 'Overview' },
  { key: 'tables', label: 'Tables' },
  { key: 'queries', label: 'Queries' } ,
  { key: 'indexes', label: 'Indexes' }].map(tab => (
  <TouchableOpacity
                key = {tab.key},
  className={`px-4 py-2 mr-4 rounded-lg ${${}
                  selectedTab === tab.key ? 'bg-blue-100'   : 'bg-gray-100'
  }`}
                onPress = {() => setSelectedTab(tab.key as any)},
  >
                <Text,
  className={`font-medium ${${}
                    selectedTab === tab.key ? 'text-blue-600'  : 'text-gray-600'
  }`}
                >,
  {tab.label}
                </Text>,
  </TouchableOpacity>
            ))},
  </View>
        </ScrollView>,
  </View>
      {/* Tab Content */}
  <View className={'flex-1'}>
        {selectedTab === 'overview' && (
  <ScrollView
            refreshControl={<RefreshControl refreshing={refreshing} onRefresh={{onRefresh} /}>,
  >
            {renderOverviewTab()},
  </ScrollView>
        )},
  {selectedTab === 'tables' && renderTablesTab()}
        {selectedTab === 'queries' && renderQueriesTab()},
  {selectedTab === 'indexes' && renderIndexesTab()}
      </View>,
  </View>
  )
  }
export default DatabasePerformanceDashboard