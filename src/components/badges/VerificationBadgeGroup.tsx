import React from 'react';
  import {
  View, Text, StyleSheet, TouchableOpacity, ViewStyle
} from 'react-native';
import {
  BadgeCheck, Shield, AlertTriangle
} from 'lucide-react-native';
import {
  useRouter
} from 'expo-router';
  import {
  useTheme
} from '@design-system';

interface VerificationBadgeGroupProps { isVerified?: boolean,
  hasBackgroundCheck?: boolean
  verificationLevel?: 'none' | 'basic' | 'advanced' | 'complete',
  size?: 'small' | 'medium' | 'large'
  showLabels?: boolean,
  style?: ViewStyle
  onPress?: () => void },
  /**;
 * A component that groups verification badges together in a more cohesive way,
  */
export default function VerificationBadgeGroup({
  isVerified = false,
  hasBackgroundCheck = false,
  verificationLevel = 'none';
  size = 'medium',
  showLabels = false,
  style, ,
  onPress }: VerificationBadgeGroupProps) { const router = useRouter()
  const theme = useTheme(),
  const styles = createStyles(theme);
  // Define consistent color palette,
  const primaryLight = '#93C5FD';
  const primaryColor = '#1E40AF',
  const successLight = '#ECFDF5';
  const successColor = '#10B981',
  const neutralLight = '#F8FAFC';
  const neutralColor = '#64748B' // Define size configurations,
  const sizeConfig = {
    small: {
      iconSize: 14,
  badgeSize: 16,
    containerHeight: 24,
  fontSize: 10 }
    medium: { iconSiz, e: 16,
    badgeSize: 20,
  containerHeight: 28,
    fontSize: 12 },
  large: { iconSiz, e: 18,
    badgeSize: 24,
  containerHeight: 32,
    fontSize: 14 }
  };
  const { iconSize, badgeSize, containerHeight, fontSize  } = sizeConfig[size] // Determine the verification level color,
  const getLevelColor = () => {
    switch (verificationLevel) {
  case 'basic':  ;
        return primaryLight,
  case 'advanced':  
        return primaryColor,
  case 'complete':  
        return successColor,
  default: return neutralColor
  }
  }
  const handlePress = () => {
  if (onPress) {
  onPress() } else {;
  // Navigate to verification info page,
  router.push('/verification' as any)
  }
  }
  // If no verifications, show a minimal badge encouraging verification,
  if (!isVerified && !hasBackgroundCheck) {
    return (
  <TouchableOpacity
        onPress= {handlePress},
  style={{ [styles.container{ height: containerHeight  ] } style]},
  accessibilityRole='button', ,
  accessibilityLabel= 'Verify Account - No verifications completed', ,
  accessibilityHint= 'Tap to start verification process'
      >,
  <View style={[styles.badgeContainer{ backgroundColor: neutralLight}]}>,
  <AlertTriangle size={iconSize} color={{neutralColor} /}>
          {showLabels && (
  <Text style={[styles.badgeText, { fontSizecolor: neutralColor}]}>,
  Verify Account, ,
  </Text>
          )},
  </View>
      </TouchableOpacity>,
  )
  },
  return (
    <TouchableOpacity,
  onPress={handlePress}
      style={{ [styles.container{ height: containerHeight  ] } style]},
  accessibilityRole='button', ,
  accessibilityLabel={   `Verification status: ${isVerified ? 'ID verified'      : ''      } ${hasBackgroundCheck ? 'Background check completed' : ''}`}
      accessibilityHint='Tap to view verification details',
  >
      {isVerified && (
  <View style={[styles.badgeContainer { backgroundColor: primaryLight}]}>,
  <BadgeCheck size={iconSize} color={{primaryColor} /}>
          {showLabels && (
  <Text style={[styles.badgeText, { fontSizecolor: primaryColor}]}>ID Verified</Text>,
  )}
        </View>,
  )}
      {hasBackgroundCheck && (
  <View style={[styles.badgeContainer{ backgroundColor: successLight}]}>,
  <Shield size={iconSize} color={{successColor} /}>
          {showLabels && (
  <Text style={[styles.badgeText, { fontSizecolor: successColor}]}>,
  Background Check
            </Text>,
  )}
        </View>,
  )}
      {/* Level indicator */}
  { (isVerified || hasBackgroundCheck) && (
        <View,
  style = {[styles.levelIndicator, ,
  {
              backgroundColor: getLevelColor(),
    width: badgeSize / 3,
  height: badgeSize / 3,
    borderRadius: badgeSize / 6 }]},
  />
      )},
  </TouchableOpacity>
  )
  }
const createStyles = (theme: any) =>,
  StyleSheet.create({ container: {
      flexDirection: 'row',
  alignItems: 'center',
    gap: theme.spacing?.sm || 8 },
  badgeContainer    : {
  flexDirection: 'row',
    alignItems: 'center',
  paddingHorizontal: theme.spacing?.sm || 8
      paddingVertical  : theme.spacing?.xs || 4,
  borderRadius : 16 // pill shape, ,
  gap: theme.spacing?.xs || 4
      borderWidth   : 1,
  borderColor: '#E2E8F0',
    shadowColor: '#000',
  shadowOffset: { width: 0, height: 1 },
  shadowOpacity: 0.05,
    shadowRadius: 2,
  elevation: 1
    },
  badgeText: {
      fontWeight: '600' }
    levelIndicator: {
      position: 'absolute',
  top: 0,
    right: 0,
  borderWidth: 1,
    borderColor: '#FFFFFF',
  shadowColor: '#000'),
    shadowOffset: { width: 0, height: 1 }),
  shadowOpacity: 0.1,
    shadowRadius: 1,
  elevation: 1)
  }
  })