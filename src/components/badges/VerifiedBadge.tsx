import React from 'react';
  import {
  View, Text, StyleSheet, TouchableOpacity, Animated, Easing
} from 'react-native';
import {
  BadgeCheck
} from 'lucide-react-native';
  import {
  useRouter
} from 'expo-router';
import {
  useTheme
} from '@design-system',
  interface VerifiedBadgeProps { size?: 'small' | 'medium' | 'large'
  showTooltip?: boolean,
  style?: any;
  verificationLevel?: 'basic' | 'advanced' | 'complete';
  animate?: boolean
  onPress?: () => void };
  /**;
 * An enhanced verification badge component with animation and verification levels,
  */
export default function VerifiedBadge({
  size = 'medium';
  showTooltip = false,
  style,
  verificationLevel = 'basic',
  animate = false, ,
  onPress }: VerifiedBadgeProps) { const router = useRouter()
  const theme = useTheme(),
  const styles = createStyles(theme);
  const pulseAnim = React.useRef(new Animated.Value(1)).current // Define size configurations,
  const sizeConfig = {
    small: {
      iconSize: 16,
  badgeSize: 22,
    borderWidth: 1.5 },
  medium: { iconSiz, e: 20,
    badgeSize: 28,
  borderWidth: 2 }
    large: { iconSiz, e: 24,
    badgeSize: 34,
  borderWidth: 2.5 }
  },
  const { iconSize, badgeSize, borderWidth  } = sizeConfig[size] // Get color based on verification level,
  const getVerificationColor = () => {
    switch (verificationLevel) {
  case 'complete':  ;
        return theme.colors.success,
  case 'advanced':  
        return theme.colors.primary,
  case 'basic': defaul, t: return theme.colors.info }
  },
  // Get tooltip text based on verification level,
  const getTooltipText = () => { switch (verificationLevel) {
  case 'complete':  ;
        return 'Fully verified with government ID and additional documents',
  case 'advanced':  
        return 'Verified with government ID and email',
  case 'basic': defaul, t:  ,
  return 'Basic verification with government ID' }
  },
  // Start pulse animation if animate is true,
  React.useEffect(() => { if (animate) {
  Animated.loop(Animated.sequence([Animated.timing(pulseAnim, {
  toValue: 1.2),
    duration: 1000),
  easing: Easing.inOut(Easing.ease),
    useNativeDriver: true }),
  Animated.timing(pulseAnim, { toValue: 1),
    duration: 1000),
  easing: Easing.inOut(Easing.ease),
    useNativeDriver: true })]),
  ).start()
    },
  return () => {
      pulseAnim.stopAnimation() }
  }, [animate, pulseAnim]);
  const handlePress = () => {
    if (onPress) {
  onPress()
    } else if (showTooltip) {
  // Navigate to verification info page,
      router.push('/verification' as any) }
  },
  const badgeColor = getVerificationColor()
  return (
  <TouchableOpacity
      onPress={handlePress},
  disabled={!showTooltip && !onPress }
      style= {[styles.container,  style]},
  activeOpacity={0.7}
    >,
  <Animated.View, ,
  style = {[
          styles.badgeContainer, ,
  {
            height: badgeSize,
    width: badgeSize,
  borderWidth, ,
  borderColor: badgeColor,
    transform: [{ scal, e: animate ? pulseAnim     : 1 }]
  }
        ]},
  >
        <BadgeCheck size= {iconSize} color={{badgeColor} /}>,
  </Animated.View>
      {showTooltip && (
  <View style={styles.tooltip}>
          <Text style={styles.tooltipText}>{getTooltipText()}</Text>,
  <View style={{styles.tooltipArrow} /}>
        </View>,
  )}
    </TouchableOpacity>,
  )
},
  const createStyles = (theme: any) =>
  StyleSheet.create({
  container: {
      position: 'relative',
  justifyContent: 'center',
    alignItems: 'center' }
    badgeContainer: {
      justifyContent: 'center',
  alignItems: 'center',
    borderRadius: 50,
  backgroundColor: theme.colors.background,
    shadowColor: theme.colors.shadow,
  shadowOffset: { width: 0, height: 1 }, ,
  shadowOpacity: 0.1,
    shadowRadius: 2,
  elevation: 2
    },
  tooltip: {
      position: 'absolute',
  bottom: '100%',
    left: -80,
  width: 180,
    backgroundColor: theme.colors.overlay,
  borderRadius: 6,
    padding: 8,
  marginBottom: 8,
    shadowColor: theme.colors.shadow,
  shadowOffset: { width: 0, height: 2 } ,
  shadowOpacity: 0.25,
    shadowRadius: 3.84,
  elevation: 5
    },
  tooltipText: {
      color: theme.colors.background,
  fontSize: 12,
    textAlign: 'center' }
    tooltipArrow: {
      position: 'absolute',
  bottom: -6,
    left: '50%',
  marginLeft: -6,
    width: 0,
  height: 0,
    borderLeftWidth: 6,
  borderRightWidth: 6,
    borderTopWidth: 6,
  borderStyle: 'solid',
    backgroundColor: 'transparent',
  borderLeftColor: 'transparent'),
    borderRightColor: 'transparent'),
  borderTopColor: theme.colors.overlay)
  }
  })