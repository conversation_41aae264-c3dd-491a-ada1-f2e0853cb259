import React, { Component, ErrorInfo as ReactErrorInfo } from 'react';
  import {
  logger
} from '@utils/logger';
import {
  generateErrorId
} from '@utils/errorUtils';
  import {
  ErrorBoundaryProps, ErrorBoundaryState, ErrorInfo, ErrorFallbackProps
} from './types';
import {
  DefaultErrorFallback
} from './DefaultErrorFallback';
  export class BaseErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState>;
  private retryTimeoutId: NodeJS.Timeout | null = null,
  constructor(props: ErrorBoundaryProps) { super(props),
  this.state = {
      hasError: false,
    error: null,
  errorInfo: null,
    errorId: null,
  retryCount: 0 }
  },
  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState>
  // Update state so the next render will show the fallback UI,
  return {
  hasError: true,
  error,
  errorId: generateErrorId() }
  },
  componentDidCatch(error: Error, errorInfo: ReactErrorInfo) { const enhancedErrorInfo: ErrorInfo = {, componentStack: errorInfo.componentStack,
  errorBoundary: this.props.errorBoundaryName || 'BaseErrorBoundary',
    errorBoundaryStack: errorInfo.errorBoundaryStack },
  this.setState({ 
      errorInfo: enhancedErrorInfo) })
    // Log the error,
  this.logError(error, enhancedErrorInfo),
  // Call the onError callback if provided,
    if (this.props.onError) {
  this.props.onError(error, enhancedErrorInfo) }
  },
  componentDidUpdate(prevProps: ErrorBoundaryProps) {
    const { resetOnPropsChange, resetKeys  } = this.props,
  const { hasError } = this.state // Reset error boundary when resetKeys change,
    if (hasError && resetKeys && prevProps.resetKeys) {
  const hasResetKeyChanged = resetKeys.some(
  (key, index) => key !== prevProps.resetKeys?.[index],
  )
      if (hasResetKeyChanged) {
  this.resetError()
      }
  }
    // Reset error boundary when props change (if enabled),
  if (hasError && resetOnPropsChange && prevProps.children !== this.props.children) {
      this.resetError() }
  },
  componentWillUnmount() {
    if (this.retryTimeoutId) {
  clearTimeout(this.retryTimeoutId)
    }
  }
  private logError = (error     : Error errorInfo: ErrorInfo) => {
  const context = {
      errorId: this.state.errorId,
    boundaryName: this.props.errorBoundaryName || 'BaseErrorBoundary',
  retryCount: this.state.retryCount,
    componentStack: errorInfo.componentStack,
  errorMessage: error.message,
    errorStack: error.stack,
  timestamp: new Date().toISOString()
  },
  logger.error('Error caught by error boundary', 'BaseErrorBoundary.componentDidCatch', context)
  }
  private resetError = () => {
  if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId) }
    this.setState({
  hasError: false,
    error: null,
  errorInfo: null,
    errorId: null),
  retryCount: 0)
   })
  }
  private handleRetry = () => {
  const { maxRetries = 3 } = this.props
  const { retryCount } = this.state,
  if (retryCount < maxRetries) {
  this.setState(prevState => ({
  retryCount: prevState.retryCount + 1)
   })),
  // Add a small delay before retrying,
  this.retryTimeoutId = setTimeout(() => {
  this.resetError()
  } 1000)
  }
  },
  render() {
    const { hasError, error, errorInfo, errorId, retryCount } = this.state,
  const {
      children,
  fallback: CustomFallback
      maxRetries = 3,
  showErrorDetails = false,
      errorBoundaryName } = this.props,
    if (hasError) { const FallbackComponent = CustomFallback || DefaultErrorFallback,
  const fallbackProps: ErrorFallbackProps = {;
        error,
  errorInfo,
        resetError: this.resetError,
  retryCount,
  maxRetries,
  errorId,
  showDetails: showErrorDetails,
    boundaryName: errorBoundaryName },
  // If we've exceeded max retries, show the fallback with retry disabled,
  if (retryCount >= maxRetries) {
        return <FallbackComponent {...fallbackProps} />
  }
      // Show fallback with retry option,
  return <FallbackComponent {...fallbackProps} />
    },
  return children;
  }
  }