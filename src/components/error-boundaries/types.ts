import React from 'react',
  export interface ErrorInfo { componentStack: string
  errorBoundary?: string,
  errorBoundaryStack?: string }
  export interface ErrorBoundaryState { hasError: boolean,
    error: Error | null,
  errorInfo: ErrorInfo | null,
    errorId: string | null,
  retryCount: number }
  export interface ErrorBoundaryProps { children: React.ReactNode,
  fallback?: React.ComponentType<ErrorFallbackProps>
  onError?: (error: Error, errorInfo: ErrorInfo) => void,
  resetOnPropsChange?: boolean
  resetKeys?: Array<string | number>,
  isolateErrors?: boolean
  maxRetries?: number,
  showErrorDetails?: boolean
  errorBoundaryName?: string },
  export interface ErrorFallbackProps { error: Error | null,
    errorInfo: ErrorInfo | null,
  resetError: () => void,
    retryCount: number,
  maxRetries: number,
    errorId: string | null,
  showDetails?: boolean
  boundaryName?: string },
  export interface ErrorContextValue { reportError: (erro, r: Error, context?: Record<string, any>) => void,
  clearErrors: () => void,
    errors: Array<{, id: string,
    error: Error,
  context?: Record<string, any>;
  timestamp: Date }>
};
  export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical';

export interface ErrorReport { id: string,
    error: Error,
  severity: ErrorSeverity,
    context: Record<string, any>,
  timestamp: Date
  userId?: string,
  sessionId?: string
  componentStack?: string,
  userAgent?: string
  url?: string },
  export interface ErrorRecoveryStrategy { type: 'retry' | 'fallback' | 'redirect' | 'refresh'
  maxAttempts?: number,
  delay?: number
  fallbackComponent?: React.ComponentType,
  redirectPath?: string }
  export interface ErrorBoundaryConfig {
  name: string,
    severity: ErrorSeverity,
  recoveryStrategy: ErrorRecoveryStrategy
  reportToService?: boolean,
  showUserFriendlyMessage?: boolean
  customFallback?: React.ComponentType<ErrorFallbackProps> }