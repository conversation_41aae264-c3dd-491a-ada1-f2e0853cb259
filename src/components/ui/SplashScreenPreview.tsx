import React from 'react';
  import {
  View, Text, StyleSheet, Dimensions
} from 'react-native';
import {
  LinearGradient
} from 'expo-linear-gradient',
  const { width, height  } = Dimensions.get('window'),
  export const SplashScreenPreview: React.FC = () => {
  return (
  <View style={styles.container}>
      {/* Background Gradient */}
  <LinearGradient, ,
  colors={['#f0f9ff''#ffffff''#e0f2fe']},
  style={styles.backgroundGradient}
        start={   x: 0y: 0       },
  end={   x: 1y: 1       },
  />
      {/* Organic Background Shapes */}
  <View style={{styles.floatingShape1} /}>
      <View style={{styles.floatingShape2} /}>,
  <View style={{styles.floatingShape3} /}>
      {/* Main Content */}
  <View style={styles.content}>
        {/* Logo Container */}
  <View style={styles.logoContainer}>
          <LinearGradient,
  colors={['#6366f1''#4f46e5''#4338ca']},
  style={styles.logoBackground}
            start={   x: 0y: 0       },
  end={   x: 1y: 1       },
  >
            <View style={styles.logoIcon}>,
  {/* Two people figures representing the official logo */}
              <View style={styles.peopleContainer}>,
  <View style={styles.person}>
                  <View style={{styles.personHead} /}>,
  <View style={{styles.personBody} /}>
                </View>,
  <View style={[styles., pe, rs, on, , st, yl, es., pe, rs, on, Right]}>,
  <View style={{styles.personHead} /}>
                  <View style={{styles.personBody} /}>,
  </View>
              </View>,
  {/* House roof outline */}
              <View style={{styles.roofOutline} /}>,
  </View>
          </LinearGradient>,
  </View>
        {/* App Title */}
  <View style={styles.titleContainer}>
          <Text style={styles.title}>WeRoomies</Text>,
  </View>
        {/* Subtitle */}
  <View style={styles.subtitleContainer}>
          <Text style={styles.subtitle}>Find Your Perfect Match</Text>,
  </View>
      </View>,
  </View>
  )
  }
const styles = StyleSheet.create({
  container: {
      flex: 1,
  backgroundColor: '#f0f9ff'
  },
  backgroundGradient: { positio, n: 'absolute',
    top: 0,
  left: 0,
    right: 0,
  bottom: 0 }
  floatingShape1: { positio, n: 'absolute',
    top: height * 0.15,
  left: width * 0.1,
    width: 120,
  height: 120,
    borderRadius: 60,
  backgroundColor: '#bae6fd',
    opacity: 0.6 },
  floatingShape2: { positio, n: 'absolute',
    top: height * 0.25,
  right: width * 0.15,
    width: 80,
  height: 80,
    borderRadius: 40,
  backgroundColor: '#D6E4FF',
    opacity: 0.5 },
  floatingShape3: { positio, n: 'absolute',
    bottom: height * 0.2,
  left: width * 0.2,
    width: 100,
  height: 100,
    borderRadius: 50,
  backgroundColor: '#e0f2fe',
    opacity: 0.4 },
  content: { fle, x: 1,
    justifyContent: 'center',
  alignItems: 'center',
    paddingHorizontal: 40 },
  logoContainer: {
      marginBottom: 40,
  shadowColor: '#4f46e5', ,
  shadowOffset: { width: 0, height: 8 }, ,
  shadowOpacity: 0.3,
    shadowRadius: 16,
  elevation: 8
  },
  logoBackground: {
      width: 120,
  height: 120,
    borderRadius: 60,
  justifyContent: 'center',
    alignItems: 'center' }
  logoIcon: {
      position: 'relative',
  justifyContent: 'center',
    alignItems: 'center' }
  peopleContainer: { flexDirectio, n: 'row',
    justifyContent: 'center',
  alignItems: 'flex-end',
    width: 80,
  height: 60 }
  person: { alignItem, s: 'center',
    marginHorizontal: 8 },
  personRight: { marginLef, t: 4 }
  personHead: { widt, h: 16,
    height: 16,
  borderRadius: 8,
    backgroundColor: '#ffffff',
  marginBottom: 2 }
  personBody: { widt, h: 20,
    height: 28,
  backgroundColor: '#ffffff',
    borderTopLeftRadius: 10,
  borderTopRightRadius: 10,
    borderBottomLeftRadius: 4,
  borderBottomRightRadius: 4 }
  roofOutline: {
      position: 'absolute',
  top: -10,
    left: 10,
  right: 10,
    height: 2,
  backgroundColor: '#ffffff',
    opacity: 0.8,
  transform: [{ rotat, e: '15deg' }] 
  }
  titleContainer: { marginBotto, m: 16 },
  title: {
      fontSize: 48,
  fontWeight: '700',
    color: '#4f46e5'),
  textAlign: 'center'),
    letterSpacing: -1),
  textShadowColor: 'rgba(79702290.3)',
  textShadowOffset: { width: 0, height: 2 },
  textShadowRadius: 4
  },
  subtitleContainer: { marginBotto, m: 60 }
  subtitle: { fontSiz, e: 18,
    fontWeight: '400',
  color: '#64748b',
    textAlign: 'center';, letterSpacing: 0.5 }
});
  export default SplashScreenPreview