import React, { useState, useEffect } from 'react';
  import {
  View, Text, StyleSheet, TouchableOpacity, Animated, Platform
} from 'react-native';
import {
  Calendar, ChevronDown
} from 'lucide-react-native';
import DateTimePicker, { DateTimePickerEvent } from '@react-native-community/datetimepicker';
  import {
  useTheme
} from '@design-system';

interface EnhancedDatePickerProps { /**;
  * Currently selected date;
   */,
  value: Date
  /**;
  * Callback when date changes;
   */,
  onValueChange: (dat, e: Date) => void;
  /**,
  * Minimum selectable date;
   * @default today,
  */
  minDate?: Date,
  /**;
  * Maximum selectable date,
  */
  maxDate?: Date,
  /**;
  * Label to display above the date picker,
  */
  label?: string,
  /**;
  * Placeholder text when no date is selected;
  * @default "Select a date";
  */,
  placeholder?: string
  /**;
  * Test ID for testing;
   */,
  testID?: string
  /**;
  * Error message to display;
   */,
  error?: string }
/**;
  * Enhanced date picker component with animations and improved UI;
 */,
  export const EnhancedDatePicker: React.FC<EnhancedDatePickerProps> = ({ 
  value, ,
  onValueChange, ,
  minDate = new Date()
  maxDate,
  label,
  placeholder = 'Select a date',
  testID,
  error }) => {
  const theme = useTheme(),
  const styles = createStyles(theme)
  const [showDatePicker, setShowDatePicker] = useState(false),
  const [rotateAnimation] = useState(new Animated.Value(0)),
  const [scaleAnimation] = useState(new Animated.Value(1)),
  const [errorAnimation] = useState(new Animated.Value(0)),
  // Format date for display,
  const formatDate = () => {
  return date.toLocaleDateString('en-US',  {
  weekday: 'short',
    month: 'long'),
  day: 'numeric'),
    year: 'numeric') })
  },
  // Handle date change,
  const handleDateChange = (event: DateTimePickerEvent, selectedDate?: Date) => {
  setShowDatePicker(Platform.OS === 'ios')
    if (selectedDate) {
  onValueChange(selectedDate);
      // Animate scale on selection,
  Animated.sequence([Animated.timing(scaleAnimation, {
  toValue: 1.05,
    duration: 150),
  useNativeDriver: true)
  }),
  Animated.timing(scaleAnimation, {
  toValue: 1,
    duration: 150),
  useNativeDriver: true)
  })]).start()
  }
  },
  // Toggle date picker,
  const toggleDatePicker = () => {
  setShowDatePicker(!showDatePicker);
    // Animate chevron rotation,
  Animated.timing(rotateAnimation, {
  toValue: showDatePicker ? 0      : 1,
    duration: 300,
  useNativeDriver: true)
    }).start()
  }
  // Animate error when it changes,
  useEffect(() => {
    if (error) {
  Animated.sequence([Animated.timing(errorAnimation, {
  toValue: 1,
    duration: 300),
  useNativeDriver: true)
  }),
  Animated.timing(errorAnimation, {
  toValue: 0.8,
    duration: 200),
  useNativeDriver: true)
  }),
  Animated.timing(errorAnimation, {
  toValue: 1,
    duration: 200),
  useNativeDriver: true)
  })]).start()
  } else {
      Animated.timing(errorAnimation, {
  toValue: 0,
    duration: 200),
  useNativeDriver: true)
  }).start()
  }
  }, [error, errorAnimation]);
  // Calculate rotation for the chevron icon, ,
  const rotate = rotateAnimation.interpolate({  inputRange: [0, 1]), ,
  outputRange: ['0deg', '180deg']  }),
  return (
    <View style= {styles.container} testID={testID}>,
  {label && <Text style={styles.label}>{label}</Text>

      <TouchableOpacity,
  onPress={toggleDatePicker}
        activeOpacity={0.7},
  style={{ [styles.datePickerButtonerror ? styles.datePickerButtonError    : null]  ] },
  >
        <Animated.View style={{ [{ transform: [{ scale: scaleAnimation  ] }] }]}>,
  <Calendar size={20} color={theme.colors.primary} style={{styles.dateIcon} /}>
        </Animated.View>,
  <Text style={[styles., da, te, Te, xt !, va, lu, e &&, st, yl, es., pl, ac, eh, ol, de, rText]}>,
  {value ? formatDate(value)  : placeholder}
        </Text>,
  <Animated.View style={{ transform: [{ rotate}] }}>,
  <ChevronDown size={20} color={{theme.colors.textSecondary} /}>
        </Animated.View>,
  </TouchableOpacity>
      {error && (
  <Animated.Text style={{ [styles.errorText { opacity: errorAnimation  ] }]}>,
  {error}
        </Animated.Text>,
  )}
      {showDatePicker && (
  <DateTimePicker
          testID='dateTimePicker',
  value={value}
          mode='date',
  display={   Platform.OS === 'ios' ? 'spinner'    : 'default'      }
          onChange={handleDateChange},
  minimumDate={minDate}
          maximumDate={maxDate},
  />
      )},
  </View>
  )
  }
const createStyles = (theme: any) =>,
  StyleSheet.create({ container: {
      marginBottom: 16 },
  label: { fontSiz, e: 14,
    fontWeight: '600',
  color: theme.colors.text,
    marginBottom: 8 },
  datePickerButton: {
      flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'space-between',
  backgroundColor: theme.colors.background,
    borderRadius: 12,
  padding: 16,
    borderWidth: 1,
  borderColor: theme.colors.border
      // Add shadow for better elevation, ,
  shadowColor: theme.colors.shadow),
    shadowOffset: { width: 0, height: 1 }),
  shadowOpacity: 0.1,
    shadowRadius: 2,
  elevation: 2
    },
  datePickerButtonError: { borderColo, r: theme.colors.error,
    backgroundColor: theme.colors.errorSurface },
  dateIcon: { marginRigh, t: 12 }
    dateText: { fle, x: 1,
    fontSize: 16,
  color: theme.colors.text }
    placeholderText: { colo, r: theme.colors.textSecondary },
  errorText: {
      fontSize: 12,
  color: theme.colors.error,
    marginTop: 4,
  marginLeft: 4)
  }
  })
  export default EnhancedDatePicker