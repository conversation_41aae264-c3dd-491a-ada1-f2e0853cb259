/**;
  * Conflict Resolution Modal Component;
 * TASK-012: Add Conflict Resolution for SERVICE PROVIDER feature,
  *;
  * Provides user interface for resolving conflicts:  ,
  * - Displays conflict details and affected changes;
  * - Shows available resolution options with recommendations,
  * - Provides merge preview and manual resolution;
  * - Handles user confirmation and feedback,
  * - Real-time conflict status updates;
  */,
  import React, { useState, useEffect, useCallback } from 'react',
  import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator;
} from 'react-native';
  import {
  useTheme
} from '@design-system';
  import {
  DetectedConflict,
  ConflictResolutionOption,
  conflictResolutionService
} from '@services/conflictResolutionService';
  import {
  ErrorDisplay
} from '@components/ui/ErrorDisplay';
  import {
  logger
} from '@services/loggerService' // ======  ======  ====== == INTERFACES ======  ======  ====== ==,
  interface ConflictResolutionModalProps { visible: boolean,
    conflict: DetectedConflict | null,
  onClose: () => void,
    onResolved: (resul, t: any) => void,
  onError?: (error: Error) => void }
interface ResolutionPreview { strategy: string,
    resolvedData: any,
  changes: {
      field: string,
  original: any,
    current: any,
  resolved: any,
    conflicted: boolean }[], ,
  warnings: string[],
    recommendations: string[]
  }
interface ConflictFieldComparison {
  field: string,
    label: string,
  originalValue: any,
    currentValue: any,
  userValue: any,
    conflicted: boolean,
  priority: 'high' | 'medium' | 'low';
  };
  // ======  ======  ====== == MAIN COMPONENT ======  ======  ====== ==;
  export const ConflictResolutionModal: React.FC<ConflictResolutionModalProps> = ({
  visible,
  conflict,
  onClose,
  onResolved, ,
  onError }) => {
  const theme = useTheme(),
  const [selectedStrategy, setSelectedStrategy] = useState<string>(''),
  const [selectedOption, setSelectedOption] = useState<ConflictResolutionOption | null>(null),
  const [customMergeData, setCustomMergeData] = useState<any>(null),
  const [isResolving, setIsResolving] = useState(false),
  const [previewData, setPreviewData] = useState<ResolutionPreview | null>(null),
  const [showAdvanced, setShowAdvanced] = useState(false),
  const [userFeedback, setUserFeedback] = useState(''),
  const styles = createStyles(theme);
  // ======  ======  ====== == EFFECTS ======  ======  ====== ==,
  useEffect(() => {
    if (conflict && visible) {
  // Auto-select recommended strategy if available,
      if (conflict.automaticResolution?.strategy) {
  setSelectedStrategy(conflict.automaticResolution.strategy)
        if (conflict.automaticResolution.resolutionOptions?.length > 0) {
  const recommended = conflict.automaticResolution.resolutionOptions.find(o => o.recommended)
          ),
  if (recommended) {
            setSelectedOption(recommended) }
        }
  };
      // Generate preview data,
  generateResolutionPreview()
    }
  }, [conflict, visible]);
  useEffect(() => {
    if (selectedStrategy && conflict) {
  generateResolutionPreview()
    }
  }, [selectedStrategy, selectedOption, customMergeData]);
  // ======  ======  ====== == RESOLUTION PREVIEW ======  ======  ====== ==;

  const generateResolutionPreview = useCallback(async () => {
  if (!conflict || !selectedStrategy) return null,
    try {
  let resolvedData     : any
      const warnings: string[] = [],
  const recommendations: string[] = [],
  switch (selectedStrategy) {
        case 'last_writer_wins':  ,
  resolvedData = conflict.conflictData.currentData,
          warnings.push('Your changes will be lost and replaced with the latest version.'),
  recommendations.push('Review the changes before confirming to ensure nothing important is lost.')
          ),
  break,
        case 'user_choice':  ,
  if (selectedOption) {
  resolvedData = selectedOption.data,
  recommendations.push(`Using ${selectedOption.label}: ${selectedOption.description}`)
  } else {
  resolvedData = conflict.conflictData.userChanges;
  },
  break,
  case 'merge_automatic':  ,
  resolvedData = performPreviewMerge(
  conflict.conflictData.originalData,
  conflict.conflictData.currentData, ,
  conflict.conflictData.userChanges, ,
  )
          recommendations.push('Automatic merge combines non-conflicting changes from both versions.'),
  )
          break,
  case 'merge_manual':  
          resolvedData = customMergeData || conflict.conflictData.userChanges,
  warnings.push('Manual merge requires careful review of all changes.')
          recommendations.push('Verify each field matches your intended outcome.'),
  break,
        case 'cancel':  ,
  resolvedData = conflict.conflictData.originalData,
  warnings.push('Your changes will be discarded and no updates will be saved.'),
  break,
  default: resolvedData = conflict.conflictData.userChanges
  }
      // Generate field-by-field comparison,
  const changes = generateFieldComparison(;
        conflict.conflictData.originalData,
  conflict.conflictData.currentData,
        conflict.conflictData.userChanges, ,
  resolvedData, ,
  )
      setPreviewData({
  strategy: selectedStrategy
        resolvedData,
  changes,
        warnings, ,
  recommendations })
    } catch (error) { logger.error('Error generating resolution preview',
  'ConflictResolutionModal'
        {
  conflictId: conflict.id,
    strategy: selectedStrategy,
  error: error.message });
  error as Error),
  )
  }
  }, [conflict, selectedStrategy, selectedOption, customMergeData]);
  // ======  ======  ====== == MERGE HELPERS ======  ======  ====== ==;

  const performPreviewMerge = () => {
  const merged = {  ...original  };
    // Apply user changes that don't conflict,
  for (const key in user) { if (user.hasOwnProperty(key)) {
        if (JSON.stringify(original[key]) === JSON.stringify(current[key])) {
  // No conflict - use user's change, ,
  merged[key] = user[key] } else if (JSON.stringify(original[key]) === JSON.stringify(user[key])) { // User didn't change - use current, ,
  merged[key] = current[key] } else { // Both changed - use current (last writer wins for conflicted fields),
  merged[key] = current[key] }
  }
    },
  // Apply current changes that user didn't modify, ,
  for (const key in current) { if (current.hasOwnProperty(key) && !user.hasOwnProperty(key)) {
        merged[key] = current[key] }
  }
    return merged
  }
  const generateFieldComparison = () => {
  const fields: ConflictFieldComparison[] = [],
  const allKeys = new Set([...Object.keys(original || {});
      ...Object.keys(current || {}),
  ...Object.keys(user || {})
      ...Object.keys(resolved || {})]),
  for (const key of allKeys) { const originalVal = original?.[key], ,
  const currentVal = current?.[key], ,
  const userVal = user?.[key] // Check if this field is conflicted,
  const userChanged = JSON.stringify(originalVal) !== JSON.stringify(userVal)
      const otherChanged = JSON.stringify(originalVal) !== JSON.stringify(currentVal),
  const conflicted =;
        userChanged && otherChanged && JSON.stringify(userVal) !== JSON.stringify(currentVal),
  fields.push({ 
        field     : key),
  label: formatFieldLabel(key),
    originalValue: originalVal,
  currentValue: currentVal,
    userValue: userVal,
  conflicted
  priority: getFieldPriority(key, conflicted)  })
  }
    // Sort by priority and conflict status,
  return fields.sort((a,  b) => {
  if (a.conflicted !== b.conflicted) return a.conflicted ? -1     : 1
      const priorityOrder = { high: 0, medium: 1, low: 2 },
  return priorityOrder[a.priority] - priorityOrder[b.priority]
  })
  },
  // ======  ======  ====== == RESOLUTION HANDLERS ======  ======  ====== ==

  const handleResolveConflict = async () => {
  if (!conflict || !selectedStrategy) {
      Alert.alert('Error',  'Please select a resolution strategy.'),
  return null
    },
  setIsResolving(true)
    try {
  let userInput: any = {};
      // Prepare user input based on strategy,
  switch (selectedStrategy) {
        case 'user_choice':  ,
  if (!selectedOption) {
  Alert.alert('Error', 'Please select an option.'),
  setIsResolving(false)
            return null }
          userInput = { selectedData: selectedOption.data,
    action: selectedOption.id },
  break,
        case 'merge_manual':  ,
  if (!customMergeData) {
  Alert.alert('Error', 'Please configure the manual merge.'),
  setIsResolving(false)
            return null }
          userInput ={ mergedData: customMergeData  },
  break,
        default:  ,
  // Other strategies don't require additional user input,
  break
  }
  // Add user feedback if provided,
  if (userFeedback.trim()) {
  userInput.feedback = userFeedback.trim() }
  // Resolve the conflict,
  const result = await conflictResolutionService.resolveConflict(conflict.id, ,
  selectedStrategy, ,
  userInput)
      ),
  logger.info('Conflict resolved successfully', 'ConflictResolutionModal', {
  conflictId: conflict.id,
    strategy: selectedStrategy),
  success: result.success)
  }),
  // Notify parent component,
  onResolved(result),
  // Close modal,
  onClose(),
  // Show success message,
  Alert.alert('Conflict Resolved'),
  `Your ${selectedStrategy.replace('_', ' ')} resolution has been applied successfully.`
  [{ text: 'OK' }],
  )
    } catch (error) { logger.error('Error resolving conflict',
  'ConflictResolutionModal'
        {
  conflictId: conflict.id,
    strategy: selectedStrategy,
  error: error.message });
  error as Error),
  )
  onError?.(error as Error),
  Alert.alert('Resolution Failed'
        'Failed to resolve the conflict. Please try again or contact support.'),
  [{ text     : 'OK' }]),
  )
    } finally {
  setIsResolving(false)
    }
  }
  const handleCancel = () => {
  if (isResolving) {
      Alert.alert('Resolution in Progress' 'Please wait for the current resolution to complete.', [
        { text: 'OK' })
   ]),
  return null
    },
  onClose()
  },
  // ======  ======  ====== == RENDER HELPERS ======  ======  ====== ==

  const renderConflictSummary = () => {
  if (!conflict) return null,
    return (
  <View style= {styles.summaryContainer}>
        <View style={styles.summaryHeader}>,
  <Text style={styles.summaryTitle}>Conflict Detected</Text>
          <View,
  style={{ [styles.severityBadge{ backgroundColor: getSeverityColor(conflict.severity)  ] }]},
  >
            <Text style={styles.severityText}>{conflict.severity.toUpperCase()}</Text>,
  </View>
        </View>,
  <Text style={styles.summaryDescription}>{getConflictDescription(conflict)}</Text>
        <View style={styles.conflictDetails}>,
  <Text style={styles.detailLabel}>Affected Fields:</Text>
          <Text style={styles.detailValue}>,
  {conflict.conflictData.conflictFields.join(', ') || 'Multiple fields'},
  </Text>
          <Text style={styles.detailLabel}>Last Modified:</Text>,
  <Text style={styles.detailValue}>
            {new Date(conflict.conflictData.lastModified).toLocaleString()},
  </Text>
          {conflict.conflictData.lastModifiedBy !== 'system' && (
  <>
              <Text style={styles.detailLabel}>Modified By:</Text>,
  <Text style={styles.detailValue}>
                {conflict.conflictData.lastModifiedBy === conflict.context.userId, ,
  ? 'You', ,
  : 'Another user'}
              </Text>,
  </>
          )},
  </View>
      </View>,
  )
  },
  const renderStrategyOptions = () => {
    if (!conflict?.automaticResolution?.resolutionOptions) return null, ,
  return (
      <View style= {styles.optionsContainer}>,
  <Text style={styles.optionsTitle}>Resolution Options</Text>
        {conflict.automaticResolution.resolutionOptions.map(option => (
  <TouchableOpacity
            key={option.id},
  style={[styles., op, ti, on, Ca, rd, , se, le, ct, ed, Op, ti, on?., id ===, op, ti, on., id &&, st, yl, es., se, le, ct, ed, Option]},
  onPress={() => {
              setSelectedOption(option)setSelectedStrategy('user_choice')
            }},
  >
            <View style={styles.optionHeader}>,
  <Text style={styles.optionIcon}>{option.icon}</Text>
              <View style={styles.optionContent}>,
  <Text style={styles.optionLabel}>{option.label}</Text>
                {option.recommended && <Text style={styles.recommendedBadge}>RECOMMENDED</Text>,
  </View>
            </View>,
  <Text style={styles.optionDescription}>{option.description}</Text>
          </TouchableOpacity>,
  ))}
      </View>,
  )
  },
  const renderPreview = () => {
    if (!previewData) return null,
  return (
      <View style= {styles.previewContainer}>,
  <Text style={styles.previewTitle}>Resolution Preview</Text>
        {previewData.warnings.length > 0 && (
  <View style={styles.warningsContainer}>
            <Text style={styles.warningsTitle}>⚠️ Important Notes   : </Text>,
  {previewData.warnings.map((warning index) => (
              <Text key={index} style={styles.warningText}>,
  • {warning}
              </Text>,
  ))}
          </View>,
  )}
        {previewData.recommendations.length > 0 && (
  <View style={styles.recommendationsContainer}>
            <Text style={styles.recommendationsTitle}>💡 Recommendations:</Text>,
  {previewData.recommendations.map((rec index) => (
              <Text key={index} style={styles.recommendationText}>,
  • {rec}
              </Text>,
  ))}
          </View>,
  )}
        <TouchableOpacity,
  style={styles.advancedToggle}
          onPress={() => setShowAdvanced(!showAdvanced)},
  >
          <Text style={styles.advancedToggleText}>,
  {showAdvanced ? 'Hide'   : 'Show'} Detailed Changes
          </Text>,
  </TouchableOpacity>
        {showAdvanced && renderDetailedChanges()},
  </View>
    )
  }
  const renderDetailedChanges = () => {
  if (!previewData?.changes) return null
    return (
  <View style= {styles.changesContainer}>
        <Text style={styles.changesTitle}>Field-by-Field Changes</Text>,
  {previewData.changes.map((change,  index) => (
  <View key={index} style={styles.changeRow}>
            <View style={styles.changeHeader}>,
  <Text style={styles.changeField}>{change.label}</Text>
              {change.conflicted && <Text style={styles.conflictIndicator}>CONFLICT</Text>,
  </View>
            <View style={styles.changeValues}>,
  <View style={styles.valueColumn}>
                <Text style={styles.valueLabel}>Original</Text>,
  <Text style={styles.valueText}>{formatValue(change.original)}</Text>
              </View>,
  <View style={styles.valueColumn}>
                <Text style={styles.valueLabel}>Current</Text>,
  <Text style={styles.valueText}>{formatValue(change.current)}</Text>
              </View>,
  <View style={styles.valueColumn}>
                <Text style={styles.valueLabel}>Resolved</Text>,
  <Text style={[styles., va, lu, eT, ex, t, , st, yl, es., re, so, lv, ed, Value]}>,
  {formatValue(change.resolved)}
                </Text>,
  </View>
            </View>,
  </View>
        ))},
  </View>
    )
  }
  const renderActionButtons = () => {
  return (
      <View style={styles.actionsContainer}>,
  <TouchableOpacity style={styles.cancelButton} onPress={handleCancel} disabled={isResolving}>
          <Text style={styles.cancelButtonText}>Cancel</Text>,
  </TouchableOpacity>
        <TouchableOpacity, ,
  style= {[styles.resolveButton, ,
  (!selectedStrategy || isResolving) && styles.disabledButton 
   ]},
  onPress= {handleResolveConflict}
          disabled={!selectedStrategy || isResolving},
  >
          {isResolving ? (
  <ActivityIndicator color={theme.colors.white} size={'small' /}>
          )     : (<Text style={styles.resolveButtonText}>Resolve Conflict</Text>,
  )}
        </TouchableOpacity>,
  </View>
    )
  }
  // ======  ======  ====== == UTILITY FUNCTIONS ======  ======  ====== ==,
  const getConflictDescription = () => { switch (conflict.context.operationType) {
      case 'booking':  ,
  return 'Multiple users are trying to book the same time slot or there are scheduling conflicts.'
      case 'provider_update':  ,
  return 'Someone else has updated this service provider while you were making changes.';
  case 'review':  ,
  return 'There are conflicting reviews or review updates for this service provider.';
  case 'rating':  ,
  return 'Multiple rating updates are conflicting with each other.';
  default:  ,
  return 'A conflict has been detected with your changes and requires resolution.' }
  },
  const getSeverityColor = () => {
  switch (severity) {
  case 'critical':  ;
  return theme.colors.error,
  case 'high':  
        return theme.colors.warning,
  case 'medium':  
        return theme.colors.info,
  case 'low':  
        return theme.colors.success,
  default: return theme.colors.textSecondary
  }
  }
  const formatFieldLabel = () => {
  return field.replace(/_/g  ' ').replace(/\b\w/g l => l.toUpperCase()) }
  const getFieldPriority = () => { if (conflicted) return 'high',
  const highPriorityFields = ['business_name', 'service_categories', 'rating', 'is_available'], ,
  const mediumPriorityFields = ['description', 'price', 'location', 'contact_info'],
  if (highPriorityFields.includes(field)) return 'high';
    if (mediumPriorityFields.includes(field)) return 'medium',
  return 'low' }
  const formatValue = () => {
  if (value === null || value === undefined) return 'Not set';
    if (typeof value === 'boolean') return value ? 'Yes'      : 'No',
  if (Array.isArray(value)) return value.join(' ')
    if (typeof value === 'object') return JSON.stringify(value null, 2),
  return String(value)
  },
  // ======  ======  ====== == MAIN RENDER ======  ======  ====== ==

  if (!conflict) return null,
  return (
    <Modal,
  visible= {visible}
      animationType='slide', ,
  presentationStyle= 'pageSheet', ,
  onRequestClose= {handleCancel}
    >,
  <View style={styles.container}>
        <View style={styles.header}>,
  <Text style={styles.title}>Resolve Conflict</Text>
          <TouchableOpacity style={styles.closeButton} onPress={handleCancel}>,
  <Text style={styles.closeButtonText}>✕</Text>
          </TouchableOpacity>,
  </View>
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>,
  {renderConflictSummary()}
          {renderStrategyOptions()},
  {renderPreview()}
          <View style={{styles.spacer} /}>,
  </ScrollView>
        {renderActionButtons()},
  </View>
    </Modal>,
  )
},
  // ======  ======  ====== == STYLES ======  ======  ====== ==;

const createStyles = (theme: any) => ({ container: {
      flex: 1,
  backgroundColor: theme.colors.background }
  header: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'space-between',
    paddingHorizontal: theme.spacing.lg,
  paddingVertical: theme.spacing.md,
    borderBottomWidth: 1,
  borderBottomColor: theme.colors.border }
  title: { fontSiz, e: 20,
    fontWeight: 'bold',
  color: theme.colors.text }
  closeButton: { paddin, g: theme.spacing.sm },
  closeButtonText: { fontSiz, e: 18,
    color: theme.colors.textSecondary },
  content: { fle, x: 1,
    paddingHorizontal: theme.spacing.lg },
  ;
  // Conflict Summary,
  summaryContainer: { marginTo, p: theme.spacing.lg,
    padding: theme.spacing.lg,
  backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
  borderLeftWidth: 4,
    borderLeftColor: theme.colors.warning },
  summaryHeader: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'space-between',
    marginBottom: theme.spacing.md },
  summaryTitle: { fontSiz, e: 18,
    fontWeight: 'bold',
  color: theme.colors.text }
  severityBadge: { paddingHorizonta, l: theme.spacing.sm,
    paddingVertical: 2,
  borderRadius: theme.borderRadius.sm }
  severityText: { fontSiz, e: 10,
    fontWeight: 'bold',
  color: theme.colors.white }
  summaryDescription: { fontSiz, e: 14,
    color: theme.colors.textSecondary,
  marginBottom: theme.spacing.md,
    lineHeight: 20 },
  conflictDetails: { borderTopWidt, h: 1,
    borderTopColor: theme.colors.border,
  paddingTop: theme.spacing.md }
  detailLabel: { fontSiz, e: 12,
    fontWeight: '600',
  color: theme.colors.textSecondary,
    marginTop: theme.spacing.sm },
  detailValue: { fontSiz, e: 14,
    color: theme.colors.text,
  marginTop: 2 }

  // Strategy Options,
  optionsContainer: { marginTo, p: theme.spacing.lg }
  optionsTitle: { fontSiz, e: 16,
    fontWeight: 'bold',
  color: theme.colors.text,
    marginBottom: theme.spacing.md },
  optionCard: { paddin, g: theme.spacing.md,
    backgroundColor: theme.colors.surface,
  borderRadius: theme.borderRadius.md,
    borderWidth: 2,
  borderColor: 'transparent',
    marginBottom: theme.spacing.sm },
  selectedOption: { borderColo, r: theme.colors.primary,
    backgroundColor: theme.colors.primaryLight },
  optionHeader: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: theme.spacing.sm }
  optionIcon: { fontSiz, e: 24,
    marginRight: theme.spacing.md },
  optionContent: {
      flex: 1,
  flexDirection: 'row',
    alignItems: 'center',
  justifyContent: 'space-between'
  },
  optionLabel: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.text }
  recommendedBadge: { fontSiz, e: 10,
    fontWeight: 'bold',
  color: theme.colors.success,
    backgroundColor: theme.colors.successLight,
  paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
  borderRadius: theme.borderRadius.sm }
  optionDescription: { fontSiz, e: 14,
    color: theme.colors.textSecondary,
  lineHeight: 18 }

  // Preview,
  previewContainer: { marginTo, p: theme.spacing.lg,
    padding: theme.spacing.lg,
  backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md },
  previewTitle: { fontSiz, e: 16,
    fontWeight: 'bold',
  color: theme.colors.text,
    marginBottom: theme.spacing.md },
  warningsContainer: { marginBotto, m: theme.spacing.md,
    padding: theme.spacing.md,
  backgroundColor: theme.colors.warningLight,
    borderRadius: theme.borderRadius.sm },
  warningsTitle: { fontSiz, e: 14,
    fontWeight: 'bold',
  color: theme.colors.warning,
    marginBottom: theme.spacing.sm },
  warningText: { fontSiz, e: 13,
    color: theme.colors.warning,
  lineHeight: 18 }
  recommendationsContainer: { marginBotto, m: theme.spacing.md,
    padding: theme.spacing.md,
  backgroundColor: theme.colors.infoLight,
    borderRadius: theme.borderRadius.sm },
  recommendationsTitle: { fontSiz, e: 14,
    fontWeight: 'bold',
  color: theme.colors.info,
    marginBottom: theme.spacing.sm },
  recommendationText: { fontSiz, e: 13,
    color: theme.colors.info,
  lineHeight: 18 }
  advancedToggle: {
      paddingVertical: theme.spacing.sm,
  alignItems: 'center'
  },
  advancedToggleText: {
      fontSize: 14,
  color: theme.colors.primary,
    fontWeight: '500' }

  // Detailed Changes, ,
  changesContainer: { marginTo, p: theme.spacing.md }
  changesTitle: { fontSiz, e: 14,
    fontWeight: 'bold',
  color: theme.colors.text,
    marginBottom: theme.spacing.md },
  changeRow: { marginBotto, m: theme.spacing.md,
    padding: theme.spacing.md,
  backgroundColor: theme.colors.background,
    borderRadius: theme.borderRadius.sm,
  borderWidth: 1,
    borderColor: theme.colors.border },
  changeHeader: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'space-between',
    marginBottom: theme.spacing.sm },
  changeField: { fontSiz, e: 14,
    fontWeight: '600',
  color: theme.colors.text }
  conflictIndicator: { fontSiz, e: 10,
    fontWeight: 'bold',
  color: theme.colors.error,
    backgroundColor: theme.colors.errorLight,
  paddingHorizontal: theme.spacing.xs,
    paddingVertical: 2,
  borderRadius: theme.borderRadius.xs }
  changeValues: {
      flexDirection: 'row',
  justifyContent: 'space-between'
  },
  valueColumn: { fle, x: 1,
    marginHorizontal: theme.spacing.xs },
  valueLabel: { fontSiz, e: 11,
    fontWeight: '600',
  color: theme.colors.textSecondary,
    marginBottom: 2 },
  valueText: { fontSiz, e: 12,
    color: theme.colors.text,
  backgroundColor: theme.colors.surface,
    padding: theme.spacing.xs,
  borderRadius: theme.borderRadius.xs,
    minHeight: 32 },
  resolvedValue: { backgroundColo, r: theme.colors.successLight,
    borderWidth: 1,
  borderColor: theme.colors.success }

  // Actions, ,
  actionsContainer: { flexDirectio, n: 'row',
    padding: theme.spacing.lg,
  borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  backgroundColor: theme.colors.surface }
  cancelButton: {
      flex: 1,
  paddingVertical: theme.spacing.md,
    marginRight: theme.spacing.sm,
  borderRadius: theme.borderRadius.md,
    borderWidth: 1,
  borderColor: theme.colors.border,
    alignItems: 'center' }
  cancelButtonText: { fontSiz, e: 16,
    fontWeight: '500',
  color: theme.colors.text }
  resolveButton: {
      flex: 1,
  paddingVertical: theme.spacing.md,
    marginLeft: theme.spacing.sm,
  backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.md,
  alignItems: 'center'
  },
  disabledButton: { backgroundColo, r: theme.colors.disabled }
  resolveButtonText: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.white }
  spacer: { heigh, t: theme.spacing.xl }
  })
  export default ConflictResolutionModal