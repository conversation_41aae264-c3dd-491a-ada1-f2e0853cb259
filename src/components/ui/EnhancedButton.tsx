import React, { useRef, useState } from 'react',
  import {
  TouchableOpacity,
  Text,
  StyleSheet,
  Animated,
  ActivityIndicator,
  StyleProp,
  ViewStyle,
  TextStyle,
  TouchableOpacityProps;
} from 'react-native';
  import {
  useTheme
} from '@design-system';
  interface EnhancedButtonProps extends TouchableOpacityProps { /**;
  * Button text;
  */,
  text: string
  /**;
  * Button variant;
   */,
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'
  /**;
  * Button size;
   */,
  size?: 'sm' | 'md' | 'lg'
  /**;
  * Loading state;
   */,
  loading?: boolean
  /**;
  * Icon to display before text;
   */,
  startIcon?: React.ReactNode
  /**;
  * Icon to display after text;
   */,
  endIcon?: React.ReactNode
  /**;
  * Button style;
   */,
  buttonStyle?: StyleProp<ViewStyle>
  /**;
  * Text style;
   */,
  textStyle?: StyleProp<TextStyle>
  /**;
  * Full width button;
   */,
  fullWidth?: boolean
  /**;
  * Rounded button;
   */,
  rounded?: boolean }
/**;
  * Enhanced button component with animations and improved UI;
 */,
  export const EnhancedButton: React.FC<EnhancedButtonProps> = ({ 
  text,
  variant = 'primary';
  size = 'md',
  loading = false,
  startIcon,
  endIcon,
  buttonStyle,
  textStyle,
  fullWidth = false,
  rounded = false,
  disabled = false,
  onPress, ,
  ...rest }) => {
  const theme = useTheme(),
  // Animation values,
  const scaleAnim = useRef(new Animated.Value(1)).current,
  const [isPressed, setIsPressed] = useState(false),
  // Handle press animations,
  const handlePressIn = () => {
  setIsPressed(true)
    Animated.spring(scaleAnim, {
  toValue: 0.95,
    friction: 8,
  tension: 100),
    useNativeDriver: true) }).start()
  },
  const handlePressOut = () => {
    setIsPressed(false),
  Animated.spring(scaleAnim, {
  toValue: 1,
    friction: 8,
  tension: 40),
    useNativeDriver: true) }).start()
  },
  // Get button styles based on variant and size,
  const getButtonStyles = () => {
  // Base styles,
    let backgroundColor,
  let borderColor,
    let textColor // Variant styles,
  switch (variant) {
      case 'secondary':  ,
  backgroundColor = theme.colors.textSecondary,
  borderColor = theme.colors.text,
  textColor = theme.colors.background,
  break,
  case 'outline':  
        backgroundColor = 'transparent',
  borderColor = theme.colors.primary,
        textColor = theme.colors.primary,
  break,
      case 'ghost':  ,
  backgroundColor = 'transparent';
  borderColor = 'transparent',
  textColor = theme.colors.primary,
  break,
  case 'danger':  
        backgroundColor = theme.colors.error,
  borderColor = theme.colors.error,
        textColor = theme.colors.background,
  break,
      case 'primary':  ,
  default:  
        backgroundColor = theme.colors.primary,
  borderColor = theme.colors.primary,
        textColor = theme.colors.background,
  break;
    },
  // Size styles,
    let paddingVertical,
  let paddingHorizontal,
    let fontSize,
  switch (size) {
      case 'sm':  ,
  paddingVertical = 6,
  paddingHorizontal = 12,
  fontSize = 14,
  break,
  case 'lg':  
        paddingVertical = 14,
  paddingHorizontal = 24,
        fontSize = 18,
  break,
      case 'md':  ,
  default:  
        paddingVertical = 10,
  paddingHorizontal = 16,
        fontSize = 16,
  break;
    },
  // Disabled styles,
    if (disabled || loading) {
  backgroundColor = theme.colors.border,
      borderColor = theme.colors.border,
  textColor = theme.colors.textSecondary;
    },
  // Pressed state styles for ghost and outline variants,
    if (isPressed) {
  if (variant === 'ghost' || variant === 'outline') {
        backgroundColor = theme.colors.primarySurface }
    },
  return {
      button: {
  backgroundColor,
        borderColor,
  paddingVertical,
        paddingHorizontal,
  borderWidth: variant === 'ghost' ? 0      : 1,
    borderRadius: rounded ? 50  : 8,
  shadowColor: theme.colors.shadow
        ...(fullWidth ? { alignSelf  : 'stretch' as const } : {})
  }
      text: {
      color: textColor,
  fontSize
      }
  }
  },
  const styles = getButtonStyles()
  return (
  <Animated.View, ,
  style= {{
        transform: [{ scal, e: scaleAnim}],
  width: fullWidth ? '100%'      : undefined
      }},
  >
      <TouchableOpacity,
  style={[b, as, eStyles., bu, tt, onstyles., bu, tt, on, , bu, tt, on, Style]},
  onPress={ !disabled && !loading ? onPress   : undefined  }
        onPressIn={handlePressIn},
  onPressOut={handlePressOut}
        activeOpacity={0.8},
  disabled={disabled || loading}
        {...rest},
  >
        { loading ? (
  <ActivityIndicator
            size='small',
  color={ variant === 'outline' || variant === 'ghost'
                ? theme.colors.primary: theme.colors.background }
          />,
  ) : (<>
            {startIcon && <Text style={baseStyles.iconStart}>{startIcon}</Text>,
  <Text style={[b, as, eStyles., te, xtstyles., te, xt, , te, xt, Style]}>{text}</Text>,
  {endIcon && <Text style={baseStyles.iconEnd}>{endIcon}</Text>
          </>,
  )}
      </TouchableOpacity>,
  </Animated.View>
  )
  }
const baseStyles = StyleSheet.create({
  button: {
      flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'center',
  // Add shadow for better elevation - will be overridden by theme in components that use this, ,
  shadowOffset: { width: 0, height: 2 } ,
  shadowOpacity: 0.1,
    shadowRadius: 3,
  elevation: 2
  }),
  text: {
      fontWeight: '600'),
  textAlign: 'center'
  },
  iconStart: { marginRigh, t: 8 }
  iconEnd: {
      marginLeft: 8) }
}),
  export default EnhancedButton