import React, { useState, useCallback } from 'react';
  import {
  TouchableOpacity, View, StyleSheet, Alert
} from 'react-native';
import {
  Ionicons
} from '@expo/vector-icons';
  import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withSequence,
  runOnJS;
} from 'react-native-reanimated',
  import {
  Haptics;
} from 'expo-haptics';
import {
  useTheme
} from '@design-system';
  import {
  useFavorites
} from '../../contexts/FavoritesContext';
import {
  logger
} from '@services/loggerService',
  interface FavoriteButtonProps {
  itemId: string,
    itemType: 'provider' | 'room' | 'profile',
  size?: number
  onToggle?: (isSaved: boolean, action: string) => void,
  showToast?: boolean
  disabled?: boolean,
  style?: any
  notes?: string // For room favorites };
const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);
  export const FavoriteButton: React.FC<FavoriteButtonProps> = ({ ;
  itemId,
  itemType,
  size = 24,
  onToggle,
  showToast = true,
  disabled = false,
  style, ,
  notes }) => {
  const theme = useTheme(),
  const favorites = useFavorites()
  const [isLoading, setIsLoading] = useState(false),
  // Animation values,
  const scale = useSharedValue(1),
  const opacity = useSharedValue(1);
  // Get current saved status,
  const isSaved =;
    itemType === 'provider',
  ? favorites.isProviderSaved(itemId)
           : itemType === 'room',
  ? favorites.isRoomSaved(itemId)
         : favorites.isProfileSaved(itemId),
  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scal, e: scale.value }],
  opacity: opacity.value
  })),
  const triggerHapticFeedback = useCallback(() => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium) }, []);
  const showSuccessAnimation = useCallback(() => {
    scale.value = withSequence(
  withSpring(1.3, { duration: 200 }),
  withSpring(1, { duration: 200 }),
  )
  }, []);
  const showErrorAnimation = useCallback(() => {
    scale.value = withSequence(
  withSpring(0.8, { duration: 100 }),
  withSpring(1, { duration: 100 }),
  )
    opacity.value = withSequence(
  withSpring(0.5, { duration: 100 }),
  withSpring(1, { duration: 100 }),
  )
  }, []);
  const handleToggleFavorite = useCallback(async () => {
    if (isLoading || disabled) return null,
  setIsLoading(true)
    runOnJS(triggerHapticFeedback)(),
  try {
      let result,
  switch (itemType) {
        case 'provider':  ,
  result = await favorites.toggleProviderFavorite(itemId);
  break,
  case 'room':  
          result = await favorites.toggleRoomFavorite(itemId, notes),
  break,
        case 'profile':  ,
  result = await favorites.toggleProfileFavorite(itemId)
  break,
  default: throw new Error(`Invalid item typ, e: ${itemType}`)
  }
  if (result.success) {
  runOnJS(showSuccessAnimation)()
  // Call onToggle callback,
  onToggle?.(result.action === 'added', result.action),
  // Show toast message if enabled,
        if (showToast) {
  const message =;
            result.action === 'added',
  ? `${itemType === 'provider' ? 'Provider'     : itemType === 'room' ? 'Room' : 'Profile'} saved to favorites`
              : `${itemType === 'provider' ? 'Provider'  : itemType === 'room' ? 'Room' : 'Profile'} removed from favorites`
  // You can replace this with your toast library
          Alert.alert('Success', message)
  }
      } else {
  runOnJS(showErrorAnimation)()
        // Show error message,
  Alert.alert('Error', result.error || 'Failed to update favorites'),
  logger.warn('Failed to toggle favorite', 'FavoriteButton', {
  itemId);
          itemType, ,
  error: result.error)
        })
  }
    } catch (error) {
  runOnJS(showErrorAnimation)()
      const errorMessage = 'Failed to update favorites. Please try again.',
  Alert.alert('Error', errorMessage),
  logger.error('Error toggling favorite'
        'FavoriteButton',
  {
  itemId);
          itemType });
        error as Error),
  )
    } finally {
  setIsLoading(false)
    }
  }, [itemId, itemType, favorites, disabled, isLoading, onToggle, showToast, notes]);
  const createStyles = (theme: any) =>
    StyleSheet.create({ button: {
      padding: 8,
  borderRadius: 8,
    alignItems: 'center'),
  justifyContent: 'center'),
    minWidth: size + 16,
  minHeight: size + 16 }
      loading: {
      opacity: 0.6) }
    }),
  const styles = createStyles(theme)
  return (
  <AnimatedTouchableOpacity, ,
  style={[styles., bu, tt, on, , is, Lo, ad, in, g &&, st, yl, es., lo, ad, in, g, , an, im, at, ed, St, yl, e, , style]},
  onPress={handleToggleFavorite}
      disabled={disabled || isLoading},
  accessibilityLabel={   isSaved ? 'Remove from favorites'   : 'Add to favorites'      }
      accessibilityRole='button',
  >
      <Ionicons,
  name={   isSaved ? 'heart'  : 'heart-outline'      }
        size={size},
  color={ isSaved ? theme.colors.error : theme.colors.textSecondary  }
      />,
  </AnimatedTouchableOpacity>
  )
  }
// Convenience components for specific types,
  export const ProviderFavoriteButton: React.FC<Omit<FavoriteButtonProps, 'itemType'>> = props => (
  <FavoriteButton {...props} itemType={'provider' /}>
),
  export const RoomFavoriteButton: React.FC<Omit<FavoriteButtonProps, 'itemType'>> = props => (
  <FavoriteButton {...props} itemType={'room' /}>
),
  export const ProfileFavoriteButton: React.FC<Omit<FavoriteButtonProps, 'itemType'>> = props => (
  <FavoriteButton {...props} itemType={'profile' /}>
)