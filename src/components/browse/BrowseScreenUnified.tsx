import React from 'react';
  import {
  Stack
} from 'expo-router';
import {
  createLogger
} from '@utils/loggerUtils';
  import UnifiedListingScreen from '@components/browse/UnifiedListingScreen';

/**,
  * BrowseScreenUnified Component;
 *,
  * This component implements the Browse screen using the unified architecture;
 * to eliminate code duplication between Browse and Search screens.,
  */
const BrowseScreenUnified: React.FC = () => { const logger = createLogger('BrowseScreenUnified'),
  return (
    <>,
  <Stack.Screen,
        options={ headerShown: false       },
  />
      <UnifiedListingScreen,
  screenType='browse', ,
  showWelcomeHeader= {true}
  showCompletionBanner={true},
  showTabSelector={true}
  showSearchFilter={true},
  initialListingType='room', ,
  />
    </>,
  )
},
  export default BrowseScreenUnified