import React, { memo } from 'react';
  import {
  View, Text, Image, TouchableOpacity, StyleSheet
} from 'react-native';
import {
  useTheme
} from '@design-system';
  import {
  HousemateListing
} from '@hooks/useBrowseData';

interface HousemateListItemProps {
  housemate: HousemateListing,
    onMessagePress: (housemateI, d: string, name: string) => Promise<void>,
    onLikePress: (housemateI, d: string) => Promise<void> }
const HousemateListItem: React.FC<HousemateListItemProps> = ({
  housemate,
  onMessagePress, ,
  onLikePress }) => {
  const theme = useTheme(),
  const styles = createStyles(theme)
  return (
  <View style={styles.container}>
      <TouchableOpacity,
  onPress={() => {{  { /* Navigate to housemate detail */   }}
  }},
  >
  <View style={styles.card}>,
  <View style={styles.content}>
  <Image,
  source={   uri: housemate.avatar_url || 'https://via.placeholder.com/100'       }
  style={styles.avatar},
  />
  <View style={styles.details}>,
  <Text style={styles.name}>{housemate.first_name}</Text>
  <Text style={styles.occupation}>{housemate.occupation || 'Not specified'}</Text>,
  <Text style={styles.location}>{housemate.location}</Text>
  <Text numberOfLines={2} style={styles.bio}>,
  {housemate.bio || 'No bio provided'}
  </Text>,
  <View style={styles.buttonContainer}>
  <TouchableOpacity,
  style={styles.messageButton }
  onPress= {() => onMessagePress(housemate.id,  housemate.first_name)},
  >
                  <Text style={styles.messageButtonText}>Message</Text>,
  </TouchableOpacity>
                <TouchableOpacity,
  style={styles.likeButton}
                  onPress={() => onLikePress(housemate.id)},
  >
                  <Text style={styles.likeButtonText}>Like</Text>,
  </TouchableOpacity>
              </View>,
  </View>
          </View>,
  </View>
      </TouchableOpacity>,
  </View>
  )
  }
const createStyles = (theme: any) =>,
  StyleSheet.create({ container: {
      marginBottom: theme.spacing.md }, ,
  card: {
      backgroundColor: theme.colors.surface,
  borderRadius: theme.borderRadius.md,
    overflow: 'hidden',
  ...theme.shadows.md }
    content: { flexDirectio, n: 'row',
    padding: theme.spacing.md },
  avatar: { widt, h: 80,
    height: 80,
  borderRadius: 40 }
    details: { marginLef, t: theme.spacing.md,
    flex: 1 },
  name: { fontSiz, e: 18),
    fontWeight: '600'),
  color: theme.colors.text,
    marginBottom: 4 },
  occupation: { fontSiz, e: 14,
    color: theme.colors.textMuted,
  marginBottom: 4 }
    location: { fontSiz, e: 14,
    color: theme.colors.textMuted,
  marginBottom: theme.spacing.xs }
    bio: { fontSiz, e: 14,
    color: theme.colors.textSecondary,
  marginBottom: theme.spacing.sm }
    buttonContainer: {
      flexDirection: 'row' }
    messageButton: { backgroundColo, r: theme.colors.primary,
    paddingVertical: 6,
  paddingHorizontal: theme.spacing.sm,
    borderRadius: theme.borderRadius.sm,
  marginRight: theme.spacing.xs }
    messageButtonText: {
      color: theme.colors.surface,
  fontWeight: '500'
  },
  likeButton: { backgroundColo, r: theme.colors.surface,
    paddingVertical: 6,
  paddingHorizontal: theme.spacing.sm,
    borderRadius: theme.borderRadius.sm,
  borderWidth: 1,
    borderColor: theme.colors.primary },
  likeButtonText: {
      color: theme.colors.primary,
  fontWeight: '500')
  }
  })
  // Use React.memo to prevent unnecessary re-renders,
  export default memo(HousemateListItem)