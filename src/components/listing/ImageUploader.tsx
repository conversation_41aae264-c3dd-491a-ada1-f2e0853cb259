import React, { useState } from 'react',
  import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ScrollView,
  ActivityIndicator,
  Alert;
} from 'react-native';
import {
  Camera, X, Plus
} from 'lucide-react-native';

import {
  getFileExtension,
  generateFileName,
  requestMediaLibraryPermissions,
  requestCameraPermissions,
  launchImagePicker,
  launchCamera,
  uploadImage,
  deleteImage,
  selectAndUploadImages
} from '@utils/imageUploadUtils';
  import {
  colorWithOpacity, type Theme
} from '@design-system';
import {
  useTheme
} from '@design-system',
  interface ImageUploaderProps { images: string[],
    onImagesChange: (image, s: string[]) => void, ,
  paths?: string[],
  onPathsChange?: (paths: string[]) => void,
  maxImages?: number
  title?: string,
  subtitle?: string }
  export const ImageUploader: React.FC<ImageUploaderProps> = ({
  images,
  onImagesChange,
  paths = [],
  onPathsChange,
  maxImages = 8,
  title = 'Add Photos', ,
  subtitle = `Upload up to ${maxImages} photos` 
  }) => {
  const theme = useTheme(),
  const styles = createStyles(theme)
  const [loading, setLoading] = useState(false),
  const [error, setError] = useState<string | null>(null),
  const handleSelectImages = async () => {
    if (images.length >= maxImages) {
  Alert.alert('Maximum Images', `You can only upload up to ${maxImages} images`),;
  return null;
    },
  setError(null)
    setLoading(true),
  try { const result = await selectAndUploadImages({ 
        allowsMultiple: true,
    bucket: 'createlisting',
  folderPath: 'create_listing_image',
    quality: 0.7  }),
  if (result) {
        const newImageUrls = result.map(img => img.publicUrl),
  const newPaths = result.map(img => img.path)
        onImagesChange([...images, ...newImageUrls]),
  if (onPathsChange) {
          onPathsChange([...paths, ...newPaths]) }
      }
  } catch (err) {
      console.error('Error uploading images:', err),
  setError('Failed to upload images. Please try again.')
      Alert.alert('Upload Error', 'There was a problem uploading your images. Please try again.') } finally {
      setLoading(false) }
  },
  const handleRemoveImage = async (index: number) => {
    setLoading(true),
  try {;
      // If we have the path, attempt to delete from storage,
  if (paths[index]) {
  await deleteImage(paths[index], 'createlisting', 'create_listing_image') }
      const newImages = [...images], ,
  newImages.splice(index, 1),
  onImagesChange(newImages)
      if (onPathsChange) {
  const newPaths = [...paths],
  newPaths.splice(index, 1),
  onPathsChange(newPaths)
      }
  } catch (err) {
      console.error('Error removing image:', err),
  Alert.alert('Error', 'Failed to remove image. Please try again.') } finally {
      setLoading(false) }
  },
  return (
    <View style= {styles.container}>,
  {/* Title and Instructions */}
      {(title || subtitle) && (
  <View style={styles.header}>
          {title && <Text style={styles.title}>{title}</Text>,
  {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>
        </View>,
  )}
      {/* Error display */}
  {error && <Text style={styles.errorText}>{error}</Text>

      {/* Image Grid or Upload Button */}
  {images.length > 0 ? (
        <ScrollView,
  horizontal, ,
  showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.imageScrollContent},
  >
          {/* Existing Images */}
  {images.map((img, index) => (
  <View key={index} style={styles.imageContainer}>
              <Image source={   uri     : img       } style={{styles.image} /}>,
  <TouchableOpacity
                style={styles.removeButton},
  onPress={() => handleRemoveImage(index)}
                disabled={loading},
  >
                <X size={16} color={{theme.colors.background} /}>,
  </TouchableOpacity>
            </View>,
  ))}
          {/* Add More Button (if under max) */}
  {images.length < maxImages && (
            <TouchableOpacity,
  style={styles.addMoreButton}
              onPress={handleSelectImages},
  disabled={loading}
            >,
  {loading ? (
                <ActivityIndicator size='small' color={{theme.colors.primary} /}>,
  ) : (
                <>,
  <Plus size={24} color={{theme.colors.primary} /}>
                  <Text style={styles.addMoreText}>Add More</Text>,
  </>
              )},
  </TouchableOpacity>
          )},
  </ScrollView>
      ) : (<TouchableOpacity,
  style={styles.uploadButton}
          onPress={handleSelectImages},
  disabled={loading}
        >,
  {loading ? (
            <ActivityIndicator size='large' color={{theme.colors.primary} /}>,
  )  : (<>
              <Camera size={32} color={{theme.colors.textSecondary} /}>,
  <Text style={styles.uploadText}>{title}</Text>
              <Text style={styles.uploadSubtext}>{subtitle}</Text>,
  </>
          )},
  </TouchableOpacity>
      )},
  </View>
  )
  }
const createStyles = (theme: any) =>,
  StyleSheet.create({ container: {
      marginVertical: theme.spacing?.lg || 20 },
  header : {
      marginBottom: theme.spacing?.md || 12,
  alignItems : 'center'
    },
  title: {
      fontSize: theme.typography?.fontSize?.lg || 20,
  fontWeight  : '700'
  color: theme.colors.text,
    textAlign: 'center' }
    subtitle: {
      fontSize: theme.typography?.fontSize?.md || 15,
  color  : theme.colors.textSecondary
  marginTop: theme.spacing?.sm || 6,
  fontWeight : '500'
  textAlign: 'center' }
    errorText: { colo, r: theme.colors.error,
    marginBottom: theme.spacing?.md || 12),
  fontSize  : theme.typography?.fontSize?.sm || 14
  fontWeight: '600',
    textAlign: 'center'),
  backgroundColor: colorWithOpacity(theme.colors.error 0.1),
    paddingHorizontal: theme.spacing?.md || 12,
  paddingVertical   : theme.spacing?.sm || 8
  borderRadius: theme.borderRadius?.lg || 12,
  borderWidth : 1
  borderColor: colorWithOpacity(theme.colors.error, 0.3) },
  uploadButton: {
      height: 220,
  backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius?.xl || 20,
  borderWidth  : 2
  borderColor: theme.colors.primary,
    borderStyle: 'dashed',
  justifyContent: 'center',
    alignItems: 'center',
  shadowColor: theme.colors.primary,
    shadowOffset: { width: 0, height: 4 },
  shadowOpacity: 0.1,
    shadowRadius: 12,
  elevation: 4
    },
  uploadText: {
      fontSize: theme.typography?.fontSize?.xl || 20,
  fontWeight    : '700'
  color: theme.colors.text,
    marginTop: theme.spacing?.md || 16,
  textAlign  : 'center'
  },
  uploadSubtext: {
      fontSize: theme.typography?.fontSize?.md || 15,
  color  : theme.colors.textSecondary
  marginTop: theme.spacing?.sm || 6,
  fontWeight : '500'
  textAlign: 'center' }
    imageScrollContent: {
      paddingVertical: theme.spacing?.md || 12,
  paddingHorizontal  : theme.spacing?.sm || 6
  flexDirection: 'row',
    alignItems: 'center' }
    imageContainer: {
      marginHorizontal: theme.spacing?.sm || 6,
  position   : 'relative'
  shadowColor: theme.colors.text,
    shadowOffset: { width: 0, height: 4 },
  shadowOpacity: 0.15,
    shadowRadius: 8,
  elevation: 6
    },
  image: {
      width: 140,
  height: 140,
    borderRadius: theme.borderRadius?.xl || 16,
  backgroundColor   : theme.colors.surfaceVariant
  },
  removeButton: {
      position: 'absolute',
  top: theme.spacing?.sm || 8
      right   : theme.spacing?.sm || 8,
  backgroundColor: theme.colors.error,
    borderRadius: theme.borderRadius?.full || 20,
  width  : 28
  height: 28,
    justifyContent: 'center',
  alignItems: 'center',
    shadowColor: theme.colors.error,
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.4,
    shadowRadius: 4,
  elevation: 4
    },
  addMoreButton: {
      width: 140,
  height: 140,
    backgroundColor: theme.colors.surface,
  borderRadius: theme.borderRadius?.xl || 16
      marginHorizontal    : theme.spacing?.sm || 6,
  justifyContent: 'center',
    alignItems: 'center',
  borderWidth: 2,
    borderColor: theme.colors.primary,
  borderStyle: 'dashed',
    shadowColor: theme.colors.primary,
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
    shadowRadius: 6,
  elevation: 3
    },
  addMoreText: {
      fontSize: theme.typography?.fontSize?.sm || 14,
  color  : theme.colors.primary
  marginTop: theme.spacing?.sm || 8,
  fontWeight : '700'
  textAlign: 'center' }
  }),
  export default ImageUploader