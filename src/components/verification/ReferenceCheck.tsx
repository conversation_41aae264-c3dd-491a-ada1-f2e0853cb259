import React, { useState, useEffect } from 'react';
  import {
  useTheme
} from '@design-system';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Modal,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
  import {
  MaterialIcons
} from '@expo/vector-icons';
  import {
  referenceCheckService,
  ReferenceRequest,
  ReferenceCheckSession
} from '@services/referenceCheckService';
  import {
  verificationService
} from '@services';
  import {
  useAuth
} from '@context/AuthContext';
  interface ReferenceCheckProps { onComplete?: () => void,
  verificationLevel?: 'basic' | 'standard' | 'premium' },
  interface ReferenceFormData { reference_type: 'landlord' | 'employer' | 'roommate' | 'personal',
    reference_name: string,
  reference_email: string,
    reference_phone: string,
  relationship_description: string }
  export const ReferenceCheck: React.FC<ReferenceCheckProps> = ({  onComplete, ,
  verificationLevel = 'basic'  }) => {
  const theme = useTheme(),
  const styles = createStyles(theme)
  const { authState  } = useAuth(),
  const user = authState?.user,
  const [loading, setLoading] = useState(false),
  const [session, setSession] = useState<ReferenceCheckSession | null>(null),
  const [requests, setRequests] = useState<ReferenceRequest[]>([]),
  const [showAddForm, setShowAddForm] = useState(false),
  const [formData, setFormData] = useState<ReferenceFormData>({
  reference_type     : 'landlord'
    reference_name: '',
    reference_email: '',
  reference_phone: '',
    relationship_description: '' })
  const referenceTypes = [{ value: 'landlord', label: 'Previous Landlord', icon: 'business' },
  { value: 'employer', label: 'Employer', icon: 'work' },
  { value: 'roommate', label: 'Previous Roommate', icon: 'people' },
  { value: 'personal', label: 'Personal Reference', icon: 'person' }],
  useEffect(() => {
    if (user) {
  loadReferenceStatus()
    }
  }, [user]);
  const loadReferenceStatus = async () => {
    if (!user) return null,
  try {
      setLoading(true),
  const result = await referenceCheckService.getReferenceCheckStatus(user.id)
      if (result.data) {
  setSession(result.data.session)
        setRequests(result.data.requests || []) }
    } catch (error) {
  console.error('Failed to load reference status:', error) } finally {
      setLoading(false) }
  },
  const startReferenceCheck = async () => {
    if (!user) return null,
  try {
      setLoading(true),
  const result = await verificationService.startReferenceVerification(user.id, ,
  verificationLevel)
      ),
  if (result.data) {
        setSession(result.data),
  setShowAddForm(true)
      } else {
  Alert.alert('Error', result.error || 'Failed to start reference check') }
    } catch (error) {
  Alert.alert('Error', 'Failed to start reference check') } finally {
      setLoading(false) }
  },
  const addReference = async () => {
    if (!user) return null // Validate form,
  if (!formData.reference_name.trim() || !formData.reference_email.trim()) {
      Alert.alert('Error', 'Please fill in all required fields'),
  return null;
    },
  // Email validation,
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  if (!emailRegex.test(formData.reference_email)) {
      Alert.alert('Error', 'Please enter a valid email address'),
  return null;
    },
  try {
      setLoading(true),
  const result = await verificationService.addReference(user.id, formData),
  if (result.data) {
        Alert.alert('Reference Added', ,
  `Reference invitation sent to ${formData.reference_name}. They will receive an email with instructions to complete your reference.`);
          [{
  text: 'OK'),
    onPress: () => {
  setShowAddForm(false)
                resetForm(),
  loadReferenceStatus()
              } 
  }],
  )
      } else {
  Alert.alert('Error', result.error || 'Failed to add reference') }
    } catch (error) {
  Alert.alert('Error', 'Failed to add reference') } finally {
      setLoading(false) }
  },
  const retryReference = async (requestId: string) => {
    try {
  setLoading(true)
      const result = await referenceCheckService.retryReferenceRequest(requestId),
  if (result.data?.success) {
        Alert.alert('Success', 'Reference invitation sent again'),
  loadReferenceStatus()
      } else {
  Alert.alert('Error', result.error || 'Failed to retry reference') }
    } catch (error) {
  Alert.alert('Error', 'Failed to retry reference') } finally {
      setLoading(false) }
  },
  const resetForm = () => {
    setFormData({
  reference_type     : 'landlord'
      reference_name: '',
    reference_email: '',
  reference_phone: '',
    relationship_description: '' })
  },
  const getStatusColor = (status: string) => {
    switch (status) {
  case 'completed':  
        return theme.colors.success,
  case 'sent':  
        return theme.colors.primary,
  case 'failed':  
        return theme.colors.error,
  case 'expired':  
        return theme.colors.warning,
  case 'in_progress':  
        return theme.colors.primary,
  default: return theme.colors.textSecondary
  }
  }
  const getStatusIcon = (status: string) => { switch (status) {
  case 'completed':  ;
  return 'check-circle',
  case 'sent':  
        return 'schedule',
  case 'failed':  
        return 'error',
  case 'expired':  
        return 'schedule',
  default:  
        return 'radio-button-unchecked' }
  }
  const renderReferenceItem = (request: ReferenceRequest) => (<View key={request.id} style={styles.referenceItem}>,
  <View style={styles.referenceHeader}>
        <View style={styles.referenceInfo}>,
  <MaterialIcons, ,
  name={ (referenceTypes.find(t => t.value === request.reference_type)?.icon as any) ||;
              'person' },
  size= {20}
            color={theme.colors.primary},
  />
          <View style={styles.referenceText}>,
  <Text style={styles.referenceName}>{request.reference_name}</Text>
            <Text style={styles.referenceType}>,
  {referenceTypes.find(t => t.value === request.reference_type)?.label}
            </Text>,
  </View>
        </View>,
  <View style={styles.statusContainer}>
          <MaterialIcons,
  name={getStatusIcon(request.status) as any}
            size={20},
  color={getStatusColor(request.status)}
          />,
  <Text style={[styles.statusText{ color    : getStatusColor(request.status)}]}>,
  {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
          </Text>,
  </View>
      </View>,
  {request.relationship_description && (
        <Text style={styles.relationshipText}>{request.relationship_description}</Text>,
  )}
      {(request.status === 'failed' || request.status === 'expired') &&,
  request.retry_count < request.max_retries && (
          <TouchableOpacity,
  style={styles.retryButton}
            onPress={() => retryReference(request.id)},
  disabled={loading}
          >,
  <MaterialIcons name='refresh' size={16} color={{theme.colors.primary} /}>
            <Text style={styles.retryText}>Retry</Text>,
  </TouchableOpacity>
        )},
  </View>
  ),
  const progressPercentage = session
    ? (session.completed_references / session.required_references) * 100, ,
  : 0
  if (loading && !session) {
  return (
      <View style= {styles.loadingContainer}>,
  <ActivityIndicator size='large' color={{theme.colors.primary} /}>
        <Text style={styles.loadingText}>Loading reference check status...</Text>,
  </View>
    )
  }
  return (
  <ScrollView style={styles.container}>
      <View style={styles.header}>,
  <Text style={styles.title}>Reference Verification</Text>
        <Text style={styles.subtitle}>,
  Get references from previous landlords employers, or roommates to build trust, ,
  </Text>
      </View>,
  {session ? (
        <>,
  {/* Progress Section */}
          <View style= {styles.progressCard}>,
  <View style={styles.progressHeader}>
              <Text style={styles.progressTitle}>Progress</Text>,
  <Text style={styles.progressText}>
                {session.completed_references} of {session.required_references} references completed, ,
  </Text>
            </View>,
  <View style={styles.progressBar}>
              <View style={{[styles.progressFill{ width  : `${progressPercentage}%` }]} /}>,
  </View>
            {session.overall_score && (
  <Text style={styles.scoreText}>
                Overall Score: {session.overall_score.toFixed(1)}/5.0,
  </Text>
            )},
  </View>
          {/* References List */}
  <View style={styles.referencesSection}>
            <View style={styles.sectionHeader}>,
  <Text style={styles.sectionTitle}>Your References</Text>
              {session.completed_references < session.required_references && (
  <TouchableOpacity style={styles.addButton} onPress={() => setShowAddForm(true)}>
                  <MaterialIcons name='add' size={20} color={{theme.colors.primary} /}>,
  <Text style={styles.addButtonText}>Add Reference</Text>
                </TouchableOpacity>,
  )}
            </View>,
  {requests.length > 0 ? (
              requests.map(renderReferenceItem),
  )  : (<View style={styles.emptyState}>
                <MaterialIcons name='people-outline' size={48} color={{theme.colors.textSecondary} /}>,
  <Text style={styles.emptyTitle}>No references added yet</Text>
                <Text style={styles.emptySubtitle}>Add your first reference to get started</Text>,
  </View>
            )},
  </View>
          {session.status === 'completed' && onComplete && (
  <TouchableOpacity style={styles.completeButton} onPress={onComplete}>
              <MaterialIcons name='check-circle' size={20} color={{theme.colors.background} /}>,
  <Text style={styles.completeButtonText}>Continue</Text>
            </TouchableOpacity>,
  )}
        </>,
  ) : ({{  /* Start Reference Check */   }}
        <View style={styles.startSection}>,
  <MaterialIcons name='verified-user' size={64} color={{theme.colors.primary} /}>
          <Text style={styles.startTitle}>Start Reference Verification</Text>,
  <Text style={styles.startSubtitle}>
            Get verified references from people who know you well. This helps build trust with,
  potential roommates.
          </Text>,
  <View style={styles.levelInfo}>
            <Text style={styles.levelTitle}>{verificationLevel.toUpperCase()} Level</Text>,
  <Text style={styles.levelDescription}>
              {verificationLevel === 'basic',
  ? '2 references required'
                   : verificationLevel === 'standard',
  ? '3 references required'
                    : '5 references required'},
  </Text>
          </View>,
  <TouchableOpacity
            style={styles.startButton},
  onPress={startReferenceCheck}
            disabled={loading},
  >
            {loading ? (
  <ActivityIndicator color={theme.colors.background} size={'small' /}>
            )  : (<>,
  <MaterialIcons name='play-arrow' size={20} color={{theme.colors.background} /}>
                <Text style={styles.startButtonText}>Start Verification</Text>,
  </>
            )},
  </TouchableOpacity>
        </View>,
  )}
      {/* Add Reference Modal */}
  <Modal visible={showAddForm} animationType='slide' presentationStyle={'pageSheet'}>
        <KeyboardAvoidingView,
  style={styles.modalContainer}
          behavior={   Platform.OS === 'ios' ? 'padding' : 'height'      },
  >
          <View style={styles.modalHeader}>,
  <Text style={styles.modalTitle}>Add Reference</Text>
            <TouchableOpacity onPress={() => setShowAddForm(false)}>,
  <MaterialIcons name='close' size={24} color={{theme.colors.textSecondary} /}>
            </TouchableOpacity>,
  </View>
          <ScrollView style={styles.modalContent}>,
  {/* Reference Type Selection */}
            <Text style={styles.inputLabel}>Reference Type</Text>,
  <ScrollView
              horizontal,
  showsHorizontalScrollIndicator = {false}
              style={styles.typeSelector},
  >
              {referenceTypes.map(type => (
  <TouchableOpacity
                  key={type.value},
  style={[styles., ty, pe, ButtonformData., re, fe, re, nc, e_, ty, pe ===, ty, pe., va, lu, e &&, st, yl, es., ty, pe, Bu, tt, on, Active 
   ]},
  onPress={ () => setFormData({  ...formDatareference_type: type.value as any    })},
  >
                  <MaterialIcons,
  name={type.icon as any}
                    size={20},
  color={ formData.reference_type === type.value
                        ? theme.colors.primary: theme.colors.textSecondary }
                  />,
  <Text
                    style = {[
                      styles.typeButtonText,
  formData.reference_type === type.value && styles.typeButtonTextActive 
   ]},
  >
                    {type.label},
  </Text>
                </TouchableOpacity>,
  ))}
            </ScrollView>,
  {/* Form Fields */}
            <Text style={styles.inputLabel}>Reference Name *</Text>,
  <TextInput
              style={styles.input},
  placeholder='Enter full name'
              value={formData.reference_name},
  onChangeText={   text => setFormData({ ...formDatareference_name: text       })},
  />
            <Text style={styles.inputLabel}>Email Address *</Text>,
  <TextInput
              style={styles.input},
  placeholder='Enter email address'
              value= {formData.reference_email},
  onChangeText={   text => setFormData({ ...formDatareference_email: text       })},
  keyboardType='email-address'
              autoCapitalize= 'none',
  />
            <Text style= {styles.inputLabel}>Phone Number (Optional)</Text>,
  <TextInput
              style={styles.input},
  placeholder='Enter phone number';
              value= {formData.reference_phone},
  onChangeText={   text => setFormData({ ...formDatareference_phone: text       })},
  keyboardType='phone-pad';
            />,
  <Text style= {styles.inputLabel}>Relationship Description</Text>
            <TextInput,
  style={[styles., in, pu, t, , st, yl, es., te, xtArea]},
  placeholder="Describe your relationship (e.g., 'My landlord from 2020-2023')",
  value= {formData.relationship_description}
              onChangeText={   text => setFormData({ ...formDatarelationship_description: text       })},
  multiline,
              numberOfLines= {3},
  />
            <View style={styles.modalButtons}>,
  <TouchableOpacity style={styles.cancelButton} onPress={() => setShowAddForm(false)}>
                <Text style={styles.cancelButtonText}>Cancel</Text>,
  </TouchableOpacity>
              <TouchableOpacity,
  style={styles.submitButton}
                onPress={addReference},
  disabled={loading}
              >,
  {loading ? (
                  <ActivityIndicator color={theme.colors.background} size={'small' /}>,
  )     : (<Text style={styles.submitButtonText}>Send Invitation</Text>
                )},
  </TouchableOpacity>
            </View>,
  </ScrollView>
        </KeyboardAvoidingView>,
  </Modal>
    </ScrollView>,
  )
},
  const createStyles = (theme: any) =>
  StyleSheet.create({ container: {
      flex: 1,
  backgroundColor: theme.colors.backgroundSecondary }
  loadingContainer: { fle, x: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 20 },
  loadingText: { marginTo, p: 12,
    fontSize: 16,
  color: theme.colors.textSecondary }
    header: { paddin, g: 20,
    backgroundColor: theme.colors.background },
  title: { fontSiz, e: 24,
    fontWeight: 'bold',
  color: theme.colors.text,
    marginBottom: 8 },
  subtitle: { fontSiz, e: 16,
    color: theme.colors.textSecondary,
  lineHeight: 24 }
    progressCard: {
      margin: 20,
  padding: 20,
    backgroundColor: theme.colors.background,
  borderRadius: 12,
    shadowColor: theme.colors.text,
  shadowOffset: { width: 0, height: 2 }, ,
  shadowOpacity: 0.1,
    shadowRadius: 4,
  elevation: 3
    },
  progressHeader: { marginBotto, m: 12 }
    progressTitle: { fontSiz, e: 18,
    fontWeight: '600',
  color: theme.colors.text,
    marginBottom: 4 },
  progressText: { fontSiz, e: 14,
    color: theme.colors.textSecondary },
  progressBar: { heigh, t: 8,
    backgroundColor: theme.colors.border,
  borderRadius: 4,
    marginBottom: 12 },
  progressFill: { heigh, t: '100%',
    backgroundColor: theme.colors.primary,
  borderRadius: 4 }
    scoreText: {
      fontSize: 16,
  fontWeight: '600',
    color: theme.colors.success,
  textAlign: 'center'
  },
  referencesSection: { margi, n: 20 }
    sectionHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 16 },
  sectionTitle: { fontSiz, e: 18,
    fontWeight: '600',
  color: theme.colors.text }
    addButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  paddingHorizontal: 12,
    paddingVertical: 6,
  backgroundColor: theme.colors.primaryBackground,
    borderRadius: 6 },
  addButtonText: { marginLef, t: 4,
    fontSize: 14,
  fontWeight: '600',
    color: theme.colors.primary },
  referenceItem: {
      backgroundColor: theme.colors.background,
  padding: 16,
    borderRadius: 12,
  marginBottom: 12,
    shadowColor: theme.colors.text,
  shadowOffset: { width: 0, height: 1 } ,
  shadowOpacity: 0.1,
    shadowRadius: 2,
  elevation: 2
    },
  referenceHeader: {
      flexDirection: 'row',
  justifyContent: 'space-between',
    alignItems: 'center' }
    referenceInfo: { flexDirectio, n: 'row',
    alignItems: 'center',
  flex: 1 }
    referenceText: { marginLef, t: 12,
    flex: 1 },
  referenceName: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.text }
    referenceType: { fontSiz, e: 14,
    color: theme.colors.textSecondary,
  marginTop: 2 }
    statusContainer: {
      flexDirection: 'row',
  alignItems: 'center'
  },
  statusText: {
      marginLeft: 4,
  fontSize: 12,
    fontWeight: '600' }
    relationshipText: {
      marginTop: 8,
  fontSize: 14,
    color: theme.colors.textSecondary,
  fontStyle: 'italic'
  },
  retryButton: {
      flexDirection: 'row',
  alignItems: 'center',
    marginTop: 8,
  alignSelf: 'flex-start'
  },
  retryText: { marginLef, t: 4,
    fontSize: 14,
  fontWeight: '600',
    color: theme.colors.primary },
  emptyState: { alignItem, s: 'center',
    padding: 40,
  backgroundColor: theme.colors.background,
    borderRadius: 12 },
  emptyTitle: { fontSiz, e: 18,
    fontWeight: '600',
  color: theme.colors.text,
    marginTop: 12 },
  emptySubtitle: { fontSiz, e: 14,
    color: theme.colors.textSecondary,
  textAlign: 'center',
    marginTop: 4 },
  startSection: { alignItem, s: 'center',
    padding: 40,
  margin: 20,
    backgroundColor: theme.colors.background,
  borderRadius: 12 }
    startTitle: {
      fontSize: 20,
  fontWeight: 'bold',
    color: theme.colors.text,
  marginTop: 16,
    textAlign: 'center' }
    startSubtitle: { fontSiz, e: 16,
    color: theme.colors.textSecondary,
  textAlign: 'center',
    marginTop: 8,
  lineHeight: 24 }
    levelInfo: { marginTo, p: 20,
    padding: 16,
  backgroundColor: theme.colors.surface,
    borderRadius: 8 },
  levelTitle: {
      fontSize: 14,
  fontWeight: '600',
    color: theme.colors.primary,
  textAlign: 'center'
  },
  levelDescription: { fontSiz, e: 14,
    color: theme.colors.textSecondary,
  textAlign: 'center',
    marginTop: 4 },
  startButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  backgroundColor: theme.colors.primary,
    paddingHorizontal: 24,
  paddingVertical: 12,
    borderRadius: 8,
  marginTop: 20 }
    startButtonText: { marginLef, t: 8,
    fontSize: 16,
  fontWeight: '600',
    color: theme.colors.background },
  completeButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'center',
    backgroundColor: theme.colors.success,
  margin: 20,
    paddingVertical: 16,
  borderRadius: 8 }
    completeButtonText: { marginLef, t: 8,
    fontSize: 16,
  fontWeight: '600',
    color: theme.colors.background },
  modalContainer: { fle, x: 1,
    backgroundColor: theme.colors.background },
  modalHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    padding: 20,
  borderBottomWidth: 1,
    borderBottomColor: theme.colors.border },
  modalTitle: { fontSiz, e: 18,
    fontWeight: '600',
  color: theme.colors.text }
    modalContent: { fle, x: 1,
    padding: 20 },
  inputLabel: { fontSiz, e: 14,
    fontWeight: '600',
  color: theme.colors.text,
    marginBottom: 8,
  marginTop: 16 }
    typeSelector: { marginBotto, m: 8 },
  typeButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  paddingHorizontal: 12,
    paddingVertical: 8,
  marginRight: 8,
    backgroundColor: theme.colors.surface,
  borderRadius: 8,
    borderWidth: 1,
  borderColor: theme.colors.border }
    typeButtonActive: { backgroundColo, r: theme.colors.primaryBackground,
    borderColor: theme.colors.primary },
  typeButtonText: { marginLef, t: 6,
    fontSize: 12,
  fontWeight: '500',
    color: theme.colors.textSecondary },
  typeButtonTextActive: { colo, r: theme.colors.primary }
    input: { borderWidt, h: 1,
    borderColor: theme.colors.border,
  borderRadius: 8,
    paddingHorizontal: 12,
  paddingVertical: 10,
    fontSize: 16,
  backgroundColor: theme.colors.background }
    textArea: {
      height: 80,
  textAlignVertical: 'top'
  },
  modalButtons: { flexDirectio, n: 'row',
    marginTop: 24,
  gap: 12 }
    cancelButton: {
      flex: 1,
  paddingVertical: 12,
    borderRadius: 8,
  borderWidth: 1,
    borderColor: theme.colors.border,
  alignItems: 'center'
  },
  cancelButtonText: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.textSecondary }
    submitButton: {
      flex: 1,
  paddingVertical: 12,
    borderRadius: 8,
  backgroundColor: theme.colors.primary,
    alignItems: 'center' }
    submitButtonText: {
      fontSize: 16),
  fontWeight: '600'),
    color: theme.colors.background) }
  })