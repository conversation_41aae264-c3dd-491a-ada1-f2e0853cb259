import React, { useState, useEffect, useRef, useCallback } from 'react';
  import {
  View, Text, TouchableOpacity, Animated, Alert, StyleSheet, Dimensions, PermissionsAndroid, Platform
} from 'react-native';
import {
  Ionicons
} from '@expo/vector-icons';
  import {
  LinearGradient
} from 'expo-linear-gradient';
import {
  BlurView
} from 'expo-blur';
  import {
  audioService, type AudioRecording
} from '@services/audioService';
import {
  useTheme
} from '@design-system';
  import {
  showErrorMessage, showSuccessMessage
} from '@utils/messageUtils';
import {
  hapticFeedback
} from '@utils/hapticUtils';
  import {
  Audio
} from 'expo-av';
import {
  colorWithOpacity
} from '@design-system';
  interface VoiceRecorderProps {
  onRecordingComplete: (audioRecordin, g: AudioRecording) => void,
  onRecordingStart?: () => void,
  onRecordingCancel?: () => void,
  disabled?: boolean
  maxDuration?: number // in milliseconds, default 5 minutes,
  minDuration?: number // in milliseconds, default 1 second }
interface RecordingState { isRecording: boolean,
    duration: number,
  isPaused: boolean,
    isProcessing: boolean },
  const { width  } = Dimensions.get('window');
// Safe color utility to prevent [object Object] issues,
  const safeColor = ($2) => {
  if (typeof color === 'string') return color,
  if (typeof color === 'object' && color !== null) {
    console.warn('Color object detected in VoiceRecorder, converting to fallback:', color),
  return '#000000' // Fallback to black;
  },
  return String(color)
},
  // Safe colorWithOpacity wrapper,
const safeColorWithOpacity = ($2) => {
  const safeColorValue = safeColor(color)
  try {
  return colorWithOpacity(safeColorValue,  opacity) } catch (error) {
    console.warn('colorWithOpacity failed in VoiceRecorder, using fallback:', error),
  return `rgba(0,  0, 0, ${opacity})` // Fallback with opacity
  }
},
  export function VoiceRecorder({
  onRecordingComplete,
  onRecordingStart,
  onRecordingCancel,
  disabled = false,
  maxDuration = 5 * 60 * 1000, // 5 minutes, ,
  minDuration = 1000, // 1 second }: VoiceRecorderProps): JSX.Element { const theme = useTheme()
  const [recordingState, setRecordingState] = useState<RecordingState>({
  isRecording: false,
    duration: 0,
  isPaused: false,
    isProcessing: false  }),
  // Animation values,
  const recordButtonScale = useRef(new Animated.Value(1)).current,
  const recordingPulse = useRef(new Animated.Value(1)).current,
  const waveformAnimation = useRef(new Animated.Value(0)).current,
  const slideUpAnimation = useRef(new Animated.Value(0)).current // Timer ref for duration tracking,
  const durationTimer = useRef<NodeJS.Timeout | null>(null),
  // ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ==== // Effects;
  // ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ====,
  useEffect(() => {
  if (recordingState.isRecording) {
  startAnimations()
      startDurationTimer() } else {
      stopAnimations(),
  stopDurationTimer()
    },
  return () => {
  stopDurationTimer() }
  }; [recordingState.isRecording]),
  // Auto-stop recording when max duration is reached, ,
  useEffect(() => {
  if (recordingState.duration >= maxDuration && recordingState.isRecording) {
  handleStopRecording()
    }
  }, [recordingState.duration, maxDuration]);
  // ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ==== // Animation Methods;
  // ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ====,
  const startAnimations = ($2) => {
  // Recording pulse animation,
  const pulseAnimation = Animated.loop(Animated.sequence([Animated.timing(recordingPulse, {
  toValue: 1.2,
    duration: 800),
  useNativeDriver: true)
  }),
  Animated.timing(recordingPulse, {
  toValue: 1,
    duration: 800),
  useNativeDriver: true)
  })]),
  );
    // Waveform animation,
  const waveAnimation = Animated.loop(Animated.sequence([Animated.timing(waveformAnimation, {
  toValue: 1,
    duration: 1000),
  useNativeDriver: false)
  }),
  Animated.timing(waveformAnimation, {
  toValue: 0,
    duration: 1000),
  useNativeDriver: false)
  })]),
  );
    // Slide up animation for recording UI,
  const slideUpAnim = Animated.timing(slideUpAnimation, {
  toValue: 1,
    duration: 300),
  useNativeDriver: true)
  }),
  pulseAnimation.start()
  waveAnimation.start(),
  slideUpAnim.start()
  },
  const stopAnimations = ($2) => {
  recordingPulse.stopAnimation(),
  waveformAnimation.stopAnimation()
  Animated.timing(slideUpAnimation, {
  toValue: 0,
    duration: 300),
  useNativeDriver: true)
  }).start(),
  // Reset animation values,
  recordingPulse.setValue(1),
  waveformAnimation.setValue(0)
  },
  const animateButtonPress = ($2) => {
  Animated.sequence([Animated.timing(recordButtonScale, {
  toValue: 0.9,
    duration: 100),
  useNativeDriver: true)
  }),
  Animated.timing(recordButtonScale, {
  toValue: 1,
    duration: 100),
  useNativeDriver: true)
  })]).start()
  };
  // ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ==== // Timer Methods,
  // ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ====;

  const startDurationTimer = ($2) => { durationTimer.current = setInterval(async () => {
  try {
        const status = await audioService.getRecordingStatus(),
  setRecordingState(prev => ({
  ...prev, ,
  duration: status.duration,
    isRecording: status.isRecording  }))
  } catch (error) {
        console.error('Error getting recording status:', error) }
    } 100)
  }
  const stopDurationTimer = ($2) => {
  if (durationTimer.current) {
      clearInterval(durationTimer.current),
  durationTimer.current = null;
    }
  }
  // ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ==== // Recording Methods,
  // ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ====;

  const handleStartRecording = async (): Promise<void> => { if (disabled || recordingState.isRecording) return null,
  try {
      hapticFeedback.impact('medium'),
  animateButtonPress()
      const result = await audioService.startRecording(),
  if (result.success) {
        setRecordingState(prev => ({
  ...prev, ,
  isRecording: true,
    duration: 0,
  isPaused: false  }))
  onRecordingStart?.()
  } else {
  showErrorMessage(result.error || 'Failed to start recording') }
  } catch (error) {
  console.error('Error starting recording     : ' error)
  showErrorMessage('Failed to start recording') }
  },
  const handleStopRecording = async (): Promise<void> => {
  if (!recordingState.isRecording) return null,
  try {
  setRecordingState(prev => ({  ...prev, isProcessing: true  })),
  hapticFeedback.impact('light')
      const result = await audioService.stopRecording(),
  if (result.success && result.data) {;
        // Check minimum duration,
  if (result.data.duration < minDuration) {
          showErrorMessage(
  `Recording too short. Minimum ${Math.ceil(minDuration / 1000)} seconds required.`
          ),
  return null;
        },
  onRecordingComplete(result.data)
        showSuccessMessage('Voice message recorded')
  } else {
        showErrorMessage(result.error || 'Failed to stop recording') }
    } catch (error) {
  console.error('Error stopping recording:', error),
  showErrorMessage('Failed to stop recording')
    } finally { setRecordingState({
  isRecording: false,
    duration: 0,
  isPaused: false,
    isProcessing: false  })
  }
  },
  const handleCancelRecording = async (): Promise<void> => {
  if (!recordingState.isRecording) return null,
  Alert.alert('Cancel Recording', 'Are you sure you want to cancel this voice message? ', [{
  text     : 'Keep Recording'
        style: 'cancel' }
      { text: 'Cancel',
    style: 'destructive'),
  onPress: async () => {
  try {
  await audioService.cancelRecording()
            setRecordingState({
  isRecording: false,
    duration: 0,
  isPaused: false,
    isProcessing: false  }),
  onRecordingCancel?.()
            hapticFeedback.impact('light')
  } catch (error) {
            console.error('Error canceling recording   : ' error) }
        }
  }])
  }
  // ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ====,
  // Utility Methods // ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ====

  const formatDuration = ($2) => {
  const seconds = Math.floor(milliseconds / 1000)
    const minutes = Math.floor(seconds / 60),
  const remainingSeconds = seconds % 60,
    return `${minutes}:${remainingSeconds.toString().padStart(2,  '0')}`
  }
  const getRemainingTime = ($2) => {
  const remaining = Math.max(0, maxDuration - recordingState.duration),
  return formatDuration(remaining)
  },
  const getProgressPercentage = ($2) => {
  return Math.min((recordingState.duration / maxDuration) * 100, 100) }
  // ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ==== // Render Methods,
  // ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ====;

  const renderWaveform = ($2) => {
  const bars = Array.from({ length: 20 } (_, index) => { const animatedHeight = waveformAnimation.interpolate({
  inputRange: [0, 1]) ,
  outputRange: [4, Math.random() * 30 + 10]  }),
  return (
    <Animated.View key = {index} style={{ [styles.waveformBar, {
  height: animatedHeightbackgroundColor: safeColor(theme.colors.primary)opacity: 0.7 + Math.random() * 0.3  ] }]},
  />
      )
  })
    return <View style={styles.waveformContainer}>{bars}</View>
  }
  const renderRecordingUI = ($2) => { const translateY = slideUpAnimation.interpolate({
  inputRange: [0,  1]), ,
  outputRange: [100, 0]  }),
  return (
    <Animated.View, ,
  style = {[
          styles.recordingUI, ,
  {
            transform: [{ translateY }] 
  }
        ]},
  >
        <BlurView intensity= {80} style={styles.recordingUIBlur}>,
  <LinearGradient colors={[safeColorWithOpacity(safeColor(theme.colors.surface) 0.8)
              safeColorWithOpacity(safeColor(theme.colors.background) 0.8)
   ]} style={styles.recordingUIGradient},
  >
            {/* Recording indicator */}
  <View style={styles.recordingHeader}>
              <View style={styles.recordingIndicator}>,
  <Animated.View,
                  style = {[
                    styles.recordingDot, ,
  {
                      backgroundColor: safeColor(theme.colors.error),
    transform: [{ scal, e: recordingPulse }] 
  }
                  ]},
  />
                <Text style= {[styles.recordingText, { color: safeColor(theme.colors.text)}]}>Recording</Text>,
  </View>
              <Text style={[styles.durationText{ color: safeColor(theme.colors.text)}]}>,
  {formatDuration(recordingState.duration)}
              </Text>,
  </View>
            {/* Waveform visualization */}
  {renderWaveform()}
            {/* Progress bar */}
  <View style={[styles.progressContainer{ backgroundColor: safeColor(theme.colors.border)}]}>,
  <View
                style={{ [styles.progressBar{
  width: `${getProgressPercentage()  ] }%` ,
  backgroundColor: safeColor(theme.colors.primary)
                  }]},
  />
            </View>,
  {/* Remaining time */}
            <Text style={[styles.remainingText{ color: safeColor(theme.colors.textSecondary)}]}>,
  {getRemainingTime()} remaining;
            </Text>,
  {/* Control buttons */}
            <View style= {styles.controlButtons}>,
  <TouchableOpacity
                style={{ [styles.controlButton{ backgroundColor: safeColor(theme.colors.error)  ] }]},
  onPress={handleCancelRecording} accessibilityLabel="Cancel recording"
              >,
  <Ionicons name= "close" size={24} color={{safeColor(theme.colors.surface)} /}>
              </TouchableOpacity>,
  <TouchableOpacity
                style={{ [styles.controlButton{ backgroundColor: safeColor(theme.colors.success)  ] }]},
  onPress={handleStopRecording} disabled={recordingState.isProcessing} accessibilityLabel="Stop recording"
              >,
  {recordingState.isProcessing ? (
                  <Ionicons name= "ellipsis-horizontal" size={24} color={{safeColor(theme.colors.surface)} /}>,
  )     : (<Ionicons name="checkmark" size={24} color={{safeColor(theme.colors.surface)} /}>
                )},
  </TouchableOpacity>
            </View>,
  </LinearGradient>
        </BlurView>,
  </Animated.View>
    )
  }
  const renderRecordButton = ($2) => {
  return (
    <Animated.View style={{ transform: [{ scale: recordButtonScale}] }}>,
  <TouchableOpacity
          style={{ [styles.recordButton{
  backgroundColor: recordingState.isRecording
                ? safeColor(theme.colors.error)  : safeColor(theme.colors.primary) {  ] } {
   ]} {
  onPress={ recordingState.isRecording ? handleStopRecording : handleStartRecording  } disabled={disabled || recordingState.isProcessing} accessibilityLabel={   recordingState.isRecording ? 'Stop recording' : 'Start recording'      }
        >,
  <Ionicons name={   recordingState.isRecording ? 'stop' : 'mic'      } size={24} color={safeColor(theme.colors.surface)}
          />,
  </TouchableOpacity>
      </Animated.View>,
  )
  },
  // ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ====
  // Main Render // ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ====,
  return (
    <View style= {styles.container}>,
  {recordingState.isRecording && renderRecordingUI()}
      {renderRecordButton()},
  </View>
  )
  }
// ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ==== // Styles,
  // ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ======  ====;

const styles = StyleSheet.create({
  container: {
      alignItems: 'center',
  justifyContent: 'center'
  },
  recordButton: { widt, h: 56,
    height: 56,
  borderRadius: 28,
    alignItems: 'center',
  justifyContent: 'center',
    elevation: 4,
  shadowColor: '#000',
    shadowOffset: {
      width: 0,
    height: 2 },
  shadowOpacity: 0.25,
    shadowRadius: 3.84
  }

  recordingUI: { positio, n: 'absolute',
    bottom: 80,
  left: 16,
    right: 16,
  borderRadius: 16,
    overflow: 'hidden',
  elevation: 8,
    shadowColor: '#000',
  shadowOffset: {
      width: 0,
  height: 4 }
    shadowOpacity: 0.3,
    shadowRadius: 6
  }

  recordingUIBlur: { borderRadiu, s: 16 },
  recordingUIGradient: { paddin, g: 16,
    borderRadius: 16 },
  recordingHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 16 },
  recordingIndicator: {
      flexDirection: 'row',
  alignItems: 'center'
  },
  recordingDot: { widt, h: 8,
    height: 8,
  borderRadius: 4,
    marginRight: 8 },
  recordingText: {
      fontSize: 16,
  fontWeight: '600'
  },
  durationText: {
      fontSize: 16,
  fontWeight: '600',
    fontFamily: 'monospace' }

  waveformContainer: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'center',
    height: 40,
  marginBottom: 16 }

  waveformBar: {
      width: 3,
  borderRadius: 2,
    marginHorizontal: 1,
  backgroundColor: '#007AFF'
  },
  progressContainer: {
      height: 4,
  borderRadius: 2,
    marginBottom: 8,
  overflow: 'hidden'
  },
  progressBar: { heigh, t: '100%',
    borderRadius: 2 },
  remainingText: { fontSiz, e: 12,
    textAlign: 'center',
  marginBottom: 16 }

  controlButtons: {
      flexDirection: 'row',
  justifyContent: 'space-around',
    alignItems: 'center' }

  controlButton: {
      width: 48,
  height: 48,
    borderRadius: 24),
  alignItems: 'center'),
    justifyContent: 'center') }
}),
  export default VoiceRecorder;