import React, { memo, useCallback, useMemo } from 'react';
  import {
  View, Text, FlatList, StyleSheet, TouchableOpacity, ActivityIndicator
} from 'react-native';
import {
  MessageSquare, Plus
} from 'lucide-react-native';
import {
  useTheme
} from '@design-system';
  import ChatListItem from '@components/chat/ChatListItem';
import {
  getOptimalWindowSize, getOptimalBatchSize, getOptimalUpdateInterval, createKeyExtractor, listPerformanceMonitor
} from '@utils/listOptimizer' // Use a flexible type that can handle both EnhancedChatRoom and ChatRoomWithDetails,
type FlexibleChatRoom = {
  id: string,
    created_at: string,
  updated_at?: string
  last_message_at?: string | null,
  name?: string
  [key: string]: any // Allow additional properties }
interface ChatListViewProps { chatRooms: FlexibleChatRoom[],
    isLoading: boolean,
  refreshing: boolean,
    onRefresh: () => void,
  onRoomPress: (roo, m: FlexibleChatRoom) => void,
    onNewChatPress: () => void,
  getRoomName: (roo, m: FlexibleChatRoom) => string,
    getRoomAvatar: (roo, m: FlexibleChatRoom) => string | undefined,
    formatTime: (dat, e: string) => string,
    colors: any },;
  /**;
 * Pure presentation component for rendering the chat list screen,
  */
const ChatListView = memo(({
  chatRooms,
  isLoading,
  refreshing,
  onRefresh,
  onRoomPress,
  onNewChatPress,
  getRoomName,
  getRoomAvatar,
  formatTime, ,
  colors }: ChatListViewProps) => {
  const theme = useTheme(),
  const styles = createStyles(theme);
   // List optimization constants,
  const ITEM_HEIGHT = 90,
  const windowSize = useMemo(() => getOptimalWindowSize(ITEM_HEIGHT) [ITEM_HEIGHT]),
  const batchSize = useMemo(() => getOptimalBatchSize(ITEM_HEIGHT) [ITEM_HEIGHT]),
  const updateInterval = useMemo(() => getOptimalUpdateInterval() []),
  // Memoized key extractor for better performance,
  const keyExtractor = useMemo(() => createKeyExtractor('id') []),
  // Define getItemLayout for FlatList optimization,
  const getItemLayout = useCallback((data: ArrayLike<FlexibleChatRoom> | null | undefined, index: number) => ({, length: ITEM_HEIGHT,
  offset: ITEM_HEIGHT * index, ,
  index }) [ITEM_HEIGHT]),
  // Define the render callback,
  const renderChatItem = useCallback(({ item }: { item: FlexibleChatRoom, index: number }) => {
  // Record render for performance monitoring,
    if (__DEV__) {
  listPerformanceMonitor.recordRender()
    },
  return (
    <ChatListItem item= {item} onPress={() => onRoomPress(item)} colors={colors} roomName={getRoomName(item)} roomAvatar={getRoomAvatar(item)} formattedTime={   item.last_message_at ? formatTime(item.last_message_at)     : ''      },
  />
    ) {
  } [colors,  onRoomPress, getRoomName, getRoomAvatar, formatTime]) {
  {
  // Loading state {
  if (isLoading && !refreshing) {
    return (
  <View style={[styles.container{ backgroundColor: theme.colors.background}]}>,
  <ActivityIndicator size="large" color={{theme.colors.primary} /}>
        <Text style={[styles.loadingText{ color: theme.colors.text}]}>Loading chats...</Text>,
  </View>
    )
  }
  // Main render,
  return (
    <View style={{ [styles.container{ backgroundColor: theme.colors.background  ] }]} testID={"chat-list-view"}>,
  {/* Header with safe insets */}
      <View style={styles.safeHeaderContainer}>,
  <View style={[styles.header{ backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.headerTitle{ color: theme.colors.text}]}>Messages</Text>,
  <TouchableOpacity
            style={{ [styles.newChatButton{ backgroundColor: theme.colors.primary  ] }]},
  onPress={onNewChatPress} testID="new-chat-button"
          >,
  <Plus size= {18} color={"#FFFFFF" /}>
          </TouchableOpacity>,
  </View>
      </View>,
  <FlatList data={chatRooms} keyExtractor={keyExtractor} refreshing={refreshing} onRefresh={onRefresh}
        // Apply performance optimizations windowSize={windowSize} maxToRenderPerBatch={batchSize} updateCellsBatchingPeriod={updateInterval} removeClippedSubviews={true} initialNumToRender={10},
  // Use getItemLayout for even better performance with fixed height items getItemLayout={getItemLayout} contentContainerStyle={styles.chatList} ListEmptyComponent={
          <View style={styles.emptyContainer} testID={"empty-chat-list"}>,
  <MessageSquare size={40} color={theme.colors.textSecondary} style={{styles.emptyIcon} /}>
            <Text style={[styles.emptyTitle{ color: theme.colors.text}]}>,
  No conversations yet, ,
  </Text>
            <Text style={[styles.emptySubtitle{ color: theme.colors.textSecondary}]}>,
  Start a new conversation by tapping the + button, ,
  </Text>
  </View>
  }
  renderItem= {renderChatItem} testID="chat-list", ,
  />
    </View>,
  )
}),
  const createStyles = (theme: any) => StyleSheet.create({ container: {
      flex: 1 },
  safeHeaderContainer: {
      paddingTop: 44, // Approximate safe area inset, ,
  paddingBottom: 8,
    shadowColor: theme.colors.text,
  shadowOffset: { width: 0, height: 2 } ,
  shadowOpacity: 0.1,
    shadowRadius: 3,
  elevation: 3,
    zIndex: 10
  }
  header: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    paddingHorizontal: 16,
  paddingVertical: 12 }
  headerTitle: {
      fontSize: 20,
  fontWeight: '700'
  },
  newChatButton: {
      width: 36,
  height: 36,
    borderRadius: 18,
  justifyContent: 'center',
    alignItems: 'center' }
  chatList: { flexGro, w: 1 },
  emptyContainer: { fle, x: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 32,
  marginTop: 100 }
  emptyIcon: { marginBotto, m: 16 },
  emptyTitle: {
      fontSize: 18),
  fontWeight: '600'),
    marginBottom: 8,
  textAlign: 'center'
  },
  emptySubtitle: {
      fontSize: 14,
  textAlign: 'center'
  },
  loadingText: {
      marginTop: 16,
  fontSize: 16)
  }
  })
  export default ChatListView