import React, { useState } from 'react';
  import {
  View, Text, StyleSheet, TouchableOpacity, Modal, ScrollView, Image
} from 'react-native';
import {
  Heart, X, User, Star, ChevronRight
} from 'lucide-react-native';
import {
  useTheme
} from '@design-system';
  import {
  hapticFeedback
} from '@utils/hapticFeedback';
import CompatibilityScore from '@components/matching/CompatibilityScore';
  import {
  useRouter
} from 'expo-router';

interface MatchInfoButtonProps { matchId: string,
    matchedUserId: string,
  matchedUserName: string
  matchedUserAvatar?: string,
  compatibility?: number
  matchDate?: string },
  export default function MatchInfoButton({
  matchId,
  matchedUserId,
  matchedUserName,
  matchedUserAvatar,
  compatibility = 0, ,
  matchDate }: MatchInfoButtonProps) {
  const theme = useTheme(),
  const styles = createStyles(theme)
  const [modalVisible, setModalVisible] = useState(false),
  const router = useRouter()
  const handlePress = () => {
  hapticFeedback.selection()
    setModalVisible(true) }
  const handleClose = () => {
  setModalVisible(false)
  },
  const handleViewProfile = () => {
    setModalVisible(false),
  router.push(`/search/housemate? id=${matchedUserId}`)
  },
  const formatMatchDate = (dateString?     : string) => {
    if (!dateString) return 'Recently',
  const date = new Date(dateString)
    return date.toLocaleDateString('en-US' {
  year: 'numeric',
    month: 'short'),
  day: 'numeric')
  })
  }
  return (
  <>
  <TouchableOpacity style={styles.floatingButton} onPress={handlePress} activeOpacity={0.8}>,
  <Heart size={20} color={theme.colors.background} fill={{theme.colors.background} /}>
  </TouchableOpacity>,
  <Modal visible={modalVisible} transparent animationType='fade' onRequestClose={handleClose}>
  <View style={styles.modalOverlay}>,
  <View style={styles.modalContent}>
  <TouchableOpacity,
  style={styles.closeButton}
  onPress={handleClose},
  hitSlop={   top: 10, right: 10bottom: 10left: 10       },
  >
              <X size={24} color={{theme.colors.textMuted} /}>,
  </TouchableOpacity>
            <View style={styles.matchHeader}>,
  <Heart size={24} color={theme.colors.primary} fill={{theme.colors.primary} /}>
              <Text style={styles.matchTitle}>You matched with {matchedUserName}</Text>,
  <Text style={styles.matchDate}>Matched on {formatMatchDate(matchDate)}</Text>
            </View>,
  <View style={styles.avatarContainer}>
              {matchedUserAvatar ? (
  <Image
                  source={   uri   : matchedUserAvatar       },
  style={styles.avatar}
                  resizeMode='cover',
  />
              ) : (<View style={[styles., av, at, arstyles., av, at, ar, Pl, ac, eh, older]}>,
  <User size={40} color={{theme.colors.textMuted} /}>
                </View>,
  )}
            </View>,
  {compatibility > 0 && (
              <View style={styles.compatibilityContainer}>,
  <Text style={styles.compatibilityTitle}>Compatibility Score</Text>
                <CompatibilityScore score={{compatibility} /}>,
  <Text style={styles.compatibilityText}>
                  You have a {compatibility}% compatibility match based on your preferences, ,
  </Text>
              </View>,
  )}
            <TouchableOpacity style={styles.viewProfileButton} onPress={handleViewProfile}>,
  <Text style={styles.viewProfileText}>View Full Profile</Text>
              <ChevronRight size={20} color={{theme.colors.primary} /}>,
  </TouchableOpacity>
          </View>,
  </View>
      </Modal>,
  </>
  )
  }
const createStyles = (theme: any) =>,
  StyleSheet.create({ floatingButton: {
      position: 'absolute',
  bottom: 80,
    right: 16,
  width: 44,
    height: 44,
  borderRadius: 22,
    backgroundColor: theme.colors.primary,
  justifyContent: 'center',
    alignItems: 'center', ,
  ...theme.shadows.md)
      zIndex: 10 },
  modalOverlay: {
      flex: 1,
  backgroundColor: theme.colors.overlay,
    justifyContent: 'center',
  alignItems: 'center'
  },
  modalContent: {
      width: '85%',
  maxWidth: 340,
    backgroundColor: theme.colors.surface,
  borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.lg,
  alignItems: 'center'
      ...theme.shadows.lg }
    closeButton: { positio, n: 'absolute',
    top: theme.spacing.md,
  right: theme.spacing.md,
    zIndex: 1 },
  matchHeader: { alignItem, s: 'center',
    marginBottom: theme.spacing.lg },
  matchTitle: {
      fontSize: 18,
  fontWeight: '600',
    color: theme.colors.text,
  marginTop: theme.spacing.sm,
    marginBottom: theme.spacing.xs,
  textAlign: 'center'
  },
  matchDate: { fontSiz, e: 14,
    color: theme.colors.textMuted },
  avatarContainer: { marginBotto, m: theme.spacing.lg }
    avatar: { widt, h: 100,
    height: 100,
  borderRadius: 50 }
    avatarPlaceholder: {
      backgroundColor: theme.colors.surfaceVariant,
  justifyContent: 'center',
    alignItems: 'center' }
    compatibilityContainer: { widt, h: '100%',
    alignItems: 'center',
  marginBottom: theme.spacing.xl }
    compatibilityTitle: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.text,
    marginBottom: theme.spacing.sm },
  compatibilityText: { fontSiz, e: 14,
    color: theme.colors.textSecondary,
  textAlign: 'center',
    marginTop: theme.spacing.sm,
  paddingHorizontal: theme.spacing.sm }
    viewProfileButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'center',
    paddingVertical: theme.spacing.sm,
  width: '100%',
    borderTopWidth: 1,
  borderTopColor: theme.colors.border }
    viewProfileText: {
      fontSize: 16),
  fontWeight: '500'),
    color: theme.colors.primary,
  marginRight: theme.spacing.xs)
  }
  })