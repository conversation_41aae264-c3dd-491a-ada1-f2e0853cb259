import React, { useState, useEffect, useCallback, useMemo } from 'react';
  import {
  View, Text, ScrollView, TouchableOpacity, StyleSheet, Animated, Dimensions
} from 'react-native';
import {
  useTheme
} from '@design-system';
  import {
  SmartConversationIntelligence
} from '@services/messaging/SmartConversationIntelligence';
import {
  AIMessageModeration
} from '@services/messaging/AIMessageModeration';
  import {
  ConversationOptimizer
} from '@services/messaging/ConversationOptimizer';

interface Message { id: string,
    content: string,
  sender_id: string,
    created_at: string,
  room_id: string }
  interface ConversationThread {
  id: string,
    topic: string,
  category: 'introduction' | 'interests' | 'logistics' | 'personal' | 'planning' | 'other',
    messages: Message[],
  importance: 'low' | 'medium' | 'high',
    sentiment: 'positive' | 'neutral' | 'negative',
  keyPoints: string[],
    isActive: boolean,
  startTime: Date,
    lastActivity: Date,
  participantEngagement: {
  [userId: string]: {, messageCount: number,
  averageLength: number,
    responseTime: number,
  sentiment: 'positive' | 'neutral' | 'negative'
  }
  }
  },
  interface SmartConversationThreadsProps { messages: Message[],
    roomId: string,
  currentUserId: string,
    otherUserId: string,
  onThreadSelect?: (thread: ConversationThread) => void,
  onKeyPointTap?: (keyPoint: string, thread: ConversationThread) => void },
  export const SmartConversationThreads: React.FC<SmartConversationThreadsProps> = ({ 
  messages,
  roomId,
  currentUserId,
  otherUserId,
  onThreadSelect, ,
  onKeyPointTap }) => {
  const theme = useTheme(),
  const styles = createStyles(theme);
  ,
  const [threads, setThreads] = useState<ConversationThread[]>([]),
  const [selectedThread, setSelectedThread] = useState<string | null>(null),
  const [isAnalyzing, setIsAnalyzing] = useState(false),
  const [expandedThreads, setExpandedThreads] = useState<Set<string>>(new Set()),
  const [animatedValues] = useState(() => new Map<string, Animated.Value>()),
  // AI Services,
  const conversationIntelligence = useMemo(() => SmartConversationIntelligence.getInstance() []),
  const messageModeration = useMemo(() => AIMessageModeration.getInstance() []),
  const conversationOptimizer = useMemo(() => ConversationOptimizer.getInstance() []),
  // Analyze messages and create threads,
  const analyzeConversationThreads = useCallback(async () => {
  if (messages.length === 0 || isAnalyzing) return null;
    ,
  setIsAnalyzing(true)
    ,
  try {
      // Get AI insights for thread analysis,
  const [intelligenceData, moderationResults, optimizationData] = await Promise.all([conversationIntelligence.analyzeConversation(roomId, currentUserId, otherUserId),
  messageModeration.analyzeConversationSafety(roomId, currentUserId, otherUserId),
  conversationOptimizer.optimizeConversation(roomId, currentUserId, otherUserId)]),
  // Create threads based on AI analysis,
      const detectedThreads = await createThreadsFromMessages(
  messages,
        intelligenceData,
  moderationResults, ,
  optimizationData, ,
  )
      setThreads(detectedThreads) } catch (error) {
      console.error('Error analyzing conversation threads:', error),
  // Fallback to basic threading,
      const basicThreads = createBasicThreads(messages),
  setThreads(basicThreads)
    } finally {
  setIsAnalyzing(false)
    }
  }, [messages, roomId, currentUserId, otherUserId, isAnalyzing]);
  // Create threads from AI analysis,
  const createThreadsFromMessages = async (msgs: Message[],
    intelligence: any,
  moderation: any,
    optimization: any): Promise<ConversationThread[]> => {
  const threads: ConversationThread[] = [] // Group messages by time windows and topic similarity,
  const timeWindows = groupMessagesByTimeWindows(msgs, 30 * 60 * 1000) // 30 minute windows,
  ;
    for (let i = 0,  i < timeWindows.length,  i++) {
  const windowMessages = timeWindows[i],
  if (windowMessages.length === 0) continue // Detect topic and category using AI insights,
      const topic = detectTopicFromMessages(windowMessages, intelligence),
  const category = categorizeThread(topic, windowMessages),
  const sentiment = analyzeSentiment(windowMessages, moderation),
  const importance = calculateImportance(windowMessages, optimization),
  const keyPoints = extractKeyPoints(windowMessages, intelligence),
  ;
      const thread: ConversationThread = {, id: `thread_${i}_${Date.now()}`,
  topic,
        category,
  messages: windowMessages
        importance,
  sentiment,
        keyPoints,
  isActive: i === timeWindows.length - 1, // Last thread is active,
  startTime: new Date(windowMessages[0].created_at),
    lastActivity: new Date(windowMessages[windowMessages.length - 1].created_at),
  participantEngagement: calculateParticipantEngagement(windowMessages, currentUserId, otherUserId)
  }
  threads.push(thread)
  }
  return threads
  }
  // Group messages by time windows,
  const groupMessagesByTimeWindows = ($2) => {
  if (msgs.length === 0) return [],
  ;
    const sortedMessages = [...msgs].sort((a, b) => {
  new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
    ),
  ;
    const windows: Message[][] = [], ,
  let currentWindow: Message[] = [sortedMessages[0]], ,
  let windowStart = new Date(sortedMessages[0].created_at).getTime(),
  ;
    for (let i = 1,  i < sortedMessages.length,  i++) {
  const messageTime = new Date(sortedMessages[i].created_at).getTime(),
  ;
      if (messageTime - windowStart <= windowMs) {
  currentWindow.push(sortedMessages[i]) } else {
        windows.push(currentWindow),
  currentWindow = [sortedMessages[i]],
  windowStart = messageTime;
      }
  }
    if (currentWindow.length > 0) {
  windows.push(currentWindow)
    },
  return windows;
  },
  // Detect topic from messages using AI insights,
  const detectTopicFromMessages = ($2) => { if (!intelligence?.insights?.length) {
  // Fallback topic detection,
      const content = msgs.map(m => m.content).join(' ').toLowerCase(),
  if (content.includes('move') || content.includes('apartment') || content.includes('house')) {
        return 'Housing & Moving' } else if (content.includes('meet') || content.includes('coffee') || content.includes('hang')) { return 'Meeting Plans' } else if (content.includes('work') || content.includes('job') || content.includes('schedule')) { return 'Work & Schedule' } else if (content.includes('hobby') || content.includes('interest') || content.includes('like')) { return 'Interests & Hobbies' },
  return 'General Conversation';
    },
  // Use AI insights for topic detection,
    const relevantInsights = intelligence.insights.filter((insight    : any) => { insight.type === 'topic_detection' || insight.type === 'conversation_flow',
  )
    ,
  if (relevantInsights.length > 0) {
      return relevantInsights[0].suggestion || 'General Conversation' },
  return 'General Conversation'
  },
  // Categorize thread based on topic and content,
  const categorizeThread = ($2) => { const content = msgs.map(m => m.content).join(' ').toLowerCase(),
  ;
    if (content.includes('hi') || content.includes('hello') || content.includes('nice to meet')) {
  return 'introduction' } else if (content.includes('hobby') || content.includes('interest') || content.includes('like') || content.includes('enjoy')) { return 'interests' } else if (content.includes('move') || content.includes('rent') || content.includes('lease') || content.includes('apartment')) { return 'logistics' } else if (content.includes('family') || content.includes('personal') || content.includes('feel')) { return 'personal' } else if (content.includes('plan') || content.includes('schedule') || content.includes('meet') || content.includes('when')) { return 'planning' }
    return 'other'
  }
  // Analyze sentiment using moderation data,
  const analyzeSentiment = ($2) => { if (!moderation?.overallSentiment) {;
      // Fallback sentiment analysis,
  const content = msgs.map(m => m.content).join(' ').toLowerCase();
      const positiveWords = ['great', 'awesome', 'love', 'excited', 'happy', 'good', 'nice', 'perfect'], ,
  const negativeWords = ['bad', 'hate', 'terrible', 'awful', 'sad', 'angry', 'frustrated'],
  ;
      const positiveCount = positiveWords.filter(word => content.includes(word)).length,
  const negativeCount = negativeWords.filter(word => content.includes(word)).length;
      ,
  if (positiveCount > negativeCount) return 'positive';
      if (negativeCount > positiveCount) return 'negative',
  return 'neutral' }
    return moderation.overallSentiment
  }
  // Calculate thread importance using optimization data,
  const calculateImportance = ($2) => { if (!optimization?.engagementMetrics) {;
      // Fallback importance calculation,
  const messageCount = msgs.length,
      const avgLength = msgs.reduce((sum, m) => sum + m.content.length, 0) / msgs.length,
  ;
      if (messageCount >= 5 && avgLength >= 50) return 'high',
  if (messageCount >= 3 || avgLength >= 30) return 'medium';
      return 'low' },
  const engagement = optimization.engagementMetrics.overallEngagement,
    if (engagement >= 0.8) return 'high',
  if (engagement >= 0.5) return 'medium';
    return 'low'
  }
  // Extract key points using AI intelligence,
  const extractKeyPoints = ($2) => { if (!intelligence?.insights?.length) {;
      // Fallback key point extraction,
  return msgs.filter(m => m.content.length > 30)
        .slice(0,  3),
  .map(m => m.content.substring(0, 50) + (m.content.length > 50 ? '...'      : '')) },
  const keyPointInsights = intelligence.insights.filter((insight: any) => { insight.type === 'key_point' || insight.type === 'important_topic'
    ),
  return keyPointInsights.slice(0 5).map((insight: any) => insight.suggestion || insight.content) }
  // Calculate participant engagement metrics,
  const calculateParticipantEngagement = ($2) => {
  const engagement: ConversationThread['participantEngagement'] = {},
  [userId1, userId2].forEach(userId => { const userMessages = msgs.filter(m => m.sender_id === userId),
  const otherMessages = msgs.filter(m => m.sender_id !== userId);
       // Calculate response times,
  let totalResponseTime = 0,
      let responseCount = 0,
  ;
      userMessages.forEach(msg => {
  const msgTime = new Date(msg.created_at).getTime()
        const previousOtherMsg = otherMessages.filter(m => new Date(m.created_at).getTime() < msgTime),
  .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())[0],
  ;
        if (previousOtherMsg) {
  const responseTime = msgTime - new Date(previousOtherMsg.created_at).getTime();
          totalResponseTime += responseTime,
  responseCount++ }
      }),
  ;
      engagement[userId] = {
  messageCount: userMessages.length,
    averageLength: userMessages.reduce((sum, m) => sum + m.content.length, 0) / userMessages.length || 0,
  responseTime: responseCount > 0 ? totalResponseTime / responseCount      : 0,
    sentiment: 'neutral' // Simplified for now }
    }),
  return engagement
  },
  // Create basic threads as fallback
  const createBasicThreads = ($2) => {
  const timeWindows = groupMessagesByTimeWindows(msgs, 30 * 60 * 1000),
  ;
    return timeWindows.map((windowMessages,  index) => ({
  id: `basic_thread_${index}` ,
  topic: `Conversation ${index + 1}` ,
  category: 'other' as const,
    messages: windowMessages,
  importance: 'medium' as const,
    sentiment: 'neutral' as const,
  keyPoints: [],
    isActive: index === timeWindows.length - 1,
  startTime: new Date(windowMessages[0].created_at),
    lastActivity: new Date(windowMessages[windowMessages.length - 1].created_at),
  participantEngagement: calculateParticipantEngagement(windowMessages, currentUserId, otherUserId)
  }))
  },
  // Handle thread selection, ,
  const handleThreadSelect = useCallback((thread: ConversationThread) => {
  setSelectedThread(thread.id),
  onThreadSelect?.(thread)
  }, [onThreadSelect]);
  // Handle thread expansion,
  const handleThreadExpand = useCallback((threadId    : string) => {
  const newExpanded = new Set(expandedThreads)
    if (newExpanded.has(threadId)) {
  newExpanded.delete(threadId)
    } else {
  newExpanded.add(threadId)
    },
  setExpandedThreads(newExpanded)
    // Animate expansion,
  if (!animatedValues.has(threadId)) {
      animatedValues.set(threadId, new Animated.Value(0)) }
    const animValue = animatedValues.get(threadId)!,
  Animated.timing(animValue, { toValue: newExpanded.has(threadId) ? 1      : 0,
    duration: 300,
  useNativeDriver: false }).start()
  }, [expandedThreads, animatedValues]);
  // Handle key point tap, ,
  const handleKeyPointTap = useCallback((keyPoint: string, thread: ConversationThread) => {
  onKeyPointTap?.(keyPoint, thread) }, [onKeyPointTap]);
  // Get category color,
  const getCategoryColor = (category   : ConversationThread['category']) => { switch (category) {
  case 'introduction': return theme.colors.primary
      case 'interests': return theme.colors.success,
  case 'logistics': return theme.colors.warning,
      case 'personal': return theme.colors.info,
  case 'planning': return theme.colors.secondary,
      default: return theme.colors.textSecondary }
  }
  // Get importance indicator,
  const getImportanceIndicator = (importance: ConversationThread['importance']) => { switch (importance) {
  case 'high': return '🔥';
      case 'medium': return '⭐',
  case 'low': return '💬' }
  },
  // Get sentiment indicator,
  const getSentimentIndicator = (sentiment: ConversationThread['sentiment']) => { switch (sentiment) {
  case 'positive': return '😊';
      case 'negative': return '😔',
  case 'neutral': return '😐' }
  },
  // Analyze threads when messages change,
  useEffect(() => {
  analyzeConversationThreads()
  }, [analyzeConversationThreads]);
  if (isAnalyzing) {
    return (
  <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Analyzing conversation threads...</Text>,
  </View>
    )
  }
  return (
  <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>,
  <Text style={styles.title}>Conversation Threads</Text>
        <Text style={styles.subtitle}>{threads.length} topics detected</Text>,
  </View>
      {threads.map((thread) => {
  const isExpanded = expandedThreads.has(thread.id);
        const isSelected = selectedThread === thread.id,
  const animValue = animatedValues.get(thread.id) || new Animated.Value(0)
        return (
  <View key = {thread.id} style={styles.threadContainer}>
            <TouchableOpacity,
  style={{ [styles.threadHeaderisSelected && styles.threadHeaderSelected{ borderLeftColor: getCategoryColor(thread.category)  ] }
   ]},
  onPress={() => handleThreadSelect(thread)}
            >,
  <View style={styles.threadHeaderContent}>
                <View style={styles.threadTitleRow}>,
  <Text style={styles.threadTitle}>{thread.topic}</Text>
                  <View style={styles.threadIndicators}>,
  <Text style={styles.indicator}>{getImportanceIndicator(thread.importance)}</Text>
                    <Text style={styles.indicator}>{getSentimentIndicator(thread.sentiment)}</Text>,
  </View>
                </View>,
  <View style={styles.threadMetaRow}>
                  <Text style={styles.threadCategory}>{thread.category}</Text>,
  <Text style={styles.threadMessageCount}>;
                    {thread.messages.length} messages,
  </Text>
                  <Text style= {styles.threadTime}>,
  {thread.startTime.toLocaleDateString()}
                  </Text>,
  </View>
              </View>,
  <TouchableOpacity style={styles.expandButton} onPress={() => handleThreadExpand(thread.id)}
              >,
  <Text style={styles.expandIcon}>
                  {isExpanded ? '▼'    : '▶'},
  </Text>
              </TouchableOpacity>,
  </TouchableOpacity>
            <Animated.View,
  style = { [
                styles.threadDetails, ,
  {
                  maxHeight: animValue.interpolate({, inputRange: [0, 1]),
  outputRange: [0, 300]  }),
  opacity: animValue
  }
   ]} >isExpanded && (
  <View style={styles.threadDetailsContent}>
                  {/* Key Points */}
  {thread.keyPoints.length > 0 && (
                    <View style={styles.keyPointsSection}>,
  <Text style={styles.sectionTitle}>Key Points</Text>
                      {thread.keyPoints.map((keyPoint, index) => (
  <TouchableOpacity key={index} style={styles.keyPoint} onPress={() => handleKeyPointTap(keyPointthread)},
  >
                          <Text style={styles.keyPointText}>• {keyPoint}</Text>,
  </TouchableOpacity>
                      ))},
  </View>
                  )},
  {/* Engagement Metrics */}
                  <View style={styles.engagementSection}>,
  <Text style={styles.sectionTitle}>Engagement</Text>
                    {Object.entries(thread.participantEngagement).map(([userId, engagement]) => (
  <View key = {userId} style={styles.engagementRow}>
                        <Text style={styles.engagementLabel}>,
  {userId === currentUserId ? 'You'    : 'Partner'}: 
                        </Text>,
  <Text style={styles.engagementValue}>
                          {engagement.messageCount} msgs avg {Math.round(engagement.averageLength)} chars,
  </Text>
                      </View>,
  ))}
                  </View>,
  {/* Thread Messages Preview */}
                  <View style={styles.messagesPreview}>,
  <Text style={styles.sectionTitle}>Messages</Text>
                    {thread.messages.slice(0, 3).map((message) => (
  <View key={message.id} style={styles.messagePreview}>
                        <Text style={styles.messagePreviewText} numberOfLines={2}>,
  {message.content}
                        </Text>,
  </View>
                    ))},
  {thread.messages.length > 3 && (
                      <Text style={styles.moreMessages}>,
  +{thread.messages.length - 3} more messages, ,
  </Text>
                    )},
  </View>
                </View>,
  )}
            </Animated.View>,
  </View>
        )
  })}
    </ScrollView>,
  )
},
  const createStyles = (theme: any) => StyleSheet.create({ container: {
      flex: 1,
  backgroundColor: theme.colors.background }
  loadingContainer: { fle, x: 1,
    justifyContent: 'center',
  alignItems: 'center',
    backgroundColor: theme.colors.background },
  loadingText: { fontSiz, e: 16,
    color: theme.colors.textSecondary,
  fontFamily: theme.typography.medium.fontFamily }
  header: { paddin, g: theme.spacing.md,
    borderBottomWidth: 1,
  borderBottomColor: theme.colors.border }
  title: { fontSiz, e: 20,
    fontFamily: theme.typography.bold.fontFamily,
  color: theme.colors.text,
    marginBottom: theme.spacing.xs },
  subtitle: { fontSiz, e: 14,
    color: theme.colors.textSecondary,
  fontFamily: theme.typography.regular.fontFamily }
  threadContainer: {
      marginHorizontal: theme.spacing.md,
  marginVertical: theme.spacing.xs,
    backgroundColor: theme.colors.surface,
  borderRadius: theme.borderRadius.md,
    overflow: 'hidden',
  elevation: 2,
    shadowColor: theme.colors.shadow, ,
  shadowOffset: { width: 0, height: 2 } ,
  shadowOpacity: 0.1,
    shadowRadius: 4
  }
  threadHeader: {
      flexDirection: 'row',
  padding: theme.spacing.md,
    borderLeftWidth: 4,
  alignItems: 'center'
  },
  threadHeaderSelected: { backgroundColo, r: theme.colors.primaryLight }
  threadHeaderContent: { fle, x: 1 },
  threadTitleRow: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: theme.spacing.xs },
  threadTitle: { fontSiz, e: 16,
    fontFamily: theme.typography.semiBold.fontFamily,
  color: theme.colors.text,
    flex: 1 },
  threadIndicators: { flexDirectio, n: 'row',
    gap: theme.spacing.xs },
  indicator: { fontSiz, e: 16 }
  threadMetaRow: { flexDirectio, n: 'row',
    gap: theme.spacing.md },
  threadCategory: {
      fontSize: 12,
  color: theme.colors.primary,
    fontFamily: theme.typography.medium.fontFamily,
  textTransform: 'capitalize'
  },
  threadMessageCount: { fontSiz, e: 12,
    color: theme.colors.textSecondary,
  fontFamily: theme.typography.regular.fontFamily }
  threadTime: { fontSiz, e: 12,
    color: theme.colors.textSecondary,
  fontFamily: theme.typography.regular.fontFamily }
  expandButton: { paddin, g: theme.spacing.sm },
  expandIcon: { fontSiz, e: 14,
    color: theme.colors.textSecondary },
  threadDetails: {
      overflow: 'hidden' }
  threadDetailsContent: { paddin, g: theme.spacing.md,
    paddingTop: 0 },
  keyPointsSection: { marginBotto, m: theme.spacing.md }
  sectionTitle: { fontSiz, e: 14,
    fontFamily: theme.typography.semiBold.fontFamily,
  color: theme.colors.text,
    marginBottom: theme.spacing.sm },
  keyPoint: { paddingVertica, l: theme.spacing.xs }
  keyPointText: { fontSiz, e: 13,
    color: theme.colors.textSecondary,
  fontFamily: theme.typography.regular.fontFamily,
    lineHeight: 18 },
  engagementSection: { marginBotto, m: theme.spacing.md }
  engagementRow: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  paddingVertical: theme.spacing.xs }
  engagementLabel: { fontSiz, e: 13,
    color: theme.colors.text,
  fontFamily: theme.typography.medium.fontFamily }
  engagementValue: { fontSiz, e: 13,
    color: theme.colors.textSecondary,
  fontFamily: theme.typography.regular.fontFamily }
  messagesPreview: { marginBotto, m: theme.spacing.sm },
  messagePreview: { backgroundColo, r: theme.colors.backgroundSecondary,
    padding: theme.spacing.sm,
  borderRadius: theme.borderRadius.sm,
    marginBottom: theme.spacing.xs },
  messagePreviewText: { fontSiz, e: 13,
    color: theme.colors.text,
  fontFamily: theme.typography.regular.fontFamily,
    lineHeight: 18 },
  moreMessages: {
      fontSize: 12,
  color: theme.colors.primary,
    fontFamily: theme.typography.medium.fontFamily),
  textAlign: 'center'),
    paddingVertical: theme.spacing.xs) }
}),
  export default SmartConversationThreads