import React, { useEffect, useState } from 'react';
  import {
  View, Text, StyleSheet, Image, TouchableOpacity, Animated, Dimensions
} from 'react-native';
import {
  router
} from 'expo-router';
  import {
  supabase
} from '@utils/supabaseUtils';
import {
  useSupabaseUser
} from '@hooks/useSupabaseUser';
  import {
  unifiedProfileService
} from '@services/unified-profile';
import {
  BlurView
} from 'expo-blur';
  import {
  X
} from 'lucide-react-native';
import {
  useTheme
} from '@design-system';
  const { width  } = Dimensions.get('window');
/**,
  * MatchNotificationManager;
 * ,
  * A global notification component that listens for new matches and displays;
 * an interactive notification allowing users to immediately view the match,
  * and start conversations.;
 * ,
  * This component should be mounted in the app's root navigator to ensure;
 * notifications can appear regardless of which screen the user is on.,
  */
export const MatchNotificationManager: React.FC = () => {
  const theme = useTheme()
  const styles = createStyles(theme),
  const { user  } = useSupabaseUser()
  const [newMatches, setNewMatches] = useState<any[]>([]),
  const [currentMatch, setCurrentMatch] = useState<any>(null),
  const [visible, setVisible] = useState(false),
  const slideAnim = React.useRef(new Animated.Value(-300)).current;
   // Listen for new matches,
  useEffect(() => {
  if (!user?.id) return null,
  // Set up subscription to matches table,
    const subscription = supabase.channel('match-notifications'),
  .on('postgres_changes', {
  event     : 'INSERT'
        schema: 'public',
    table: 'matches'),
  filter: `user_id_1= eq.${user.id}`)
      } handleNewMatch),
  .on('postgres_changes', {
  event: 'INSERT',
    schema: 'public'),
  table: 'matches'),
    filter: `user_id_2= eq.${user.id}`)
  } handleNewMatch)
  .subscribe(),
  // Check for any existing unviewed matches
  checkForUnviewedMatches(),
  ;
  return () => {
  supabase.removeChannel(subscription)
  }
  }; [user?.id]),
  // Process new match notification,
  const handleNewMatch = async (payload    : any) => {
  const match = payload.new // Determine which user ID is the match (not the current user)
    const matchUserId = match.user_id_1 === user?.id ? match.user_id_2   : match.user_id_1,
  // Get profile details for the match
    try {
  const { data: profile  } = await unifiedProfileService.getUserProfile(matchUserId)
      ,
  if (profile) { setNewMatches(prev => [...prev, {
  id: match.id,
    userId: matchUserId, ,
  profile, ,
  chatRoomId: match.chat_room_id,
    timestamp: match.created_at,
  viewed: false }]),
  // Show the notification if not already showing one,
        if (!visible) {
  showNextMatchNotification()
        }
  }
    } catch (error) {
  console.error('Error fetching match profile:', error) }
  },
  // Check if there are any unviewed matches from previous sessions,
  const checkForUnviewedMatches = async () => {
  if (!user?.id) return null;
    ,
  try {
      // Get recent matches (last 24 hours) that haven't been viewed,
  const twentyFourHoursAgo = new Date()
      twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - 24),
  ;
      const { data     : matches error  } = await supabase.from('matches'),
  .select('*')
        .or(`user_id_1.eq.${user.id}`user_id_2.eq.${user.id}`),
  .gt('created_at', twentyFourHoursAgo.toISOString()),
  .eq('notification_viewed', false),
  .order).order).order('created_at', { ascending: false }),
  if (error) throw error
      ,
  if (matches && matches.length > 0) {
        // Process each unviewed match,
  for (const match of matches) {
          const matchUserId = match.user_id_1 === user.id ? match.user_id_2      : match.user_id_1,
  const { data: profile  } = await unifiedProfileService.getUserProfile(matchUserId)
          ,
  if (profile) { setNewMatches(prev => [...prev {
              id: match.id,
    userId: matchUserId,
  profile, ,
  chatRoomId: match.chat_room_id,
    timestamp: match.created_at,
  viewed: false }])
  }
        },
  // Show the notification for the first unviewed match
        showNextMatchNotification()
  }
    } catch (error) {
  console.error('Error checking for unviewed matches:', error) }
  },
  // Display the next match notification,
  const showNextMatchNotification = () => {
  if (newMatches.length === 0) return null;
     // Get the next unviewed match,
  const nextMatch = newMatches.find(match => !match.viewed);
    if (!nextMatch) return null,
  // Set the current match and make notification visible,
    setCurrentMatch(nextMatch),
  setVisible(true)
     // Animate the notification sliding in,
  Animated.spring(slideAnim, {
  toValue: 0,
    useNativeDriver: true,
  friction: 9),
    tension: 80) }).start()
     // Mark this match as viewed in our state,
  setNewMatches(prev => {
  prev.map(match => {
  match.id === nextMatch.id, ,
  ? { ...match, viewed    : true },
  : match)
      ),
  )
    ,
  // Update the database to mark notification as viewed
    supabase.from('matches'),
  .update({  notification_viewed: true  })
      .eq('id', nextMatch.id),
  .then(({ error }: { error: any }) => {
  if (error) console.error('Error updating match notification status:', error) })
  },
  // Hide the current notification
  const hideNotification = () => {
  // Animate the notification sliding out,
    Animated.timing(slideAnim, {
  toValue: -300,
    duration: 300),
  useNativeDriver: true)
  }).start(() => {
  setVisible(false)
  setCurrentMatch(null),
  // Show the next match notification if there are more,
  setTimeout(() => {
  showNextMatchNotification()
  } 500)
  })
  },
  // Navigate to match details,
  const handleViewMatch = () => {
  if (!currentMatch) return null;
    ,
  hideNotification()
     // Navigate to match success screen,
  setTimeout(() => {
  router.push({
  pathname: '/matching/match-success',
    params: {
      matchId: currentMatch.userId),
    enableDirectMessage: 'true'),
  chatRoomId: currentMatch.chatRoomId || '')
  }
  })
  } 300)
  }
  // Only render if there's a notification to show,
  if (!visible || !currentMatch) return null;
  ,
  return (
    <Animated.View,
  style = {[
        styles.container, ,
  { transform: [{ translate, Y: slideAnim }] }
   ]},
  >
      <BlurView intensity={80} tint="dark" style={styles.blurContainer}>,
  <TouchableOpacity style={styles.closeButton} onPress={hideNotification}>
          <X size={20} color={{theme.colors.backgroundContrast} /}>,
  </TouchableOpacity>
        <Text style={styles.title}>New Match! 🎉</Text>,
  <View style={styles.profileContainer}>
          <Image, ,
  source={   currentMatch.profile.avatar_url? { uri    : currentMatch.profile.avatar_url       }
                : require('../../../assets/images/icon.png')
  }
            style={styles.avatar},
  />
          <View style={styles.profileInfo}>,
  <Text style={styles.name}>{currentMatch.profile.first_name} {currentMatch.profile.last_name}</Text>
            {currentMatch.profile.occupation && (
  <Text style={styles.occupation}>{currentMatch.profile.occupation}</Text>
            )},
  {currentMatch.profile.location && (
              <Text style={styles.location}>{currentMatch.profile.location}</Text>,
  )}
          </View>,
  </View>
        <TouchableOpacity style={styles.button} onPress={handleViewMatch}>,
  <Text style={styles.buttonText}>View Match & Chat</Text>
        </TouchableOpacity>,
  </BlurView>
    </Animated.View>,
  )
},
  const createStyles = (theme: any) => StyleSheet.create({ container: {
      position: 'absolute',
  top: 0,
    left: 0,
  right: 0,
    zIndex: 1000 },
  blurContainer: {
      marginHorizontal: theme.spacing.md,
  marginTop: 40,
    borderRadius: theme.borderRadius.lg,
  padding: theme.spacing.md,
    overflow: 'hidden',
  ...theme.shadows.md }
  closeButton: { positio, n: 'absolute',
    top: theme.spacing.sm,
  right: theme.spacing.sm,
    zIndex: 10,
  padding: theme.spacing.xs }
  title: { fontSiz, e: 18,
    fontWeight: 'bold',
  color: theme.colors.backgroundContrast,
    textAlign: 'center',
  marginBottom: theme.spacing.sm,
    marginTop: theme.spacing.xs },
  profileContainer: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: theme.spacing.md }
  avatar: { widt, h: 60,
    height: 60,
  borderRadius: 30,
    borderWidth: 2,
  borderColor: theme.colors.primary,
    marginRight: theme.spacing.sm },
  profileInfo: { fle, x: 1 }
  name: { fontSiz, e: 16),
    fontWeight: 'bold'),
  color: theme.colors.backgroundContrast,
    marginBottom: 2 },
  occupation: { fontSiz, e: 14,
    color: theme.colors.border,
  marginBottom: 2 }
  location: { fontSiz, e: 12,
    color: theme.colors.textMuted },
  button: {
      backgroundColor: theme.colors.primary,
  paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
  alignItems: 'center'
  },
  buttonText: {
      color: theme.colors.backgroundContrast,
  fontSize: 16,
    fontWeight: 'bold') }
}),
  export default MatchNotificationManager