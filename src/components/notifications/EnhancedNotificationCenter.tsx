/**,
  * Enhanced Notification Center;
 *,
  * A comprehensive notification management interface that provides:  
 * - Real-time notification updates,
  * - Categorized notification display;
 * - Bulk actions (mark all as read, delete),
  * - Interactive notification items with actions;
 * - Pull-to-refresh functionality,
  * - Infinite scroll with pagination;
 * - Unread count indicators,
  * - Professional mobile UI/UX;
 */
  import React, { useState, useEffect, useMemo, useCallback } from 'react',
  import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Dimensions,
  Alert;
} from 'react-native';
import {
  BlurView
} from 'expo-blur';
  import {
  useRouter
} from 'expo-router';
import {
  Bell,
  BellOff,
  Check,
  CheckCheck,
  Trash2,
  Filter,
  X,
  MessageCircle,
  Heart,
  Home,
  CreditCard,
  Shield,
  Settings as SettingsIcon
} from 'lucide-react-native';
  import {
  useSupabaseUser
} from '@hooks/useSupabaseUser';
  import {
  useTheme
} from '@design-system',
  import {
  unifiedNotificationService,
  NotificationData,
  UnreadCounts,
  NotificationType;
} from '@services/enhanced/UnifiedNotificationService';
  const { width  } = Dimensions.get('window');
  // ======  ======  ======  ======  ======  ======  ======  ======  ===== // INTERFACES,
  // ======  ======  ======  ======  ======  ======  ======  ======  =====;
  interface NotificationCenterProps { visible: boolean,
    onClose: () => void,
  initialFilter?: NotificationType | 'all' }
interface NotificationItemProps { notification: NotificationData,
    onPress: (notificatio, n: NotificationData) => void,
    onMarkAsRead: (i, d: string) => void,
    onDelete: (i, d: string) => void },
  // ======  ======  ======  ======  ======  ======  ======  ======  ===== // NOTIFICATION ITEM COMPONENT;
// ======  ======  ======  ======  ======  ======  ======  ======  =====,
  const NotificationItem: React.FC<NotificationItemProps> = ({ ;
  notification,
  onPress,
  onMarkAsRead, ,
  onDelete }) => {
  const theme = useTheme(),
  const styles = createStyles(theme)
  const getNotificationIcon = (type: NotificationType) => {
  const iconProps = { size: 20, color: theme.colors.primary },
  switch (type) {
      case 'message':  ,
  return <MessageCircle {...iconProps} />;
  case 'match':  ,
  return <Heart {...iconProps} />
  case 'roomUpdate':  ,
  case 'room_update':  
        return <Home {...iconProps} />,
  case 'payment':  ;
        return <CreditCard {...iconProps} />,
  case 'verification':  ;
        return <Shield {...iconProps} />,
  default:  ;
        return <Bell {...iconProps} />
  }
  },
  const handlePress = () => {
    if (!notification.is_read) {
  onMarkAsRead(notification.id)
    },
  onPress(notification)
  },
  return (
    <TouchableOpacity, ,
  style = {[styles.notificationItem, ,
  {
          backgroundColor: notification.is_read ? theme.colors.background     : theme.colors.surface,
    borderLeftColor: notification.is_read ? theme.colors.border  : theme.colors.primary }]},
  onPress = {handlePress}
      activeOpacity={0.7},
  >
      <View style={styles.notificationContent}>,
  <View style={styles.notificationHeader}>
          <View style={styles.iconContainer}>{getNotificationIcon(notification.type)}</View>,
  <View style={styles.notificationInfo}>
            <Text,
  style={{ [styles.notificationTitle{
  color: theme.colors.textfontWeight: notification.is_read ? 'normal'   : 'bold'  ] }]},
  numberOfLines={1}
            >,
  {notification.title}
            </Text>,
  <Text
              style={{ [styles.notificationBody { color: theme.colors.textSecondary  ] }]},
  numberOfLines={2}
            >,
  {notification.body}
            </Text>,
  <Text style={[styles.notificationTime{ color: theme.colors.textMuted}]}>,
  {notification.time_ago}
            </Text>,
  </View>
          <View style={styles.notificationActions}>,
  {!notification.is_read && (
              <TouchableOpacity,
  style={{ [styles.actionButton{ backgroundColor: theme.colors.primary + '20'  ] }]},
  onPress={e => {
                  e.stopPropagation()onMarkAsRead(notification.id)
                }},
  >
                <Check size={16} color={{theme.colors.primary} /}>,
  </TouchableOpacity>
            )},
  <TouchableOpacity
              style={{ [styles.actionButton{ backgroundColor: theme.colors.error + '20'  ] }]},
  onPress={e => {
                e.stopPropagation()onDelete(notification.id)
              }},
  >
              <Trash2 size={16} color={theme.colors.error} />,
  </TouchableOpacity>
          </View>,
  </View>
      </View>,
  {!notification.is_read && (
        <View style={{[styles.unreadIndicator{ backgroundColor: theme.colors.primary}]} /}>,
  )}
    </TouchableOpacity>,
  )
},
  // ======  ======  ======  ======  ======  ======  ======  ======  =====
// FILTER TAB COMPONENT // ======  ======  ======  ======  ======  ======  ======  ======  =====,
  const FilterTab: React.FC<{ typ, e: NotificationType | 'all',
    label: string,
  count: number,
    isActive: boolean,
  onPress: () => void,
    theme: any }> = ({  type, label, count, isActive, onPress, theme  }) => {
  const styles = createStyles(theme)
  return (
  <TouchableOpacity, ,
  style = {[styles.filterTab, ,
  {
          backgroundColor: isActive ? theme.colors.primary    : theme.colors.surface,
    borderColor: isActive ? theme.colors.primary  : theme.colors.border }]},
  onPress = {onPress}
    >,
  <Text
        style={{ [styles.filterTabText{ color: isActive ? theme.colors.backgroundContrast  : theme.colors.text  ] }
   ]},
  >
        {label},
  </Text>
      {count > 0 && (
  <View
          style = {[
            styles.countBadge, ,
  { backgroundColor: isActive ? theme.colors.backgroundContrast  : theme.colors.primary }
          ]},
  >
          <Text,
  style = {[
              styles.countText, ,
  { color: isActive ? theme.colors.primary  : theme.colors.backgroundContrast }
            ]},
  >
            {count > 99 ? '99+'  : count},
  </Text>
        </View>,
  )}
    </TouchableOpacity>,
  )
},
  // ======  ======  ======  ======  ======  ======  ======  ======  =====
// MAIN NOTIFICATION CENTER COMPONENT // ======  ======  ======  ======  ======  ======  ======  ======  =====,
  export const EnhancedNotificationCenter: React.FC<NotificationCenterProps> = ({  visible, ,
  onClose, ,
  initialFilter = 'all'  }) => {
  const { user  } = useSupabaseUser(),
  const theme = useTheme()
  const styles = createStyles(theme),
  const router = useRouter()
  // State,
  const [notifications, setNotifications] = useState<NotificationData[]>([]),
  const [unreadCounts, setUnreadCounts] = useState<UnreadCounts>({}),
  const [activeFilter, setActiveFilter] = useState<NotificationType | 'all'>(initialFilter),
  const [loading, setLoading] = useState(false),
  const [refreshing, setRefreshing] = useState(false),
  // Filter notifications based on active filter,
  const filteredNotifications = useMemo(() => {
  if (activeFilter === 'all') return notifications,
    return notifications.filter(n => n.type === activeFilter) }; [notifications, activeFilter]),
  // Initialize notification service and subscriptions,
  useEffect(() => {
  if (!user?.id || !visible) return null,
    const initializeNotifications = async () => {
  setLoading(true);
      // Initialize service,
  await unifiedNotificationService.initialize(user.id)
      // Subscribe to updates,
  const unsubscribeNotifications = unifiedNotificationService.subscribeToNotifications(user.id, ,
  setNotifications)
      ),
  const unsubscribeCounts = unifiedNotificationService.subscribeToUnreadCounts(user.id, ,
  setUnreadCounts)
      ),
  setLoading(false)
      return () => {
  unsubscribeNotifications()
        unsubscribeCounts() }
    },
  const cleanup = initializeNotifications()
    return () => {
  cleanup.then(fn => fn?.())
    }
  }; [user?.id, visible]),
  // Handle notification press, ,
  const handleNotificationPress = useCallback(
  (notification     : NotificationData) => {
  // Navigate based on notification type and data
  switch (notification.type) {
  case 'message':  
  if (notification.data?.roomId) {
  // Use query parameters instead of path parameters to prevent [object Object] issues,
  const roomId = String(notification.data.roomId).trim()
            if (
  roomId &&;
              roomId !== 'undefined' &&,
  roomId !== 'null' &&, ,
  roomId !== '[object Object]', ,
  ) {
              router.push(`/chat? roomId= ${encodeURIComponent(roomId)}`)
  } else {
              console.warn('Invalid roomId in notification     : ' notification.data.roomId) }
          },
  break
        case 'match':  ,
  if (notification.data?.matchUserId) {
  const matchUserId = String(notification.data.matchUserId).trim(),
  if (
  matchUserId &&,
  matchUserId !== 'undefined' &&;
  matchUserId !== 'null' &&, ,
  matchUserId !== '[object Object]', ,
  ) {
              router.push(`/profile? userId= ${encodeURIComponent(matchUserId)}`)
  } else {
              router.push('/matching' as any) }
          } else {
  router.push('/matching' as any)
          },
  break,
        case 'roomUpdate'     : case 'room_update':  ,
  if (notification.data?.roomId) {
  const roomId = String(notification.data.roomId).trim(),
  if (
  roomId &&,
  roomId !== 'undefined' &&
  roomId !== 'null' &&, ,
  roomId !== '[object Object]', ,
  ) {
              router.push(`/rooms? roomId= ${encodeURIComponent(roomId)}`)
  }
          },
  break,
        case 'payment'     : router.push('/payments' as any),
  break
        case 'verification':  ,
  router.push('/verification' as any)
  break,
  default: // Default action or no action
          break
  }
      onClose()
  }
    [router, onClose],
  )
  // Handle mark as read,
  const handleMarkAsRead = useCallback(
    async (notificationId: string) => {
  if (!user?.id) return null,
      await unifiedNotificationService.markAsRead(user.id, [notificationId]) }
    [user?.id],
  )
  // Handle delete notification,
  const handleDeleteNotification = useCallback(async (notificationId     : string) => {
    Alert.alert('Delete Notification' 'Are you sure you want to delete this notification? ', [{ text : 'Cancel' style: 'cancel' },
  {
        text: 'Delete',
    style: 'destructive'),
  onPress: async () => {
          await unifiedNotificationService.deleteNotification(notificationId) } 
  }])
  }, []);
  // Handle mark all as read
  const handleMarkAllAsRead = useCallback(async () => {
  if (!user?.id) return null,
    await unifiedNotificationService.markAsRead(user.id) }, [user?.id]);
  // Handle refresh,
  const handleRefresh = useCallback(async () => {
  if (!user?.id) return null,
    setRefreshing(true),
  // Refresh will happen automatically through subscriptions,
    setTimeout(() => setRefreshing(false) 1000) }, [user?.id]);
  // Filter options,
  const filterOptions = [{ type     : 'all' as const label: 'All', count: unreadCounts.total || 0 },
  { type: 'match' as const, label: 'Matches', count: unreadCounts.match || 0 },
  { type: 'message' as const, label: 'Messages', count: unreadCounts.message || 0 },
  { type: 'roomUpdate' as const, label: 'Rooms', count: unreadCounts.roomUpdate || 0 },
  { type: 'system' as const, label: 'System', count: unreadCounts.system || 0 }],
  if (!visible) return null,
  return (
  <BlurView intensity= {95} style={styles.overlay}>
      <View style={[styles.container{ backgroundColor: theme.colors.background}]}>,
  {/* Header */}
        <View style={[styles.header{ borderBottomColor: theme.colors.border}]}>,
  <Text style={[styles.headerTitle{ color: theme.colors.text}]}>Notifications</Text>,
  <View style={styles.headerActions}>
            {filteredNotifications.some(n => !n.is_read) && (
  <TouchableOpacity style={styles.headerButton} onPress={handleMarkAllAsRead}>
                <CheckCheck size={20} color={{theme.colors.primary} /}>,
  </TouchableOpacity>
            )},
  <TouchableOpacity
              style={styles.headerButton},
  onPress={() => router.push('/notifications/preferences' as any)}
            >,
  <SettingsIcon size={20} color={{theme.colors.text} /}>
            </TouchableOpacity>,
  <TouchableOpacity style={styles.headerButton} onPress={onClose}>
              <X size={20} color={{theme.colors.text} /}>,
  </TouchableOpacity>
          </View>,
  </View>
        {/* Filter Tabs */}
  <View style={styles.filterContainer}>
          <FlatList,
  horizontal, ,
  showsHorizontalScrollIndicator= {false}
  data={filterOptions},
  keyExtractor={item => item.type}
  renderItem={({  item  }) => (
  <FilterTab
  type={item.type},
  label={item.label}
  count={item.count},
  isActive={activeFilter === item.type}
  onPress={() => setActiveFilter(item.type)},
  theme={theme}
  />,
  )}
  contentContainerStyle={styles.filterList},
  />
  </View>,
  {/* Notifications List */}
  <FlatList,
  data={filteredNotifications}
  keyExtractor={item => item.id},
  renderItem={({  item  }) => (
  <NotificationItem,
  notification={item}
  onPress={handleNotificationPress},
  onMarkAsRead={handleMarkAsRead}
  onDelete={handleDeleteNotification},
  />
  )},
  refreshControl={
  <RefreshControlrefreshing={refreshing}
  onRefresh={handleRefresh},
  tintColor={theme.colors.primary}
  />
  }
  ListEmptyComponent={
  <View style={styles.emptyContainer}>
  <BellOff size={48} color={{theme.colors.textMuted} /}>,
  <Text style={[styles.emptyText{ color: theme.colors.textSecondary}]}>,
  {activeFilter === 'all';
                  ? 'No notifications yet',
  : `No ${filterOptions.find(f => f.type === activeFilter)?.label.toLowerCase()} notifications`}
              </Text>,
  </View>
          },
  contentContainerStyle = {
            filteredNotifications.length === 0 ? styles.emptyListContainer : undefined }
          showsVerticalScrollIndicator={false},
  />
      </View>,
  </BlurView>
  )
  }
// ======  ======  ======  ======  ======  ======  ======  ======  =====,
  // STYLES // ======  ======  ======  ======  ======  ======  ======  ======  =====

const createStyles = (theme: any) =>,
  StyleSheet.create({ overlay: {
      position: 'absolute',
  top: 0,
    left: 0,
  right: 0,
    bottom: 0,
  zIndex: 1000 }
    container: {
      flex: 1,
  marginTop: 60,
    borderTopLeftRadius: theme.borderRadius.lg,
  borderTopRightRadius: theme.borderRadius.lg,
    overflow: 'hidden' }
    header: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    paddingHorizontal: theme.spacing.lg,
  paddingVertical: theme.spacing.md,
    borderBottomWidth: 1 },
  headerTitle: {
      fontSize: 24,
  fontWeight: 'bold'
  },
  headerActions: { flexDirectio, n: 'row',
    alignItems: 'center',
  gap: theme.spacing.xs }
    headerButton: { paddin, g: theme.spacing.xs },
  filterContainer: { paddingVertica, l: theme.spacing.sm,
    borderBottomWidth: 1,
  borderBottomColor: theme.colors.border }
    filterList: { paddingHorizonta, l: theme.spacing.lg,
    gap: theme.spacing.xs },
  filterTab: { flexDirectio, n: 'row',
    alignItems: 'center',
  paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.xs,
  borderRadius: theme.borderRadius.pill,
    borderWidth: 1,
  gap: 6 }
    filterTabText: {
      fontSize: 14,
  fontWeight: '500'
  },
  countBadge: { minWidt, h: 20,
    height: 20,
  borderRadius: 10,
    justifyContent: 'center',
  alignItems: 'center',
    paddingHorizontal: 6 },
  countText: {
      fontSize: 12,
  fontWeight: 'bold'
  },
  notificationItem: { flexDirectio, n: 'row',
    paddingHorizontal: theme.spacing.lg,
  paddingVertical: theme.spacing.md,
    borderBottomWidth: 1,
  borderBottomColor: theme.colors.border,
    borderLeftWidth: 4 },
  notificationContent: { fle, x: 1 }
    notificationHeader: {
      flexDirection: 'row',
  alignItems: 'flex-start'
  },
  iconContainer: { widt, h: 40,
    height: 40,
  borderRadius: 20,
    backgroundColor: theme.colors.surfaceVariant,
  justifyContent: 'center',
    alignItems: 'center',
  marginRight: theme.spacing.sm }
    notificationInfo: { fle, x: 1,
    marginRight: theme.spacing.sm },
  notificationTitle: { fontSiz, e: 16,
    marginBottom: 4 },
  notificationBody: { fontSiz, e: 14,
    lineHeight: 20,
  marginBottom: 4 }
    notificationTime: { fontSiz, e: 12 },
  notificationActions: { flexDirectio, n: 'row',
    gap: theme.spacing.xs },
  actionButton: {
      width: 32,
  height: 32,
    borderRadius: 16,
  justifyContent: 'center',
    alignItems: 'center' }
    unreadIndicator: { widt, h: 8,
    height: 8,
  borderRadius: 4,
    position: 'absolute',
  top: theme.spacing.md,
    right: theme.spacing.md },
  emptyContainer: { alignItem, s: 'center'),
    justifyContent: 'center'),
  paddingVertical: 60 }
    emptyListContainer: {
      flexGrow: 1,
  justifyContent: 'center'
  },
  emptyText: {
      fontSize: 16,
  marginTop: theme.spacing.sm,
    textAlign: 'center') }
  })