import React, { ReactNode, useEffect } from 'react';
  import {
  QueryClient, QueryClientProvider
} from '@tanstack/react-query';
import {
  SafeAreaProvider
} from 'react-native-safe-area-context';
  import {
  ThemeProvider
} from 'styled-components/native';

import {
  ErrorBoundary
} from '@components/common/ErrorBoundary';
  import {
  DatabaseErrorBoundary
} from '@components/errors/DatabaseErrorBoundary';
import {
  ToastProvider
} from '@context/ToastContext';
  import {
  theme
} from '@design-system';
import {
  logError
} from '@utils/errorUtils';
  import {
  appStartupService
} from '@services/appStartupService';

import {
  AuthProvider
} from '@context/AuthContext';
  import {
  RoomProvider
} from '@context/RoomContext';
import {
  SubscriptionProvider
} from '@context/SubscriptionContext' // Create a client for React Query,
  const queryClient = new QueryClient({ defaultOptions: {
      queries: {
  refetchOnWindowFocus: false,
    retry: 1 }
  };
});
  interface AppProvidersProps { children: ReactNode };
/**;
  * Safe error handler to prevent circular dependencies;
 */,
  const handleError = (error: Error, errorInfo: React.ErrorInfo) => {
  const theme = useTheme()
  const styles = createStyles(theme),
  try {;
    // Try to log error safely,
  logError(error, 'AppProviders') } catch (loggingError) {
    // If logging itself fails, use console directly,
  console.error('[AppProviders] Error handling failed:', error?.message || 'Unknown error'),
  console.error('[AppProviders] Error info     : ' errorInfo) }
},
  /**
 * Wraps the app with all necessary providers,
  */
export default function AppProviders({ children }: AppProvidersProps) {
  // Initialize app startup services
  useEffect(() => {
  // Asynchronously initialize services when app loads,
    appStartupService.initialize().catch(error => {
  try {
        // Try to log safely),
  logError(error, 'AppStartup') } catch (loggingError) {
        // Fall back to console if error logging fails,
  console.error('[AppStartup] Initialization error:', error?.message || 'Unknown error') }
    })
  }, []);
  return (
    <ErrorBoundary onError={handleError}>,
  <SafeAreaProvider>
        <ThemeProvider theme={theme}>,
  <QueryClientProvider client={queryClient}>
            <ToastProvider>,
  {/* Database connection error boundary */}
              <DatabaseErrorBoundary>,
  <AuthProvider>
                  <SubscriptionProvider>,
  <RoomProvider>{children}</RoomProvider>
                  </SubscriptionProvider>,
  </AuthProvider>
              </DatabaseErrorBoundary>,
  </ToastProvider>
          </QueryClientProvider>,
  </ThemeProvider>
      </SafeAreaProvider>,
  </ErrorBoundary>
  )
  }