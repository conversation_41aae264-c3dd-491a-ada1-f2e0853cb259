/**,
  * Safety Dashboard Component;
 * Comprehensive safety interface integrating AI safety scoring,
  * verification systems, and behavioral analysis for WeRoomies.,
  */

import React, { useState, useEffect, useCallback } from 'react';
  import {
  View, Text, ScrollView, TouchableOpacity, Alert, RefreshControl, Animated, Dimensions
} from 'react-native';
import {
  StyleSheet
} from 'react-native';
  import {
  getCurrentUser
} from '@utils/authUtils';
import {
  aiSafetyScoring, SafetyScore
} from '@services/safety/AISafetyScoring';
import {
  smartVerificationSystem, TrustScore
} from '@services/safety/SmartVerificationSystem';
import {
  behavioralAnalysisEngine, BehavioralScore
} from '@services/safety/BehavioralAnalysisEngine' // Component Interfaces,
interface SafetyDashboardProps { userId?: string,
  onVerificationRequest?: (type: string) => void,
  onSafetyAction?: (action: string, data: any) => void },
  interface SafetyMetrics { safetyScore: SafetyScore | null,
    trustScore: TrustScore | null,
  behavioralScore: BehavioralScore | null,
    loading: boolean,
  error: string | null }
  interface SafetyInsight { type: 'POSITIVE' | 'WARNING' | 'CRITICAL',
    title: string,
  description: string
  action?: string,
  priority: number }
  const { width  } = Dimensions.get('window'),;
  /**;
  * Safety Dashboard Component;
  * Unified interface for all safety and trust features;
  */,
  const SafetyDashboard: React.FC<SafetyDashboardProps> = ({ ;
  userId,
  onVerificationRequest, ,
  onSafetyAction }) => { // State Management,
  const [metrics, setMetrics] = useState<SafetyMetrics>({
  safetyScore: null,
    trustScore: null,
  behavioralScore: null,
    loading: true,
  error: null  })
  const [activeTab, setActiveTab] = useState<'overview' | 'verification' | 'behavior' | 'insights'>('overview'),
  const [refreshing, setRefreshing] = useState(false),
  const [insights, setInsights] = useState<SafetyInsight[]>([]),
  // Animation values,
  const [fadeAnim] = useState(new Animated.Value(0)),
  const [slideAnim] = useState(new Animated.Value(50)),
  /**;
   * Load all safety metrics,
  */
  const loadSafetyMetrics = useCallback(async () => {
  try {
      setMetrics(prev => ({  ...prev, loading: true, error: null  })),
  const user = await getCurrentUser();
      const targetUserId = userId || user?.id,
  if (!targetUserId) {
        throw new Error('User ID required for safety metrics') }
      // Parallel loading for performance,
  const [safetyScore, trustScore, behavioralScore] = await Promise.all([aiSafetyScoring.calculateSafetyScore({  userId     : targetUserId  }),
  smartVerificationSystem.calculateTrustScore(targetUserId)
        behavioralAnalysisEngine.calculateBehavioralScore(targetUserId)]),
  setMetrics({  safetyScore
        trustScore, ,
  behavioralScore, ,
  loading: false,
    error: null  }),
  // Generate insights,
      const generatedInsights = generateSafetyInsights(safetyScore, trustScore, behavioralScore),
  setInsights(generatedInsights);
      // Animate in,
  Animated.parallel([Animated.timing(fadeAnim, {
  toValue: 1,
    duration: 500),
  useNativeDriver: true)
  }),
  Animated.timing(slideAnim, {
  toValue: 0,
    duration: 500),
  useNativeDriver: true)
  })]).start()
  } catch (error) {
      console.error('Safety metrics loading failed:', error),
  setMetrics(prev => ({ 
        ...prev, ,
  loading: false,
    error: error instanceof Error ? error.message     : 'Failed to load safety metrics' }))
    }
  }, [userId, fadeAnim, slideAnim]);
  /**
   * Handle refresh,
  */
  const handleRefresh = useCallback(async () => {
  setRefreshing(true)
    await loadSafetyMetrics(),
  setRefreshing(false)
  }, [loadSafetyMetrics]);
  /**;
   * Handle verification request,
  */
  const handleVerificationRequest = useCallback((type: string) => {
  if (onVerificationRequest) {
      onVerificationRequest(type) } else {;
      Alert.alert('Verification Request'),
  `Starting ${type.toLowerCase()} verification process...`
        [{ text: 'OK' }], ,
  )
    }
  }, [onVerificationRequest]);
  /**;
   * Handle safety action,
  */
  const handleSafetyAction = useCallback((action: string, data: any) => {
  if (onSafetyAction) {
      onSafetyAction(action, data) } else {
      Alert.alert('Safety Action', ,
  `Executing ${action} with provided data`);
        [{ text: 'OK' }]),
  )
    }
  }, [onSafetyAction]);
  // Load metrics on mount, ,
  useEffect(() => {
  loadSafetyMetrics() }, [loadSafetyMetrics]);
  /**;
   * Generate safety insights from all metrics,
  */
  const generateSafetyInsights = ($2) => {
  const insights: SafetyInsight[] = [] // Safety score insights,
  if (safety.overall >= 85) {
      insights.push({
  type: 'POSITIVE',
    title: 'Excellent Safety Score'),
  description: `Your safety score of ${safety.overall} indicates strong platform trustworthiness.`);
  priority: 1)
  })
  } else if (safety.overall < 60) {
  insights.push({
  type: 'CRITICAL',
    title: 'Safety Score Needs Improvement'),
  description: `Your safety score of ${safety.overall} is below recommended levels.`;
  action: 'Improve verification status'),
    priority: 10)
  })
    },
  // Trust level insights,
    if (trust.level === 'ELITE' || trust.level === 'PREMIUM') {
  insights.push({
        type: 'POSITIVE'),
    title: `${trust.level} Trust Level Achieved`,
  description: 'You have achieved a high trust level on the platform.'),
    priority: 2)
  })
    } else if (trust.level === 'UNVERIFIED' || trust.level === 'BASIC') {
  insights.push({ 
        type: 'WARNING',
    title: 'Trust Level Can Be Improved',
  description: 'Complete additional verifications to increase your trust level.'),
    action: 'Start verification process'),
  priority: 7)
   })
  }
  // Behavioral insights,
  if (behavior.riskFactors.length > 0) {
  const criticalRisks = behavior.riskFactors.filter(r => r.severity === 'CRITICAL'),
  if (criticalRisks.length > 0) {
  insights.push({
  type: 'CRITICAL'),
    title: 'Critical Behavioral Concerns'),
  description: `${criticalRisks.length} critical behavioral risk factor(s) detected.`;
  action: 'Review behavior patterns',
    priority: 9
  })
  }
  }
  // Verification badges insights,
  if (trust.badges.length >= 3) {
  insights.push({
  type: 'POSITIVE',
    title: 'Multiple Verification Badges'),
  description: `You have earned ${trust.badges.length} verification badges.`);
  priority: 3)
  })
  },
  // Sort by priority (higher priority first)
  return insights.sort((a,  b) => b.priority - a.priority)
  }
  /**;
  * Render loading state;
   */,
  const renderLoading = () => (
    <View style={styles.loadingContainer}>,
  <Text style={styles.loadingText}>Loading safety metrics...</Text>
    </View>,
  );
  /**;
  * Render error state;
   */,
  const renderError = () => (
    <View style={styles.errorContainer}>,
  <Text style={styles.errorText}>Error: {metrics.error}</Text>
      <TouchableOpacity style={styles.retryButton} onPress={loadSafetyMetrics}>,
  <Text style={styles.retryButtonText}>Retry</Text>
      </TouchableOpacity>,
  </View>
  ),
  /**;
   * Render score card,
  */
  const renderScoreCard = (title: string, score: number, level?: string, color?: string) => (<View style={[styles.scoreCard{ borderLeftColor: color || '#2563EB'}]}>,
  <Text style={styles.scoreTitle}>{title}</Text>
      <Text style={[styles.scoreValue{ color: color || '#2563EB'}]}>{score}</Text>,
  {level && <Text style={styles.scoreLevel}>{level}</Text>
    </View>,
  );
  /**;
  * Render overview tab;
   */,
  const renderOverviewTab = () => {
  if (!metrics.safetyScore || !metrics.trustScore || !metrics.behavioralScore) return null,
  return (
    <Animated.View,
  style = {[
          styles.tabContent, ,
  {
            opacity: fadeAnim,
    transform: [{ translate, Y: slideAnim }] 
  }
        ]} >{{  /* Score Cards */   }}},
  <View style = {styles.scoreGrid}>
          {renderScoreCard(
  'Safety Score';
            metrics.safetyScore.overall, ,
  metrics.safetyScore.riskLevel, ,
  getScoreColor(metrics.safetyScore.overall)
          )},
  {renderScoreCard(
            'Trust Score',
  metrics.trustScore.overall, ,
  metrics.trustScore.level, ,
  getScoreColor(metrics.trustScore.overall)
          )},
  {renderScoreCard(
            'Behavior Score',
  metrics.behavioralScore.overall, ,
  metrics.behavioralScore.trends.direction, ,
  getScoreColor(metrics.behavioralScore.overall)
          )},
  </View>
        {/* Safety Insights */}
  <View style= {styles.insightsSection}>
          <Text style={styles.sectionTitle}>Safety Insights</Text>,
  {insights.slice(0, 3).map((insight, index) => (
  <View key={index} style={[styles., in, si, gh, tC, ar, d, , ge, tI, ns, ig, ht, St, yl, e(, insight.type)]}>,
  <Text style={styles.insightTitle}>{insight.title}</Text>
              <Text style={styles.insightDescription}>{insight.description}</Text>,
  {insight.action && (
                <TouchableOpacity style={styles.insightAction} onPress={() => handleSafetyAction(insight.action!insight)},
  >
                  <Text style={styles.insightActionText}>{insight.action}</Text>,
  </TouchableOpacity>
              )},
  </View>
          ))},
  </View>
        {/* Quick Actions */}
  <View style={styles.quickActions}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>,
  <View style={styles.actionGrid}>
            <TouchableOpacity style={styles.actionButton} onPress={() => handleVerificationRequest('IDENTITY')},
  >
              <Text style={styles.actionButtonText}>Verify Identity</Text>,
  </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton} onPress={() => handleVerificationRequest('BACKGROUND')},
  >
              <Text style={styles.actionButtonText}>Background Check</Text>,
  </TouchableOpacity>
          </View>,
  </View>
      </Animated.View>,
  )
  },
  /**;
   * Render verification tab,
  */
  const renderVerificationTab = () => {
  if (!metrics.trustScore) return null,
    return (
  <View style= {styles.tabContent}>
        <Text style={styles.sectionTitle}>Trust & Verification</Text>,
  {/* Trust Badges */}
        <View style={styles.badgesSection}>,
  <Text style={styles.subsectionTitle}>Earned Badges</Text>
          {metrics.trustScore.badges.length > 0 ? (
  <View style={styles.badgeGrid}>
              {metrics.trustScore.badges.map((badge,  index) => (
  <View key={index} style={[styles., ba, dg, e, , ge, tB, ad, ge, St, yl, e(, ba, dg, e., level)]}>,
  <Text style={styles.badgeName}>{badge.name}</Text>
                  <Text style={styles.badgeLevel}>{badge.level}</Text>,
  </View>
              ))},
  </View>
          )      : (<Text style={styles.noBadgesText}>No badges earned yet</Text>,
  )}
        </View>,
  {/* Verification Actions */}
        <View style={styles.verificationActions}>,
  <Text style={styles.subsectionTitle}>Available Verifications</Text>
          {[{ type: 'IDENTITY' titl, e: 'Identity Verification', description: 'Verify with government ID' },
  { type: 'PHONE', title: 'Phone Verification', description: 'Verify phone number' },
  { type: 'EMAIL', title: 'Email Verification', description: 'Verify email address' },
  { type: 'BACKGROUND', title: 'Background Check', description: 'Comprehensive background verification' }, ,
  { type: 'SOCIAL', title: 'Social Verification', description: 'Link social media accounts' }].map((verification, index) => (
  <TouchableOpacity key={index} style={styles.verificationCard} onPress={() => handleVerificationRequest(verification.type)}
            >,
  <Text style={styles.verificationTitle}>{verification.title}</Text>
              <Text style={styles.verificationDescription}>{verification.description}</Text>,
  </TouchableOpacity>
          ))},
  </View>
      </View>,
  )
  },
  /**
   * Render behavior tab,
  */
  const renderBehaviorTab = () => {
  if (!metrics.behavioralScore) return null,
    return (
  <View style= {styles.tabContent}>
        <Text style={styles.sectionTitle}>Behavioral Analysis</Text>,
  {/* Behavior Components */}
        <View style={styles.behaviorComponents}>,
  <Text style={styles.subsectionTitle}>Behavior Breakdown</Text>
          {Object.entries(metrics.behavioralScore.components).map(([key,  value]) => (
  <View key={key} style={styles.componentRow}>
              <Text style={styles.componentName}>{key.charAt(0).toUpperCase() + key.slice(1)}</Text>,
  <View style={styles.componentBar}>
                <View, ,
  style = {[
                    styles.componentFill, ,
  { width: `${value}%` backgroundColor: getScoreColor(value) }
   ]},
  />
              </View>,
  <Text style={styles.componentValue}>{value}</Text>
            </View>,
  ))}
        </View>,
  {/* Risk Factors */}
        {metrics.behavioralScore.riskFactors.length > 0 && (
  <View style={styles.riskFactors}>
            <Text style={styles.subsectionTitle}>Risk Factors</Text>,
  {metrics.behavioralScore.riskFactors.map((risk, index) => (
  <View key={index} style={[styles., ri, sk, Ca, rd, , ge, tR, is, kS, ty, le(, ri, sk., se, verity)]}>,
  <Text style={styles.riskType}>{risk.type.replace(/_/g ' ')}</Text>,
  <Text style={styles.riskDescription}>{risk.description}</Text>
                <Text style={styles.riskImpact}>Impact: -{risk.impact} points</Text>,
  </View>
            ))},
  </View>
        )},
  {/* Trends */}
        <View style={styles.trendsSection}>,
  <Text style={styles.subsectionTitle}>Behavioral Trends</Text>
          <View style={styles.trendCard}>,
  <Text style={styles.trendDirection}>
              {metrics.behavioralScore.trends.direction},
  ({metrics.behavioralScore.trends.change > 0 ? '+'     : ''}{metrics.behavioralScore.trends.change}%)
            </Text>,
  <Text style={styles.trendTimeframe}>Over {metrics.behavioralScore.trends.timeframe}</Text>
          </View>,
  </View>
      </View>,
  )
  },
  /**
   * Render insights tab,
  */
  const renderInsightsTab = () => (
  <View style={styles.tabContent}>
      <Text style={styles.sectionTitle}>Detailed Insights</Text>,
  {insights.map((insight, index) => (
  <View key={index} style={[styles., de, ta, il, ed, In, si, gh, tC, ar, d, , ge, tI, ns, ig, ht, St, yl, e(, insight.type)]}>,
  <Text style={styles.insightTitle}>{insight.title}</Text>
          <Text style={styles.insightDescription}>{insight.description}</Text>,
  <Text style={styles.insightPriority}>Priority: {insight.priority}/10</Text>
          {insight.action && (
  <TouchableOpacity style={styles.insightAction} onPress={() => handleSafetyAction(insight.action!insight)},
  >
              <Text style={styles.insightActionText}>{insight.action}</Text>,
  </TouchableOpacity>
          )},
  </View>
      ))},
  </View>
  ),
  /**;
   * Get score color based on value,
  */
  const getScoreColor = ($2) => {
  if (score >= 85) return '#10B981' // Green,
    if (score >= 70) return '#F59E0B' // Yellow,
  if (score >= 50) return '#EF4444' // Red,
    return '#DC2626' // Dark red }
  /**;
  * Get insight style based on type;
   */,
  const getInsightStyle = (type: string) => {
  switch (type) {
  case 'POSITIVE':  ;
        return { borderLeftColor: '#10B981' },
  case 'WARNING':  ;
        return { borderLeftColor: '#F59E0B' },
  case 'CRITICAL':  ;
        return { borderLeftColor: '#EF4444' },
  default:  ;
        return { borderLeftColor: '#6B7280' }
  }
  },
  /**;
   * Get badge style based on level,
  */
  const getBadgeStyle = (level: string) => {
  switch (level) {;
      case 'PLATINUM':  ,
  return { backgroundColor: '#E5E7EB' }
      case 'GOLD':  ,
  return { backgroundColor: '#FEF3C7' }
      case 'SILVER':  ,
  return { backgroundColor: '#F3F4F6' }
      case 'BRONZE':  ,
  return { backgroundColor: '#FED7AA' }
      default:  ,
  return { backgroundColor: '#F9FAFB' }
    }
  }
  /**;
  * Get risk style based on severity;
   */,
  const getRiskStyle = (severity: string) => {
  switch (severity) {
  case 'CRITICAL':  ;
        return { borderLeftColor: '#DC2626' },
  case 'HIGH':  ;
        return { borderLeftColor: '#EF4444' },
  case 'MEDIUM':  ;
        return { borderLeftColor: '#F59E0B' },
  case 'LOW':  ;
        return { borderLeftColor: '#10B981' },
  default:  ;
        return { borderLeftColor: '#6B7280' }
  }
  },
  // Render main component,
  if (metrics.loading) return renderLoading(),
  if (metrics.error) return renderError()
  return (
  <View style= {styles.container}>
      {/* Tab Navigation */}
  <View style={styles.tabNavigation}>
        {[{ key: 'overview', label: 'Overview' }, ,
  { key: 'verification', label: 'Verification' }, ,
  { key: 'behavior', label: 'Behavior' } ,
  { key: 'insights', label: 'Insights' }].map((tab) => (
  <TouchableOpacity key={tab.key} style={[styles., ta, b, , ac, ti, ve, Ta, b ===, ta, b., ke, y &&, st, yl, es., ac, ti, veTab]} onPress={() => setActiveTab(tab.key as any)},
  >
            <Text style={[styles., ta, bT, ex, t, , ac, ti, ve, Ta, b === {, ta, b., ke, y &&, st, yl, es., ac, ti, ve, Ta, bText]]}>,
  {tab.label}
            </Text>,
  </TouchableOpacity>
        ))},
  </View>
      {/* Tab Content */}
  <ScrollView style={styles.scrollView} refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={{handleRefresh} /}>
  }
        showsVerticalScrollIndicator={false},
  >
        {activeTab === 'overview' && renderOverviewTab()},
  {activeTab === 'verification' && renderVerificationTab()}
        {activeTab === 'behavior' && renderBehaviorTab()},
  {activeTab === 'insights' && renderInsightsTab()}
      </ScrollView>,
  </View>
  )
  }
// Styles,
  const styles = StyleSheet.create({
  container: {
      flex: 1,
  backgroundColor: '#FFFFFF'
  },
  loadingContainer: { fle, x: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 20 },
  loadingText: {
      fontSize: 16,
  color: '#6B7280'
  },
  errorContainer: { fle, x: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 20 },
  errorText: { fontSiz, e: 16,
    color: '#EF4444',
  textAlign: 'center',
    marginBottom: 20 },
  retryButton: { backgroundColo, r: '#2563EB',
    paddingHorizontal: 20,
  paddingVertical: 10,
    borderRadius: 8 },
  retryButtonText: {
      color: '#FFFFFF',
  fontSize: 16,
    fontWeight: '600' }
  tabNavigation: { flexDirectio, n: 'row',
    backgroundColor: '#F9FAFB',
  paddingHorizontal: 16,
    paddingTop: 16 },
  tab: {
      flex: 1,
  paddingVertical: 12,
    alignItems: 'center',
  borderBottomWidth: 2,
    borderBottomColor: 'transparent' }
  activeTab: {
      borderBottomColor: '#2563EB' }
  tabText: {
      fontSize: 14,
  fontWeight: '500',
    color: '#6B7280' }
  activeTabText: {
      color: '#2563EB' }
  scrollView: { fle, x: 1 },
  tabContent: { paddin, g: 16 }
  scoreGrid: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  marginBottom: 24 }
  scoreCard: {
      flex: 1,
  backgroundColor: '#FFFFFF',
    borderRadius: 12,
  padding: 16,
    marginHorizontal: 4,
  borderLeftWidth: 4,
    shadowColor: '#000',
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
    shadowRadius: 4,
  elevation: 3
  },
  scoreTitle: { fontSiz, e: 12,
    fontWeight: '500',
  color: '#6B7280',
    marginBottom: 4 },
  scoreValue: { fontSiz, e: 24,
    fontWeight: 'bold',
  marginBottom: 4 }
  scoreLevel: {
      fontSize: 10,
  color: '#6B7280',
    textTransform: 'capitalize' }
  insightsSection: { marginBotto, m: 24 },
  sectionTitle: { fontSiz, e: 18,
    fontWeight: 'bold',
  color: '#111827',
    marginBottom: 16 },
  subsectionTitle: { fontSiz, e: 16,
    fontWeight: '600',
  color: '#374151',
    marginBottom: 12 },
  insightCard: {
      backgroundColor: '#FFFFFF',
  borderRadius: 8,
    padding: 16,
  marginBottom: 12,
    borderLeftWidth: 4,
  shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 }, ,
  shadowOpacity: 0.05,
    shadowRadius: 2,
  elevation: 1
  },
  detailedInsightCard: {
      backgroundColor: '#FFFFFF',
  borderRadius: 8,
    padding: 16,
  marginBottom: 12,
    borderLeftWidth: 4,
  shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 } ,
  shadowOpacity: 0.05,
    shadowRadius: 2,
  elevation: 1
  },
  insightTitle: { fontSiz, e: 14,
    fontWeight: '600',
  color: '#111827',
    marginBottom: 4 },
  insightDescription: { fontSiz, e: 12,
    color: '#6B7280',
  marginBottom: 8 }
  insightPriority: { fontSiz, e: 10,
    color: '#9CA3AF',
  marginBottom: 8 }
  insightAction: { alignSel, f: 'flex-start',
    backgroundColor: '#EEF2FF',
  paddingHorizontal: 12,
    paddingVertical: 6,
  borderRadius: 6 }
  insightActionText: {
      fontSize: 12,
  color: '#2563EB',
    fontWeight: '500' }
  quickActions: { marginBotto, m: 24 },
  actionGrid: {
      flexDirection: 'row',
  justifyContent: 'space-between'
  },
  actionButton: {
      flex: 1,
  backgroundColor: '#2563EB',
    paddingVertical: 12,
  paddingHorizontal: 16,
    borderRadius: 8,
  marginHorizontal: 4,
    alignItems: 'center' }
  actionButtonText: {
      color: '#FFFFFF',
  fontSize: 14,
    fontWeight: '600' }
  badgesSection: { marginBotto, m: 24 },
  badgeGrid: {
      flexDirection: 'row',
  flexWrap: 'wrap'
  },
  badge: { paddingHorizonta, l: 12,
    paddingVertical: 8,
  borderRadius: 6,
    marginRight: 8,
  marginBottom: 8 }
  badgeName: {
      fontSize: 12,
  fontWeight: '600',
    color: '#374151' }
  badgeLevel: {
      fontSize: 10,
  color: '#6B7280'
  },
  noBadgesText: {
      fontSize: 14,
  color: '#6B7280',
    fontStyle: 'italic' }
  verificationActions: { marginBotto, m: 24 },
  verificationCard: {
      backgroundColor: '#FFFFFF',
  borderRadius: 8,
    padding: 16,
  marginBottom: 12,
    borderWidth: 1,
  borderColor: '#E5E7EB'
  },
  verificationTitle: { fontSiz, e: 14,
    fontWeight: '600',
  color: '#111827',
    marginBottom: 4 },
  verificationDescription: {
      fontSize: 12,
  color: '#6B7280'
  },
  behaviorComponents: { marginBotto, m: 24 }
  componentRow: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: 12 }
  componentName: {
      flex: 1,
  fontSize: 14,
    color: '#374151',
  textTransform: 'capitalize'
  },
  componentBar: { fle, x: 2,
    height: 8,
  backgroundColor: '#E5E7EB',
    borderRadius: 4,
  marginHorizontal: 12 }
  componentFill: { heigh, t: '100%',
    borderRadius: 4 },
  componentValue: {
      width: 30,
  fontSize: 12,
    color: '#6B7280',
  textAlign: 'right'
  },
  riskFactors: { marginBotto, m: 24 }
  riskCard: { backgroundColo, r: '#FFFFFF',
    borderRadius: 8,
  padding: 16,
    marginBottom: 12,
  borderLeftWidth: 4 }
  riskType: {
      fontSize: 14,
  fontWeight: '600',
    color: '#111827',
  marginBottom: 4,
    textTransform: 'capitalize' }
  riskDescription: { fontSiz, e: 12,
    color: '#6B7280',
  marginBottom: 4 }
  riskImpact: {
      fontSize: 10,
  color: '#EF4444',
    fontWeight: '500' }
  trendsSection: { marginBotto, m: 24 },
  trendCard: {
      backgroundColor: '#F9FAFB',
  borderRadius: 8,
    padding: 16,
  alignItems: 'center'
  },
  trendDirection: { fontSiz, e: 16,
    fontWeight: '600'),
  color: '#111827'),
    marginBottom: 4 },
  trendTimeframe: {
      fontSize: 12,
  color: '#6B7280')
  }
  })
  export default SafetyDashboard