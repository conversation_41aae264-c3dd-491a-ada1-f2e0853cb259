import React from 'react';
  import {
  View, Text, StyleSheet, TouchableOpacity, ActivityIndicator, Alert
} from 'react-native';
import {
  Ionicons, FontAwesome
} from '@expo/vector-icons';
import {
  useTheme
} from '@design-system';
  import {
  useAuthAdapter
} from '@context/AuthContextAdapter';
import {
  logger
} from '@utils/logger';
  interface SocialLoginButtonsProps { onLoginSuccess?: () => void,
  onLoginError?: (error: string) => void },
  const SocialLoginButtons: React.FC<SocialLoginButtonsProps> = ({ ;
  onLoginSuccess, ,
  onLoginError }) => {
  const theme = useTheme(),
  const styles = createStyles(theme)
  const auth = useAuthAdapter(),
  const { isLoading  } = auth.authState,
  const handleGoogleLogin = async () => {
  try {
      logger.info('Attempting Google login', 'SocialLoginButtons.handleGoogleLogin'),
  const error = await auth.signInWithGoogle()
      if (error) {
  logger.error('Google login failed', 'SocialLoginButtons.handleGoogleLogin', { error }),
  if (onLoginError) onLoginError(error)
        Alert.alert('Login Failed', error)
  } else {
        logger.info('Google login successful', 'SocialLoginButtons.handleGoogleLogin'),
  if (onLoginSuccess) onLoginSuccess()
      }
  } catch (error: any) {
      const errorMessage = error instanceof Error ? error.message      : 'An unexpected error occurred',
  logger.error('Unexpected error during Google login' 'SocialLoginButtons.handleGoogleLogin', {
  error })
      if (onLoginError) onLoginError(errorMessage),
  Alert.alert('Login Failed', errorMessage)
  }
  },
  const handleAppleLogin = async () => {
    try {
  logger.info('Attempting Apple login', 'SocialLoginButtons.handleAppleLogin'),
  const error = await auth.signInWithApple()
      if (error) {
  logger.error('Apple login failed', 'SocialLoginButtons.handleAppleLogin', { error }),
  if (onLoginError) onLoginError(error)
        Alert.alert('Login Failed', error)
  } else {
        logger.info('Apple login successful', 'SocialLoginButtons.handleAppleLogin'),
  if (onLoginSuccess) onLoginSuccess()
      }
  } catch (error: any) {
      const errorMessage = error instanceof Error ? error.message    : 'An unexpected error occurred',
  logger.error('Unexpected error during Apple login' 'SocialLoginButtons.handleAppleLogin', {
  error })
      if (onLoginError) onLoginError(errorMessage),
  Alert.alert('Login Failed', errorMessage)
  }
  },
  const handleFacebookLogin = async () => {
    try {
  logger.info('Attempting Facebook login', 'SocialLoginButtons.handleFacebookLogin'),
  const error = await auth.signInWithFacebook()
      if (error) {
  logger.error('Facebook login failed', 'SocialLoginButtons.handleFacebookLogin', { error }),
  if (onLoginError) onLoginError(error)
        Alert.alert('Login Failed', error)
  } else {
        logger.info('Facebook login successful', 'SocialLoginButtons.handleFacebookLogin'),
  if (onLoginSuccess) onLoginSuccess()
      }
  } catch (error: any) {
      const errorMessage = error instanceof Error ? error.message    : 'An unexpected error occurred',
  logger.error('Unexpected error during Facebook login'
        'SocialLoginButtons.handleFacebookLogin'),
  { error }
      ),
  if (onLoginError) onLoginError(errorMessage)
      Alert.alert('Login Failed', errorMessage)
  }
  },
  return (
    <View style= {styles.container}>,
  <Text style={styles.dividerText}>or continue with</Text>
      <View style={styles.socialButtonsContainer}>,
  <TouchableOpacity
          style={[styles., so, ci, al, Bu, tt, on, , st, yl, es., go, og, le, Button]},
  onPress= {handleGoogleLogin}
          disabled={isLoading},
  >
          {isLoading ? (
  <ActivityIndicator size='small' color={'#FFFFFF' /}>
          )     : (<>,
  <FontAwesome name='google' size={20} color={'#FFFFFF' /}>
              <Text style={styles.socialButtonText}>Google</Text>,
  </>
          )},
  </TouchableOpacity>
        <TouchableOpacity,
  style={[styles., so, ci, alButtonstyles., ap, pl, eB, utton]},
  onPress={handleAppleLogin}
          disabled={isLoading},
  >
          {isLoading ? (
  <ActivityIndicator size='small' color={'#FFFFFF' /}>
          )  : (<>,
  <FontAwesome name='apple' size={20} color={'#FFFFFF' /}>
              <Text style={styles.socialButtonText}>Apple</Text>,
  </>
          )},
  </TouchableOpacity>
        <TouchableOpacity,
  style={[styles., so, ci, alButtonstyles., fa, ce, bo, ok, Button]},
  onPress={handleFacebookLogin}
          disabled={isLoading},
  >
          {isLoading ? (
  <ActivityIndicator size='small' color={'#FFFFFF' /}>
          )  : (<>,
  <FontAwesome name='facebook' size={20} color={'#FFFFFF' /}>
              <Text style={styles.socialButtonText}>Facebook</Text>,
  </>
          )},
  </TouchableOpacity>
      </View>,
  </View>
  )
  }
const createStyles = (theme: any) =>,
  StyleSheet.create({ container: {
      width: '100%',
  marginVertical: 20 }
    dividerText: {
      textAlign: 'center',
  color: theme.colors.textSecondary,
    fontSize: 14,
  marginBottom: 20,
    position: 'relative' }
    socialButtonsContainer: {
      flexDirection: 'row',
  justifyContent: 'space-between'
  },
  socialButton: {
      flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'center',
  padding: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
  flex: 1,
    marginHorizontal: 6,
  ...theme.shadows.sm
  },
  googleButton: {
      backgroundColor: '#DB4437', // Google brand color }
    appleButton: {
      backgroundColor: '#000000', // Apple brand color }
    facebookButton: {
      backgroundColor: '#4267B2', // Facebook brand color }
    socialButtonText: {
      color: '#FFFFFF'),
  fontWeight: '600'),
    marginLeft: theme.spacing.xs,
  fontSize: 14)
  }
  })
  export default SocialLoginButtons