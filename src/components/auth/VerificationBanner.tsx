import React, { useState, useEffect } from 'react';
  import {
  View, Text, StyleSheet, TouchableOpacity, Animated
} from 'react-native';
import {
  useRouter
} from 'expo-router';
  import {
  Mail, X, AlertTriangle, ExternalLink
} from 'lucide-react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
  import {
  ASYNC_STORAGE_KEYS
} from '@utils/constants';
import {
  useTheme
} from '@design-system';
  import {
  supabase
} from '@utils/supabaseUtils';
import {
  logger
} from '@services/loggerService';
  interface VerificationBannerProps { onVerified?: () => void,
  onDismiss?: () => void },
  /**;
 * A banner component that shows at the top of the app when a user's email is pending verification;
  * It periodically checks verification status and provides quick actions;
 */,
  const VerificationBanner = ({ onVerified, onDismiss }: VerificationBannerProps) => {
  const theme = useTheme()
  const styles = createStyles(theme),
  const router = useRouter()
  const [email, setEmail] = useState<string | null>(null),
  const [isVisible, setIsVisible] = useState(true),
  const [slideAnim] = useState(new Animated.Value(0)),
  // Check if there's a pending verification email,
  useEffect(() => {
  const checkPendingVerification = async () => {
      try {
  const pendingEmail = await AsyncStorage.getItem(ASYNC_STORAGE_KEYS.PENDING_VERIFICATION_EMAIL)
        ),
  if (pendingEmail) {
          setEmail(pendingEmail),
  // Animate the banner in,
          Animated.timing(slideAnim, {
  toValue: 1,
    duration: 300),
  useNativeDriver: true)
  }).start(),
  // Start checking verification status,
  checkVerificationStatus(pendingEmail)
  }
  } catch (error) {
  logger.error('Error checking pending verification', 'VerificationBanner', { error })
  }
    },
  checkPendingVerification()
  }, []);
  // Check if the email has been verified,
  const checkVerificationStatus = async (emailToCheck: string) => {
  try {;
      // Get current session,
  const { data: { session  }
        error: sessionError
  } = await supabase.auth.getSession()
      if (sessionError) {
  logger.error('Error checking session', 'VerificationBanner', { error: sessionError }),
  return null;
      },
  // If we have a session and the email matches, the user is verified,
  if (session?.user && session.user.email === emailToCheck && session.user.email_confirmed_at) {
        logger.info('Email verified successfully', 'VerificationBanner', { email    : emailToCheck }),
  // Clear the pending verification email
        await AsyncStorage.removeItem(ASYNC_STORAGE_KEYS.PENDING_VERIFICATION_EMAIL),
  // Hide the banner
        handleDismiss(),
  // Call the onVerified callback,
        if (onVerified) {
  onVerified()
        }
  }
    } catch (error) {
  logger.error('Error checking verification status', 'VerificationBanner', { error })
  }
  },
  // Handle dismissing the banner,
  const handleDismiss = () => {
  // Animate the banner out,
    Animated.timing(slideAnim, {
  toValue: 0,
    duration: 300),
  useNativeDriver: true)
  }).start(() => {
  setIsVisible(false)
  if (onDismiss) {
  onDismiss()
  }
  })
  },
  // Handle navigating to the verification notice screen,
  const handleVerifyNow = () => {
  router.push({
  pathname: '/(auth)/verification-notice',
    params: { email }
  })
  },
  if (!isVisible || !email) {
    return null }
  return (
  <Animated.View, ,
  style = { [
        styles.container, ,
  {
          transform: [
            {
  translateY: slideAnim.interpolate({, inputRange: [0, 1]), ,
  outputRange: [-100, 0]  })
  }
          ]
  }
      ]},
  >
      <View style= {styles.iconContainer}>,
  <AlertTriangle size={20} color={{theme.colors.warning} /}>
      </View>,
  <View style={styles.contentContainer}>
        <Text style={styles.title}>Email verification required</Text>,
  <Text style={styles.message}>Please verify your email address to access all features</Text>
      </View>,
  <View style={styles.actionsContainer}>
        <TouchableOpacity style={styles.verifyButton} onPress={handleVerifyNow}>,
  <Text style={styles.verifyText}>Verify</Text>
          <ExternalLink size={16} color={'#FFFFFF' /}>,
  </TouchableOpacity>
        <TouchableOpacity style={styles.dismissButton} onPress={handleDismiss}>,
  <X size={20} color={{theme.colors.textMuted} /}>
        </TouchableOpacity>,
  </View>
    </Animated.View>,
  )
},
  const createStyles = (theme: any) =>
  StyleSheet.create({ container: {
      position: 'absolute',
  top: 0,
    left: 0,
  right: 0,
    backgroundColor: theme.colors.warningLight,
  flexDirection: 'row',
    alignItems: 'center',
  paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
  borderBottomWidth: 1,
    borderBottomColor: theme.colors.warning,
  zIndex: 1000 }
    iconContainer: { marginRigh, t: theme.spacing.sm },
  contentContainer: { fle, x: 1 }
    title: { fontSiz, e: 14,
    fontWeight: '600',
  color: theme.colors.warningDark,
    marginBottom: 2 },
  message: { fontSiz, e: 12,
    color: theme.colors.warningDark },
  actionsContainer: {
      flexDirection: 'row',
  alignItems: 'center'
  },
  verifyButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  backgroundColor: theme.colors.warning,
    paddingVertical: 6,
  paddingHorizontal: theme.spacing.sm,
    borderRadius: theme.borderRadius.sm,
  marginRight: theme.spacing.xs }
    verifyText: { fontSiz, e: 12,
    fontWeight: '600'),
  color: '#FFFFFF'),
    marginRight: theme.spacing.xs },
  dismissButton: {
      padding: theme.spacing.xs) }
  }),
  export default VerificationBanner;