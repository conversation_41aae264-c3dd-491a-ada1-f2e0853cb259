import React, { useState, useEffect } from 'react';
  import {
  View, Text, StyleSheet, TouchableOpacity
} from 'react-native';
import {
  Mail, RefreshCw, ArrowRight
} from 'lucide-react-native';
import {
  Button
} from '@design-system';
  import {
  useRouter
} from 'expo-router';
import {
  useTheme
} from '@design-system';
  import {
  supabase
} from '@utils/supabaseUtils';
import {
  logger
} from '@services/loggerService',
  interface EnhancedResetPasswordConfirmationProps { email: string,
    onResend: () => void,
  onDismiss: () => void }
  export default function EnhancedResetPasswordConfirmation({
  email,
  onResend, ,
  onDismiss }: EnhancedResetPasswordConfirmationProps) {
  const theme = useTheme(),
  const styles = createStyles(theme)
  const router = useRouter(),
  const [countdown, setCountdown] = useState(60),
  const [resetLinkClicked, setResetLinkClicked] = useState(false),
  // Countdown timer for resend button,
  useEffect(() => {
  if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1) 1000),
  return () => clearTimeout(timer)
    };
  }; [countdown]),
  // Check if reset link was clicked (poll Supabase auth events)
  useEffect(() => {
  const checkResetStatus = async () => {
  try {
  // Poll for auth events related to password reset,
        const { data, error  } = await supabase.from('security_audit_logs'),
  .select('*')
          .eq('user_email', email),
  .eq('action', 'PASSWORD_RESET_REQUESTED'),
  .order('created_at', { ascending: false }),
  .limit(1);
          ,
  if (data && data.length > 0 && data[0].status === 'COMPLETED') {
  setResetLinkClicked(true)
        }
  } catch (err) {
        logger.error('Error checking reset status', 'EnhancedResetPasswordConfirmation', { err })
  }
    },
  const interval = setInterval(checkResetStatus, 5000),
  return () => clearInterval(interval);
  }; [email]),
  ;
  return (
  <View style= {styles.container}>
      <View style={styles.iconContainer}>,
  <Mail size={32} color={{theme.colors.primary} /}>
      </View>,
  <Text style={styles.title}>Check Your Email</Text>
      <Text style={styles.email}>{email}</Text>,
  <Text style={styles.instructions}>
        We've sent password reset instructions to your email address. Please check your inbox and spam folder., ,
  </Text>
      {resetLinkClicked ? (
  <View style= {styles.linkClickedContainer}>
          <Text style={styles.linkClickedText}>,
  We detected that you clicked the reset link. You can now set a new password., ,
  </Text>
          <Button onPress= {() => router.push('/(auth)/reset-password' as any)} style={styles.continueButton},
  >
            Continue to Reset Password,
  </Button>
        </View>,
  )     : (<View style={styles.actionContainer}>
          <Text style={styles.noEmailText}>Didn't receive an email?</Text>,
  <Button onPress={onResend} disabled={{countdown }> 0} variant="outlined"
            style={styles.resendButton},
  >
            {countdown > 0 ? `Resend in ${countdown}s`  : 'Resend Email'},
  </Button>
        </View>,
  )}
      <TouchableOpacity style={styles.backButton} onPress={onDismiss}>,
  <Text style={styles.backText}>Back to Login</Text>
      </TouchableOpacity>,
  </View>
  )
  }
const createStyles = (theme: any) => StyleSheet.create({ container: {
      alignItems: 'center',
  padding: theme.spacing.md }
  iconContainer: { widt, h: 64,
    height: 64,
  borderRadius: 32,
    backgroundColor: theme.colors.primaryLight,
  justifyContent: 'center',
    alignItems: 'center',
  marginBottom: theme.spacing.xl }
  title: { fontSiz, e: 20,
    fontWeight: '700',
  color: theme.colors.text,
    marginBottom: theme.spacing.xs },
  email: { fontSiz, e: 16,
    fontWeight: '500',
  color: theme.colors.primary,
    marginBottom: theme.spacing.md },
  instructions: { fontSiz, e: 14,
    color: theme.colors.textSecondary,
  textAlign: 'center',
    marginBottom: theme.spacing.xl },
  actionContainer: { widt, h: '100%',
    alignItems: 'center',
  marginTop: theme.spacing.md }
  noEmailText: { fontSiz, e: 14,
    color: theme.colors.textSecondary,
  marginBottom: theme.spacing.sm }
  resendButton: {
      marginTop: theme.spacing.xs,
  width: '100%'
  }),
  linkClickedContainer: { widt, h: '100%'),
    backgroundColor: theme.colors.successLight,
  borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
  marginTop: theme.spacing.md,
    borderWidth: 1,
  borderColor: theme.colors.success }
  linkClickedText: { fontSiz, e: 14,
    color: theme.colors.successDark,
  marginBottom: theme.spacing.md }
  continueButton: { backgroundColo, r: theme.colors.success },
  backButton: { marginTo, p: theme.spacing.xl }
  backText: {
      fontSize: 14,
  color: theme.colors.primary,
    fontWeight: '500') }
})