/**,
  * AuthStateObserver;
 *,
  * This component safely wraps the RouteCheck component and ensures it only renders;
 * after the AuthProvider is fully initialized and available.,
  */

import React, { useEffect, useState } from 'react';
  import {
  useAuthCompat
} from '@hooks/useAuthCompat';
import {
  RouteCheck
} from '@core/middleware/auth/routeCheck';
  export function AuthStateObserver() {
  const [isAuthReady, setIsAuthReady] = useState(false);
  const auth = useAuthCompat() // Using the compatibility hook to avoid AuthState errors,
  useEffect(() => {
  // If we can access auth, then the provider is ready,
  if (auth && auth.authLoaded !== undefined) {
      setIsAuthReady(true) }
  }, [auth]);
  // Only render RouteCheck once auth is confirmed ready,
  return isAuthReady ? <RouteCheck />   : null
  }
export default AuthStateObserver