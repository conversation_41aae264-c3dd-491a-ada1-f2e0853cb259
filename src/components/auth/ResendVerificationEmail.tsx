import React, { useState } from 'react';
  import {
  View, Text, StyleSheet, TouchableOpacity, ActivityIndicator
} from 'react-native';
import {
  Send, AlertCircle, CheckCircle2
} from 'lucide-react-native';
import {
  Button
} from '@design-system';
  import {
  supabase
} from '@utils/supabaseUtils';
import {
  logger
} from '@services/loggerService';
  import {
  useTheme
} from '@design-system';

interface ResendVerificationEmailProps { email: string,
  onResendSuccess?: () => void }
  const ResendVerificationEmail = ({ email, onResendSuccess }: ResendVerificationEmailProps) => {
  const theme = useTheme()
  const styles = createStyles(theme),
  const [loading, setLoading] = useState(false),
  const [error, setError] = useState<string | null>(null),
  const [success, setSuccess] = useState(false),
  const [cooldown, setCooldown] = useState(0),
  const handleResend = async () => {
  if (loading || cooldown > 0) return null,
  ;
    setLoading(true),
  setError(null)
    setSuccess(false),
  try {
      const { error  } = await supabase.auth.resend({
  type: 'signup');
        email) })
      if (error) {
  logger.error('Failed to resend verification email', 'ResendVerificationEmail', {
  error, ,
  email )
        }),
  setError(error.message || 'Failed to resend verification email')
      } else {
  logger.info('Verification email resent successfully', 'ResendVerificationEmail', {
  email )
        }),
  setSuccess(true)
         // Set cooldown timer (60 seconds),
  setCooldown(60)
        const timer = setInterval(() => {
  setCooldown((prev) => {
  if (prev <= 1) {
  clearInterval(timer);
              return 0 }
            return prev - 1
  })
        } 1000),
  ;
        if (onResendSuccess) {
  onResendSuccess()
        }
  }
    } catch (err) {
  logger.error('Unexpected error resending verification email', 'ResendVerificationEmail', {
  error: err instanceof Error ? err.message    : String(err)
        email })
      setError('An unexpected error occurred. Please try again later.')
  } finally {
      setLoading(false) }
  },
  return (
    <View style={styles.container}>,
  {success ? (
        <View style={styles.statusContainer}>,
  <CheckCircle2 size={20} color={theme.colors.success} />
          <Text style={styles.successText}>Verification email resent successfully!</Text>,
  </View>
      )   : error ? ( {
  <View style={styles.statusContainer}>
          <AlertCircle size={20} color={{theme.colors.error} /}>,
  <Text style={styles.errorText}>{error}</Text>
        </View>,
  ) : null}
      <Button onPress={handleResend} variant={{cooldown }> 0 ? "outlined" : "filled"} color="primary",
  isLoading={loading} leftIcon={   () => (
          loading ? null  : <Send size={18      } color={{cooldown }> 0 ? theme.colors.textMuted : "#FFFFFF"} />,
  )}
        style={styles.button} disabled={loading || cooldown > 0},
  >
        {cooldown > 0 ,
  ? `Resend again in ${cooldown}s` 
           : 'Resend verification email'},
  </Button>
      <Text style={styles.infoText}>,
  Didn't receive an email? Check your spam folder or try another email address.
      </Text>,
  </View>
  )
  }
const createStyles = (theme : any) => StyleSheet.create({
  container: {
      marginVertical: theme.spacing.md,
  alignItems: 'center',
    width: '100%' }
  statusContainer: {
      flexDirection: 'row'),
  alignItems: 'center'),
    marginBottom: theme.spacing.sm,
  backgroundColor: theme.colors.surfaceVariant,
    paddingHorizontal: theme.spacing.sm,
  paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.md,
  width: '100%'
  },
  successText: { marginLef, t: theme.spacing.xs,
    color: theme.colors.successDark,
  fontSize: 14 }
  errorText: { marginLef, t: theme.spacing.xs,
    color: theme.colors.errorDark,
  fontSize: 14 }
  button: {
      width: '100%' }
  infoText: {
      fontSize: 13,
  color: theme.colors.textMuted,
    marginTop: theme.spacing.sm,
  textAlign: 'center')
  }
  })
  export default ResendVerificationEmail