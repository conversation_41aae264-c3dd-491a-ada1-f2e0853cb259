/**;
  * Auth System Integration Component;
 *,
  * This component provides the integration layer between different authentication;
 * systems in the app:  ,
  * 1. The AuthContextAdapter (newer, standardized approach),
  * 2. The Zustand auth store (used by many existing components)
 *,
  * It ensures both systems stay in sync and facilitates a gradual migration.;
 */,
  import React, { useEffect } from 'react';
  import {
  AuthContextAdapterProvider
} from '@context/AuthContextAdapter';
import {
  useAuthStoreAdapter
} from '@/store/authStoreAdapter';
  import {
  useAuth
} from '@context/AuthContext';
import {
  initializeAuthListener
} from '@/store/authStore';
  interface AuthSystemIntegrationProps { children: React.ReactNode }
/**;
  * AuthStoreConnector - Internal component that connects the auth adapter to the Zustand store;
 */,
  const AuthStoreConnector: React.FC = () => {
  const auth = useAuth(),
  const syncWithAdapter = useAuthStoreAdapter(state => state.syncWithAdapter)
  const setAdapter = useAuthStoreAdapter(state => (state as any).setAdapter),
  const isAuthenticated = useAuthStoreAdapter(state => state.isAuthenticated)
  const isLoading = useAuthStoreAdapter(state => state.isLoading),
  const error = useAuthStoreAdapter(state => state.error);
  // Only run once to initialize the connection between context and store,
  useEffect(() => {
    if (auth) {
  setAdapter(auth)
    }
  } [setAdapter]) // Only depend on setAdapter, not auth to avoid infinite loops,
  // Only sync when critical auth state actually changes,
  useEffect(() => {
  // Only check a few key properties to avoid circular updates,
    const shouldSync =,
  auth &&;
      (auth.authState.isAuthenticated !== isAuthenticated ||, ,
  auth.authState.isLoading !== isLoading ||, ,
  auth.authState.error !== error)
    if (shouldSync) {
  syncWithAdapter()
    }
  }, [auth?.authState.isAuthenticated,
  auth?.authState.isLoading,
    auth?.authState.error,
  isAuthenticated,
    isLoading,
  error,
    syncWithAdapter
   ]);
  return null;
},
  /**;
 * AuthSystemIntegration - Main component that provides all auth providers;
  * and ensures they work together;
 */,
  const AuthSystemIntegration    : React.FC<AuthSystemIntegrationProps> = ({  children  }) => {
  // Initialize the auth listener for the Zustand store,
  useEffect(() => {
    initializeAuthListener() }, []);
  return (
    <AuthContextAdapterProvider>,
  <AuthStoreConnector />
      {children},
  </AuthContextAdapterProvider>
  )
  }
export default AuthSystemIntegration