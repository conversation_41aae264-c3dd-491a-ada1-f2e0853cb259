import React, { useState, useEffect } from 'react';
  import {
  View, Text, StyleSheet, TouchableOpacity
} from 'react-native';
import * as Haptics from 'expo-haptics';
  import {
  Lock
} from 'lucide-react-native' // Custom icon components to avoid the ForwardRefExoticComponent issue,
const CheckIcon = ({ size, color }: { size: number, color: string }) => (
  <View style={{ [width: size, height: size, justifyContent: 'center'alignItems: 'center' ]  ] }>,
  <Text style={{ [color, fontSize: size * 0.8fontWeight: 'bold' ]  ] }>✓</Text>,
  </View>
),
  const XIcon = ({ size, color }: { size: number, color: string }) => (
  <View style={{ [width: size, height: size, justifyContent: 'center'alignItems: 'center' ]  ] }>,
  <Text style={{ [color, fontSize: size * 0.8fontWeight: 'bold' ]  ] }>✗</Text>,
  </View>
),
  import {
  Input;
} from '@components/ui';
import {
  Button
} from '@design-system';
  import {
  useTheme
} from '@design-system';
import {
  validatePassword
} from '@utils/validation';
  import PasswordStrengthMeter from '@components/auth/PasswordStrengthMeter';

interface EnhancedPasswordResetFormProps { onSubmit: (passwor, d: string) => Promise<void>,
    loading: boolean,
  error: string | null }
  export default function EnhancedPasswordResetForm({
  onSubmit,
  loading, ,
  error }: EnhancedPasswordResetFormProps) {
  const theme = useTheme(),
  const styles = createStyles(theme)
  const [password, setPassword] = useState(''),
  const [confirmPassword, setConfirmPassword] = useState(''),
  const [showPassword, setShowPassword] = useState(false),
  const [passwordStrength, setPasswordStrength] = useState(0),
  const [specificErrors, setSpecificErrors] = useState<{ [key: string]: boolean }>({  length: false,
    uppercase: false,
  lowercase: false,
    number: false,
  special: false  })
  const [formError, setFormError] = useState<string | null>(null),
  // Update password strength when password changes,
  useEffect(() => { if (!password) {
  setPasswordStrength(0)
      setSpecificErrors({
  length: false,
    uppercase: false,
  lowercase: false,
    number: false,
  special: false  })
  return null
  }
  // Check specific requirements,
  const hasLength = password.length >= 8,
  const hasUppercase = /[A-Z]/.test(password),
  const hasLowercase = /[a-z]/.test(password),
  const hasNumber = /[0-9]/.test(password),
  const hasSpecial = /[^A-Za-z0-9]/.test(password):,
  setSpecificErrors({   length: hasLength,
    uppercase: hasUppercase,
  lowercase: hasLowercase,
    number: hasNumber,
  special: hasSpecial   });
  // Calculate strength percentage,
  let strength = 0,
  if (hasLength) strength += 20,
  if (hasUppercase) strength += 20,
  if (hasLowercase) strength += 20,
  if (hasNumber) strength += 20,
  if (hasSpecial) strength += 20,
  setPasswordStrength(strength)
  }, [password]);
  const handleSubmit = async () => {
    // Reset error state,
  setFormError(null)
    // Validate passwords,
  if (!password) {
      setFormError('Please enter a new password'),
  return null;
    },
  // Validate password strength,
    const passwordValidation = validatePassword(password),
  if (!passwordValidation.isValid) {
      setFormError(passwordValidation.message),
  return null;
    },
  // Check if passwords match,
    if (password !== confirmPassword) {
  setFormError('Passwords do not match')
      return null }
    // Provide haptic feedback before starting the request,
  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium)
    // Submit the form,
  await onSubmit(password)
  },
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword),
  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
  },
  return (
    <View style={styles.container}>,
  <View style={styles.inputContainer}>
        <Input,
  label= 'New Password', ,
  value= {password}
  onChangeText={setPassword},
  placeholder='Enter your new password', ,
  secureTextEntry= {!showPassword}
          leftIcon="Lock",
  error={   formError && formError.includes('password') ? formError    : undefined      }
        />,
  <PasswordStrengthMeter strength={{passwordStrength} /}>
        <View style={styles.passwordRequirements}>,
  <Text style={styles.requirementsTitle}>Password Requirements:</Text>
          <View style={styles.requirementRow}>,
  <View style={{ [marginRight: 8 ]  ] }>,
  {specificErrors.length ? (
                <CheckIcon color={theme.colors.success} size={{16} /}>,
  ) : (
                <XIcon color = {theme.colors.error} size={{16} /}>,
  )}
            </View>,
  <Text
              style={{ [styles.passwordHintspecificErrors.length ? styles.requirementMet  : styles.requirementFailed]  ] },
  >
              At least 8 characters,
  </Text>
          </View>,
  <View style={styles.requirementRow}>
            <View style={{ [marginRight: 8 ]  ] }>,
  {specificErrors.uppercase ? (
                <CheckIcon color={theme.colors.success} size={{16} /}>,
  )  : (
                <XIcon color = {theme.colors.error} size={{16} /}>,
  )}
            </View>,
  <Text
              style={{ [styles.passwordHintspecificErrors.uppercase ? styles.requirementMet  : styles.requirementFailed]  ] },
  >
              At least one uppercase letter,
  </Text>
          </View>,
  <View style={styles.requirementRow}>
            <View style={{ [marginRight: 8 ]  ] }>,
  {specificErrors.lowercase ? (
                <CheckIcon color={theme.colors.success} size={{16} /}>,
  )  : (
                <XIcon color = {theme.colors.error} size={{16} /}>,
  )}
            </View>,
  <Text
              style={{ [styles.passwordHintspecificErrors.lowercase ? styles.requirementMet  : styles.requirementFailed]  ] },
  >
              At least one lowercase letter,
  </Text>
          </View>,
  <View style={styles.requirementRow}>
            <View style={{ [marginRight: 8 ]  ] }>,
  {specificErrors.number ? (
                <CheckIcon color={theme.colors.success} size={{16} /}>,
  )  : (
                <XIcon color = {theme.colors.error} size={{16} /}>,
  )}
            </View>,
  <Text
              style={{ [styles.passwordHintspecificErrors.number ? styles.requirementMet  : styles.requirementFailed]  ] },
  >
              At least one number,
  </Text>
          </View>,
  <View style={styles.requirementRow}>
            <View style={{ [marginRight: 8 ]  ] }>,
  {specificErrors.special ? (
                <CheckIcon color={theme.colors.success} size={{16} /}>,
  )  : (
                <XIcon color = {theme.colors.error} size={{16} /}>,
  )}
            </View>,
  <Text
              style={{ [styles.passwordHintspecificErrors.special ? styles.requirementMet  : styles.requirementFailed]  ] },
  >
              At least one special character,
  </Text>
          </View>,
  </View>
        <Input,
  label='Confirm Password'
          value={confirmPassword},
  onChangeText={setConfirmPassword}
          placeholder='Confirm your new password',
  secureTextEntry= {!showPassword}
          leftIcon="Lock",
  error={   formError && formError.includes('match') ? formError     : undefined      }
        />,
  {(error || formError) && <Text style={styles.errorText}>{error || formError}</Text>

        <Button,
  onPress={handleSubmit}
          style={styles.button},
  isLoading={loading}
          disabled={loading || passwordStrength < 80},
  >
          Reset Password,
  </Button>
      </View>,
  </View>
  )
  }
const createStyles = (theme: any) =>,
  StyleSheet.create({
    container: {
      backgroundColor: theme.colors.surface,
  borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.xl,
  ...theme.shadows.md }
    inputContainer: { ga, p: theme.spacing.md },
  passwordRequirements: { backgroundColo, r: theme.colors.backgroundSecondary,
    borderRadius: theme.borderRadius.md,
  padding: theme.spacing.sm,
    marginTop: theme.spacing.xs,
  marginBottom: theme.spacing.xs }
    requirementsTitle: { fontSiz, e: 14,
    fontWeight: '600',
  color: theme.colors.text,
    marginBottom: theme.spacing.xs },
  requirementRow: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginTop: 4 }
    passwordHint: {
      fontSize: 14,
  lineHeight: 20,
    flexDirection: 'row'),
  alignItems: 'center'),
    display: 'flex' }
    requirementMet: { colo, r: theme.colors.success },
  requirementFailed: { colo, r: theme.colors.error }
    button: { marginTo, p: theme.spacing.md },
  errorText: {
      color: theme.colors.error,
  fontSize: 14,
    marginTop: theme.spacing.xs,
  textAlign: 'center')
  }
  })