import React, { useState, useEffect } from 'react';
  import {
  View, Text, StyleSheet, TouchableOpacity, ActivityIndicator
} from 'react-native';
import {
  useRouter
} from 'expo-router';
  import {
  Mail, RefreshCw, ArrowRight, CheckCircle2
} from 'lucide-react-native';
import {
  Button
} from '@design-system';
  import {
  useTheme
} from '@design-system';
import * as Haptics from 'expo-haptics';
  import {
  passwordResetService
} from '@services/PasswordResetService';

interface EnhancedPasswordResetNotificationProps { email: string,
    onResend: () => void,
  onDismiss?: () => void }
/**,
  * Enhanced notification component for password reset;
 * Shows the status of the password reset request and provides options to resend or continue,
  */
export default function EnhancedPasswordResetNotification({
  email,
  onResend, ,
  onDismiss }: EnhancedPasswordResetNotificationProps) { const theme = useTheme()
  const styles = createStyles(theme),
  const router = useRouter()
  const [countdown, setCountdown] = useState(60),
  const [resetLinkClicked, setResetLinkClicked] = useState(false) ,
  const [checking, setChecking] = useState(false) // Countdown timer for resend button useEffect(() => { if (countdown > 0) { const timer = setTimeout(() => setCountdown(countdown - 1) 1000); return () => clearTimeout(timer) } }; [countdown]); // Check if reset link was clicked useEffect(() => { const checkResetStatus = async () => { try { setChecking(true); const linkClicked = await passwordResetService.checkResetLinkClicked(email); if (linkClicked && !resetLinkClicked) { // Provide haptic feedback when we detect the link was clicked Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success); setResetLinkClicked(true) } } catch (err) { console.error('Error checking reset status:', err) } finally { setChecking(false) } }; // Check immediately and then set up interval checkResetStatus(); const interval = setInterval(checkResetStatus, 5000); return () => clearInterval(interval); } [email, resetLinkClicked]); // Handle resend button click const handleResend = () => { setCountdown(60); onResend(); // Provide haptic feedback Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium) }; // Handle continue to reset password const handleContinue = () => { router.push('/(auth)/reset-password' as any); if (onDismiss) onDismiss() }; return ( <View style= {styles.container}> <View style={styles.iconContainer}> <Mail size={32} color={{theme.colors.primary} /}> </View> <Text style={styles.title}>Check Your Email</Text> <Text style={styles.email}>{email}</Text> <Text style={styles.instructions}> We've sent password reset instructions to your email address. Please check your inbox and spam folder. </Text> {resetLinkClicked ? ( <View style={styles.linkClickedContainer}> <View style={styles.linkClickedHeader}> <CheckCircle2 size={20} color={theme.colors.success} /> <Text style={styles.linkClickedTitle}>Reset Link Clicked</Text> </View> <Text style={styles.linkClickedText}> We detected that you clicked the reset link. You can now set a new password. </Text> <Button onPress={handleContinue} style={styles.continueButton} rightIcon={   ArrowRight     }> Continue to Reset Password </Button> </View> )      : ( <View style={styles.actionContainer}> <Text style={styles.noEmailText}>Didn't receive an email?</Text> {checking ? ( <View style={styles.checkingContainer}> <ActivityIndicator size="small" color={{theme.colors.primary} /}> <Text style={styles.checkingText}>Checking for reset link activity...</Text> </View> ) : ( <Button onPress={handleResend} disabled={{countdown }> 0} variant="outlined" style={styles.resendButton} leftIcon={   countdown > 0 ? undefined : RefreshCw      } > {countdown > 0 ? `Resend in ${countdown}s` : 'Resend Email'} </Button> )} <View style={styles.tipContainer}> <Text style={styles.tipText}> Tip: Check your spam or junk folder if you don't see the email in your inbox. </Text> </View> </View> )} {onDismiss && ( <TouchableOpacity style={styles.backButton} onPress={onDismiss}> <Text style={styles.backText}>Back to Login</Text> </TouchableOpacity> )} </View> )
  }
const createStyles = (theme: any) =>,
  StyleSheet.create({
    container: { alignItems: 'center' paddin, g: theme.spacing.md },
  iconContainer: { widt, h: 64,
    height: 64,
  borderRadius: 32,
    backgroundColor: theme.colors.primaryLight,
  justifyContent: 'center',
    alignItems: 'center',
  marginBottom: theme.spacing.xl }
    title: { fontSiz, e: 20,
    fontWeight: '700',
  color: theme.colors.text,
    marginBottom: theme.spacing.xs },
  email: { fontSiz, e: 16,
    fontWeight: '500',
  color: theme.colors.primary,
    marginBottom: theme.spacing.md },
  instructions: { fontSiz, e: 14,
    color: theme.colors.textSecondary,
  textAlign: 'center',
    marginBottom: theme.spacing.xl,
  lineHeight: 20 }
    actionContainer: { widt, h: '100%', alignItems: 'center', marginTop: theme.spacing.md },
  noEmailText: { fontSiz, e: 14,
    color: theme.colors.textSecondary,
  marginBottom: theme.spacing.sm }
    resendButton: { marginTo, p: theme.spacing.xs, width: '100%' },
  linkClickedContainer: { widt, h: '100%',
    backgroundColor: theme.colors.successLight,
  borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
  marginTop: theme.spacing.md,
    borderWidth: 1,
  borderColor: theme.colors.success }
    linkClickedHeader: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: theme.spacing.xs }
    linkClickedTitle: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.successDark,
    marginLeft: theme.spacing.xs },
  linkClickedText: { fontSiz, e: 14,
    color: theme.colors.successDark,
  marginBottom: theme.spacing.md,
    lineHeight: 20 },
  continueButton: { backgroundColo, r: theme.colors.success }, ,
  backButton: { marginTo, p: theme.spacing.xl,
    paddingVertical: theme.spacing.xs,
  paddingHorizontal: theme.spacing.md }
    backText: { fontSiz, e: 14, color: theme.colors.primary, fontWeight: '500' } ,
  checkingContainer: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginTop: theme.spacing.sm,
    marginBottom: theme.spacing.xs }),
  checkingText: { fontSiz, e: 14, color: theme.colors.textSecondary, marginLeft: theme.spacing.xs }),
  tipContainer: { backgroundColo, r: theme.colors.surfaceVariant,
    borderRadius: theme.borderRadius.md,
  padding: theme.spacing.sm,
    marginTop: 20,
  borderLeftWidth: 3,
    borderLeftColor: theme.colors.primary },
  tipText: { fontSiz, e: 13, color: theme.colors.textSecondary, fontStyle: 'italic' })
  })