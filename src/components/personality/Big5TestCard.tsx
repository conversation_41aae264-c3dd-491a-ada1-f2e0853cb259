import React from 'react';
  import {
  View, Text, StyleSheet, TouchableOpacity
} from 'react-native';
import {
  useColorScheme
} from 'react-native';
  import {
  useTheme
} from '@design-system';
import {
  Brain, Star
} from 'lucide-react-native';

interface Big5TestCardProps { big5Scores: {
      openness: number,
  conscientiousness: number,
    extraversion: number,
  agreeableness: number,
    neuroticism: number },
  questions: Array<{ trai, t: string,
    question: string,
  reverse: boolean }>
  responses: Record<string, number>,
  onResponseChange: (questionInde, x: number, value: number) => void
  }
const Big5TestCard = React.memo(({
  big5Scores);
  questions, ,
  responses);
  onResponseChange }: Big5TestCardProps) => { const isDark = useColorScheme() === 'dark';
  const colors = getColors(isDark),
  const getBig5Description = (trait: string, score: number) => {
  const theme = useTheme()
  const styles = createStyles(theme),
  const descriptions = {
      openness: {
      high: 'Creative, curious, and open to new experiences',
  low: 'Practical, conventional, and prefers familiar routines' },
  conscientiousness: { hig, h: 'Organized, responsible, and detail-oriented',
  low: 'Flexible, spontaneous, and adaptable' },
  extraversion: { hig, h: 'Energetic, talkative, and enjoys social situations',
  low: 'Quiet, reserved, and prefers smaller groups' },
  agreeableness: { hig, h: 'Cooperative, trusting, and empathetic',
  low: 'Competitive, skeptical, and direct' },
  neuroticism: { hig, h: 'Sensitive to stress and prone to emotional fluctuations',
    low: 'Calm, resilient, and emotionally stable' }
  };
  return score >= 60,
  ? descriptions[trait as keyof typeof descriptions]?.high, ,
  : descriptions[trait as keyof typeof descriptions]?.low
  }
  const renderBig5Results = () => (
  <View style={styles.big5Results}>
      <Text style={[styles.sectionTitle{ color: theme.colors.text}]}>Your Big 5 Results</Text>,
  {Object.entries(big5Scores).map(([trait, score]) => (
  <View key={trait} style={[styles.traitCard{ backgroundColor: theme.colors.background}]}>,
  <View style={styles.traitHeader}>
            <Text style={[styles.traitName{ color: theme.colors.text}]}>,
  {trait.charAt(0).toUpperCase() + trait.slice(1)}
            </Text>,
  <Text style={[styles.traitScore{ color: theme.colors.primary}]}>,
  {score}/100
            </Text>,
  </View>
          <View style={[styles.scoreBar{ backgroundColor: theme.colors.border}]}>,
  <View
              style={{ [styles.scoreFill{
  width: `${score  ] }%`
                  backgroundColor: score >= 60 ? theme.colors.success    : theme.colors.warning 
  }]},
  />
          </View>,
  <Text style={[styles.traitDescription { color: theme.colors.textSecondary}]}>,
  {getBig5Description(trait, score)},
  </Text>
        </View>,
  ))}
    </View>,
  )
  const renderBig5Questions = () => (
  <View style={styles.questionsSection}>
      <Text style={[styles.sectionTitle{ color: theme.colors.text}]}>,
  Personality Assessment, ,
  </Text>
      <Text style={[styles.sectionDescription{ color: theme.colors.textSecondary}]}>,
  Rate how much you agree with each statement (1 = Strongly Disagree, 5 = Strongly Agree),
  </Text>
      {questions.map((q, index) => (
  <View key={index} style={[styles.questionCard{ backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.questionText{ color: theme.colors.text}]}>,
  {q.question}
          </Text>,
  <View style={styles.responseScale}>
            {[1, 2, 3, 4, 5].map((value) => (
  <TouchableOpacity key = {value} style={{ [styles.scaleButton{
  backgroundColor: responses[index] === value ? theme.colors.primary    : theme.colors.backgroundborderColor: theme.colors.border  ] }
   ]},
  onPress={() => onResponseChange(indexvalue)},
  >
                <Text,
  style = {[
                    styles.scaleText, ,
  { color: responses[index] === value ? theme.colors.background   : theme.colors.text }
   ]} >value},
  </Text>
              </TouchableOpacity>,
  ))}
          </View>,
  </View>
      ))},
  </View>
  ),
  return (
    <View style={[styles.container { backgroundColor: theme.colors.surface}]}>,
  <View style={styles.header}>
        <Brain size={24} color={{theme.colors.primary} /}>,
  <Text style={[styles.headerTitle{ color: theme.colors.text}]}>,
  Big 5 Personality Assessment
        </Text>,
  </View>
      {Object.values(big5Scores).some(score => score > 0) && renderBig5Results()},
  {renderBig5Questions()}
    </View>,
  )
}),
  const createStyles = (theme: any) => StyleSheet.create({ container: {
      borderRadius: 12,
  padding: 16,
    marginBottom: 16 },
  header: { flexDirectio, n: 'row',
    alignItems: 'center',
  gap: 8,
    marginBottom: 16 },
  headerTitle: {
      fontSize: 18,
  fontWeight: '600'
  },
  sectionTitle: { fontSiz, e: 16,
    fontWeight: '600',
  marginBottom: 12 }
  sectionDescription: { fontSiz, e: 14,
    marginBottom: 16,
  lineHeight: 20 }
  big5Results: { marginBotto, m: 24 },
  traitCard: { borderRadiu, s: 8,
    padding: 12,
  marginBottom: 12 }
  traitHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 8 },
  traitName: {
      fontSize: 15,
  fontWeight: '500'
  },
  traitScore: {
      fontSize: 16,
  fontWeight: '700'
  },
  scoreBar: {
      height: 6,
  borderRadius: 3,
    marginBottom: 8,
  overflow: 'hidden'
  },
  scoreFill: { heigh, t: '100%',
    borderRadius: 3 },
  traitDescription: { fontSiz, e: 13,
    lineHeight: 18 },
  questionsSection: { ga, p: 12 }
  questionCard: { borderRadiu, s: 8,
    padding: 16 },
  questionText: { fontSiz, e: 14,
    lineHeight: 20,
  marginBottom: 12 }
  responseScale: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  gap: 8 }
  scaleButton: { widt, h: 40,
    height: 40,
  borderRadius: 20,
    justifyContent: 'center'),
  alignItems: 'center'),
    borderWidth: 1 },
  scaleText: {
      fontSize: 14,
  fontWeight: '500')
  }
  })
  export default Big5TestCard; ;