import React, { useState, useEffect } from 'react';
  import {
  useTheme
} from '@design-system';
import {
  View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator, Alert, Switch
} from 'react-native';
import {
  personalityService, PersonalityTraitCategory
} from '@services/personalityService';
import {
  profileCompletionService
} from '@services/profileCompletionService';
  import {
  useAuth
} from '@hooks/useAuth';
,
  import {
  supabase
} from '@utils/supabaseUtils' // Key personality traits that affect roommate compatibility,
const PERSONALITY_TRAITS = [
  {
  category: PersonalityTraitCategory.Lifestyle,
    name: 'cleanliness',
  label: 'Cleanliness',
    description: 'How important is cleanliness to you? ' }
  {
  category     : PersonalityTraitCategory.Lifestyle
    name: 'noise_tolerance',
    label: 'Noise Tolerance',
  description: 'How tolerant are you of noise? '
  },
  {
  category   : PersonalityTraitCategory.Lifestyle,
  name: 'social_hosting',
    label: 'Social Hosting',
  description: 'How often do you like to have guests over? '
  },
  {
  category   : PersonalityTraitCategory.Lifestyle,
  name: 'sleep_schedule',
    label: 'Sleep Schedule',
  description: 'Are you a night owl or an early bird? '
  },
  {
  category   : PersonalityTraitCategory.Habits,
  name: 'cooking',
    label: 'Cooking Frequency',
  description: 'How often do you cook at home? '
  },
  {
  category   : PersonalityTraitCategory.Habits,
  name: 'sharing',
    label: 'Sharing Comfort',
  description: 'How comfortable are you sharing belongings? '
  },
  {
  category   : PersonalityTraitCategory.Communication,
  name: 'communication',
    label: 'Communication Style',
  description: 'Do you prefer direct or indirect communication? '
  },
  {
  category   : PersonalityTraitCategory.ConflictResolution,
  name: 'conflict_resolution',
    label: 'Conflict Resolution',
  description: 'How do you approach conflicts? '
  } 
] // Preference traits that affect roommate matching,
  const PREFERENCE_TRAITS = [
  {
  category   : 'preferences'
    name: 'smoking',
    label: 'Smoking',
  description: 'Are you comfortable with smoking? '
  } ,
  {
    category    : 'preferences',
  name: 'pets',
    label: 'Pets',
  description: 'Are you comfortable with pets? '
  },
  {
  category   : 'preferences',
  name: 'quiet_hours',
    label: 'Quiet Hours',
  description: 'Do you prefer quiet hours? '
  },
  {
  category   : 'preferences',
  name: 'shared_groceries',
    label: 'Shared Groceries',
  description: 'Do you prefer to share groceries? '
  }
],
  interface SimplePersonalityFormProps { onComplete?   : () => void
  onProgressUpdate?: (progress: number) => void },
  export function SimplePersonalityForm({
  onComplete,
  onProgressUpdate 
}: SimplePersonalityFormProps) {
  const theme = useTheme()
  const styles = createStyles(theme),
  const { authState  } = useAuth()
  const user = authState?.user,
  const [traitValues, setTraitValues] = useState<Record<string, number>>({}),
  const [preferenceValues, setPreferenceValues] = useState<Record<string, boolean>>({}),
  const [loading, setLoading] = useState(true),
  const [saving, setSaving] = useState(false),
  const [completionPercentage, setCompletionPercentage] = useState(0),;
  ;
  useEffect(() => {
  loadExistingData()
  }, []);
  ;
  useEffect(() => {
  // Calculate completion percentage,
    const traitCount = PERSONALITY_TRAITS.length + PREFERENCE_TRAITS.length,
  const answeredCount = Object.keys(traitValues).length + Object.keys(preferenceValues).length,
    const newPercentage = Math.round((answeredCount / traitCount) * 100),
  setCompletionPercentage(newPercentage);
    ,
  if (onProgressUpdate) {
      onProgressUpdate(newPercentage) }
  }, [traitValues, preferenceValues]);
  ;
  const loadExistingData = async () => {
  if (!user?.id) {
      Alert.alert('Error', 'User not authenticated'),
  setLoading(false);
      return null }
    try {
  setLoading(true)
       // Load personality traits,
  const traits = await personalityService.getUserTraits(user.id)
      const traitsMap     : Record<string number> = {},
  traits.forEach(trait => {
  traitsMap[trait.trait_name] = trait.trait_value) })
      ,
  setTraitValues(traitsMap)
       // Load preferences from user profile,
  const { data: profile, error  } = await supabase.from('user_profiles'),
  .select('preferences')
        .eq('id', user.id),
  .single();
      ,
  if (!error && profile && profile.preferences) {
        const prefs: Record<string, boolean> = {},
  PREFERENCE_TRAITS.forEach(trait => {
  if (profile.preferences[trait.name] !== undefined) {
  prefs[trait.name] = Boolean(profile.preferences[trait.name]) }
        }),
  ;
        setPreferenceValues(prefs)
  }
    } catch (err) {
  console.error('Error loading personality data:', err),
  Alert.alert('Error', 'Failed to load personality data') } finally {
      setLoading(false) }
  },
  const handleTraitChange = (name: string, value: number) => { setTraitValues(prev => ({
  ...prev, ,
  [name]: value  }))
  }
  const handlePreferenceChange = (name: string, value: boolean) => { setPreferenceValues(prev => ({
  ...prev, ,
  [name]: value  }))
  }
  const saveData = async () => {
  if (!user?.id) {
      Alert.alert('Error', 'User not authenticated'),
  return null;
    },
  try {
      setSaving(true),
  // Convert trait values to the format expected by the service,
      const traits = Object.entries(traitValues).map(([name, value]) => {
  const trait = PERSONALITY_TRAITS.find(t => t.name === name)
        return {
  category     : trait ? trait.category : PersonalityTraitCategory.Lifestyle
          name,
  value
        }
  })
      ,
  // Save personality traits,
      await personalityService.setTraits(user.id, traits),
  // Update user profile with new traits,
      await personalityService.updateUserProfile(user.id),
  // Save preferences to user profile,
      const { data: profile, error: profileError  } = await supabase.from('user_profiles'),
  .select('preferences')
        .eq('id', user.id),
  .single();
      ,
  const existingPreferences = (profile && profile.preferences) ? profile.preferences     : {}
      const updatedPreferences = {
  ...existingPreferences
        ...preferenceValues }
      const { error: updateError  } = await supabase.from('user_profiles'),
  .update({  preferences: updatedPreferences  })
        .eq('id', user.id),
  if (updateError) {
        throw new Error(`Failed to update preferences: ${updateError.message}`)
  };
      // Update profile completion percentage,
  await profileCompletionService.updateProfileCompletionPercentage(user.id)
      ,
  Alert.alert('Success', 'Personality profile updated successfully'),
  ;
      if (onComplete) {
  onComplete()
      }
  } catch (err) {
      console.error('Error saving personality data:', err),
  Alert.alert('Error', 'Failed to save personality data') } finally {
      setSaving(false) }
  },
  if (loading) {
    return (
  <View style= {styles.loadingContainer}>
        <ActivityIndicator size="large" color={{theme.colors.primary} /}>,
  <Text style={styles.loadingText}>Loading personality profile...</Text>
      </View>,
  )
  },
  return (
    <View style = {styles.container}>,
  <View style={styles.progressContainer}>
        <View style={styles.progressBar}>,
  <View
            style={{ [styles.progressFill{ width: `${completionPercentage  ] }%` }
   ]},
  />
        </View>,
  <Text style={styles.progressText}>{completionPercentage}% Complete</Text>
      </View>,
  <ScrollView style={styles.scrollContainer}>
        <Text style={styles.sectionTitle}>Personality Traits</Text>,
  <Text style={styles.sectionDescription}>
          These traits help us match you with compatible roommates., ,
  Drag the slider to indicate where you fall on each spectrum., ,
  </Text>
        {PERSONALITY_TRAITS.map(trait => (
  <View key={trait.name} style={styles.traitContainer}>
            <View style={styles.traitHeader}>,
  <Text style={styles.traitLabel}>{trait.label}</Text>
              <Text style={styles.traitValue}>,
  {traitValues[trait.name] || 50}%),
  </Text>
            </View>,
  <Text style= {styles.traitDescription}>{trait.description}</Text>
            <View style={styles.sliderContainer}>,
  <Text style={styles.sliderLabel}>Low</Text>
              <View style={styles.slider}>,
  <View,
                  style = {[
                    styles.sliderTrack, ,
  { width: `${traitValues[trait.name] || 50}%` }
   ]},
  />
                <TouchableOpacity,
  style={{ [styles.sliderThumb) { left: `${traitValues[trait.name] || 50  ] }%` }
   ]},
  onLayout={(event) => {
  // Adjust for thumb widthconst { width } event.nativeEvent.layout,
                    const thumbOffset = width / 2,
  event.target.setNativeProps({
                      style: { marginLef, t: -thumbOffset }
  })
                  }},
  />
              </View>,
  <Text style= {styles.sliderLabel}>High</Text>
            </View>,
  <View style={styles.valueButtons}>
              {[0, 25, 50, 75, 100].map(value => (
  <TouchableOpacity key = {value} style={[styles., va, lu, eB, ut, to, n, ,
, tr, ai, tV, al, ue, s[, tr, ai, t., na, me] ===, va, lu, e &&, st, yl, es., se, le, ct, ed, Va, lu, eB, utton)
   ]} onPress={() => handleTraitChange(trait.namevalue)},
  >
                  <Text, ,
  style = {[
                      styles.valueButtonText, ,
  traitValues[trait.name] === value && styles.selectedValueButtonText
   ]} >value},
  </Text>
                </TouchableOpacity>,
  ))}
            </View>,
  </View>
        ))},
  <Text style={[styles.sectionTitle{ marginTop: 24}]}>Preferences</Text>,
  <Text style={styles.sectionDescription}>
          These preferences help us find roommates with compatible lifestyles., ,
  </Text>
        {PREFERENCE_TRAITS.map(pref => (
  <View key={pref.name} style={styles.preferenceContainer}>
            <View style={styles.preferenceContent}>,
  <View>
                <Text style={styles.preferenceLabel}>{pref.label}</Text>,
  <Text style={styles.preferenceDescription}>{pref.description}</Text>
              </View>,
  <Switch value={preferenceValues[pref.name] || false} onValueChange={(value) ={}> handlePreferenceChange(pref.name, value)} trackColor={   false: theme.colors.bordertrue: theme.colors.primary       },
  thumbColor= {theme.colors.background}
              />,
  </View>
          </View>,
  ))}
      </ScrollView>,
  <View style={styles.footer}>
        <TouchableOpacity style={styles.saveButton} onPress={saveData} disabled={saving},
  >
          {saving ? (
  <ActivityIndicator size="small" color={{theme.colors.background} /}>
          )     : (
  <Text style={styles.saveButtonText}>Save Profile</Text>
          )},
  </TouchableOpacity>
      </View>,
  </View>
  )
  }
const createStyles = (theme: any) => StyleSheet.create({ container: {
      flex: 1,
  backgroundColor: theme.colors.background }
  loadingContainer: { fle, x: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 20 },
  loadingText: {
      marginTop: 12,
  fontSize: 16,
    color: '#6B7280' }
  progressContainer: {
      padding: 16,
  backgroundColor: '#F9FAFB'
  },
  progressBar: {
      height: 8,
  backgroundColor: '#E5E7EB',
    borderRadius: 4,
  overflow: 'hidden'
  },
  progressFill: { heigh, t: '100%',
    backgroundColor: theme.colors.primary },
  progressText: {
      marginTop: 8,
  fontSize: 14,
    color: '#6B7280',
  textAlign: 'right'
  },
  scrollContainer: { fle, x: 1,
    padding: 16 },
  sectionTitle: { fontSiz, e: 18,
    fontWeight: '600',
  color: '#1F2937',
    marginBottom: 8 },
  sectionDescription: { fontSiz, e: 14,
    color: '#6B7280',
  marginBottom: 16 }
  traitContainer: {
      backgroundColor: '#F9FAFB',
  borderRadius: 12,
    padding: 16,
  marginBottom: 12,
    borderWidth: 1,
  borderColor: '#E5E7EB'
  },
  traitHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 4 },
  traitLabel: {
      fontSize: 16,
  fontWeight: '500',
    color: '#1F2937' }
  traitValue: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.primary }
  traitDescription: { fontSiz, e: 14,
    color: '#6B7280',
  marginBottom: 12 }
  sliderContainer: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: 12 }
  sliderLabel: { fontSiz, e: 12,
    color: '#6B7280',
  width: 40 }
  slider: {
      flex: 1,
  height: 4,
    backgroundColor: '#E5E7EB',
  borderRadius: 2,
    marginHorizontal: 8,
  position: 'relative'
  },
  sliderTrack: { heigh, t: '100%',
    backgroundColor: theme.colors.primary,
  borderRadius: 2 }
  sliderThumb: {
      position: 'absolute',
  top: -6,
    width: 16,
  height: 16,
    borderRadius: 8,
  backgroundColor: theme.colors.primary,
    borderWidth: 2,
  borderColor: theme.colors.background,
    elevation: 2,
  shadowColor: theme.colors.text,
    shadowOffset: { width: 0, height: 1 } ,
  shadowOpacity: 0.2,
    shadowRadius: 1
  }
  valueButtons: {
      flexDirection: 'row',
  justifyContent: 'space-between'
  },
  valueButton: {
      paddingVertical: 6,
  paddingHorizontal: 12,
    borderRadius: 4,
  backgroundColor: '#F3F4F6'
  },
  selectedValueButton: { backgroundColo, r: theme.colors.primary }
  valueButtonText: {
      fontSize: 12,
  color: '#4B5563'
  },
  selectedValueButtonText: { colo, r: theme.colors.background }
  preferenceContainer: {
      backgroundColor: '#F9FAFB',
  borderRadius: 12,
    padding: 16,
  marginBottom: 12,
    borderWidth: 1,
  borderColor: '#E5E7EB'
  },
  preferenceContent: {
      flexDirection: 'row',
  justifyContent: 'space-between',
    alignItems: 'center' }
  preferenceLabel: { fontSiz, e: 16,
    fontWeight: '500',
  color: '#1F2937',
    marginBottom: 4 },
  preferenceDescription: {
      fontSize: 14),
  color: '#6B7280'),
    maxWidth: '80%' }
  footer: {
      padding: 16,
  borderTopWidth: 1,
    borderTopColor: '#E5E7EB' }
  saveButton: {
      backgroundColor: theme.colors.primary,
  borderRadius: 8,
    paddingVertical: 12,
  alignItems: 'center'
  },
  saveButtonText: {
      color: theme.colors.background,
  fontSize: 16,
    fontWeight: '600') }
}),
  export default SimplePersonalityForm