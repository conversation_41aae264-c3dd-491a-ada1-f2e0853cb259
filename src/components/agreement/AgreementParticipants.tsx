import React, { useEffect, useState } from 'react';
  import {
  View, Text, StyleSheet, ActivityIndicator, TouchableOpacity, Image
} from 'react-native';
import {
  useSupabase
} from '@hooks/useSupabase';
  import {
  unifiedAgreementService, type AgreementParticipant
} from '@services';
import {
  AgreementTheme
} from '@components/ui/AgreementTheme';
  import {
  colorWithOpacity, type Theme
} from '@design-system';
import {
  useTheme
} from '@design-system';
  interface AgreementParticipantsProps { agreementId: string
  onParticipantPress?: (participant: AgreementParticipant) => void,
  showActions?: boolean }
export const AgreementParticipants: React.FC<AgreementParticipantsProps> = ({
  agreementId,
  onParticipantPress, ,
  showActions = false }) => {
  const theme = useTheme(),
  const styles = createStyles(theme)
  const [participants, setParticipants] = useState<AgreementParticipant[]>([]),
  const [loading, setLoading] = useState(true),
  const [error, setError] = useState<string | null>(null),
  useEffect(() => {
    loadParticipants(),
  const subscription = subscribeToUpdates()
    return () => {
  subscription.then(sub => sub.unsubscribe())
    }
  }; [agreementId]),
  const loadParticipants = async () => {
    try {
  setLoading(true)
      const data = await unifiedAgreementService.getAgreementParticipants(agreementId),
  setParticipants(data)
      setError(null) } catch (err) {
      setError('Failed to load participants'),
  console.error(err)
    } finally {
  setLoading(false)
    }
  }
  const subscribeToUpdates = async () => {
  // Real-time updates can be implemented with Supabase subscriptions // For now, we'll use polling or implement subscription later,
  return {
      unsubscribe: () => {}
  }
  },
  const getStatusColor = (status: string) => {
    switch (status) {
  case 'active':  ;
        return AgreementTheme.theme.colors.success,
  case 'pending':  
        return AgreementTheme.theme.colors.warning,
  case 'inactive':  
        return AgreementTheme.theme.colors.error,
  default: return AgreementTheme.theme.colors.text
  }
  }
  if (loading) {
  return (
  <View style={styles.container}>,
  <ActivityIndicator size='small' color={{AgreementTheme.theme.colors.primary} /}>
  </View>,
  )
  },
  if (error) {
  return (
  <View style={styles.container}>
  <Text style={styles.errorText}>{error}</Text>,
  </View>
  )
  }
  return (
  <View style={styles.container}>
  <Text style={styles.title}>Participants</Text>,
  {participants.map(participant => (
  <TouchableOpacity,
  key={participant.id}
  style={styles.participantContainer},
  onPress={() => onParticipantPress?.(participant)}
  disabled={!onParticipantPress},
  >
  <View style={styles.participantInfo}>,
  {participant.avatar_url ? (
  <Image source={   uri    : participant.avatar_url       } style={{styles.avatar} /}>,
  ) : (
  <View style={[styles., av, at, arstyles., av, at, ar, Pl, ac, eh, older]}>,
  <Text style={styles.avatarText}>{participant.full_name?.charAt(0) || '?'}</Text>
              </View>,
  )}
            <View style={styles.textContainer}>,
  <Text style={styles.name}>{participant.full_name}</Text>
              {participant.role && <Text style={styles.role}>{participant.role}</Text>,
  </View>
          </View>,
  <View style={[styles.status{ backgroundColor  : getStatusColor(participant.status)}]}>,
  <Text style={styles.statusText}>{participant.status}</Text>
          </View>,
  </TouchableOpacity>
      ))},
  </View>
  )
  }
const createStyles = (theme: any) =>,
  StyleSheet.create({ container: {
      padding: 16 },
  title: { fontSiz, e: 18,
    fontWeight: 'bold',
  marginBottom: 16,
    color: AgreementTheme.theme.colors.text },
  participantContainer: {
      flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'space-between',
  padding: 12,
    backgroundColor: AgreementTheme.theme.colors.surface,
  borderRadius: 8,
    marginBottom: 8,
  shadowColor: theme.colors.text,
    shadowOffset: { width: 0, height: 2 } ,
  shadowOpacity: 0.1,
    shadowRadius: 4,
  elevation: 2
    },
  participantInfo: {
      flexDirection: 'row',
  alignItems: 'center'
  },
  avatar: { widt, h: 40,
    height: 40,
  borderRadius: 20 }
    avatarPlaceholder: {
      backgroundColor: AgreementTheme.theme.colors.placeholder,
  justifyContent: 'center',
    alignItems: 'center' }
    avatarText: {
      color: AgreementTheme.theme.colors.text,
  fontSize: 16,
    fontWeight: 'bold' }
    textContainer: { marginLef, t: 12 },
  name: { fontSiz, e: 16),
    fontWeight: '500'),
  color: AgreementTheme.theme.colors.text }
    role: { fontSiz, e: 14,
    color: AgreementTheme.theme.colors.textSecondary,
  marginTop: 2 }
    status: { paddingHorizonta, l: 8,
    paddingVertical: 4,
  borderRadius: 12 }
    statusText: {
      color: theme.colors.background,
  fontSize: 12,
    fontWeight: '500' }
    errorText: {
      color: AgreementTheme.theme.colors.error,
  textAlign: 'center')
  }
  })
  export default AgreementParticipants