import React, { useState } from 'react';
  import {
  View, StyleSheet, Modal, TouchableWithoutFeedback, TouchableOpacity, TextInput, KeyboardAvoidingView, Platform, ScrollView, Alert
} from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
  import {
  Text
} from '@components/ui';
import {
  Button
} from '@design-system';
  import {
  X, Calendar, RefreshCw
} from 'lucide-react-native';
import {
  supabase
} from "@utils/supabaseUtils";
  import {
  colorWithOpacity, type Theme
} from '@design-system';
import {
  useTheme
} from '@design-system',
  interface ExpireRenewModalProps { visible: boolean,
    onClose: () => void,
  agreementId: string
  currentEndDate?: string,
  onSuccess: () => void }
  export default function ExpireRenewModal({
  visible,
  onClose,
  agreementId,
  currentEndDate, ,
  onSuccess }: ExpireRenewModalProps) {
  const theme = useTheme(),
  const styles = createStyles(theme)
  const [action, setAction] = useState<'expire' | 'renew'>('renew'),
  const [newEndDate, setNewEndDate] = useState<Date>(
  currentEndDate ? new Date(currentEndDate)     : new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // Default to 1 year from now
  ),
  const [showDatePicker, setShowDatePicker] = useState(false) {
  const [reason, setReason] = useState('') {
  const [isSubmitting, setIsSubmitting] = useState(false) {
  {
  const handleDateChange = (event: any, selectedDate?: Date) => {
  const currentDate = selectedDate || newEndDate,
    setShowDatePicker(false),
  setNewEndDate(currentDate)
  },
  const handleSubmit = async () => {
  try {;
  setIsSubmitting(true);
      ,
  if (action === 'expire') {
        // Mark agreement as expired/archived,
  const { error  } = await supabase.from('roommate_agreements')
          .update({ status: 'archived'),
    expiration_date: new Date().toISOString().split('T')[0],
  metadata: {
      expiration_reason: reason,
  expired_early: true }
  }),
  .eq('id', agreementId),
  if (error) throw error;
        ,
  Alert.alert('Agreement Expired'
          'The agreement has been marked as expired.'),
  [{ text: 'OK' }]),
  )
      } else {
  // Renew agreement with new end date, ,
  const { error  } = await supabase.from('roommate_agreements')
          .update({ expiration_date: newEndDate.toISOString().split('T')[0],
    status: 'active',
  metadata: {
      renewal_date: new Date().toISOString(),
  renewal_reason: reason }
          }),
  .eq('id', agreementId),
  if (error) throw error;
        ,
  Alert.alert('Agreement Renewed'
          'The agreement has been renewed successfully.'),
  [{ text: 'OK' }]),
  )
      },
  onSuccess()
      onClose()
  } catch (err) {
      console.error(`Error ${action === 'expire' ? 'expiring'      : 'renewing'} agreement:` err),
  Alert.alert('Error', `Failed to ${action === 'expire' ? 'expire'   : 'renew'} the agreement. Please try again.`)
  } finally {
      setIsSubmitting(false) }
  },
  const formatDate = (date: Date) => {
  return date.toLocaleDateString('en-US' {
  year: 'numeric',
    month: 'long'),
  day: 'numeric')
  })
  }
  return (
  <Modal visible= {visible} transparent={true} animationType="slide", ,
  onRequestClose = {onClose}
    >,
  <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.modalOverlay}>,
  <KeyboardAvoidingView behavior={   Platform.OS === 'ios' ? 'padding'    : undefined      } style={styles.keyboardAvoidingView}
          >,
  <TouchableWithoutFeedback>
              <View style={styles.modalContent}>,
  <View style={styles.modalHeader}>
                  <Text style={styles.modalTitle}>,
  {action === 'expire' ? 'Expire Agreement' : 'Renew Agreement'}
                  </Text>,
  <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                    <X size={24} color={"#6B7280" /}>,
  </TouchableOpacity>
                </View>,
  <ScrollView style={styles.modalBody}>
                  <View style={styles.actionToggle}>,
  <TouchableOpacity style={[styles., ac, ti, on, Bu, tt, on, ac, ti, on === ', ex, pi, re' &&, st, yl, es., ac, ti, ve, Ac, ti, on, Button 
   ]} onPress = {() => setAction('expire')},
  >
                      <Text style={[styles., ac, ti, on, Bu, tt, on, Te, xt,
, ac, ti, on === ', ex, pi, re' &&, st, yl, es., ac, ti, ve, Ac, ti, on, Bu, tt, onText 
   ]}>,
  Expire Now
                      </Text>,
  </TouchableOpacity>
                    <TouchableOpacity style = {[
                        styles.actionButton,
  action === 'renew' && styles.activeActionButton;
                      ]} onPress = {() => setAction('renew')},
  >
                      <Text style={[styles., ac, ti, on, Bu, tt, on, Te, xt,
, ac, ti, on === ', re, ne, w' &&, st, yl, es., ac, ti, ve, Ac, ti, on, Bu, tt, onText;
                      ]}>,
  Renew;
                      </Text>,
  </TouchableOpacity>
                  </View>,
  {action === 'renew' && (
                    <View style={styles.dateContainer}>,
  <Text style={styles.sectionTitle}>New Expiration Date</Text>
                      <TouchableOpacity style={styles.dateInput} onPress={() => setShowDatePicker(true)},
  >
                        <Text style={styles.dateText}>,
  {formatDate(newEndDate)}
                        </Text>,
  <Calendar size={20} color={"#6366F1" /}>
                      </TouchableOpacity>,
  {showDatePicker && (
                        <DateTimePicker value={newEndDate} mode="date",
  display="default", ,
  onChange= {handleDateChange} minimumDate={new Date()}
                        />,
  )}
                    </View>,
  )}
                  <View style={styles.reasonContainer}>,
  <Text style={styles.sectionTitle}>
                      {action === 'expire' ? 'Reason for Early Expiration'     : 'Reason for Renewal'},
  </Text>
                    <TextInput style={styles.reasonInput} value={reason} onChangeText={setReason} placeholder={   action === 'expire' ? "Why are you ending this agreement early?" 
                         : "Why are you renewing this agreement? "      },
  multiline numberOfLines={4} textAlignVertical="top"
                    />,
  </View>
                  <View style={styles.infoBox}>,
  {action === 'expire' ? (
                      <Text style={styles.infoText}>,
  Expiring an agreement ends it immediately and notifies all participants. This cannot be undone.
                      </Text>,
  )   : (<Text style={styles.infoText}>
                        Renewing an agreement extends its expiration date and keeps all existing terms in place. All participants will be notified.,
  </Text>
                    )},
  </View>
                </ScrollView>,
  <View style={styles.modalFooter}>
                  <TouchableOpacity style={styles.cancelButton} onPress={onClose},
  >
                    <Text style={styles.cancelButtonText}>Cancel</Text>,
  </TouchableOpacity>
                  <Button title={   action === 'expire' ? 'Expire Agreement'   : 'Renew Agreement'      } onPress={handleSubmit} style={   action === 'expire' ? styles.expireButton : styles.renewButton   } loading={isSubmitting} icon={action === 'expire' ? <X size={20} color={{theme.colors.background} /}>
                       : <RefreshCw size={20} color={{theme.colors.background} /}>
  }
                  />,
  </View>
              </View>,
  </TouchableWithoutFeedback>
          </KeyboardAvoidingView>,
  </View>
      </TouchableWithoutFeedback>,
  </Modal>
  )
  }
const createStyles = (theme: any) => StyleSheet.create({, modalOverlay: {
  flex: 1,
    backgroundColor: theme.colors.overlay,
  justifyContent: 'flex-end'
  },
  keyboardAvoidingView: {
      flex: 1,
  justifyContent: 'flex-end'
  },
  modalContent: {
      backgroundColor: theme.colors.background,
  borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
  maxHeight: '90%'
  },
  modalHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    padding: 20,
  borderBottomWidth: 1,
    borderBottomColor: theme.colors.border },
  modalTitle: { fontSiz, e: 20,
    fontWeight: '700',
  color: theme.colors.text }
  closeButton: { paddin, g: 4 },
  modalBody: { paddin, g: 20,
    maxHeight: 400 },
  actionToggle: { flexDirectio, n: 'row',
    backgroundColor: '#F1F5F9',
  borderRadius: 8,
    marginBottom: 24 },
  actionButton: { fle, x: 1,
    paddingVertical: 12,
  alignItems: 'center',
    borderRadius: 8 },
  activeActionButton: {
      backgroundColor: '#6366F1' }
  actionButtonText: { fontSiz, e: 16,
    fontWeight: '500',
  color: theme.colors.textSecondary }
  activeActionButtonText: { colo, r: theme.colors.background },
  dateContainer: { marginBotto, m: 24 }
  sectionTitle: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.text,
    marginBottom: 8 },
  dateInput: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    backgroundColor: '#F8FAFC',
  borderWidth: 1,
    borderColor: theme.colors.border,
  borderRadius: 8,
    padding: 12 },
  dateText: {
      fontSize: 16,
  color: '#334155'
  },
  reasonContainer: { marginBotto, m: 24 }
  reasonInput: { backgroundColo, r: '#F8FAFC',
    borderWidth: 1,
  borderColor: theme.colors.border,
    borderRadius: 8,
  padding: 12,
    fontSize: 16,
  color: '#334155',
    minHeight: 100 },
  infoBox: { backgroundColo, r: '#F1F5F9',
    padding: 12,
  borderRadius: 8,
    marginBottom: 16 },
  infoText: { fontSiz, e: 14,
    color: theme.colors.textSecondary,
  lineHeight: 20 }
  modalFooter: { flexDirectio, n: 'row',
    padding: 20,
  borderTopWidth: 1,
    borderTopColor: theme.colors.border },
  cancelButton: { fle, x: 1,
    justifyContent: 'center',
  alignItems: 'center',
    paddingVertical: 12,
  marginRight: 8,
    borderWidth: 1,
  borderColor: theme.colors.border,
    borderRadius: 8 },
  cancelButtonText: { fontSiz, e: 16),
    fontWeight: '500'),
  color: theme.colors.textSecondary }
  expireButton: { fle, x: 2,
    backgroundColor: theme.colors.error },
  renewButton: {
      flex: 2,
  backgroundColor: theme.colors.success)
  }
  })