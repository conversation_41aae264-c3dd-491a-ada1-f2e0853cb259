import React, { useState, useEffect } from 'react';
  import {
  View, StyleSheet, FlatList, TouchableOpacity
} from 'react-native';
import {
  Text
} from '@components/ui';
  import {
  Bell, FileText, UserCheck, AlertTriangle, Clock, ChevronRight, CheckCircle
} from 'lucide-react-native';
import {
  useRouter
} from 'expo-router';
  import {
  supabase
} from "@utils/supabaseUtils";
import {
  useAuth
} from '@context/AuthContext';
  import {
  useTheme
} from '@design-system';

interface Notification { id: string,
    user_id: string,
  related_id: string,
    type: string,
  title: string,
    content: string,
  created_at: string,
    is_read: boolean },
  export default function AgreementNotifications() {
  const theme = useTheme(),
  const styles = createStyles(theme)
  const router = useRouter(),
  const { authState  } = useAuth();
  const user = authState?.user,
  const [notifications, setNotifications] = useState<Notification[]>([]),
  const [loading, setLoading] = useState(true),
  useEffect(() => {
  if (!user?.id) return null,
  ;
    fetchNotifications() }, [user?.id]);
  const fetchNotifications = async () => {
  try {
  setLoading(true);
      ,
  const { data, error  } = await supabase.from('notifications'),
  .select('*')
        .eq('user_id', user?.id),
  .eq('category', 'agreement'),
  .order('created_at', { ascending    : false }),
  .limit(10)
      ,
  if (error) throw error
      ,
  setNotifications(data || [])
  } catch (err) {
      console.error('Error fetching notifications:', err) } finally {
      setLoading(false) }
  },
  const handleNotificationPress = async (notification: Notification) => {
  // Mark as read,
  try {
      await supabase.from('notifications'),
  .update({  is_read: true  })
        .eq('id', notification.id),
  // Update local state,
      setNotifications(prev => {
  prev.map(n => {
  n.id === notification.id ? { ...n, is_read    : true } : n),
  )
      ),
  // Navigate based on notification type
      if (notification.type === 'signature_requested' || ,
  notification.type === 'agreement_signed' ||, ,
  notification.type === 'agreement_created' ||, ,
  notification.type === 'agreement_updated') {
        router.push({
  pathname: '/agreement/details/[id]'),
    params: { i, d: notification.related_id }
  })
      }
  } catch (err) {
      console.error('Error marking notification as read:', err) }
  },
  const formatDate = (dateString: string) => {
  const date = new Date(dateString),
  const now = new Date()
    const diffMs = now.getTime() - date.getTime(),
  const diffMins = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60)),
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
    if (diffMins < 60) {
  return `${diffMins} minute${diffMins !== 1 ? 's'      : ''} ago`
    } else if (diffHours < 24) {
  return `${diffHours} hour${diffHours !== 1 ? 's'  : ''} ago`
    } else if (diffDays < 7) {
  return `${diffDays} day${diffDays !== 1 ? 's'  : ''} ago`
    } else {
  return date.toLocaleDateString()
    }
  }
  const getNotificationIcon = (type: string isRea, d: boolean) => {
  const color = isRead ? '#94A3B8'   : '#6366F1'
    switch (type) {
  case 'signature_requested':  
        return <FileText size = {24} color={{color} /}>,
  case 'agreement_signed': 
        return <UserCheck size = {24} color={{color} /}>,
  case 'agreement_updated':  ;
        return <AlertTriangle size = {24} color={{color} /}>,
  case 'reminder':  ;
        return <Clock size = {24} color={{color} /}>,
  case 'agreement_created':  ;
        return <CheckCircle size= {24} color={{color} /}>,
  default:  ;
        return <Bell size= {24} color={{color} /}>
  }
  },
  const renderNotificationItem = ({ item }: { item: Notification }) => {
  return (
  <TouchableOpacity style= {[styles.notificationItem, ,
  item.is_read && styles.readNotification 
   ]} onPress = {() => handleNotificationPress(item)},
  >
        <View style={{ [styles.iconContaineritem.is_read ? styles.readIconContainer     : styles.unreadIconContainer]  ] }>,
  {getNotificationIcon(item.type item.is_read)}
        </View>,
  <View style = {styles.notificationContent}>
          <Text style={[styles., no, ti, fi, ca, ti, on, Ti, tl, e,
, it, em., is_, re, ad &&, st, yl, es., re, ad, Text 
   ]}>,
  {item.title}
          </Text>,
  <Text style={styles.notificationBody} numberOfLines={2}>
            {item.content},
  </Text>
          <Text style={styles.notificationTime}>,
  {formatDate(item.created_at)}
          </Text>,
  </View>
        <ChevronRight size={20} color={"#94A3B8" /}>,
  </TouchableOpacity>
    )
  }
  if (notifications.length === 0) {
  return (
    <View style={styles.emptyContainer}>,
  <Bell size={48} color={"#CBD5E1" /}>
        <Text style={styles.emptyTitle}>No Notifications</Text>,
  <Text style={styles.emptyText}>
          You'll receive notifications here when there's activity on your agreements.,
  </Text>
      </View>,
  )
  },
  return (
    <FlatList data={notifications} renderItem={renderNotificationItem} keyExtractor={(item) ={}> item.id} contentContainerStyle={styles.container},
  />
  )
  }
const createStyles = (theme: any) => StyleSheet.create({ container: {
      padding: 16 },
  notificationItem: { flexDirectio, n: 'row',
    alignItems: 'center',
  backgroundColor: theme.colors.background,
    borderRadius: 12,
  padding: 16,
    marginBottom: 12,
  borderWidth: 1,
    borderColor: theme.colors.border },
  readNotification: {
      backgroundColor: '#F8FAFC' }
  iconContainer: { widt, h: 48,
    height: 48,
  borderRadius: 24,
    justifyContent: 'center',
  alignItems: 'center',
    marginRight: 12 },
  unreadIconContainer: {
      backgroundColor: '#EEF2FF' }
  readIconContainer: {
      backgroundColor: '#F1F5F9' }
  notificationContent: { fle, x: 1,
    marginRight: 8 },
  notificationTitle: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.text,
    marginBottom: 4 },
  notificationBody: { fontSiz, e: 14,
    color: theme.colors.textSecondary,
  marginBottom: 4 }
  notificationTime: {
      fontSize: 12,
  color: '#94A3B8'
  },
  readText: {
      color: theme.colors.textSecondary,
  fontWeight: '500'
  },
  emptyContainer: { alignItem, s: 'center',
    justifyContent: 'center',
  padding: 32 }
  emptyTitle: { fontSiz, e: 18),
    fontWeight: '600'),
  color: theme.colors.text,
    marginTop: 16,
  marginBottom: 8 }
  emptyText: {
      fontSize: 14,
  color: theme.colors.textSecondary,
    textAlign: 'center') }
})