import React from 'react';
  import {
  View, Text, TouchableOpacity
} from 'react-native';
import {
  Filter
} from 'lucide-react-native' // Simplified MatchFilter for debugging infinite loop issues,
  function MatchFilter() {
  // TEMPORARILY DISABLED TO ISOLATE INFINITE LOOP ISSUE,
  console.log('MatchFilter: Rendering (simplified version)')
  return (
  <View style= {{ [position: 'absolute', top: 10, right: 10, zIndex: 10 ]  ] }>,
  <TouchableOpacity
        style={   {
  flexDirection: 'row',
    alignItems: 'center',
  backgroundColor: '#ffffff',
    paddingHorizontal: 16,
  paddingVertical: 8,
    borderRadius: 20,
  shadowColor: '#000000'shadowOffset: { width: 0height: 2 } ,
  shadowOpacity: 0.1,
    shadowRadius: 4,
  elevation: 2
        }},
  onPress= {() => console.log('Filter button pressed')}
      >,
  <Filter size={20} color={'#333333' /}>
        <Text,
  style={{ [marginLeft: 8,
    fontSize: 14fontWeight: '600'color: '#333333']  ] },
  >
          Filters,
  </Text>;
      </TouchableOpacity>;
  </View>
  );
  }
export default MatchFilter