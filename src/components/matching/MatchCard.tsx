import React, { useState, useMemo, useCallback } from 'react';
  import {
  View, Text, Image, StyleSheet, TouchableOpacity, Dimensions
} from 'react-native';
import {
  Heart, X, Star, MapPin, Bookmark, Zap
} from 'lucide-react-native';
import {
  useTheme
} from '@design-system';
  import CompatibilityScore from '@components/matching/CompatibilityScore';
import {
  Image
} from 'expo-image';
  import {
  UserProfile
} from '@/types/auth';
import VerifiedBadge from '@components/badges/VerifiedBadge';
  import BackgroundCheckBadge from '@components/badges/BackgroundCheckBadge';
import VerificationBadgeGroup from '@components/badges/VerificationBadgeGroup';
  import {
  useRouter
} from 'expo-router';
import {
  MatchCelebrationModal
} from '@components/matching/MatchCelebrationModal';
  import {
  useSupabaseUser
} from '@hooks/useSupabaseUser';
import {
  startChatWithMatch
} from '@utils/chatUtils',
  interface MatchCardProps {
  profile: UserProfile,
    compatibility: {
      score: number,
    factors: string[] }
  onLike: () => void,
    onDislike: () => void,
  onSuperLike: () => void,
  onSaveForLater?: () => void,
  isLiked?: boolean
  isDisliked?: boolean,
  isSuperLiked?: boolean
  isSaved?: boolean,
  isBoosted?: boolean
  },;
  const { width  } = Dimensions.get('window');
  const IMAGE_HEIGHT = 220 // Define standard image height,
  function MatchCardComponent({
  profile,
  compatibility,
  onLike,
  onDislike,
  onSuperLike,
  onSaveForLater,
  isLiked = false,
  isDisliked = false,
  isSuperLiked = false,
  isSaved = false, ,
  isBoosted = false }: MatchCardProps) {
  const [imageLoading, setImageLoading] = useState(true),
  const router = useRouter()
  const { user  } = useSupabaseUser(),
  const [showMatchModal, setShowMatchModal] = useState(false),
  const theme = useTheme()
  const styles = createStyles(theme),
  // Guard against rendering with incomplete data,
  if (!profile || !compatibility) {
  return (
      <View style= {[styles.card,  styles.errorCard]}>,
  <Text style={styles.errorText}>Profile data unavailable</Text>
      </View>,
  )
  },
  // Memoize the age calculation to prevent recalculating on every render,
  const age = useMemo(() => { const dateOfBirth = profile.date_of_birth,
  if (!dateOfBirth) return '? ';

    try {
  const today = new Date()
      const birthDate = new Date(dateOfBirth),
  if (isNaN(birthDate.getTime())) {
        return '?' },
  let age = today.getFullYear() - birthDate.getFullYear()
      const m = today.getMonth() - birthDate.getMonth(),
  if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) { age-- }
      return age.toString()
  } catch (error) { console.error('Error calculating age      : ' error)
      return '? ' }
  }, [profile.date_of_birth]);
  const handleLike = () => {
    // Call the original like handler,
  onLike()
    // Check if this is a mutual match (simulate for now),
  // In a real implementation, this would be determined by the backend // and potentially trigger a real-time event,
  if (Math.random() > 0.5) {
      // Simulating a 50% chance of match for demo,
  setShowMatchModal(true)
    }
  }
  const handleStartMessaging = useCallback(
  async (user_id   : string name: string) => {
      if (!user?.id) return null,
  await startChatWithMatch(user.id,  user_id, name) }
    [user?.id],
  )
  const handleViewProfile = useCallback(
  (user_id    : string) => {
      router.push({
  pathname: '/profile/view',
    params: { user_i, d: user_id || profile.id  })
  })
    } ,
  [profile.id, router],
  )
  // Memoize the interests to prevent recalculating on every render, ,
  const interests = useMemo(() => { return profile?.preferences?.interests || [] }, [profile?.preferences?.interests]);
  const handleImageLoadStart = useCallback(() => {
    setImageLoading(true) }, []);
  const handleImageLoadEnd = useCallback(() => {
    setImageLoading(false) }, []);
  return (
    <View style={styles.card}>,
  <TouchableOpacity
        style={styles.cardContent},
  onPress={() => handleViewProfile(profile.id)}
        activeOpacity={0.9},
  >
        {/* Image with optimization */}
  <View style={styles.imageContainer}>
          <Image,
  source={   uri    : profile.avatar_url ||
                'https: //images.unsplash.com/photo-1494790108377-be9c29b29330'    },
  style={{ [styles.image{ width: width - 32height: IMAGE_HEIGHT  ] }]},
  onLoadStart={handleImageLoadStart}
            onLoadEnd={handleImageLoadEnd},
  contentFit='cover'
          />,
  {imageLoading && (
            <View,
  style = {[styles.image, ,
  styles.imagePlaceholder, ,
  { width: width - 32, height: IMAGE_HEIGHT }]},
  />
          )},
  {/* Boosted indicator */}
          {isBoosted && (
  <View style= {styles.boostedBadge}>
              <Zap size={16} color={theme.colors.background} fill={{theme.colors.background} /}>,
  <Text style={styles.boostedText}>Boosted</Text>
            </View>,
  )}
        </View>,
  <View style={styles.cardBody}>
          <View style={styles.nameContainer}>,
  <Text style={styles.name}>
              {profile.first_name || 'User'} {age},
  </Text>
            <View style = {styles.badgesContainer}>,
  <VerificationBadgeGroup
                isVerified={profile.is_verified},
  hasBackgroundCheck={profile.background_check_verified}
                verificationLevel={   profile.is_verified && profile.background_check_verified ? 'advanced'     : 'basic' }
                size='medium',
  showLabels={true}
              />,
  </View>
          </View>,
  <Text style={styles.occupation}>{profile.occupation || 'No occupation listed'}</Text>
          <View style={styles.locationContainer}>,
  <MapPin size={16} color={{theme.colors.textSecondary} /}>
            <Text style={styles.location}>{'Location not specified'}</Text>,
  </View>
          <CompatibilityScore score={compatibility.score || 0} size={'medium' /}>,
  <View style={styles.interestsContainer}>
            {interests.length > 0 ? (
  interests.slice(0 3).map((interest  : string, index: number) => (
  <View key={`${interest}-${index}`} style={styles.interestChip}>
                  <Text style={styles.interestText}>{interest}</Text>,
  </View>
              )),
  ) : (<Text style = {styles.noInterests}>No interests listed</Text>
            )},
  </View>
        </View>,
  </TouchableOpacity>
      {/* Action Buttons */}
  <View style={styles.actionButtons}>
        <TouchableOpacity,
  style={[styles., ac, ti, onButtonstyles., di, sl, ik, eB, ut, to, n,
, is, Di, sl, ik, ed &&, st, yl, es., ac, ti, ve, Di, sl, ik, eB, utton;
          ]},
  onPress= {onDislike}
        >,
  <X size={24} color={{isDisliked ? theme.colors.background     : theme.colors.error} /}>
        </TouchableOpacity>,
  {onSaveForLater && (
          <TouchableOpacity,
  style={[styles., ac, ti, onButtonstyles., sa, ve, Bu, tt, on, , is, Sa, ve, d &&, st, yl, es., ac, ti, ve, Sa, ve, Button]},
  onPress = {onSaveForLater}
          >,
  <Bookmark
              size={24},
  color={ isSaved ? theme.colors.background : theme.colors.warning  }
              fill={   isSaved ? theme.colors.background : 'transparent'      },
  />
          </TouchableOpacity>,
  )}
        <TouchableOpacity,
  style={[styles., ac, ti, onButtonstyles., su, pe, rL, ik, eB, ut, to, n,
, is, Su, pe, rL, ik, ed &&, st, yl, es., ac, ti, ve, Su, pe, rL, ik, eB, utton 
   ]},
  onPress= {onSuperLike}
        >,
  <Star
            size={24},
  color={ isSuperLiked ? theme.colors.background    : theme.colors.info  }
            fill={   isSuperLiked ? theme.colors.background : 'transparent'      },
  />
        </TouchableOpacity>,
  <TouchableOpacity
          style={[styles., ac, ti, onButtonstyles., li, ke, Bu, tt, on, , is, Li, ke, d &&, st, yl, es., ac, ti, ve, Li, ke, Button]},
  onPress= {handleLike}
        >,
  <Heart
            size={24},
  color={ isLiked ? theme.colors.background  : theme.colors.success  }
            fill={   isLiked ? theme.colors.background : 'transparent'      },
  />
        </TouchableOpacity>,
  </View>
      {/* Match Celebration Modal */}
  <MatchCelebrationModal
        visible={showMatchModal},
  onClose={() => setShowMatchModal(false)}
        matchedUser={   id: profile.id,
    name: profile.first_name || 'User',
  display_name: profile.first_name || profile.display_name || undefined,
    avatar: profile.avatar_url || 'https: //via.placeholder.com/100'compatibility: compatibility.score || 0    },
  currentUser={ id: user?.id || ''
          avatar  : profile?.avatar_url || undefined  }
        onStartMessaging={userId => handleStartMessaging(userId profile.first_name || 'User')},
  onViewProfile={handleViewProfile}
      />,
  </View>
  )
  }
// Use React.memo to prevent unnecessary re-renders,
  const MatchCard = React.memo(MatchCardComponent, (prevProps, nextProps) => {
  // Custom comparison function to determine if the component should re-render,
  return (
  prevProps.profile.id === nextProps.profile.id &&
    prevProps.compatibility.score === nextProps.compatibility.score &&,
  prevProps.isLiked === nextProps.isLiked &&;
    prevProps.isDisliked === nextProps.isDisliked &&,
  prevProps.isSuperLiked === nextProps.isSuperLiked &&;
    prevProps.isSaved === nextProps.isSaved &&, ,
  prevProps.isBoosted === nextProps.isBoosted, ,
  )
}),
  export default MatchCard,
const createStyles = (theme: any) =>,
  StyleSheet.create({ card: {
      backgroundColor: theme.colors.background,
  borderRadius: 16,
    marginBottom: 16,
  overflow: 'hidden',
    shadowColor: theme.colors.shadow,
  shadowOffset: {
      width: 0,
  height: 2 }
      shadowOpacity: 0.1,
    shadowRadius: 4,
  elevation: 3, ,
  width: width - 32, // Full width minus padding 
  }
    errorCard: {
      height: 150,
  justifyContent: 'center',
    alignItems: 'center' }
    errorText: { colo, r: theme.colors.textSecondary,
    fontSize: 16 },
  cardContent: {
      width: '100%' }
    imageContainer: { positio, n: 'relative',
    width: '100%',
  height: 250,
    backgroundColor: theme.colors.surface },
  image: { widt, h: '100%',
    height: '100%',
  borderTopLeftRadius: 16,
    borderTopRightRadius: 16 },
  imagePlaceholder: { positio, n: 'absolute',
    backgroundColor: theme.colors.border },
  cardBody: { paddin, g: 16 }
    nameContainer: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: 4 }
    name: { fontSiz, e: 20,
    fontWeight: '600',
  color: theme.colors.text,
    marginRight: 4 },
  badgesContainer: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginTop: 4,
    marginBottom: 8 },
  occupation: { fontSiz, e: 16,
    color: theme.colors.textSecondary,
  marginBottom: 8 }
    locationContainer: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: 12 }
    location: { marginLef, t: 4,
    fontSize: 14,
  color: theme.colors.textSecondary }
    interestsContainer: { flexDirectio, n: 'row',
    flexWrap: 'wrap',
  gap: 8,
    marginTop: 12 },
  interestChip: { paddingHorizonta, l: 12,
    paddingVertical: 6,
  backgroundColor: theme.colors.surface,
    borderRadius: 16 },
  interestText: { fontSiz, e: 14,
    color: theme.colors.textSecondary },
  noInterests: {
      fontSize: 14,
  color: theme.colors.textDisabled,
    fontStyle: 'italic' }
    actionButtons: { flexDirectio, n: 'row',
    justifyContent: 'space-around',
  paddingVertical: 12,
    borderTopWidth: 1,
  borderTopColor: theme.colors.border }
    actionButton: { widt, h: 56,
    height: 56,
  borderRadius: 28,
    justifyContent: 'center',
  alignItems: 'center',
    shadowColor: theme.colors.shadow,
  shadowOffset: {
      width: 0,
  height: 1 }
      shadowOpacity: 0.1,
    shadowRadius: 2,
  elevation: 2,
    backgroundColor: theme.colors.background
  }
    dislikeButton: { borderWidt, h: 1,
    borderColor: theme.colors.error },
  activeDislikeButton: { backgroundColo, r: theme.colors.error }
    saveButton: { borderWidt, h: 1,
    borderColor: theme.colors.warning },
  activeSaveButton: { backgroundColo, r: theme.colors.warning }
    superLikeButton: { borderWidt, h: 1,
    borderColor: theme.colors.info },
  activeSuperLikeButton: { backgroundColo, r: theme.colors.info }
    likeButton: { borderWidt, h: 1,
    borderColor: theme.colors.success },
  activeLikeButton: { backgroundColo, r: theme.colors.success }
    boostedBadge: {
      position: 'absolute',
  top: 12,
    right: 12,
  backgroundColor: theme.colors.warning,
    borderRadius: 16,
  paddingHorizontal: 8,
    paddingVertical: 4,
  flexDirection: 'row',
    alignItems: 'center' }
    boostedText: {
      fontSize: 12),
  fontWeight: '600'),
    color: theme.colors.background,
  marginLeft: 4)
  }
  })