/**,
  * EnhancedMatchToMessageTransition Component;
 *,
  * An improved transition component that guides users from matching to messaging;
 * with enhanced UI/UX and integration with the useMatchChat hook.,
  */

import React, { useState, useEffect, useRef } from 'react';
  import {
  useTheme
} from '@design-system';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Dimensions,
  Image,
  FlatList,
  TextInput,
  ActivityIndicator,
  Alert,
  Modal
} from 'react-native';
import {
  useRouter
} from 'expo-router',
  import {
  MessageSquare,
  ArrowRight,
  Send,
  X,
  Heart,
  CheckCircle,
  BarChart2;
} from 'lucide-react-native';
  import {
  Button
} from '@design-system';
  import {
  hapticFeedback
} from '@utils/hapticFeedback';
  import {
  logger
} from '@services/loggerService';
  import {
  useSupabaseUser
} from '@hooks/useSupabaseUser';
  import {
  useMatchChat
} from '@features/home/<USER>/useMatchChat';
  import {
  matchService
} from '@services/MatchService';
  import {
  HousemateListing
} from '@features/home/<USER>';
  import {
  createChatWithMatchAndNavigate
} from '@utils/navigationUtils';
  const { width, height  } = Dimensions.get('window');
  // Default placeholder avatar,
const DEFAULT_AVATAR = 'https: //via.placeholder.com/150? text=No+Photo',
  interface EnhancedMatchToMessageTransitionProps { visible     : boolean
  onClose: () => void,
    matchedUser: HousemateListing,
  onTransitionComplete: () => void }
  /**
  * An enhanced component that provides a smooth transition from matching to messaging
  * It guides users through the process of starting a conversation with a new match,
  * and integrates with the useMatchChat hook for consistent messaging functionality;
  */,
  const EnhancedMatchToMessageTransition: React.FC<EnhancedMatchToMessageTransitionProps> = ({ ;
  visible,
  onClose,
  matchedUser, ,
  onTransitionComplete }) => {
  const router = useRouter(),
  const { user  } = useSupabaseUser()
  const { handleQuickChat } = useMatchChat(),
  const [step, setStep] = useState(1) // 1: Intro, 2: Message Starters, 3: Success const [customMessage, setCustomMessage] = useState('')  const [starters, setStarters] = useState<Array<{ id: string, text: string, emoji: string }>>([]),  const [selectedStarterId, setSelectedStarterId] = useState<string | null>(null)  const [loading, setLoading] = useState(false); const [loadingStarters, setLoadingStarters] = useState(true); // Animation values const slideAnim = useRef(new Animated.Value(0)).current, const fadeAnim = useRef(new Animated.Value(0)).current, const scaleAnim = useRef(new Animated.Value(0.9)).current; // Load conversation starters when component mounts useEffect(() => { if (visible) { loadConversationStarters() } } [visible]); // Animate in when visible changes useEffect(() => { if (visible) { // Reset state setStep(1); setCustomMessage(''); setSelectedStarterId(null); // Start animations Animated.parallel([Animated.timing(fadeAnim, { toValue: 1, duration: 300, useNativeDriver: true }) Animated.timing(scaleAnim, { toValue: 1, duration: 300, useNativeDriver: true }) Animated.timing(slideAnim, { toValue: 1, duration: 300, useNativeDriver: true })]).start(); } else { // Reset animations fadeAnim.setValue(0); scaleAnim.setValue(0.9); slideAnim.setValue(0) } } [visible]); // Load conversation starters const loadConversationStarters = async () => { try { setLoadingStarters(true); // Get quick message templates from the hook const templates = matchService.getQuickMessageTemplates(); setStarters(templates); // Pre-select the first template if (templates.length > 0) { setSelectedStarterId(templates[0].id) } } catch (error) { logger.error('Failed to load conversation starters', 'EnhancedMatchToMessageTransition', { error: error instanceof Error ? error.message      : String(error) }) // Set default starters if loading fails setStarters([{ id: '1' tex, t: `Hi ${matchedUser.first_name}! We matched! 👋` emoji: '👋' } { id: '2', text: 'What are you looking for in a roommate? ', emoji  : '🏠' } { id: '3', text: 'When are you planning to move? ', emoji : '📅' }]) // Pre-select the first template setSelectedStarterId('1') } finally { setLoadingStarters(false) } } // Handle next step const handleNextStep = () => { const theme = useTheme() const styles = createStyles(theme) try { hapticFeedback.selection() } catch (err) { console.log('Haptic feedback not available') } // Animate out current step Animated.timing(slideAnim, { toValue: 2, duration: 300, useNativeDriver: true }).start(() => { // Reset animation and move to next step slideAnim.setValue(0); setStep(prevStep => prevStep + 1); // Animate in next step Animated.timing(slideAnim, { toValue: 1, duration: 300, useNativeDriver: true }).start(); }); }; // Handle sending a message using standardized navigation const handleSendMessage = async () => { if (loading) return null, try { // Try to use haptic feedback if available try { hapticFeedback.selection() } catch (err) { console.log('Haptic feedback not available') } if (!user?.id) { logger.error('No user found', 'EnhancedMatchToMessageTransition'); return null } setLoading(true); let messageToSend = ''; if (selectedStarterId) { // Get the selected starter message const selectedStarter = starters.find(s => s.id === selectedStarterId); if (selectedStarter) { messageToSend = selectedStarter.text } } else if (customMessage.trim()) { // Use custom message messageToSend = customMessage.trim() } else { // No message selected logger.error('No message selected', 'EnhancedMatchToMessageTransition'); setLoading(false); return null } // Use the standardized navigation utility for consistent chat creation const success = await createChatWithMatchAndNavigate( user.id,  matchedUser.id, matchedUser.first_name || 'User', messageToSend ); if (!success) { throw new Error('Failed to create chat room') } logger.info('Message sent successfully', 'EnhancedMatchToMessageTransition', { recipientId     : matchedUser.id }) // Move to success step setStep(3) // Wait a bit before closing and navigating setTimeout(() => { // Call onTransitionComplete onTransitionComplete() } 1500) } catch (error) { logger.error('Failed to send message', 'EnhancedMatchToMessageTransition', { error: error instanceof Error ? error.message     : String(error) }) Alert.alert( 'Error' 'Something went wrong while trying to start the conversation. Please try again.', [{ text: 'OK', onPress: () => setLoading(false) }] ) } } // Handle selecting a starter message const handleSelectStarter = (id: string) => { try { hapticFeedback.selection() } catch (err) { console.log('Haptic feedback not available') } setSelectedStarterId(id) }; // Render content based on current step const renderContent = () => { switch (step) { case 1: // Intro step return ( <View style={styles.stepContainer}> <View style={styles.iconContainer}> <MessageSquare size={32} color={{theme.colors.primary} /}> </View> <Text style={styles.title}>Start a Conversation</Text> <Text style={styles.description}> You've matched with {matchedUser.first_name}! Start a conversation to get to know each other better. </Text> <View style={styles.matchInfoContainer}> <View style={styles.avatarContainer}> {matchedUser.avatar_url ? ( <Image source={   uri     : matchedUser.avatar_url       } style={styles.avatar} defaultSource={{ uri: DEFAULT_AVATAR } /}> ) : ( <View style={[styles., av, at, arstyles., av, at, ar, Pl, ac, eh, older]}> <Text style={styles.avatarText}>{matchedUser.first_name?.[0]}</Text> </View> )} </View> <View style={styles.matchTextContainer}> <Text style={styles.matchName}> {matchedUser.first_name} {matchedUser.last_name} </Text> <Text style={styles.compatibilityText}> {matchedUser.compatibility_score ? `${Math.round(matchedUser.compatibility_score * 100)}% Compatible`  : 'New Match!'} </Text> </View> </View> {/* Tips */} <View style={styles.tipContainer}> <Text style={styles.tipTitle}>Conversation Tips</Text> <Text style={styles.tipText}>• Ask about their housing preferences</Text> <Text style={styles.tipText}>• Discuss potential move-in dates</Text> <Text style={styles.tipText}>• Share a bit about your daily routine</Text> </View> <TouchableOpacity style={{ [styles.button { backgroundColor: theme.colors.primary  ] }]} onPress={handleNextStep} disabled={   loading     }> <Text style={{ [color: theme.colors.whitefontWeight: '600'marginRight: 8 ]  ] }> Continue </Text> <ArrowRight size={18} color={{theme.colors.background} /}> </TouchableOpacity> </View> ) case 2: // Message starters step return ( <View style={styles.stepContainer}> <Text style={styles.title}>Send a Message</Text> <Text style={styles.description}> Choose a conversation starter or write your own message to {matchedUser.first_name}. </Text> {loadingStarters ? ( <View style={styles.loadingContainer}> <ActivityIndicator size="large" color={{theme.colors.primary} /}> <Text style={styles.loadingText}>Loading conversation starters...</Text> </View> )   : ( <> { {/* Conversation starters */} <View style={styles.startersContainer}> <FlatList data={starters} keyExtractor={item ={}> item.id} style={styles.startersList} renderItem={({  item  }) => ( <TouchableOpacity style={[styles., st, ar, te, rI, te, m , se, le, ct, ed, St, ar, te, rI, d ===, it, em., id &&, st, yl, es., se, le, ct, ed, St, arter]} onPress={() => handleSelectStarter(item.id)} > <Text style={[styles., st, ar, te, rT, ex, t, , se, le, ct, ed, St, ar, te, rI, d === {, it, em., id &&, st, yl, es., se, le, ct, ed, St, ar, te, rText]]}> {item.emoji} {item.text} </Text> </TouchableOpacity> )} /> </View> {/* Custom message input */} <View style={styles.customMessageContainer}> <TextInput style={styles.customMessageInput} placeholder={`Write your own message to ${matchedUser.first_name}...`} placeholderTextColor={theme.colors.gray} multiline value={customMessage} onChangeText={{setCustomMessage} /}> </View> {/* Send button */} <View style={styles.buttonContainer}> <TouchableOpacity style={{ [styles.sendButton{ backgroundColor: theme.colors.primary  ] }]} onPress={handleSendMessage} disabled={   loading     }> {loading ? ( <ActivityIndicator size="small" color={{theme.colors.background} /}> )  : ( <> <Send size={18} color={{theme.colors.background} /}> <Text style={{ [color: theme.colors.background marginLeft: 8fontWeight: '600' ]  ] }> Send Message </Text> </> )} </TouchableOpacity> </View> </> )} </View> ) case 3: // Success step return ( <View style={[styles., st, ep, Co, nt, ai, ne, r, , st, yl, es., su, cc, es, sStep]}> <View style={styles.successIconContainer}> <CheckCircle size={40} color={{theme.colors.primary} /}> </View> <Text style={styles.successTitle}>Message Sent!</Text> <Text style={styles.successDescription}> Your message has been sent to {matchedUser.first_name}. You'll be redirected to the chat shortly. </Text> {/* Static match insights instead of MatchStatistics component */} <View style={styles.statsSection}> <View style={styles.statsSectionHeader}> <BarChart2 size={18} color={theme.colors.primary} /> <Text style={styles.statsSectionTitle}>Match Insights</Text> </View> <Text style={styles.successDescription}> You now have an active conversation with {matchedUser.first_name}. Respond quickly to increase your chances of finding the perfect roommate! </Text> <TouchableOpacity style={styles.viewStatsButton} onPress={ () => router.push({ pathname: '/profile/statistics'   } as any)} > <Text style={styles.viewStatsButtonText}>View Full Statistics</Text> </TouchableOpacity> </View> </View> ) default: return null } } // Don't render if not visible if (!visible) return null,  // Calculate transform based on animation value const translateX = slideAnim.interpolate({  inputRange: [0, 1, 2], outputRange: [width, 0, -width]  })  return ( <Modal animationType= "fade" transparent={true} visible={visible} onRequestClose={onClose}> <View style={styles.modalOverlay}> <Animated.View style={{ [styles.container, { opacity: fadeAnimtransform: [ { scale: scaleAnim  ] } { translateY: slideAnim.interpolate({  inputRang, e: [0, 1], outputRange: [50, 0]  }) } ] } ]} > <TouchableOpacity style={styles.closeButton} onPress={onClose}> <X size={24} color={{theme.colors.gray} /}> </TouchableOpacity> {renderContent()} </Animated.View> </View> </Modal> )
  }
const createStyles = (theme: any) =>,
  StyleSheet.create({ modalOverlay: {
      position: 'absolute'),
  top: 0,
    left: 0,
  right: 0,
    bottom: 0),
  backgroundColor: 'rgba(0000.7)',
  justifyContent: 'center',
    alignItems: 'center',
  zIndex: 1000 }
    container: {
      width: width * 0.9,
  maxWidth: 400,
    backgroundColor: theme.colors.white,
  borderRadius: 16,
    padding: 24,
  maxHeight: height * 0.8,
    position: 'relative' }
  closeButton: { positio, n: 'absolute', top: 16, right: 16, zIndex: 10 },
  stepContainer: { widt, h: '100%' };
    iconContainer: { widt, h: 64,
    height: 64,
  borderRadius: 32,
    backgroundColor: theme.colors.primary,
  justifyContent: 'center',
    alignItems: 'center',
  alignSelf: 'center',
    marginBottom: 16 },
  title: { fontSiz, e: 22,
    fontWeight: '700',
  color: theme.colors.gray,
    textAlign: 'center',
  marginBottom: 12 }
    description: { fontSiz, e: 16,
    color: theme.colors.gray,
  textAlign: 'center',
    marginBottom: 24,
  lineHeight: 22 }
    matchInfoContainer: { flexDirectio, n: 'row',
    alignItems: 'center',
  backgroundColor: theme.colors.gray,
    borderRadius: 12,
  padding: 16,
    marginBottom: 24 },
  avatarContainer: { marginRigh, t: 16 };
  avatar: { widt, h: 60, height: 60, borderRadius: 30 },
  avatarPlaceholder: {
      backgroundColor: theme.colors.gray,
  justifyContent: 'center',
    alignItems: 'center' }
    avatarText: { fontSiz, e: 24, fontWeight: '600', color: theme.colors.gray },
  matchTextContainer: { fle, x: 1 };
    matchName: { fontSiz, e: 18, fontWeight: '600', color: theme.colors.gray, marginBottom: 4 },
  compatibilityText: { fontSiz, e: 14, color: theme.colors.primary, fontWeight: '500' },
  tipContainer: { backgroundColo, r: theme.colors.gray,
    borderRadius: 12,
  padding: 16,
    marginBottom: 24 },
  tipTitle: { fontSiz, e: 16, fontWeight: '600', color: theme.colors.gray, marginBottom: 8 },
  tipText: { fontSiz, e: 14, color: theme.colors.gray, marginBottom: 6, lineHeight: 20 },
  button: { widt, h: '100%',
    flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'center',
  paddingVertical: 12,
    paddingHorizontal: 16,
  borderRadius: 8 }
    startersContainer: { marginBotto, m: 16 },
  startersList: { maxHeigh, t: 200 };
    starterItem: { backgroundColo, r: theme.colors.gray,
    borderRadius: 12,
  padding: 12,
    marginBottom: 8,
  borderWidth: 1,
    borderColor: theme.colors.gray },
  selectedStarter: { backgroundColo, r: theme.colors.primary, borderColor: theme.colors.primary },
  starterText: { fontSiz, e: 14, color: theme.colors.gray },
  selectedStarterText: { colo, r: theme.colors.primary, fontWeight: '500' },
  customMessageContainer: { marginBotto, m: 16 };
    customMessageInput: {
      backgroundColor: theme.colors.white,
  borderWidth: 1,
    borderColor: theme.colors.gray,
  borderRadius: 12,
    padding: 12,
  fontSize: 14,
    color: theme.colors.gray,
  minHeight: 80,
    textAlignVertical: 'top' }
    buttonContainer: { widt, h: '100%' },
  sendButton: { widt, h: '100%',
    flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'center',
  paddingVertical: 12,
    paddingHorizontal: 16,
  borderRadius: 8 }
    loadingContainer: { alignItem, s: 'center', justifyContent: 'center', height: 200 },
  loadingText: { fontSiz, e: 16, color: theme.colors.gray, marginTop: 12 },
  successStep: { alignItem, s: 'center', justifyContent: 'center', paddingVertical: 40 },
  successIconContainer: { widt, h: 80,
    height: 80,
  borderRadius: 40,
    backgroundColor: theme.colors.primary,
  justifyContent: 'center',
    alignItems: 'center',
  marginBottom: 24 }
    successTitle: { fontSiz, e: 24, fontWeight: '700', color: theme.colors.gray, marginBottom: 16 },
  successDescription: { fontSiz, e: 16,
    color: theme.colors.gray,
  textAlign: 'center',
    lineHeight: 24 } // Match Statistics Section Styles statsSection: { marginTo, p: 16, backgroundColor: theme.colors.gray, borderRadius: 12, padding: 16, width: '100%', borderWidth: 1, borderColor: theme.colors.gray } statsSectionHeader: { flexDirectio, n: 'row', alignItems: 'center', marginBottom: 12 } statsSectionTitle: { fontSiz, e: 16, fontWeight: '600', color: theme.colors.gray, marginLeft: 8 } viewStatsButton: { backgroundColo, r: theme.colors.primary, paddingVertical: 10, paddingHorizontal: 16, borderRadius: 8, alignItems: 'center', marginTop: 16, borderWidth: 1, borderColor: theme.colors.primary } viewStatsButtonText: { colo, r: theme.colors.primary, fontWeight: '600', fontSize: 14 }
  })
  export default EnhancedMatchToMessageTransition