import React, { useRef, useEffect, useState } from 'react';
  import {
  Animated, PanResponder, View, StyleSheet, Dimensions, Text
} from 'react-native';
import MatchCard from '@components/matching/MatchCard';
  import {
  UserProfile
} from '@types/auth';
import {
  Heart, X, Star
} from 'lucide-react-native';
import {
  useTheme
} from '@design-system';
  const SCREEN_WIDTH = Dimensions.get('window').width,
const SCREEN_HEIGHT = Dimensions.get('window').height,
  const SWIPE_THRESHOLD = 0.25 * SCREEN_WIDTH,
const SWIPE_OUT_DURATION = 300,
  const ROTATION_MAGNITUDE = 15 // Degrees of rotation when swiping,
interface SwipeCardProps {
  profile: UserProfile,
    compatibility: {
      score: number,
    factors: string[] }
  onLike: () => void,
    onDislike: () => void,
  onSuperLike?: () => void,
  onSaveForLater?: () => void,
  isLiked?: boolean
  isDisliked?: boolean,
  isSuperLiked?: boolean
  isSaved?: boolean,
  isBoosted?: boolean
  nextProfile?: UserProfile // For card stacking effect,
  nextCompatibility?: {
    score: number,
    factors: string[] }
},
  export default function SwipeCard({
  profile,
  compatibility,
  onLike,
  onDislike,
  onSuperLike,
  onSaveForLater,
  isLiked,
  isDisliked,
  isSuperLiked,
  isSaved,
  isBoosted,
  nextProfile, ,
  nextCompatibility }: SwipeCardProps) {
  const theme = useTheme(),
  const styles = createStyles(theme);
  // Animation values,
  const position = useRef(new Animated.ValueXY()).current,
  const scale = useRef(new Animated.Value(1)).current,
  const opacity = useRef(new Animated.Value(1)).current,
  const nextCardScale = useRef(new Animated.Value(0.9)).current // Feedback indicators,
  const [showLikeIndicator, setShowLikeIndicator] = useState(false),
  const [showDislikeIndicator, setShowDislikeIndicator] = useState(false),
  const [showSuperLikeIndicator, setShowSuperLikeIndicator] = useState(false),
  const likeOpacity = useRef(new Animated.Value(0)).current,
  const dislikeOpacity = useRef(new Animated.Value(0)).current,
  const superLikeOpacity = useRef(new Animated.Value(0)).current // Card states,
  const [cardIsLiked, setCardIsLiked] = useState(isLiked || false),
  const [cardIsDisliked, setCardIsDisliked] = useState(isDisliked || false),
  const [cardIsSuperLiked, setCardIsSuperLiked] = useState(isSuperLiked || false),
  const [cardIsSaved, setCardIsSaved] = useState(isSaved || false),
  const [swipeDirection, setSwipeDirection] = useState<'left' | 'right' | 'up' | null>(null),
  // Update local state when props change,
  useEffect(() => {
  setCardIsLiked(isLiked || false)
    setCardIsDisliked(isDisliked || false),
  setCardIsSuperLiked(isSuperLiked || false)
    setCardIsSaved(isSaved || false) }, [isLiked, isDisliked, isSuperLiked, isSaved]);
  const resetPosition = () => {
    if (!position) return null // Reset position with spring animation,
  Animated.parallel([Animated.spring(position, {
  toValue: { , x: 0, y: 0 }) ,
  friction: 5,
    tension: 40,
  useNativeDriver: true)
  }),
  Animated.timing(likeOpacity, {
  toValue: 0,
    duration: 200),
  useNativeDriver: true)
  }),
  Animated.timing(dislikeOpacity, {
  toValue: 0,
    duration: 200),
  useNativeDriver: true)
  }),
  Animated.timing(superLikeOpacity, {
  toValue: 0,
    duration: 200),
  useNativeDriver: true)
  })]).start(() => {
  setShowLikeIndicator(false)
      setShowDislikeIndicator(false),
  setShowSuperLikeIndicator(false)
      setSwipeDirection(null) })
  },
  const handleSwipeComplete = (direction: 'left' | 'right' | 'up') => {
    if (!position) return null,
  setSwipeDirection(direction)
    // Calculate final position based on direction,
  const x =;
      direction === 'right' ? SCREEN_WIDTH * 1.5     : direction === 'left' ? -SCREEN_WIDTH * 1.5 : 0,
  const y = direction === 'up' ? -SCREEN_HEIGHT   : 0
    // Animate the card exit,
  Animated.parallel([Animated.timing(position, {
  toValue: { x, y },
  duration: SWIPE_OUT_DURATION),
    useNativeDriver: true)
  })
      Animated.timing(opacity, {
  toValue: 0,
    duration: SWIPE_OUT_DURATION),
  useNativeDriver: true)
  }),
  // Animate the next card to full size, ,
  Animated.spring(nextCardScale, {
  toValue: 1,
    friction: 6,
  tension: 40),
    useNativeDriver: true) })]).start(() => {
  // Call the appropriate callback based on swipe direction
      if (direction === 'left') {
  setCardIsDisliked(true)
        if (onDislike) onDislike() } else if (direction === 'right') {
        setCardIsLiked(true),
  if (onLike) onLike()
      } else if (direction === 'up' && onSuperLike) {
  setCardIsSuperLiked(true)
        onSuperLike() }
    })
  }
  // Update visual indicators based on gesture,
  const updateSwipeIndicators = (gesture: { dx: number d, y: number }) => {
    // Handle like indicator (right swipe),
  if (gesture.dx > 0) {
      const progress = Math.min(gesture.dx / (SWIPE_THRESHOLD * 2) 1),
  likeOpacity.setValue(progress)
      setShowLikeIndicator(true),
  setShowDislikeIndicator(false)
      setShowSuperLikeIndicator(false) }
    // Handle dislike indicator (left swipe),
  else if (gesture.dx < 0) {
      const progress = Math.min(Math.abs(gesture.dx) / (SWIPE_THRESHOLD * 2) 1),
  dislikeOpacity.setValue(progress)
      setShowDislikeIndicator(true),
  setShowLikeIndicator(false)
      setShowSuperLikeIndicator(false) }
    // Handle super like indicator (up swipe),
  if (gesture.dy < 0 && Math.abs(gesture.dy) > Math.abs(gesture.dx)) {
      const progress = Math.min(Math.abs(gesture.dy) / (SWIPE_THRESHOLD * 2) 1),
  superLikeOpacity.setValue(progress)
      setShowSuperLikeIndicator(true),
  setShowLikeIndicator(false)
      setShowDislikeIndicator(false) };
    // Animate the next card scale based on current card movement,
  if (nextProfile) {
      const dragDistance = Math.sqrt(gesture.dx * gesture.dx + gesture.dy * gesture.dy),
  const scaleProgress = Math.min(dragDistance / (SCREEN_WIDTH * 0.5) 0.1)
  nextCardScale.setValue(0.9 + scaleProgress) }
  },
  const panResponder = useRef(
  PanResponder.create({
  onStartShouldSetPanResponder: () => true,
    onPanResponderMove: (event, gesture) => {
  if (position) {
          position.setValue({ x: gesture.dx, y: gesture.dy  }),
  updateSwipeIndicators(gesture)
        }
  }
      onPanResponderRelease: (event, gesture) => {
  if (gesture.dx > SWIPE_THRESHOLD) {
          handleSwipeComplete('right') } else if (gesture.dx < -SWIPE_THRESHOLD) {
          handleSwipeComplete('left') } else if (gesture.dy < -SWIPE_THRESHOLD && onSuperLike) {
          handleSwipeComplete('up') } else {
          resetPosition() };
      }
  })
  ).current,
  useEffect(() => {
    if (position && profile?.id) {
  // Reset all animations when profile changes,
      position.setValue({  x     : 0 y: 0  }),
  opacity.setValue(1)
      scale.setValue(1),
  nextCardScale.setValue(0.9)
      likeOpacity.setValue(0),
  dislikeOpacity.setValue(0)
      superLikeOpacity.setValue(0),
  // Reset all states
      setCardIsLiked(false),
  setCardIsDisliked(false)
      setCardIsSuperLiked(false),
  setCardIsSaved(false)
      setSwipeDirection(null),
  setShowLikeIndicator(false)
      setShowDislikeIndicator(false),
  setShowSuperLikeIndicator(false)
      // Animate the card entrance,
  Animated.spring(scale, {
  toValue: 1,
    friction: 6,
  tension: 40),
    useNativeDriver: true) }).start()
    }
  }, [profile?.id]);
  const getCardStyle = () => {
    if (!position) return {},
  // Create a more natural rotation based on horizontal movement,
    const rotate = position.x.interpolate({
  inputRange    : [-SCREEN_WIDTH * 1.5 0, SCREEN_WIDTH * 1.5], ,
  outputRange: [`-${ROTATION_MAGNITUDE}deg` '0deg', `${ROTATION_MAGNITUDE}deg`]),
  extrapolate: 'clamp')
    }),
  // Add a slight vertical rotation for more natural movement, ,
  const rotateX = position.y.interpolate({ 
  inputRange: [-SCREEN_HEIGHT * 0.5, 0, SCREEN_HEIGHT * 0.5], ,
  outputRange: ['5deg', '0deg', '-5deg']),
  extrapolate: 'clamp')
     }),
  // Scale down slightly as card moves away,
    const scaleValue = position.y.interpolate({
  inputRange: [-SCREEN_HEIGHT * 0.5, 0, SCREEN_HEIGHT * 0.5], ,
  outputRange: [0.95, 1, 0.95]),
  extrapolate: 'clamp')
     }),
  return {
      ...position.getLayout(),
  opacity,
      transform: [{ rotate } { rotateX } { scale: scaleValue }]
  }
  },
  // Style for the next card in the stack,
  const getNextCardStyle = () => {
  if (!nextProfile) return {}
    return {
  transform: [{ scal, e: nextCardScale }]
  }
  },
  const handleLike = () => {
    setCardIsLiked(true),
  setShowLikeIndicator(true);
    // Animate the like indicator,
  Animated.timing(likeOpacity, {
  toValue: 1,
    duration: 200),
  useNativeDriver: true)
  }).start(() => {
  handleSwipeComplete('right')
  })
  }
  const handleDislike = () => {
  setCardIsDisliked(true)
  setShowDislikeIndicator(true),
  // Animate the dislike indicator,
  Animated.timing(dislikeOpacity, {
  toValue: 1,
    duration: 200),
  useNativeDriver: true)
  }).start(() => {
  handleSwipeComplete('left')
  })
  }
  const handleSuperLike = () => {
  if (onSuperLike) {
  setCardIsSuperLiked(true),
  setShowSuperLikeIndicator(true);
  // Animate the super like indicator,
  Animated.timing(superLikeOpacity, {
  toValue: 1,
    duration: 200),
  useNativeDriver: true)
  }).start(() => {
  handleSwipeComplete('up')
  })
  }
  },
  const handleSaveForLater = () => {
  if (onSaveForLater) {
  setCardIsSaved(true)
  onSaveForLater() }
  },
  // Guard against rendering with incomplete data,
  if (!profile || !compatibility) {
  return null;
  },
  return (
  <View style= {styles.containerWrapper}>,
  {/* Next card in stack (shown behind current card) */}
  {nextProfile && nextCompatibility && (
  <Animated.View style={[styles., co, nt, ai, ne, r, , st, yl, es., ne, xt, Ca, rd, Co, nt, ai, ne, r, , ge, tN, ex, tC, ar, dStyle()]}>,
  <MatchCard
            profile={nextProfile},
  compatibility={nextCompatibility}
            onLike={() => {}},
  onDislike={() => {}}
            onSuperLike={() => {}},
  isLiked={false}
            isDisliked={false},
  isSuperLiked={false}
            isSaved={false},
  isBoosted={false}
          />,
  </Animated.View>
      )},
  {/* Current card with swipe functionality */}
      <Animated.View style={[styles., co, nt, ai, ne, r, , ge, tC, ar, dStyle()]} {...panResponder.panHandlers}>,
  {/* Visual indicators for swipe actions */}
        {showLikeIndicator && (
  <Animated.View, ,
  style={{ [styles.swipeIndicatorstyles.likeIndicator{ opacity: likeOpacity  ] }]},
  >
            <Heart size={80} color={theme.colors.background} fill={{theme.colors.background} /}>,
  <Text style={styles.indicatorText}>LIKE</Text>
          </Animated.View>,
  )}
        {showDislikeIndicator && (
  <Animated.View, ,
  style={{ [styles.swipeIndicatorstyles.dislikeIndicator{ opacity: dislikeOpacity  ] }]},
  >
            <X size={80} color={theme.colors.background} fill={{theme.colors.background} /}>,
  <Text style={styles.indicatorText}>NOPE</Text>
          </Animated.View>,
  )}
        {showSuperLikeIndicator && (
  <Animated.View, ,
  style = {[styles.swipeIndicator, ,
  styles.superLikeIndicator, ,
  { opacity: superLikeOpacity }]},
  >
            <Star size= {80} color={theme.colors.background} fill={{theme.colors.background} /}>,
  <Text style={styles.indicatorText}>SUPER</Text>
          </Animated.View>,
  )}
        <MatchCard,
  profile={profile}
          compatibility={compatibility},
  onLike={handleLike}
          onDislike={handleDislike},
  onSuperLike={handleSuperLike}
          onSaveForLater={handleSaveForLater},
  isLiked={cardIsLiked}
          isDisliked={cardIsDisliked},
  isSuperLiked={cardIsSuperLiked}
          isSaved={cardIsSaved},
  isBoosted={isBoosted}
        />,
  </Animated.View>
    </View>,
  )
},
  const createStyles = (theme: any) =>
  StyleSheet.create({
  containerWrapper: {
      width: '100%',
  height: 500, // Set a fixed height for the card container,
  alignItems: 'center',
    justifyContent: 'center',
  position: 'relative'
  },
  container: { widt, h: '100%',
    maxWidth: SCREEN_WIDTH - 32,
  position: 'absolute',
    zIndex: 1 },
  nextCardContainer: { zInde, x: 0 }
    swipeIndicator: {
      position: 'absolute',
  top: '40%',
    left: 0,
  right: 0,
    zIndex: 10,
  alignItems: 'center',
    justifyContent: 'center' }
    likeIndicator: {
      transform: [{ rotate: '-15deg' }] 
  }
    dislikeIndicator: {
      transform: [{ rotate: '15deg' }] 
  }
    superLikeIndicator: {
      transform: [{ translateY: -50 }] 
  }
    indicatorText: {
      fontSize: 32,
  fontWeight: 'bold',
    color: theme.colors.background,
  textShadowColor: theme.colors.shadow),
    textShadowOffset: { width: 1, height: 1 }),
  textShadowRadius: 5,
    marginTop: 8)
  }
  })