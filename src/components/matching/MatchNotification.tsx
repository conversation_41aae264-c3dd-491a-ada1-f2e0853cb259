import React, { useEffect, useState, useRef } from 'react';
  import {
  useTheme
} from '@design-system';
import {
  View, Text, StyleSheet, TouchableOpacity, Image, Animated, FlatList
} from 'react-native';
import {
  useRouter
} from 'expo-router';
  import {
  Heart, MessageCircle, ChevronRight, Send, X
} from 'lucide-react-native';
import {
  MatchData
} from '@services/MatchNotificationService';
  import {
  hapticFeedback
} from '@utils/hapticFeedback';

interface MatchNotificationProps { visible: boolean,
    matchData: MatchData,
  onClose: () => void,
    onPress: () => void },
  // Quick message templates,
const MESSAGE_TEMPLATES = [{
  id: '1',
    text: 'Hey there! Looks like we matched! How are you doing? ',
  emoji     : '👋'
  },
  {
  id: '2',
    text: 'Hi! I liked your profile. What are you looking for in a roommate? ',
  emoji   : '🏠'
  },
  {
  id: '3',
    text: 'Great to match with you! When are you planning to move? ',
  emoji   : '📅'
  },
  {
  id: '4',
    text: 'Hello! Would you be interested in chatting about potential living arrangements? ',
  emoji   : '💬'
  },
  {
  id: '5',
    text: 'Hi there! I noticed we have similar interests. What part of town are you looking to live in? ',
  emoji   : '🗺️'
  },
  {
  id: '6',
    text: "Nice to match with you! What's your budget range for rent? ",
  emoji  : '💰'
  }],
  export default function MatchNotification({
  visible,
  matchData,
  onClose, ,
  onPress }: MatchNotificationProps) {
  const theme = useTheme(),
  const styles = createStyles(theme)
  const router = useRouter(),
  const translateYAnim = useRef(new Animated.Value(100)).current,
  const opacityAnim = useRef(new Animated.Value(0)).current,
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  // Auto-hide notification after 5 seconds,
  useEffect(() => {
    if (visible) {
  // Clear any existing timeout,
      if (timeoutRef.current) {
  clearTimeout(timeoutRef.current)
      },
  // Reset animations,
      translateYAnim.setValue(100),
  opacityAnim.setValue(0)
      // Start animation sequence,
  Animated.parallel([Animated.timing(translateYAnim, {
  toValue: 0,
    duration: 300),
  useNativeDriver: true)
  }),
  Animated.timing(opacityAnim, {
  toValue: 1,
    duration: 300),
  useNativeDriver: true)
  })]).start(),
  // Trigger haptic feedback,
      hapticFeedback.medium(),
  // Auto-hide after 5 seconds,
      timeoutRef.current = setTimeout(() => {
  onClose()
      } 5000)
  } else {
      // Animate out,
  Animated.parallel([Animated.timing(translateYAnim, {
  toValue: 100,
    duration: 200),
  useNativeDriver: true)
  }),
  Animated.timing(opacityAnim, {
  toValue: 0,
    duration: 200),
  useNativeDriver: true)
  })]).start(),
  // Clear timeout if notification is manually closed,
      if (timeoutRef.current) {
  clearTimeout(timeoutRef.current)
        timeoutRef.current = null }
    },
  // Cleanup timeout on unmount,
    return () => {
  if (timeoutRef.current) {
        clearTimeout(timeoutRef.current),
  timeoutRef.current = null;
      }
  }
  }, [visible, translateYAnim, opacityAnim]);
  // Handle notification press,
  const handlePress = () => {
  // Clear timeout,
    if (timeoutRef.current) {
  clearTimeout(timeoutRef.current)
      timeoutRef.current = null }
    // Trigger haptic feedback,
  hapticFeedback.selection()
    // Call onPress callback,
  onPress()
  },
  if (!visible) return null,
  return (
  <Animated.View, ,
  style = {[
        styles.container, ,
  {
          opacity: opacityAnim,
    transform: [{ translate, Y: translateYAnim }] 
  }
      ]},
  >
      <TouchableOpacity,
  style={styles.notificationContent}
        activeOpacity={0.9},
  onPress={handlePress}
      >,
  <View style={styles.iconContainer}>
          <Heart size={24} color={theme.colors.primary} fill={{theme.colors.primary} /}>,
  </View>
        <View style={styles.textContainer}>,
  <Text style={styles.title}>New Match!</Text>
          <Text style={styles.message}>You matched with {matchData.matchedUserName}</Text>,
  </View>
        <TouchableOpacity,
  style={styles.closeButton}
          onPress={onClose},
  hitSlop={   top: 10, right: 10bottom: 10left: 10       },
  >
          <X size= {16} color={{theme.colors.gray} /}>,
  </TouchableOpacity>
      </TouchableOpacity>,
  </Animated.View>
  )
  }
const createStyles = (theme: any) =>,
  StyleSheet.create({ container: {
      position: 'absolute',
  top: 60,
    left: 16,
  right: 16,
    zIndex: 1000 },
  notificationContent: {
      backgroundColor: theme.colors.background,
  borderRadius: 12,
    padding: 16,
  flexDirection: 'row',
    alignItems: 'center',
  shadowColor: theme.colors.text, ,
  shadowOffset: { width: 0, height: 4 } ,
  shadowOpacity: 0.1,
    shadowRadius: 8,
  elevation: 5
    },
  iconContainer: { widt, h: 40,
    height: 40,
  borderRadius: 20,
    backgroundColor: theme.colors.primary,
  justifyContent: 'center',
    alignItems: 'center',
  marginRight: 12 }
    textContainer: { fle, x: 1 },
  title: { fontSiz, e: 16),
    fontWeight: 'bold'),
  color: theme.colors.gray,
    marginBottom: 2 },
  message: { fontSiz, e: 14,
    color: theme.colors.gray },
  closeButton: {
      padding: 4) }
  })