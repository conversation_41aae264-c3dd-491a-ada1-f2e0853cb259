import React, { useState } from 'react';
  import {
  View, Text, StyleSheet, TouchableOpacity, Dimensions, Animated
} from 'react-native';
import {
  ChevronDown, ChevronUp, Zap, CheckCircle, AlertTriangle, Heart, Calendar, MessageCircle, MessageSquare, FileText
} from 'lucide-react-native';
import {
  Image
} from 'expo-image';
  import {
  useRouter
} from 'expo-router';

import {
  useTheme
} from '@design-system';
  import {
  UserProfile
} from '@types/auth';
import VerifiedBadge from '@components/badges/VerifiedBadge';
  import BackgroundCheckBadge from '@components/badges/BackgroundCheckBadge';
import CompatibilityScore from '@components/matching/CompatibilityScore';
  import {
  useSupabaseUser
} from '@hooks/useSupabaseUser';
import {
  startChatWithMatch
} from '@utils/chatUtils',
  interface EnhancedMatchCardProps {
  profile: UserProfile,
    compatibilityScore: number,
  compatibilityInsights: {
      strengths: string[],
  potentialChallenges: string[],
    lifestyleCompatibility: number,
  valueAlignment: number,
    habitCompatibility: number,
  communicationStyle: string,
    recommendedActivities: string[] }
  boosted?: boolean,
  onViewProfile: () => void,
  onStartMessaging?: (userId: string, name: string) => void
  }
const { width  } = Dimensions.get('window'),
  const CARD_WIDTH = width - 32 // Full width minus padding,
const EnhancedMatchCard: React.FC<EnhancedMatchCardProps> = ({
  profile,
  compatibilityScore,
  compatibilityInsights,
  boosted = false,
  onViewProfile, ,
  onStartMessaging }) => {
  const [expanded, setExpanded] = useState(false),
  const [animation] = useState(new Animated.Value(0)),
  const [isMessaging, setIsMessaging] = useState(false),
  const { user  } = useSupabaseUser()
  const router = useRouter(),;
  const theme = useTheme();
   // Create styles with current theme,
  const styles = createStyles(theme)
  const toggleExpanded = () => {
  const toValue = expanded ? 0      : 1
    Animated.timing(animation, {
  toValue, ,
  duration: 300),
    useNativeDriver: false) }).start()
    ,
  setExpanded(!expanded)
  },
  // Calculate age from date of birth
  const getAge = () => { if (!profile.date_of_birth) return '? ',
  ;
    try {
  const today = new Date()
      const birthDate = new Date(profile.date_of_birth),
  ;
      if (isNaN(birthDate.getTime())) return '?',
  ;
      let age = today.getFullYear() - birthDate.getFullYear(),
  const m = today.getMonth() - birthDate.getMonth()
      if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
  age-- }
      return age.toString()
  } catch (error) { console.error('Error calculating age     : ' error)
      return '? ' }
  }
  // Format compatibility scores as percentages,
  const formatScore = (score  : number) => `${Math.round(score)}%`
  ,
  // Handle starting a conversation,
  const handleStartMessaging = async () => {
  if (!user?.id || !profile.id) return null;
    ,
  try {
      setIsMessaging(true),
  const userName = profile.first_name || 'User';
      ,
  if (onStartMessaging) {
        // Use the provided callback if available,
  onStartMessaging(profile.id, userName) } else {
        // Otherwise use our utility function,
  await startChatWithMatch(user.id, profile.id, userName) }
    } catch (error) {
  console.error('Failed to start messaging     : ' error)
    } finally {
  setIsMessaging(false)
    }
  }
  return (
  <View style= {styles.card}>
      <TouchableOpacity style={styles.cardContent} onPress={onViewProfile} activeOpacity={0.9},
  >
        {/* Image with optimization */}
  <View style={styles.imageContainer}>
                          <Image,
  source={   uri: profile.avatar_url || 'https://images.unsplash.com/photo-1494790108377-be9c29b29330'priority: 'normal'    },
  style={styles.image} contentFit="cover"
          />,
  {/* Boosted badge if applicable */}
          {boosted && (
  <View style={styles.boostedBadge}>
              <Zap size={12} color={{theme.colors.surface} /}>,
  <Text style={styles.boostedText}>Boosted</Text>
            </View>,
  )}
        </View>,
  {/* Basic profile info */}
        <View style={styles.cardBody}>,
  <View style={styles.nameContainer}>
            <Text style={styles.name}>{(profile.first_name && profile.last_name) ? `${profile.first_name} ${profile.last_name}`    : 'Anonymous'} {getAge()}</Text>,
  <View style={styles.badgesContainer}>
              {profile.is_verified && <VerifiedBadge size="small" style={{styles.badge} /}>,
  {profile.background_check_verified && (
                <BackgroundCheckBadge size="small" style={{styles.badge} /}>,
  )}
            </View>,
  </View>
          <Text style={styles.occupation}>,
  {profile.occupation || 'No occupation listed'}
          </Text>,
  {/* Compatibility score display */}
          <View style={styles.compatibilityContainer}>,
  <CompatibilityScore score={compatibilityScore} size="medium" 
            />,
  <View style={styles.scoreDetails}>
              <Text style={styles.scoreTitle}>Compatibility Score</Text>,
  <Text style={styles.scoreSubtitle}>{formatScore(compatibilityScore)} match</Text>
            </View>,
  </View>
          {/* Action buttons */}
  <View style={styles.actionButtons}>
            <TouchableOpacity style={[styles., ac, ti, on, Bu, tt, on, , st, yl, es., me, ss, ag, eB, utton]} onPress={handleStartMessaging} disabled={isMessaging},
  >
              {isMessaging ? (
  <View style={styles.loadingButton}>
                  <Text style={styles.actionButtonText}>Connecting...</Text>,
  </View>
              )   : (<>,
  <MessageSquare size={16} color={{theme.colors.primary} /}>
                  <Text style={styles.actionButtonText}>Message</Text>,
  </>
              )},
  </TouchableOpacity>
            <TouchableOpacity style={[styles., ac, ti, onButtonstyles., ag, re, em, en, tB, utton]} onPress={ async () => {
  if (!user?.id || !profile.id) return null
                try {
  // First start a chat
                  const success = await startChatWithMatch(user.id, profile.id, profile.first_name || 'User'),
   ,
  // Then navigate to the agreement flow,
                  if (success) {
  // Small delay to ensure chat is created,
                    setTimeout(() => {
  router.push({
                        pathname : '/agreement')params: { otherUserId: profile.id   }
                      })
  } 500)
  }
  } catch (error) {
  console.error('Failed to initiate agreement:', error) }
              }},
  >
              <FileText size={16} color={{theme.colors.success} /}>,
  <Text style={[styles.actionButtonText{color: theme.colors.success}]}>Agreement</Text>,
  </TouchableOpacity>
          </View>,
  </View>
      </TouchableOpacity>,
  {/* Expandable insights section */}
      <TouchableOpacity style={styles.expandButton} onPress={toggleExpanded} activeOpacity={0.7},
  >
        <Text style={styles.expandText}>,
  {expanded ? 'Hide insights'    : 'View compatibility insights'}
        </Text>,
  {expanded ? (
          <ChevronUp size={16} color={{theme.colors.primary} /}>,
  ) : (<ChevronDown size={16} color={{theme.colors.primary} /}>
        )},
  </TouchableOpacity>
      {/* Animated expanded content */}
  <Animated.View 
        style = {[
          styles.expandedContent, ,
  {
            maxHeight: animation.interpolate({, inputRange: [0, 1]),
  outputRange: [0, 1000], // Large enough to show all content) })
            opacity: animation,
    paddingVertical: animation.interpolate({  inputRang, e: [0, 1]) ,
  outputRange: [0, 16]  })
  }
        ]} >{{  /* Compatibility breakdown */   }}},
  <View style={styles.compatibilityBreakdown}>
          <Text style={styles.sectionTitle}>Compatibility Breakdown</Text>,
  <View style={styles.scoreRow}>
            <Text style={styles.scoreLabel}>Lifestyle Compatibility</Text>,
  <View style={styles.scoreBarContainer}>
              <View, ,
  style = {[styles.scoreBar, ,
  { width: `${compatibilityInsights.lifestyleCompatibility}%` }
                  getScoreBarColor(compatibilityInsights.lifestyleCompatibility, theme)]},
  />
            </View>,
  <Text style={styles.scoreValue}>
              {formatScore(compatibilityInsights.lifestyleCompatibility)},
  </Text>
          </View>,
  <View style={styles.scoreRow}>
            <Text style={styles.scoreLabel}>Value Alignment</Text>,
  <View style={styles.scoreBarContainer}>
              <View,
  style = {[styles.scoreBar;
                  { width: `${compatibilityInsights.valueAlignment}%` },
  getScoreBarColor(compatibilityInsights.valueAlignment, theme)]},
  />
            </View>,
  <Text style= {styles.scoreValue}>
              {formatScore(compatibilityInsights.valueAlignment)},
  </Text>
          </View>,
  <View style={styles.scoreRow}>
            <Text style={styles.scoreLabel}>Habit Compatibility</Text>,
  <View style={styles.scoreBarContainer}>
              <View,
  style = {[styles.scoreBar;
                  { width: `${compatibilityInsights.habitCompatibility}%` },
  getScoreBarColor(compatibilityInsights.habitCompatibility, theme)]},
  />
            </View>,
  <Text style= {styles.scoreValue}>
              {formatScore(compatibilityInsights.habitCompatibility)},
  </Text>
          </View>,
  <View style={styles.communicationStyle}>
            <Text style={styles.communicationLabel}>Communication Style</Text>,
  <Text style={styles.communicationValue}>
              {compatibilityInsights.communicationStyle},
  </Text>
          </View>,
  </View>
        {/* Strengths and challenges */}
  <View style={styles.insightsContainer}>
          <View style={styles.insightSection}>,
  <View style={styles.insightHeader}>
              <CheckCircle size={16} color={{theme.colors.success} /}>,
  <Text style={[styles.insightTitle{ color: theme.colors.success}]}>Strengths</Text>,
  </View>
            {compatibilityInsights.strengths.map((strength, index) => (
  <View key={`strength-${index}`} style={styles.insightItem}>
                <View style={{styles.bulletPoint} /}>,
  <Text style={styles.insightText}>{strength}</Text>
              </View>,
  ))}
          </View>,
  <View style={styles.insightSection}>
            <View style={styles.insightHeader}>,
  <AlertTriangle size={16} color={{theme.colors.warning} /}>
              <Text style={[styles.insightTitle{ color: theme.colors.warning}]}>,
  Potential Challenges, ,
  </Text>
            </View>,
  {compatibilityInsights.potentialChallenges.map((challenge, index) => (
  <View key={`challenge-${index}`} style={styles.insightItem}>
                <View style={{[styles.bulletPoint{ backgroundColor: theme.colors.warning}]} /}>,
  <Text style={styles.insightText}>{challenge}</Text>
              </View>,
  ))}
          </View>,
  </View>
        {/* Recommended activities */}
  <View style={styles.activitiesContainer}>
          <Text style={styles.sectionTitle}>Recommended Activities</Text>,
  {compatibilityInsights.recommendedActivities.map((activity, index) => (
  <View key={`activity-${index}`} style={styles.activityItem}>
              {index === 0 ? (
  <Heart size={16} color={{theme.colors.primary} /}>
              )     : index === 1 ? (<Calendar size={16} color={{theme.colors.primary} /}>,
  ) : (<MessageCircle size={16} color={{theme.colors.primary} /}>
              )},
  <Text style={styles.activityText}>{activity}</Text>
            </View>,
  ))}
        </View>,
  </Animated.View>
    </View>,
  )
},
  // Helper function to get color based on score
const getScoreBarColor = (score: number, theme: any) => {
  if (score >= 80) return { backgroundColor: theme.colors.success }
  if (score >= 60) return { backgroundColor: theme.colors.primary },
  if (score >= 40) return { backgroundColor: theme.colors.warning }
  return { backgroundColor: theme.colors.error }
  }
const createStyles = (theme: any) => StyleSheet.create({ card: {
      backgroundColor: theme.colors.surface,
  borderRadius: theme.borderRadius.lg,
    marginBottom: theme.spacing.md,
  overflow: 'hidden', ,
  ...theme.shadows.md)
    width: CARD_WIDTH },
  cardContent: {
      width: '100%' }
  imageContainer: { positio, n: 'relative',
    width: '100%',
  height: 250,
    backgroundColor: theme.colors.surfaceVariant },
  image: { widt, h: '100%',
    height: '100%',
  borderTopLeftRadius: theme.borderRadius.lg,
    borderTopRightRadius: theme.borderRadius.lg },
  boostedBadge: {
      position: 'absolute',
  top: theme.spacing.sm,
    right: theme.spacing.sm,
  backgroundColor: theme.colors.warning,
    borderRadius: theme.borderRadius.pill,
  paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
  flexDirection: 'row',
    alignItems: 'center' }
  boostedText: { fontSiz, e: 12,
    fontWeight: '600',
  color: theme.colors.surface,
    marginLeft: 4 },
  cardBody: { paddin, g: theme.spacing.md }
  nameContainer: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: 4 }
  name: { fontSiz, e: 20,
    fontWeight: '600',
  color: theme.colors.text,
    marginRight: 4 },
  badgesContainer: { flexDirectio, n: 'row',
    alignItems: 'center',
  gap: 4 }
  badge: { marginLef, t: 4 },
  occupation: { fontSiz, e: 16,
    color: theme.colors.textSecondary,
  marginBottom: theme.spacing.sm }
  compatibilityContainer: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginTop: theme.spacing.sm }
  scoreDetails: { marginLef, t: theme.spacing.sm,
    flex: 1 },
  scoreTitle: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.text }
  scoreSubtitle: { fontSiz, e: 14,
    color: theme.colors.textSecondary },
  expandButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'center',
    paddingVertical: theme.spacing.sm,
  borderTopWidth: 1,
    borderTopColor: theme.colors.border },
  expandText: { fontSiz, e: 14,
    fontWeight: '500',
  color: theme.colors.primary,
    marginRight: 4 },
  expandedContent: { overflo, w: 'hidden',
    paddingHorizontal: theme.spacing.md },
  compatibilityBreakdown: { marginBotto, m: theme.spacing.md }
  sectionTitle: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.text,
    marginBottom: theme.spacing.sm },
  scoreRow: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: theme.spacing.sm }
  scoreLabel: { widt, h: '35%',
    fontSize: 14,
  color: theme.colors.textSecondary }
  scoreBarContainer: {
      flex: 1,
  height: 8,
    backgroundColor: theme.colors.border,
  borderRadius: theme.borderRadius.sm,
    overflow: 'hidden' }
  scoreBar: { heigh, t: '100%',
    borderRadius: theme.borderRadius.sm },
  scoreValue: { widt, h: '15%',
    fontSize: 14,
  fontWeight: '500',
    color: theme.colors.text,
  textAlign: 'right',
    marginLeft: theme.spacing.sm },
  communicationStyle: { marginTo, p: theme.spacing.sm }
  communicationLabel: { fontSiz, e: 14,
    color: theme.colors.textSecondary,
  marginBottom: 4 }
  communicationValue: {
      fontSize: 14,
  color: theme.colors.text,
    fontStyle: 'italic' }
  insightsContainer: { marginBotto, m: theme.spacing.md },
  insightSection: { marginBotto, m: theme.spacing.md }
  insightHeader: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: theme.spacing.sm }
  insightTitle: { fontSiz, e: 16,
    fontWeight: '600',
  marginLeft: theme.spacing.sm,
    color: theme.colors.text },
  insightItem: { flexDirectio, n: 'row',
    alignItems: 'flex-start',
  marginBottom: 6,
    paddingLeft: 24 },
  bulletPoint: { widt, h: 6,
    height: 6,
  borderRadius: 3,
    backgroundColor: theme.colors.success,
  marginTop: 6,
    marginRight: theme.spacing.sm },
  insightText: { fle, x: 1,
    fontSize: 14,
  color: theme.colors.text,
    lineHeight: 20 },
  activitiesContainer: { marginBotto, m: theme.spacing.sm }
  activityItem: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: theme.spacing.sm }
  activityText: { fontSiz, e: 14,
    color: theme.colors.text,
  marginLeft: theme.spacing.sm }
  actionButtons: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  marginTop: theme.spacing.md,
    paddingTop: theme.spacing.md,
  borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  gap: theme.spacing.sm }
  actionButton: { fle, x: 1,
    flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'center',
  backgroundColor: theme.colors.surfaceVariant,
    paddingVertical: theme.spacing.sm,
  paddingHorizontal: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
  borderWidth: 1,
    borderColor: theme.colors.border },
  messageButton: { backgroundColo, r: theme.colors.surfaceVariant }
  agreementButton: {
      backgroundColor: theme.colors.success + '10', // 10% opacity,
  borderColor: theme.colors.success + '40', // 25% opacity }
  loadingButton: {
      flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'center' }
  actionButtonText: {
      fontSize: 14),
  fontWeight: '500'),
    color: theme.colors.primary,
  marginLeft: theme.spacing.sm)
  }
  })
  export default EnhancedMatchCard