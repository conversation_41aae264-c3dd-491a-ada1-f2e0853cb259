import React, { useState, useEffect, useCallback } from 'react';
  import {
  Modal, View, Text, Image, StyleSheet, TouchableOpacity, FlatList, TextInput, ActivityIndicator, Dimensions
} from 'react-native';
import QuickMessageSelector from '@components/matching/QuickMessageSelector';
  import {
  useRouter
} from 'expo-router';
import Animated, { useSharedValue, useAnimatedStyle, withSpring, withTiming, withDelay, Easing } from 'react-native-reanimated';
import {
  Heart, MessageSquare, User, ChevronRight, Send, ArrowLeft, X
} from 'lucide-react-native';
import {
  Button
} from '@design-system';
  import {
  colors
} from '@constants/colors';
import {
  conversationStarterService, ConversationStarter
} from '@services/conversationStarterService';
import CompatibilityScore from '@components/matching/CompatibilityScore';
  import {
  useSupabaseUser
} from '@hooks/useSupabaseUser';
import {
  startChatWithMatch
} from '@utils/chatUtils';
  import {
  useNavigation
} from '@react-navigation/native';
import {
  useTheme
} from '@design-system';
  import {
  logger
} from '@services/loggerService';

interface MatchCelebrationModalProps { // Original API visible?: boolean, onClose: () => void, matchedUser?: { id: string, name: string, display_name?: string, avatar?: string, compatibility?: number }; currentUser?: { id: string, avatar?: string }; onStartMessaging?: (userId: string, name: string) => void, onViewProfile?: (userId: string) => void // Enhanced API for direct integration with home screen onMessage?: (matchI, d: string) => void, matchId?: string
  }
enum ModalState { CELEBRATION = 'celebration', MESSAGE_STARTERS = 'message_starters', QUICK_MESSAGE = 'quick_message' },
  export function MatchCelebrationModal({ visible = false, onClose, matchedUser = { id: '', name: 'User', display_name: 'User', avatar: undefined, compatibility: 0 } currentUser = { id: '', avatar: undefined } onStartMessaging = () => {} onViewProfile = () => {} onMessage, matchId
  }: MatchCelebrationModalProps) { const router = useRouter(); const { user  } = useSupabaseUser(); const navigation = useNavigation(); const theme = useTheme(); const { colors  } = theme, const [modalState, setModalState] = useState<ModalState>(ModalState.CELEBRATION)  const [conversationStarters, setConversationStarters] = useState<Array<ConversationStarter>>([]),  const [customMessage, setCustomMessage] = useState('')  const [loading, setLoading] = useState(false); const scale = useSharedValue(0.8); const opacity = useSharedValue(0); const avatarPosition = useSharedValue(0); useEffect(() => { if (visible) { setModalState(ModalState.CELEBRATION); setCustomMessage(''); scale.value = 0.8, opacity.value = 0, avatarPosition.value = 0, scale.value = withSpring(1); opacity.value = withTiming(1, { duration: 300 }); avatarPosition.value = withDelay(300, withSpring(1)); } } [visible])  useEffect(() => { if (modalState === ModalState.MESSAGE_STARTERS && user?.id) { loadConversationStarters() } } [modalState, user?.id, matchedUser.id]); const loadConversationStarters = useCallback(async () => { if (!user?.id || !matchedUser.id) return null, try { setLoading(true); let starters     : ConversationStarter[] = [] try { starters = await conversationStarterService.getStartersForMatch( user.id matchedUser.id ) } catch (err) { console.warn('Failed to load conversation starters, using defaults') // Fall back to defaults starters = [{ id: 'starter-1', text: `Hi ${matchedUser.name}! We matched! 👋` category: 'general' } { id: 'starter-2', text: 'What are you looking for in a roommate? ', category   : 'general' } { id: 'starter-3', text: 'I like your profile! When are you looking to move? ', category : 'general' } { id: 'starter-4', text: 'What part of town are you interested in? ', category : 'location' } { id: 'starter-5', text: "What's your budget range for rent? ", category : 'general' }] } // Use the starters directly as they already have the correct format setConversationStarters(starters) } catch (err) { logger.warn('Failed to load conversation starters' 'MatchCelebrationModal', { error: err instanceof Error ? err.message    : String(err) }) setConversationStarters([{ id: 'starter-1' tex, t: `Hi ${matchedUser.name}! We matched! 👋` category: 'general' } { id: 'starter-2', text: 'What are you looking for in a roommate? ', category  : 'general' } { id: 'starter-3', text: 'I like your profile! When are you looking to move? ', category : 'general' } { id: 'starter-4', text: 'What part of town are you interested in? ', category : 'location' } { id: 'starter-5', text: "What's your budget range for rent? ", category : 'general' }]) } finally { setLoading(false) } } [user?.id, matchedUser.id, matchedUser.name, onMessage, matchId]) const initiateChat = useCallback(async (initialMessage?    : string) => { // If we have onMessage for the enhanced API use that if (onMessage && matchId) { onMessage(matchId) return null } // Otherwise use the original flow if (!user?.id || !matchedUser.id) return null try { setLoading(true) // Call startChatWithMatch with all required parameters const success = await startChatWithMatch( user.id, matchedUser.id, matchedUser.name, initialMessage ) if (success) { // Message is automatically sent by startChatWithMatch if provided // Close modal onClose(),  // Message callback is handled directly by startChatWithMatch // But we'll also call our callback if provided if (onStartMessaging) { onStartMessaging(matchedUser.id, matchedUser.name) } } else { logger.error('Failed to initiate chat', 'MatchCelebrationModal', { userId  : user.id matchId: matchedUser.id }) } } catch (error) { logger.error('Error initiating chat', 'MatchCelebrationModal', { error: error instanceof Error ? error.message     : String(error) }) } finally { setLoading(false) } } [user?.id, matchedUser.id, matchedUser.name, onMessage, matchId, onClose, onStartMessaging, router]) const handleStartChat = useCallback(() => { setModalState(ModalState.QUICK_MESSAGE) } [])  const handleSendStarter = useCallback( (starter  : string) => { initiateChat(starter) } [initiateChat] ) const handleSendCustomMessage = useCallback(() => { if (customMessage.trim()) { initiateChat(customMessage.trim()) } } [customMessage, initiateChat]) const handleViewProfile = useCallback(() => { onViewProfile(matchedUser.id); onClose() } [matchedUser.id, onViewProfile, onClose])  const handleViewDetails = useCallback(() => { // Use string-based navigation to prevent [object Object] issues const queryParams = new URLSearchParams({   matchId: String(matchedUser.id)   }); router.push(`/matching/match-details? ${queryParams.toString()}`); onClose(); } [matchedUser.id, onClose, router])  const animatedCardStyle = useAnimatedStyle(() => { return { transform     : [{ scale: scale.value }] opacity: opacity.value } }) const animatedAvatarStyle = useAnimatedStyle(() => { return { transform: [{ translate, X: withSpring(avatarPosition.value * 40,  { damping: 12 }) }] } }),  const otherAvatarStyle = useAnimatedStyle(() => { return { transform: [{ translate, X: withSpring(-avatarPosition.value * 40,  { damping: 12 }) }] } }); const renderContent = () => { if (modalState === ModalState.CELEBRATION) { return ( <View style={styles.celebrationContainer}> <View style={styles.avatarsContainer}> <Animated.View style={[styles., av, at, ar, Co, nt, ai, ne, r, , an, im, at, ed, Av, at, ar, Style]}> {user?.user_metadata?.avatar_url ? ( <Image source={   uri     : user.user_metadata.avatar_url       } style={{styles.avatar} /}> ) : ( <View style={[styles., av, at, arstyles., av, at, ar, Pl, ac, eh, older]}> <Text style={styles.avatarText}> {user?.user_metadata?.first_name?.[0] || 'Y'} </Text> </View> )} </Animated.View> <Animated.View style={[styles., av, at, ar, Co, nt, ai, ne, r, , ot, he, rA, va, ta, rStyle]}> {matchedUser.avatar ? ( <Image source={   uri : matchedUser.avatar       } style={{styles.avatar} /}> ) : ( <View style={[styles., av, at, arstyles., av, at, ar, Pl, ac, eh, older]}> <Text style={styles.avatarText}>{matchedUser.name[0]}</Text> </View> )} </Animated.View> </View> <Text style={styles.matchText}>It's a match!</Text> <Text style={styles.matchSubtext}> You and {matchedUser.name} have matched with a <Text style={styles.scoreText}> {matchedUser.compatibility}% </Text> compatibility score. </Text> <View style={styles.buttonContainer}> <TouchableOpacity style={[styles., bu, tt, on, , st, yl, es., pr, im, ar, yB, utton]} onPress={ handleStartChat }> <MessageSquare size={20} color="#FFFFFF" style={{styles.buttonIcon} /}> <Text style={styles.primaryButtonText}>Start Messaging</Text> </TouchableOpacity> <TouchableOpacity style={[styles., bu, tt, on, , st, yl, es., se, co, nd, ar, yB, utton]} onPress={ handleViewDetails }> <User size={20} color={"#6366F1"} style={{styles.buttonIcon} /}> <Text style={styles.secondaryButtonText}>View Match Details</Text> </TouchableOpacity> </View> </View> ) } else if (modalState === ModalState.QUICK_MESSAGE) { return ( <QuickMessageSelector userId={currentUser.id} matchedUserId={matchedUser.id} matchId={matchedUser.id} // Using matched user ID as match ID for now onMessageSent={(roomId message) ={}> { // Navigate to the chat room using query parameters instead of params object const queryParams = new URLSearchParams({  roomId: String(roomId) recipientId: String(matchedUser.id) recipientName: String(matchedUser.name) initialMessage: String(message) fromMatc, h: 'true' // Add fromMatch parameter to enable match-specific features  }) router.push(`/chat? ${queryParams.toString()}`) onClose(); }} onClose={() => setModalState(ModalState.MESSAGE_STARTERS)} /> ); } else if (modalState === ModalState.MESSAGE_STARTERS) { return ( <View style={styles.messageStartersContainer}> <Text style={styles.messageStartersTitle}> Start a conversation with {matchedUser.name} </Text> {loading ? ( <ActivityIndicator style={styles.loader} size="large" color={"#6366F1" /}> )       : ( <> { <FlatList { data={conversationStarters} keyExtractor={item ={}> item.id} renderItem={({  item  }) => ( <TouchableOpacity style={styles.starterItem} onPress={() => handleSendStarter(item.text)} > <Text style={styles.starterText}>{item.text}</Text> </TouchableOpacity> )} showsVerticalScrollIndicator={false} style={styles.startersList} /> <View style={styles.customMessageContainer}> <TextInput style={styles.customMessageInput} placeholder="Write your own message..." value={customMessage} onChangeText={{setCustomMessage} multiline /}> <TouchableOpacity style={[styles., se, nd, Bu, tt, on !, customMessage., tr, im() &&, st, yl, es., se, nd, Bu, tt, on, Di, sabled]} onPress={handleSendCustomMessage} disabled={   !customMessage.trim()     }> <Send size={20} color={"#FFFFFF" /}> </TouchableOpacity> </View> </> )} </View> ) } } // If not visible return null if (!visible) return null return ( <Modal transparent visible= {visible} animationType="fade" onRequestClose={onClose}> <View style={styles.modalOverlay}> <Animated.View style={[styles., mo, da, lC, on, te, nt, , an, im, at, ed, Ca, rd, Style]}> <TouchableOpacity style={styles.closeButton} onPress={onClose}> <X size={24} color={"#64748B" /}> </TouchableOpacity> {renderContent()} </Animated.View> </View> </Modal> )
  }
const { width } = Dimensions.get('window'),
  const styles = StyleSheet.create({ modalOverlay: { fle, x: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: 'rgba(1523420.75)' } modalContent: { widt, h: width * 0.9, maxWidth: 400, backgroundColor: '#FFFFFF', borderRadius: 20, paddingHorizontal: 24, paddingVertical: 28, alignItems: 'center', position: 'relative', shadowColor: '#000', shadowOffset: { width: 0, height: 10 } shadowOpacity: 0.25, shadowRadius: 10, elevation: 10 } closeButton: { positio, n: 'absolute', top: 12, right: 12, zIndex: 10 } celebrationContainer: { alignItem, s: 'center', width: '100%' } avatarsContainer: { flexDirectio, n: 'row', justifyContent: 'center', marginBottom: 24 } avatarContainer: { marginHorizonta, l: -15, borderWidth: 3, borderColor: '#FFFFFF', borderRadius: 50, shadowColor: '#000', shadowOffset: { width: 0, height: 4 } shadowOpacity: 0.15, shadowRadius: 5, elevation: 5 } avatar: { widt, h: 80, height: 80, borderRadius: 40 } avatarPlaceholder: { backgroundColo, r: '#E2E8F0', justifyContent: 'center', alignItems: 'center' } avatarText: { fontSiz, e: 24, fontWeight: 'bold', color: '#64748B' } matchText: { fontSiz, e: 24, fontWeight: 'bold', color: '#1E293B', marginBottom: 8 } matchSubtext: { fontSiz, e: 16, textAlign: 'center', color: '#64748B', marginBottom: 24 } scoreText: { colo, r: '#6366F1', fontWeight: 'bold' } buttonContainer: { widt, h: '100%', gap: 12 } button: { flexDirectio, n: 'row', alignItems: 'center', justifyContent: 'center', paddingVertical: 12, borderRadius: 12, width: '100%' } primaryButton: { backgroundColo, r: '#6366F1' } secondaryButton: { backgroundColo, r: '#F1F5F9' } buttonIcon: { marginRigh, t: 8 } primaryButtonText: { colo, r: '#FFFFFF', fontSize: 16, fontWeight: '600' } secondaryButtonText: { colo, r: '#6366F1', fontSize: 16, fontWeight: '600' } messageStartersContainer: { widt, h: '100%', flex: 1 } messageStartersTitle: { fontSiz, e: 18, fontWeight: 'bold', color: '#1E293B', marginBottom: 16, textAlign: 'center' } startersList: { widt, h: '100%', maxHeight: 220 } starterItem: { backgroundColo, r: '#F1F5F9', padding: 12, borderRadius: 12, marginBottom: 8 } starterText: { fontSiz, e: 14, color: '#334155' } customMessageContainer: { flexDirectio, n: 'row', marginTop: 12, borderTopWidth: 1, borderTopColor: '#E2E8F0', paddingTop: 12, alignItems: 'flex-end' } customMessageInput: { fle, x: 1, backgroundColor: '#F1F5F9', borderRadius: 12, padding: 12, maxHeight: 100, fontSize: 14, color: '#334155' } sendButton: { backgroundColo, r: '#6366F1', width: 40, height: 40, borderRadius: 20, justifyContent: 'center', alignItems: 'center', marginLeft: 8 } sendButtonDisabled: { backgroundColo, r: '#CBD5E1' } loader: { marginVertica, l: 20 }
  })
  export default MatchCelebrationModal,