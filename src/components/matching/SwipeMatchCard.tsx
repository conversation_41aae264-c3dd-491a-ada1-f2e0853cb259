import React, { useRef, useState, useEffect } from 'react';
  import {
  View, Text, StyleSheet, Animated, PanResponder, Dimensions, TouchableOpacity
} from 'react-native';
import {
  Heart, X, Star, MapPin, Bookmark, Zap, ChevronDown, ChevronUp, Info
} from 'lucide-react-native';
import {
  Image
} from 'expo-image';
  import {
  useTheme
} from '@design-system';
import {
  UserProfile
} from '@/types/auth';
  import VerifiedBadge from '@components/badges/VerifiedBadge';
import BackgroundCheckBadge from '@components/badges/BackgroundCheckBadge';
  import CompatibilityScore from '@components/matching/CompatibilityScore';
import {
  useSupabaseUser
} from '@hooks/useSupabaseUser';
  import {
  startChatWithMatch
} from '@utils/chatUtils';

const { width, height  } = Dimensions.get('window'),
  const CARD_WIDTH = width * 0.9,
const SWIPE_THRESHOLD = width * 0.25,
  const SWIPE_OUT_DURATION = 250,
interface SwipeMatchCardProps {
  profile: UserProfile,
    compatibility: {
      score: number,
    factors: string[] }
  compatibilityInsights?: {
  strengths: string[],
    potentialChallenges: string[],
  lifestyleCompatibility: number,
    valueAlignment: number,
  habitCompatibility: number,
    communicationStyle: string,
  recommendedActivities: string[] }
  onLike: () => void,
    onDislike: () => void,
  onSuperLike: () => void,
    onViewProfile: (userI, d: string) => void,
  boosted?: boolean
  isLastCard?: boolean
  }
const SwipeMatchCard: React.FC<SwipeMatchCardProps> = ({
  profile,
  compatibility,
  compatibilityInsights,
  onLike,
  onDislike,
  onSuperLike,
  onViewProfile,
  boosted = false, ,
  isLastCard = false }) => {
  const [expanded, setExpanded] = useState(false),
  const [contentHeight, setContentHeight] = useState(0),
  const { user  } = useSupabaseUser()
  const theme = useTheme(),
  const styles = createStyles(theme);
   // Animation values,
  const position = useRef(new Animated.ValueXY()).current,
  const rotation = position.x.interpolate({
  inputRange: [-CARD_WIDTH / 2, 0, CARD_WIDTH / 2], ,
  outputRange: ['-10deg', '0deg', '10deg']),
  extrapolate: 'clamp')
   }),
  // Opacity for the like/dislike overlays,
  const likeOpacity = position.x.interpolate({
  inputRange: [0, CARD_WIDTH / 4], ,
  outputRange: [0, 1]),
  extrapolate: 'clamp')
   }),
  ;
  const dislikeOpacity = position.x.interpolate({
  inputRange: [-CARD_WIDTH / 4, 0], ,
  outputRange: [1, 0]),
  extrapolate: 'clamp')
   }),
  ;
  const superLikeOpacity = position.y.interpolate({
  inputRange: [-CARD_WIDTH / 4, 0], ,
  outputRange: [1, 0]),
  extrapolate: 'clamp')
   }),
  // Animation for expanded content,
  const expandAnim = useRef(new Animated.Value(0)).current,
  const maxHeight = expandAnim.interpolate({  inputRange: [0, 1]) ,
  outputRange: [0, contentHeight]  }),
  // Reset position when profile changes, ,
  useEffect(() => {
  position.setValue({  x: 0, y: 0  })
  }, [profile.id]);
  // Pan responder for swipe gestures,
  const panResponder = useRef(
  PanResponder.create({ 
      onStartShouldSetPanResponder: () => true,
    onPanResponderMove: (event, gesture) => {
  // Only allow swiping if not expanded,
        if (!expanded) {
  position.setValue({ x: gesture.dx, y: gesture.dy  })
  }
      },
  onPanResponderRelease: (event, gesture) => {
  if (expanded) return null // Swiping right (like)
        if (gesture.dx > SWIPE_THRESHOLD) {
  forceSwipe('right')
        },
  // Swiping left (dislike)
        else if (gesture.dx < -SWIPE_THRESHOLD) {
  forceSwipe('left')
        },
  // Swiping up (super like)
        else if (gesture.dy < -SWIPE_THRESHOLD) {
  forceSwipe('up')
        },
  // Return to center if not swiped far enough,
        else {
  resetPosition()
        }
  }
    }),
  ).current,
  const forceSwipe = (direction: 'left' | 'right' | 'up') => {
  const x = direction === 'right' ? width      : direction === 'left' ? -width : 0
    const y = direction === 'up' ? -height  : 0,
  Animated.timing(position, {
  toValue: { x, y },
  duration: SWIPE_OUT_DURATION),
    useNativeDriver: true)
  }).start(() => onSwipeComplete(direction))
  },
  const onSwipeComplete = (direction: 'left' | 'right' | 'up') => {
  if (direction === 'right') {
  onLike()
    } else if (direction === 'left') {
  onDislike()
    } else if (direction === 'up') {
  onSuperLike()
    }
  }
  const resetPosition = () => {
  Animated.spring(position, {
  toValue: { , x: 0, y: 0 },
  friction: 5),
    useNativeDriver: true)
  }).start()
  },
  const toggleExpanded = () => {
  setExpanded(!expanded),
  Animated.timing(expandAnim, {
  toValue: expanded ? 0    : 1,
    duration: 300,
  useNativeDriver: false)
    }).start()
  }
  // Calculate age from date of birth,
  const getAge = () => { if (!profile.date_of_birth) return '? ';
    ,
  try {
      const today = new Date(),
  const birthDate = new Date(profile.date_of_birth);
      ,
  if (isNaN(birthDate.getTime())) return '? ';
      ,
  let age = today.getFullYear() - birthDate.getFullYear()
      const m = today.getMonth() - birthDate.getMonth(),
  if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
        age-- },
  return age.toString()
    } catch (error) { console.error('Error calculating age     : ' error),
  return '? ' }
  },
  // Format compatibility scores as percentages,
  const formatScore = (score : number) => `${Math.round(score)}%`
  ;
  // Get color for score bars,
  const getScoreBarColor = (score: number) => {
  if (score >= 80) return theme.colors.success,
  if (score >= 60) return theme.colors.success,
    if (score >= 40) return theme.colors.warning,
  return theme.colors.error;
  },
  // Card transform styles,
  const cardStyle ={ transform: [;
      { translateX: position.x   },
  { translateY: position.y };
      { rotate: rotation }]
  }
  return (
  <Animated.View, ,
  style= {[styles.container, cardStyle]},
  {...panResponder.panHandlers}
    >,
  {/* Like overlay */}
      <Animated.View style={{ [styles.overlaystyles.likeOverlay{ opacity: likeOpacity  ] }]}>,
  <Text style={[styles.overlayText{ color: theme.colors.success}]}>LIKE</Text>,
  </Animated.View>
      {/* Dislike overlay */}
  <Animated.View style={{ [styles.overlaystyles.dislikeOverlay{ opacity: dislikeOpacity  ] }]}>,
  <Text style={[styles.overlayText{ color: theme.colors.error}]}>NOPE</Text>,
  </Animated.View>
      {/* Super Like overlay */}
  <Animated.View style={{ [styles.overlaystyles.superLikeOverlay{ opacity: superLikeOpacity  ] }]}>,
  <Text style={[styles.overlayText{ color: theme.colors.primary}]}>SUPER</Text>,
  </Animated.View>
      <View style={styles.card}>,
  {/* Profile image */}
        <View style={styles.imageContainer}>,
  <Image
            source={   uri: profile.avatar_url || 'https://images.unsplash.com/photo-1494790108377-be9c29b29330'    },
  style={styles.image} contentFit="cover", ,
  />
          {/* Boosted badge if applicable */}
  {boosted && (
            <View style={styles.boostedBadge}>,
  <Zap size="small" color={{theme.colors.background} /}>
              <Text style={styles.boostedText}>Boosted</Text>,
  </View>
          )},
  </View>
        {/* Card body with profile info */}
  <View style={styles.cardBody}>
          <View style={styles.nameContainer}>,
  <Text style={styles.name}>{profile.first_name} {getAge()}</Text>
  <View style={styles.badgesContainer}>,
  {profile.is_verified && <VerifiedBadge size={"small" /}>
  {profile.background_check_verified && <BackgroundCheckBadge size={"small" /}>,
  </View>
  </View>,
  {profile.occupation && (
  <Text style={styles.occupation}>{profile.occupation}</Text>,
  )}
  <View style={styles.locationContainer}>,
  <MapPin size="small" color={{theme.colors.textSecondary} /}>
  <Text style={styles.location}>,
  {'Location not specified'}
  </Text>,
  </View>
  {/* Compatibility score */}
  <View style={styles.compatibilityContainer}>
  <CompatibilityScore score={compatibility.score} size={"large" /}>,
  <View style={styles.scoreDetails}>
  <Text style={styles.scoreTitle}>Compatibility Score</Text>,
  <Text style={styles.scoreSubtitle}>
  {compatibility.score >= 80,
  ? 'Excellent match!';
   : compatibility.score >= 60,
  ? 'Good match'
  : compatibility.score >= 40,
  ? 'Average match'
  : 'Low compatibility'},
  </Text>
  </View>,
  </View>
  {/* Top compatibility factors */}
  <View style={styles.factorsContainer}>
  {compatibility.factors.slice(0 2).map((factor, index) => (
  <Text key={index} style={styles.factor}>
                • {factor},
  </Text>
            ))},
  </View>
        </View>,
  {/* Expandable section with detailed compatibility */}
        {compatibilityInsights && (
  <>
            <TouchableOpacity style={styles.expandButton} onPress={toggleExpanded}>,
  <Text style={styles.expandText}>
                {expanded ? 'Hide details'   : 'Show compatibility details'},
  </Text>
              {expanded ? (
  <ChevronUp size="small" color={{theme.colors.primary} /}>
              ) : (<ChevronDown size="small" color={{theme.colors.primary} /}>,
  )}
            </TouchableOpacity>,
  <Animated.View 
              style={[styles.expandedContent { maxHeight}]},
  onLayout={(event) => {
  const { height } event.nativeEvent.layout,
  setContentHeight(height)
              }},
  >
              <View style={styles.compatibilityBreakdown}>,
  <Text style={styles.sectionTitle}>Compatibility Breakdown</Text>
                {/* Lifestyle compatibility */}
  <View style={styles.scoreRow}>
                  <Text style={styles.scoreLabel}>Lifestyle</Text>,
  <View style={styles.scoreBarContainer}>
                    <View,
  style = {[
                        styles.scoreBar, ,
  {
                          width: `${compatibilityInsights.lifestyleCompatibility}%`
  backgroundColor: getScoreBarColor(compatibilityInsights.lifestyleCompatibility)
                        }
   ]},
  />
                  </View>,
  <Text style={styles.scoreValue}>{formatScore(compatibilityInsights.lifestyleCompatibility)}</Text>
                </View>,
  {/* Value alignment */}
                <View style={styles.scoreRow}>,
  <Text style={styles.scoreLabel}>Values</Text>
                  <View style={styles.scoreBarContainer}>,
  <View,
                      style = {[
                        styles.scoreBar,
  {
                          width: `${compatibilityInsights.valueAlignment}%`,
  backgroundColor: getScoreBarColor(compatibilityInsights.valueAlignment)
                        }
   ]},
  />
                  </View>,
  <Text style= {styles.scoreValue}>{formatScore(compatibilityInsights.valueAlignment)}</Text>
                </View>,
  {/* Habit compatibility */}
                <View style={styles.scoreRow}>,
  <Text style={styles.scoreLabel}>Habits</Text>
                  <View style={styles.scoreBarContainer}>,
  <View,
                      style = {[
                        styles.scoreBar,
  {
                          width: `${compatibilityInsights.habitCompatibility}%`,
  backgroundColor: getScoreBarColor(compatibilityInsights.habitCompatibility)
                        }
   ]},
  />
                  </View>,
  <Text style= {styles.scoreValue}>{formatScore(compatibilityInsights.habitCompatibility)}</Text>
                </View>,
  {/* Communication style */}
                <View style={styles.communicationStyle}>,
  <Text style={styles.communicationLabel}>Communication Style:</Text>
                  <Text style={styles.communicationValue}>{compatibilityInsights.communicationStyle}</Text>,
  </View>
              </View>,
  {/* Strengths and challenges */}
              <View style={styles.insightsContainer}>,
  {/* Strengths */}
                <View style={styles.insightSection}>,
  <View style={styles.insightHeader}>
                    <Heart size="small" color={{theme.colors.success} /}>,
  <Text style={[styles.insightTitle{ color: theme.colors.success}]}>Strengths</Text>,
  </View>
                  {compatibilityInsights.strengths.map((strength, index) => (
  <View key={index} style={styles.insightItem}>
                      <View style={{[styles.bulletPoint{ backgroundColor: theme.colors.success}]} /}>,
  <Text style={styles.insightText}>{strength}</Text>
                    </View>,
  ))}
                </View>,
  {/* Potential challenges */}
                {compatibilityInsights.potentialChallenges.length > 0 && (
  <View style={styles.insightSection}>
                    <View style={styles.insightHeader}>,
  <X size="small" color={{theme.colors.warning} /}>
                      <Text style={[styles.insightTitle{ color: theme.colors.warning}]}>,
  Potential Challenges, ,
  </Text>
                    </View>,
  {compatibilityInsights.potentialChallenges.map((challenge, index) => (
  <View key={index} style={styles.insightItem}>
                        <View style={{[styles.bulletPoint{ backgroundColor: theme.colors.warning}]} /}>,
  <Text style={styles.insightText}>{challenge}</Text>
                      </View>,
  ))}
                  </View>,
  )}
              </View>,
  {/* View full profile button */}
              <TouchableOpacity style={styles.viewProfileButton} onPress={() => onViewProfile(profile.id)},
  >
                <Text style={styles.viewProfileText}>View Full Profile</Text>,
  </TouchableOpacity>
            </Animated.View>,
  </>
        )},
  {/* Action buttons */}
        <View style={styles.actionButtons}>,
  <TouchableOpacity style={[styles., ac, ti, on, Bu, tt, on, , st, yl, es., di, sl, ik, eB, utton]} onPress={() => forceSwipe('left')},
  >
            <X size="medium" color={{theme.colors.error} /}>,
  </TouchableOpacity>
          <TouchableOpacity style={[styles., ac, ti, on, Bu, tt, on, , st, yl, es., su, pe, rL, ik, eB, utton]} onPress={() => forceSwipe('up')},
  >
            <Star size="medium" color={{theme.colors.primary} /}>,
  </TouchableOpacity>
          <TouchableOpacity style={[styles., ac, ti, on, Bu, tt, on, , st, yl, es., li, ke, Button]} onPress= {() => forceSwipe('right')},
  >
            <Heart size="medium" color={{theme.colors.success} /}>,
  </TouchableOpacity>
        </View>,
  </View>
    </Animated.View>,
  )
},
  const createStyles = (theme: any) => StyleSheet.create({, container: {
  position: 'absolute',
    width: CARD_WIDTH,
  alignSelf: 'center'
  },
  card: { backgroundColo, r: theme.colors.background,
    borderRadius: 16,
  overflow: 'hidden',
    shadowColor: theme.colors.shadow,
  shadowOffset: {
      width: 0,
  height: 2 }
    shadowOpacity: 0.1,
    shadowRadius: 4,
  elevation: 3
  },
  overlay: {
      position: 'absolute',
  top: 50,
    justifyContent: 'center',
  alignItems: 'center',
    zIndex: 999,
  borderWidth: 4,
    borderRadius: 10,
  padding: 10, ,
  transform: [{ rotat, e: '-30deg' }] 
  }
  likeOverlay: { righ, t: 20,
    borderColor: theme.colors.success },
  dislikeOverlay: { lef, t: 20,
    borderColor: theme.colors.error },
  superLikeOverlay: {
      alignSelf: 'center',
  top: 100,
    borderColor: theme.colors.primary,
  transform: [{ rotat, e: '0deg' }] 
  }
  overlayText: {
      fontSize: 32,
  fontWeight: 'bold',
    textTransform: 'uppercase' }
  imageContainer: { positio, n: 'relative',
    width: '100%',
  height: 350,
    backgroundColor: theme.colors.surface },
  image: {
      width: '100%',
  height: '100%'
  },
  boostedBadge: {
      position: 'absolute',
  top: 12,
    right: 12,
  backgroundColor: theme.colors.warning,
    borderRadius: 16,
  paddingHorizontal: 8,
    paddingVertical: 4,
  flexDirection: 'row',
    alignItems: 'center' }
  boostedText: { fontSiz, e: 12,
    fontWeight: '600',
  color: theme.colors.background,
    marginLeft: 4 },
  cardBody: { paddin, g: 16 }
  nameContainer: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: 4 }
  name: { fontSiz, e: 24,
    fontWeight: '600',
  color: theme.colors.text,
    marginRight: 8 },
  badgesContainer: { flexDirectio, n: 'row',
    alignItems: 'center',
  gap: 4 }
  occupation: { fontSiz, e: 16,
    color: theme.colors.textSecondary,
  marginBottom: 8 }
  locationContainer: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: 12 }
  location: { marginLef, t: 4,
    fontSize: 14,
  color: theme.colors.textSecondary }
  compatibilityContainer: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginTop: 8,
    marginBottom: 12 },
  scoreDetails: { marginLef, t: 12,
    flex: 1 },
  scoreTitle: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.text }
  scoreSubtitle: { fontSiz, e: 14,
    color: theme.colors.textSecondary },
  factorsContainer: { marginTo, p: 8 }
  factor: { fontSiz, e: 14,
    color: theme.colors.text,
  marginBottom: 4,
    lineHeight: 20 },
  expandButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'center',
    paddingVertical: 12,
  borderTopWidth: 1,
    borderTopColor: theme.colors.border },
  expandText: { fontSiz, e: 14,
    fontWeight: '500',
  color: theme.colors.primary,
    marginRight: 4 },
  expandedContent: { overflo, w: 'hidden',
    paddingHorizontal: 16 },
  compatibilityBreakdown: { marginBotto, m: 16 }
  sectionTitle: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.text,
    marginBottom: 12 },
  scoreRow: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: 8 }
  scoreLabel: { widt, h: '25%',
    fontSize: 14,
  color: theme.colors.text }
  scoreBarContainer: {
      flex: 1,
  height: 8,
    backgroundColor: theme.colors.surface,
  borderRadius: 4,
    overflow: 'hidden' }
  scoreBar: { heigh, t: '100%',
    borderRadius: 4 },
  scoreValue: { widt, h: '15%',
    fontSize: 14,
  fontWeight: '500',
    color: theme.colors.text,
  textAlign: 'right',
    marginLeft: 8 },
  communicationStyle: { marginTo, p: 8 }
  communicationLabel: { fontSiz, e: 14,
    color: theme.colors.text,
  marginBottom: 4 }
  communicationValue: {
      fontSize: 14,
  color: theme.colors.text,
    fontStyle: 'italic' }
  insightsContainer: { marginBotto, m: 16 },
  insightSection: { marginBotto, m: 16 }
  insightHeader: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: 8 }
  insightTitle: { fontSiz, e: 16,
    fontWeight: '600',
  marginLeft: 8 }
  insightItem: { flexDirectio, n: 'row',
    alignItems: 'flex-start',
  marginBottom: 6,
    paddingLeft: 24 },
  bulletPoint: { widt, h: 6,
    height: 6,
  borderRadius: 3,
    backgroundColor: theme.colors.success,
  marginTop: 6,
    marginRight: 8 },
  insightText: { fle, x: 1,
    fontSize: 14,
  color: theme.colors.text,
    lineHeight: 20 },
  viewProfileButton: { backgroundColo, r: theme.colors.primarySurface,
    borderRadius: 8,
  paddingVertical: 12,
    alignItems: 'center',
  marginBottom: 16,
    borderWidth: 1,
  borderColor: theme.colors.primary }
  viewProfileText: { fontSiz, e: 14,
    fontWeight: '600',
  color: theme.colors.primary }
  actionButtons: { flexDirectio, n: 'row',
    justifyContent: 'space-around',
  paddingVertical: 16,
    borderTopWidth: 1,
  borderTopColor: theme.colors.border }
  actionButton: { widt, h: 60,
    height: 60,
  borderRadius: 30,
    justifyContent: 'center'),
  alignItems: 'center'),
    backgroundColor: theme.colors.background,
  shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
    height: 1 },
  shadowOpacity: 0.1,
    shadowRadius: 2,
  elevation: 2
  },
  dislikeButton: { borderWidt, h: 1,
    borderColor: theme.colors.error },
  superLikeButton: { borderWidt, h: 1,
    borderColor: theme.colors.primary },
  likeButton: {
      borderWidth: 1,
  borderColor: theme.colors.success)
  }
  })
  export default SwipeMatchCard