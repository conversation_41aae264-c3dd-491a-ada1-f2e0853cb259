import React, { useState, useEffect, useRef } from 'react';
  import {
  useTheme
} from '@design-system';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  TouchableOpacity,
  Animated,
  Dimensions,
  Image
} from 'react-native';
import {
  RefreshCw, Info, Heart, MessageCircle, User
} from 'lucide-react-native';
import {
  useRouter
} from 'expo-router';
  import {
  matchingService
} from '@services/matchingService';
import {
  conversationStarterService
} from '@services/conversationStarterService';
  import {
  profileBoostService
} from '@services/profileBoostService';
import {
  logger
} from '@services/loggerService';
  import {
  useSupabaseUser
} from '@hooks/useSupabaseUser';
import {
  UserProfile
} from '@/types/auth';
  import {
  MatchData,
  CompatibilityInsights,
  EnhancedMatch,
  EnhancedRecommendationResult
} from '@/types/matching';
import {
  MatchResult
} from '@services/matchingService';
  import SwipeMatchCard from '@components/matching/SwipeMatchCard';
import {
  MatchCelebrationModal
} from '@components/matching/MatchCelebrationModal';
  import EnhancedMatchToMessageTransition from '@components/matching/EnhancedMatchToMessageTransition' // import ConfettiCannon from 'react-native-confetti-cannon'; // TODO: Install react-native-confetti-cannon package
import {
  navigateToChat,
  createChatWithMatchAndNavigate,
  navigateToProfile,
  navigateToMessagesList
} from '@utils/navigationUtils';
import {
  supabase
} from '@utils/supabaseUtils';
  const { width, height  } = Dimensions.get('window');
  interface SwipeMatchDeckProps { onRefresh?: () => void,
  onMatchesEmpty?: () => void,
  limit?: number }
// Styles function,
  const createStyles = (theme: any) =>
  StyleSheet.create({ container: {
      flex: 1,
  justifyContent: 'center',
    alignItems: 'center',
  backgroundColor: theme.colors.background }
    centerContainer: { fle, x: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 24,
  backgroundColor: theme.colors.background }
    loadingText: {
      marginTop: 16,
  fontSize: 16,
    color: theme.colors.textSecondary,
  textAlign: 'center'
  },
  errorContainer: {
      padding: 20,
  alignItems: 'center'
  },
  errorText: { fontSiz, e: 16,
    color: theme.colors.error,
  textAlign: 'center',
    marginBottom: 16,
  marginTop: 10 }
    retryButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'center',
    backgroundColor: theme.colors.surface,
  paddingVertical: 10,
    paddingHorizontal: 16,
  borderRadius: 8,
    borderWidth: 1,
  borderColor: theme.colors.border,
    marginTop: 20 },
  retryText: { marginLef, t: 8,
    fontSize: 14,
  fontWeight: '500',
    color: theme.colors.primary },
  emptyContainer: { fle, x: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 20 },
  noMatchesTitle: { fontSiz, e: 18,
    fontWeight: '600',
  color: theme.colors.text,
    textAlign: 'center',
  marginBottom: 8 }
    noMatchesText: { fontSiz, e: 14,
    color: theme.colors.textSecondary,
  textAlign: 'center',
    marginBottom: 16,
  paddingHorizontal: 24 }
    refreshButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'center',
    backgroundColor: theme.colors.primary,
  paddingVertical: 10,
    paddingHorizontal: 16,
  borderRadius: 8 }
    refreshText: { marginLef, t: 8,
    fontSize: 14,
  fontWeight: '500',
    color: theme.colors.background },
  tipContainer: { positio, n: 'absolute',
    bottom: 24,
  flexDirection: 'row',
    alignItems: 'center',
  backgroundColor: theme.colors.surface,
    paddingVertical: 8,
  paddingHorizontal: 12,
    borderRadius: 16,
  borderWidth: 1,
    borderColor: theme.colors.border },
  tipText: { marginLef, t: 8,
    fontSize: 12, ,
  color: theme.colors.textSecondary });
    overlayContainer: { positio, n: 'absolute'),
    top: 0,
  left: 0,
    right: 0,
  bottom: 0),
    backgroundColor: 'rgba(0000.7)',
  justifyContent: 'center',
    alignItems: 'center',
  zIndex: 1000 }
    loadingOverlay: {
      backgroundColor: theme.colors.background,
  borderRadius: 16,
    padding: 24,
  width: '80%',
    alignItems: 'center',
  shadowColor: theme.colors.text,
    shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.25,
    shadowRadius: 3.84,
  elevation: 5
    }
  })
  // Using shared MatchData interface from types/matching.ts,
  const SwipeMatchDeck: React.FC<SwipeMatchDeckProps> = ({ ;
  onRefresh,
  onMatchesEmpty, ,
  limit = 10 }) => {
  const { user  } = useSupabaseUser(),
  const router = useRouter()
  const theme = useTheme(),
  const styles = createStyles(theme)
  const [loading, setLoading] = useState(true),
  const [matches, setMatches] = useState<MatchData[]>([]),
  const [currentIndex, setCurrentIndex] = useState(0),
  const [showMatchModal, setShowMatchModal] = useState(false),
  const [showMessageTransition, setShowMessageTransition] = useState(false),
  const [matchedProfile, setMatchedProfile] = useState<UserProfile | null>(null),
  const [error, setError] = useState<string | null>(null),
  const [refreshing, setRefreshing] = useState(false),
  const [noMoreMatches, setNoMoreMatches] = useState(false),
  const [startingChat, setStartingChat] = useState(false),
  // Variables to track message content and source,
  const [initialMessage, setInitialMessage] = useState<string>(''),
  const [messageSource, setMessageSource] = useState<string>(''),
  // Handle message transition completion,
  const handleMessageTransitionComplete = async (userId: string, name: string) => {
  try {;
      // Use the new standardized navigation function,
  const success = await createChatWithMatchAndNavigate(;
        user?.id || '',
  userId,
        name, ,
  initialMessage, ,
  )
      if (success) {
  // Hide the transition UI,
        setShowMessageTransition(false) } else {
        throw new Error('Failed to start chat') }
    } catch (err) {
  logger.error('Error in handleMessageTransitionComplete'
        'SwipeMatchDeck.handleMessageTransitionComplete'),
  {
          error     : err instanceof Error ? err.message : String(err),
  userId: user?.id
          matchedWithId : userId }
      ),
  // Even if there's an error, try to navigate to messages as a fallback,
  try { // Use standardized navigation function as fallback
        navigateToMessagesList({
  source: 'match',
    trackEvent: true  })
  } catch (navError) {
        console.error('Failed to navigate after error:', navError) }
    } finally {
  setShowMessageTransition(false)
    }
  }
  // Fetch potential matches from both OpenAI and Gemini models when available,
  const fetchMatches = async (isRefreshing = false) => {
    if (!user?.id) {
  setLoading(false);
      return null }
    try {
  setLoading(true)
      setError(''),
  // Get boosted user IDs to highlight premium profiles,
      let boostedUserIds    : string[] = [],
  try {
        // The profileBoostService.getBoostedUserIds() method is designed to handle // the case where the profile_boosts table doesn't exist yet: so it will,
  // return an empty array in that case rather than throwing an error,
        boostedUserIds = await profileBoostService.getBoostedUserIds(),
  if (boostedUserIds.length > 0) {
          // Only log if there are actual boosted profiles:,
  logger.info('Successfully fetched boosted user IDs', 'SwipeMatchDeck.fetchMatches', {
  count: boostedUserIds.length),
    userId: user.id.slice(-4) // Only log last 4 chars for privacy })
        }
  } catch (boostError) {
        // This should rarely happen since profileBoostService handles errors internally // Avoid excessive logging - just note it happened and continue,
  console.warn('Boost service error, continuing without boosts'),
  // Continue with empty boosted list if this fails;
      },
  // Get enhanced AI recommendations (will use both OpenAI and Gemini when possible)
      try { const enhancedRecommendations = await (
  matchingService as any, ,
  ).getEnhancedRoommateRecommendations(user.id, {
  limit, ,
  excludeIds: matches.map(m => m.profile.id) // Use profile.id instead of direct id,
  requestInsights: true,
    algorithms: ['collaborative', 'content-based', 'hybrid'] }),
  // Processing combined recommendations,
        const allMatches = enhancedRecommendations.map((recommendation: any) => {
  // Keep track of source for analytics,
          if (!recommendation.metadata) {
  recommendation.metadata = {}
          },
  recommendation.metadata.source = 'ai-enhanced-match'
          return recommendation
  })
        // Use best match recommendations,
  allMatches.forEach((recommendation: any) => { if (recommendation.score > 0.7) {
            recommendation.tags = [...(recommendation.tags || []) 'Best Match'] }
  })
        // Simple console log for debugging - avoid excessive logger calls that might cause recursion,
  console.log(`Got ${allMatches.length} recommendations`)
        if (allMatches.length === 0) {
  setNoMoreMatches(true)
          if (onMatchesEmpty) onMatchesEmpty() } else {
          // Format the data for the swipe cards,
  const formattedMatches = allMatches;
            .filter((recommendation: any): recommendation is EnhancedMatch => {
  if (!recommendation || !recommendation.profile) {
                logger.warn('Invalid recommendation data', 'SwipeMatchDeck.fetchMatches', {
  userId: user.id)
                }),
  return false;
              },
  return true;
            }),
  .map((recommendation: any) => { // Ensure compatibility insights has all required fields with fallbacks,
              const compatibilityInsights = {
  strengths: recommendation.compatibilityInsights?.strengths || [],
  potentialChallenges     : recommendation.compatibilityInsights?.potentialChallenges || [],
  lifestyleCompatibility : recommendation.compatibilityInsights?.lifestyleCompatibility || 70
                valueAlignment  : recommendation.compatibilityInsights?.valueAlignment || 70,
  habitCompatibility: recommendation.compatibilityInsights?.habitCompatibility || 70
                communicationStyle :  ,
  recommendation.compatibilityInsights?.communicationStyle ||
                  'Compatible communication styles',
  recommendedActivities : recommendation.compatibilityInsights
  ?.recommendedActivities || ['Getting to know each other'] },
  // Handle boosted status with improved error handling
              let isBoosted = false,
  try {
                // First check if boostedUserIds is valid before trying to use it,
  if (boostedUserIds && Array.isArray(boostedUserIds)) {
                  // Use optional chaining to safely access profile.id,
  const profileId = recommendation?.profile?.id,
                  if (profileId && typeof profileId === 'string') {
  isBoosted = boostedUserIds.includes(profileId)
                  }
  }
                // Fallback to the recommendation's boosted property if available,
  if (!isBoosted && recommendation?.boosted === true) {
                  isBoosted = true }
              } catch (boostErr) {
  // Use console.warn instead of logger to avoid potential recursion,
                console.warn('Error checking boosted status     : '),
  boostErr instanceof Error ? boostErr.message  : String(boostErr)
                ),
  // Default to false if there's an error
                isBoosted = false }
              return { profile: recommendation.profile,
    compatibility: {
      score: recommendation.compatibilityScore || 70,
    factors: compatibilityInsights.strengths.slice(0, 3) },
  compatibilityInsights,
  boosted: isBoosted
  } as MatchData;
  }),
  if (formattedMatches.length === 0) {
  setNoMoreMatches(true),
  if (onMatchesEmpty) onMatchesEmpty()
  } else {
  setMatches(formattedMatches)
  setCurrentIndex(0),
  setNoMoreMatches(false)
  logger.info('Successfully fetched enhanced AI matches', 'SwipeMatchDeck.fetchMatches', {
  matchCount: formattedMatches.length),
    userId: user.id) })
          }
  }
      } catch (aiError) {
  // Simplified error handling to avoid recursive logging,
        console.warn('AI matching failed, falling back to basic matches'),
  // Fall back to basic matching if AI recommendations fail,
        try {
  // Use basic roommate recommendations without AI enhancement,
          const basicRecommendations = await matchingService.getPotentialMatches(user.id, ,
  limit);
            0, // offset, ,
  {} // no filters for fallback)
          ),
  if (basicRecommendations.length === 0) {
            setNoMoreMatches(true),
  if (onMatchesEmpty) onMatchesEmpty()
          } else {
  // Format the basic recommendations from MatchResult to MatchData,
            const formattedMatches = basicRecommendations,
  .filter((matchResult: MatchResult) => {
                return matchResult && matchResult.profile })
              .map((matchResult: MatchResult) => { // Create compatibility insights from MatchResult,
  const baseScore = matchResult.compatibility.score || 70,
                const factors = matchResult.compatibility.factors || ['Similar lifestyle preferences',
  'Shared housing expectations';
                  'Compatible schedules'],
  const compatibilityInsights = { 
                  strengths: factors,
    potentialChallenges: [],
  lifestyleCompatibility: baseScore,
    valueAlignment: baseScore,
  habitCompatibility: baseScore,
    communicationStyle: 'Compatible communication preferences',
  recommendedActivities: [
                    'Getting to know each other',
  'Discussing shared interests']  },
  // Handle boosted status safely,
                let isBoosted = false,
  try {
                  isBoosted =,
  matchResult.boosted ||;
                    (Array.isArray(boostedUserIds) &&,
  boostedUserIds.includes(matchResult.profile.id))
                } catch (err: any) {
  console.warn('Could not determine boost status:', err?.message) }
                return { profile     : matchResult.profile,
  compatibility: matchResult.compatibility,
    compatibilityInsights: compatibilityInsights,
  boosted: isBoosted } as MatchData
  }),
  setMatches(formattedMatches)
  setCurrentIndex(0),
  setNoMoreMatches(false)
  console.log('Successfully fetched basic matches as fallback:', formattedMatches.length)
  }
        } catch (basicMatchError) {
  console.error('Basic match fallback failed:', basicMatchError),
  setNoMoreMatches(true)
          setError('Failed to load matches. Please try again.') }
      }
  } catch (err) {
      // Simplified error handling to prevent recursive logging,
  console.error('Error fetching matches:', err instanceof Error ? err.message     : String(err)),
  setNoMoreMatches(true)
      setError('Failed to load matches. Please try again.') } finally {
      setLoading(false),
  setRefreshing(false)
    }
  }
  // Initial load,
  useEffect(() => {
    fetchMatches() }, [user?.id]);
  // Handle refresh
  const handleRefresh = async () => {
  setRefreshing(true)
    await fetchMatches(true),
  if (onRefresh) onRefresh()
  },
  // Handle like action,
  const handleLike = async () => {
  if (!user?.id || currentIndex >= matches.length) return null,
    const currentMatch = matches[currentIndex],
  try {;
      // Save the match preference,
  const success = await matchingService.saveMatchPreference(user.id);
        currentMatch.profile.id, ,
  'like')
      ),
  if (!success) {
        logger.error('Failed to save match preference', 'SwipeMatchDeck.handleLike', {
  userId   : user.id
          matchId: currentMatch.profile.id) })
        return null
  }
      // Check if this is a mutual match,
  const isMutualMatch = await matchingService.checkMutualMatchExists(user.id, ,
  currentMatch.profile.id)
      ),
  // If this is a mutual match, trigger the celebration,
  if (isMutualMatch) {
        logger.info('Match found!', 'SwipeMatchDeck.handleLike', {
  userId: user.id),
    matchedWithId: currentMatch.profile.id) })
        try {
  // Track the match in analytics,
          await supabase.from('user_analytics').insert({
  user_id: user.id),
    event_type: 'match_created'),
  event_data: {
      matched_with: currentMatch.profile.id,
  compatibility_score: currentMatch.compatibility.score),
    timestamp: new Date().toISOString() }
            created_at: new Date().toISOString()
  })
        } catch (analyticsError) { // Just log the error but don't fail the operation,
  logger.warn('Failed to track match analytics', 'SwipeMatchDeck.handleLike', {
  error: )
              analyticsError instanceof Error ? analyticsError.message     : String(analyticsError) })
  }
        // Set matched profile and show celebration modal,
  setMatchedProfile(currentMatch.profile)
        setShowMatchModal(true)
  }
      // Move to the next card with a slight delay if it's a match,
  setTimeout(
        () => {
  setCurrentIndex(prevIndex => prevIndex + 1)
          // If we're at the end, check if we need to show no more matches,
  if (currentIndex + 1 >= matches.length) {
            setNoMoreMatches(true),
  if (onMatchesEmpty) onMatchesEmpty()
          }
  };
        isMutualMatch ? 300     : 0,
  )
    } catch (err) { logger.error('Error in handleLike', 'SwipeMatchDeck.handleLike', {
  error: err instanceof Error ? err.message   : String(err),
    userId: user.id,
  profileId: currentMatch.profile.id })
    }
  }
  // Handle dislike action,
  const handleDislike = async () => {
    if (!user?.id || currentIndex >= matches.length) return null,
  const currentMatch = matches[currentIndex],
  try {;
      // Save the match preference,
  await matchingService.saveMatchPreference(user.id, currentMatch.profile.id, 'dislike'),
  // Move to next card,
      setCurrentIndex(prevIndex => prevIndex + 1),
  // If we've reached the end, check if we need to fetch more,
  if (currentIndex + 1 >= matches.length) {
        setNoMoreMatches(true),
  if (onMatchesEmpty) onMatchesEmpty()
      }
  } catch (error) {
      console.error('Error handling dislike    : ' error) }
  },
  // Handle super like action
  const handleSuperLike = async () => {
  if (!user?.id || currentIndex >= matches.length) return null,
    const currentMatch = matches[currentIndex],
  try {
      // Save the match preference,
  await matchingService.saveMatchPreference(user.id, currentMatch.profile.id, 'superlike'),
  // Check if this is a mutual match,
      const isMutualMatch = await matchingService.checkMutualMatchExists(user.id, ,
  currentMatch.profile.id)
      ),
  if (isMutualMatch) {
        // Show match celebration modal,
  setMatchedProfile(currentMatch.profile)
        setShowMatchModal(true) }
      // Move to next card,
  setCurrentIndex(prevIndex => prevIndex + 1)
      // If we've reached the end, check if we need to fetch more,
  if (currentIndex + 1 >= matches.length) {
        setNoMoreMatches(true),
  if (onMatchesEmpty) onMatchesEmpty()
      }
  } catch (error) {
      console.error('Error handling super like     : ' error) }
  },
  // Handle view profile
  const handleViewProfile = (userId: string) => {
  try {
      navigateToProfile(userId, { source: 'match' })
  } catch (error) {
      logger.error('Failed to navigate to profile', 'SwipeMatchDeck.handleViewProfile', {
  error: error instanceof Error ? error.message    : String(error)
        userId })
    }
  }
  // Handle starting a conversation with a match,
  const handleStartMessaging = async (userId: string, name: string, selectedMessage?: string) => {
  if (!user?.id) return null,
    try {
  setStartingChat(true)
      logger.info('Starting messaging with match', 'SwipeMatchDeck.handleStartMessaging', {
  userId, ,
  userIdRedacted   : userId.slice(-4)
      }),
  const messageToSend = selectedMessage ||
        'Hi there! I saw your profile and wanted to connect about possibly being roommates.',
  setInitialMessage(messageToSend)
      setMessageSource(selectedMessage ? 'suggestion'      : 'default'),
  // Use the new standardized navigation function
      const success = await createChatWithMatchAndNavigate(user.id, userId, name, messageToSend),
  if (!success) {
        throw new Error('Failed to create chat') }
    } catch (error) {
  logger.error('Error starting messaging with match', 'SwipeMatchDeck.handleStartMessaging', {
  error: error instanceof Error ? error.message   : String(error)
        userId })
      console.error('Error starting messaging:', error),
  // Show an error or try a fallback method // Fallback to transition UI
      setShowMessageTransition(true)
  } finally {
      setStartingChat(false) }
  },
  // Render loading state,
  if (loading) {
  return (
      <View style= {styles.centerContainer}>,
  <ActivityIndicator size='large' color={{theme.colors.primary} /}>
        <Text style={styles.loadingText}>Finding your potential matches...</Text>,
  </View>
    )
  }
  // Render error state,
  if (error) {
    return (
  <View style= {styles.centerContainer}>
        <Text style={styles.errorText}>,
  {error || 'Something went wrong while fetching matches'}
        </Text>,
  <TouchableOpacity style={styles.retryButton} onPress={() => fetchMatches()}>
          <RefreshCw size={16} color={{theme.colors.primary} /}>,
  <Text style={styles.retryText}>Try again</Text>
        </TouchableOpacity>,
  </View>
    )
  }
  // Render empty state when no matches are available,
  if (matches.length === 0 && !loading) {
    return (
  <View style={styles.centerContainer}>
        <Text style={styles.noMatchesTitle}>No matches available</Text>,
  <Text style={styles.noMatchesText}>
          We couldn't find any potential matches for you at the moment. Try again later or adjust, ,
  your preferences.
        </Text>,
  <TouchableOpacity style={styles.refreshButton} onPress={handleRefresh}>
          <RefreshCw size={16} color={{theme.colors.primary} /}>,
  <Text style={styles.refreshText}>Refresh matches</Text>
        </TouchableOpacity>,
  </View>
    )
  }
  // Calculate if this is the last card,
  const isLastCard = currentIndex === matches.length - 1,
  return (
  <View style= {styles.container}>
      {/* Active match cards */}
  {matches.map((match,  index) => {
  if (index < currentIndex) return null,
        return (
  <SwipeMatchCard
            key= {match.profile.id},
  profile={match.profile}
            compatibility={match.compatibility},
  compatibilityInsights={match.compatibilityInsights}
            onLike={handleLike},
  onDislike={handleDislike}
            onSuperLike={handleSuperLike},
  onViewProfile={() => handleViewProfile(match.profile.id)}
            isLastCard={isLastCard},
  boosted={match.boosted}
          />,
  )
      })},
  {/* Match celebration modal */}
      {showMatchModal && matchedProfile && (
  <MatchCelebrationModal
          visible={showMatchModal},
  onClose={() => setShowMatchModal(false)}
          matchedUser={   {
  id: matchedProfile.idname:  `${matchedProfile.first_name || ''      } ${matchedProfile.last_name || ''}`.trim() ||;
  'User',
  display_name: matchedProfile.first_name || matchedProfile.display_name || '',
    avatar: matchedProfile.avatar_url || '',
  compatibility: 85, // Default compatibility score
  }}
          currentUser={   id: user?.id || ''avatar     : user?.user_metadata?.avatar_url || ''
     },
  onStartMessaging= {handleStartMessaging}
  onViewProfile={handleViewProfile},
  />
  )},
  {/* Match to message transition */}
  {showMessageTransition && matchedProfile && (
  <EnhancedMatchToMessageTransition
  visible={showMessageTransition},
  onClose={() => setShowMessageTransition(false)}
  matchedUser={   id: matchedProfile.id,
    first_name: matchedProfile.first_name || '',
  last_name: matchedProfile.last_name || '',
    avatar_url: matchedProfile.avatar_url || ''compatibility_score: 85// Default compatibility score }
          onTransitionComplete = {() => {
  if (matchedProfile) {
              handleMessageTransitionComplete(
  matchedProfile.id
                matchedProfile.first_name || 'User',
  )
            }
  }}
        />,
  )}
      {/* Tip at the bottom */}
  <View style={styles.tipContainer}>
        <Info size={14} color={{theme.colors.gray} /}>,
  <Text style={styles.tipText}>Swipe right to like, left to pass, or up to super like</Text>,
  </View>
      {/* Loading overlay for chat transition */}
  {startingChat && (
        <View style={styles.overlayContainer}>,
  <View style={styles.loadingOverlay}>
            <ActivityIndicator size='large' color={{theme.colors.primary} /}>,
  <Text style={styles.loadingText}>Starting conversation...</Text>
          </View>,
  </View>
      )},
  </View>
  )
  }
// Styles function moved to top of file,
  export default SwipeMatchDeck