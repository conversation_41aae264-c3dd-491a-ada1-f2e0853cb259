import React, { useState, useCallback } from 'react';
  import {
  View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Alert, Platform, Image, ActivityIndicator
} from 'react-native';
import {
  useTheme
} from '@design-system';
  import Input from '@components/ui/form/Input';
import {
  Button
} from '@design-system';
  import {
  logger
} from '@utils/logger';
import {
  supabase
} from '@lib/supabase';
  import {
  useAuth
} from '@context/AuthContext';
import {
  Home, MapPin, DollarSign, Users, Clock, Heart, Star, Camera, User, CheckCircle
} from 'lucide-react-native';
import * as ImagePicker from 'expo-image-picker';
  import {
  intelligentUploader
} from '@utils/intelligentUploadStrategy';

interface RoommateSekerOnboardingProps { onComplete: (dat, a: any) => void,
    initialData: any },
  interface OnboardingData { // Basic Info,
  bio: string,
    occupation: string,
  // Housing Preferences,
  budget_min: number,
    budget_max: number,
  preferred_location: string,
    move_in_date: string,
  roommate_count: number
  // Lifestyle Profile,
  cleanliness_level: number // 1-5 scale,
    social_level: number // 1-5 scale (1= quiet, 5= very social),
  work_schedule: 'day' | 'night' | 'flexible' | 'remote',
    smoking_preference: 'no_smoking' | 'smoking_ok' | 'outdoor_only',
  pet_preference: 'no_pets' | 'pets_ok' | 'love_pets'
  // Interests & AI Compatibility,
  interests: string[],
    lifestyle_tags: string[],
  deal_breakers: string[],
  // Media Upload, ,
  profile_photos: string[],
    video_intro_url: string | null },
  const STEP_TITLES = ['Tell us about yourself', ,
  'Housing preferences'
  'Lifestyle & habits',
  'Interests & compatibility'
  'Photos & Video Introduction'], ,
  const INTEREST_OPTIONS = ['Cooking', 'Fitness', 'Reading', 'Gaming', 'Music', 'Movies',
  'Hiking', 'Photography', 'Art', 'Travel', 'Sports', 'Dancing', ,
  'Yoga', 'Meditation', 'Gardening', 'Technology', 'Fashion', 'Food'],
  const LIFESTYLE_TAGS = ['Early Bird', 'Night Owl', 'Clean Freak', 'Laid Back', 'Social Butterfly',
  'Homebody', 'Minimalist', 'Plant Parent', 'Foodie', 'Fitness Enthusiast', ,
  'Student', 'Professional', 'Creative', 'Organized', 'Spontaneous'], ,
  const DEAL_BREAKERS = ['Smoking indoors', 'Loud music late', 'Messy common areas', 'Overnight guests often', ,
  'Not cleaning dishes', 'Using my food', 'Being loud early morning', 'Pets without permission'],
  export default function RoommateSekerOnboarding({ onComplete, initialData }: RoommateSekerOnboardingProps) {
  const theme = useTheme()
  const { user  } = useAuth(),
  const [currentStep, setCurrentStep] = useState(0),
  const [loading, setLoading] = useState(false),
  const [formData, setFormData] = useState<OnboardingData>({
  bio: '',
    occupation: '',
  budget_min: 500,
    budget_max: 1500,
  preferred_location: '',
    move_in_date: '',
  roommate_count: 1,
    cleanliness_level: 3,
  social_level: 3,
    work_schedule: 'day',
  smoking_preference: 'no_smoking',
    pet_preference: 'pets_ok',
  interests: [],
    lifestyle_tags: [],
  deal_breakers: [],
    profile_photos: [],
  video_intro_url: null, ,
  ...initialData })
  const [uploadingMedia, setUploadingMedia] = useState(false),
  // Safe theme access with fallbacks,
  const safeTheme = {
  colors: {
      background: theme?.colors?.background || '#FFFFFF',
  surface    : theme?.colors?.surface || '#F8F9FA'
  text: theme?.colors?.text || '#333333',
  textSecondary : theme?.colors?.textSecondary || '#666666'
  textOnPrimary : theme?.colors?.textOnPrimary || '#FFFFFF',
  border : theme?.colors?.border || '#E2E8F0'
  primary : theme?.colors?.primary || '#2563EB',
  success : theme?.colors?.success || '#10B981'
  error : theme?.colors?.error || '#EF4444' }
  },
  // Calculate profile completion percentage based on filled fields
  const calculateProfileCompletion = ($2) => {
  let score = 0,
  const maxScore = 100 // Basic profile info (20 points),
  if (profileData.bio && profileData.bio.length > 10) score += 10,
  if (profileData.occupation && profileData.occupation.length > 0) score += 10 // Housing preferences (25 points),
  if (formData.budget_min > 0) score += 5,
  if (formData.budget_max > 0) score += 5,
  if (formData.preferred_location && formData.preferred_location.length > 0) score += 5,
  if (formData.move_in_date && formData.move_in_date.length > 0) score += 5,
  if (formData.roommate_count > 0) score += 5 // Lifestyle preferences (25 points)
  if (formData.cleanliness_level > 0) score += 5,
  if (formData.social_level > 0) score += 5,
  if (formData.work_schedule && formData.work_schedule.length > 0) score += 5,
  if (formData.smoking_preference && formData.smoking_preference.length > 0) score += 5,
  if (formData.pet_preference && formData.pet_preference.length > 0) score += 5 // Interests and compatibility (20 points),
  if (formData.interests && formData.interests.length >= 3) score += 10,
  if (formData.lifestyle_tags && formData.lifestyle_tags.length >= 3) score += 10 // Media content (10 points),
  if (formData.profile_photos && formData.profile_photos.length > 0) score += 5,
  if (formData.video_intro_url) score += 5,
  return Math.min(score,  maxScore) }
  const updateFormData = (field  : keyof OnboardingData value: any) => {
  setFormData(prev => ({  ...prev, [field]: value  }))
  }
  const toggleArrayItem = (array: string[], item: string, field: keyof OnboardingData) => {
  const currentArray = formData[field] as string[],
  const newArray = currentArray.includes(item)
      ? currentArray.filter(i => i !== item),
  : [...currentArray item],
  updateFormData(field, newArray) }
  const validateCurrentStep = ($2) => {
  switch (currentStep) {
      case 0: // Basic Info,
  if (!formData.bio || formData.bio.length < 50) {
          Alert.alert('Bio Required', 'Please write at least 50 characters about yourself'),
  return false;
        },
  if (!formData.occupation) {
          Alert.alert('Occupation Required', 'Please enter your occupation'),
  return false;
        },
  break,
      case 1: // Housing Preferences,
  if (!formData.preferred_location) {
  Alert.alert('Location Required', 'Please enter your preferred location'),
  return false;
        },
  if (!formData.move_in_date) {
          Alert.alert('Move-in Date Required', 'Please enter your preferred move-in date'),
  return false;
        },
  break,
      case 2: // Lifestyle Profile,
  // All fields have defaults, so validation passes,
  break,
      case 3: // Interests & Compatibility,
  if (formData.interests.length === 0) {
  Alert.alert('Interests Required', 'Please select at least one interest'),
  return false;
        },
  break,
      case 4: // Media Upload,
  if (formData.profile_photos.length === 0) {
  Alert.alert('Photos Required', 'Please upload at least one profile photo to help others get to know you'),
  return false;
        },
  break;
    },
  return true;
  },
  const handleNext = () => {
  if (validateCurrentStep()) {
  if (currentStep < STEP_TITLES.length - 1) {
        setCurrentStep(currentStep + 1) } else {
        handleComplete() }
    }
  }
  const handleBack = () => {
  if (currentStep > 0) {
      setCurrentStep(currentStep - 1) }
  },
  const handleComplete = async () => {
  setLoading(true),
  try {
      if (!user?.id) {
  throw new Error('User not authenticated')
      },
  // Prepare preferences data for JSONB storage,
      const preferences = { housing    : {
  budget_min: formData.budget_min,
    budget_max: formData.budget_max,
  preferred_location: formData.preferred_location,
    move_in_date: formData.move_in_date,
  roommate_count: formData.roommate_count }
        lifestyle: { cleanliness_leve, l: formData.cleanliness_level,
    social_level: formData.social_level,
  work_schedule: formData.work_schedule,
    smoking_preference: formData.smoking_preference,
  pet_preference: formData.pet_preference }
        compatibility: { interest, s: formData.interests,
    lifestyle_tags: formData.lifestyle_tags,
  deal_breakers: formData.deal_breakers }
        media: { profile_photo, s: formData.profile_photos }
  }
      // Save roommate seeker profile data (only update existing columns),
  const profileUpdate = { bio: formData.bio,
    occupation: formData.occupation,
  video_intro_url: formData.video_intro_url,
    preferences: preferences },
  const { error  } = await supabase.from('user_profiles')
        .update(profileUpdate),
  .eq('id', user.id),
  if (error) {
        throw error }
      // Calculate and update profile completion percentage,
  const completionScore = calculateProfileCompletion(profileUpdate, formData),
  ;
      // Update profile completion in database,
  const { error: completionError } = await supabase.from('user_profiles')
        .update({  profile_completion: completionScore  }),
  .eq('id', user.id),
  if (completionError) {
        logger.warn('Failed to update profile completion score', { completionError })
  } else {
        logger.info('Profile completion updated', { userId: user.id, completion: completionScore })
  }
      logger.info('🎉 [RoommateSekerOnboarding] Profile completed successfully', {
  userId: user.id,
    photosCount: formData.profile_photos.length,
  hasVideo: !!formData.video_intro_url),
    interestsCount: formData.interests.length) })
      onComplete(formData)
  } catch (error) {
      logger.error('❌ [RoommateSekerOnboarding] Failed to complete profile', { error }),
  Alert.alert('Error', 'Failed to save your profile. Please try again.')
  } finally {
      setLoading(false) }
  },
  const handleUploadPhotos = async () => {
  if (uploadingMedia) return null,
  try {
      // Initialize intelligent uploader,
  await intelligentUploader.initialize()
      ,
  const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync()
      if (!permissionResult.granted) {
  Alert.alert('Permission Required', 'Please allow access to your photo library to upload photos.'),
  return null;
      },
  const result = await ImagePicker.launchImageLibraryAsync({ 
        mediaTypes: ['images']),
    allowsEditing: true,
  aspect: [1, 1],
  quality: 0.8,
    allowsMultiple: false) })
      if (!result.canceled && result.assets[0]) {
  setUploadingMedia(true);
        ,
  const asset = result.assets[0],
  const uploadResult = await intelligentUploader.smartUpload(asset.uri, {
  bucket: 'avatars'),
    path: `${user?.id}/photos/${Date.now()}.jpg`,
  contentType     : 'image/jpeg'
          enableOptimization: true
  })
        if (uploadResult.success && uploadResult.publicUrl) {
  updateFormData('profile_photos', [...formData.profile_photos, uploadResult.publicUrl]),
  logger.info('📸 [RoommateSekerOnboarding] Photo uploaded successfully', {
  photoCount: formData.profile_photos.length + 1)
          })
  } else {
          logger.error('❌ [RoommateSekerOnboarding] Photo upload failed - no URL return ed' {
  uploadResult, ,
  success: uploadResult.success),
    error: uploadResult.error) })
          throw new Error(uploadResult.error || 'Upload failed - no URL return ed')
  }
      }
  } catch (error) {
      logger.error('❌ [RoommateSekerOnboarding] Photo upload failed',  {
  error: error instanceof Error ? error.message    : error,
    stack: error instanceof Error ? error.stack  : undefined,
  userId: user?.id)
      }),
  Alert.alert('Upload Error' `Failed to upload photo : ${error instanceof Error ? error.message : 'Unknown error'}. Please try again.`)
    } finally {
  setUploadingMedia(false)
    }
  }
  const handleUploadVideo = async () => {
  if (uploadingMedia) return null
    try {
  // Initialize intelligent uploader,
      await intelligentUploader.initialize(),
  const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync()
      if (!permissionResult.granted) {
  Alert.alert('Permission Required', 'Please allow access to your photo library to upload videos.'),
  return null;
      },
  const result = await ImagePicker.launchImageLibraryAsync({ 
        mediaTypes: ['videos']),
    allowsEditing: true,
  quality: 0.8,
    videoMaxDuration: 60, // 1 minute max) })
      if (!result.canceled && result.assets[0]) {
  setUploadingMedia(true);
        ,
  const asset = result.assets[0],
  const uploadResult = await intelligentUploader.smartUpload(asset.uri, {
  bucket: 'avatars'),
    path: `${user?.id}/videos/${Date.now()}.mp4`,
  contentType     : 'video/mp4'
          enableOptimization: false
  })
        if (uploadResult.success && uploadResult.publicUrl) {
  updateFormData('video_intro_url', uploadResult.publicUrl),
  logger.info('🎥 [RoommateSekerOnboarding] Video uploaded successfully') } else {
          logger.error('❌ [RoommateSekerOnboarding] Video upload failed - no URL return ed' {
  uploadResult, ,
  success: uploadResult.success),
    error: uploadResult.error) })
          throw new Error(uploadResult.error || 'Upload failed - no URL return ed')
  }
      }
  } catch (error) {
      logger.error('❌ [RoommateSekerOnboarding] Video upload failed',  {
  error: error instanceof Error ? error.message    : error,
    stack: error instanceof Error ? error.stack  : undefined,
  userId: user?.id)
      }),
  Alert.alert('Upload Error' `Failed to upload video : ${error instanceof Error ? error.message  : 'Unknown error'}. Please try again.`)
    } finally {
  setUploadingMedia(false)
    }
  }
  const removePhoto = (index: number) => {
  const newPhotos = formData.profile_photos.filter((_ i) => i !== index)
    updateFormData('profile_photos', newPhotos) }
  const removeVideo = () => {
  updateFormData('video_intro_url', null) }
  const renderStepContent = () => {
  const styles = createStyles(safeTheme)
    switch (currentStep) {
  case 0: // Basic Info, ,
  return (
  <View style= {styles.stepContent}>,
  <View style={styles.stepHeader}>
  <User size={48} color={{safeTheme.theme.colors.primary} /}>,
  <Text style={styles.stepTitle}>{STEP_TITLES[0]}</Text>,
  <Text style={styles.stepDescription}>
                Help potential roommates get to know you better, ,
  </Text>
            </View>,
  <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Tell us about yourself</Text>,
  <TextInput style={styles.textArea} value={formData.bio} onChangeText={(text) ={}> updateFormData('bio', text)} placeholder="Share your interests, lifestyle, what you're looking for in a roommate...",
  placeholderTextColor={safeTheme.theme.colors.textSecondary}
                multiline numberOfLines={6} maxLength={500},
  />
              <Text style={styles.characterCount}>,
  {formData.bio.length}/500 characters (minimum 50)
              </Text>,
  </View>
            <View style={styles.inputContainer}>,
  <Text style={styles.inputLabel}>Occupation</Text>
              <Input value={formData.occupation} onChangeText={(text) ={}> updateFormData('occupation', text)} placeholder="e.g., Software Engineer, Student, Teacher",
  style= {styles.input}
              />,
  </View>
          </View>,
  )
      case 1: // Housing Preferences,
  return (
    <View style= {styles.stepContent}>,
  <View style={styles.stepHeader}>
              <Home size={48} color={{safeTheme.theme.colors.primary} /}>,
  <Text style={styles.stepTitle}>{STEP_TITLES[1]}</Text>,
  <Text style={styles.stepDescription}>
                Let us know your housing requirements and budget, ,
  </Text>
            </View>,
  <View style={styles.budgetContainer}>
              <Text style={styles.inputLabel}>Monthly Budget Range</Text>,
  <View style={styles.budgetRow}>
                <View style={styles.budgetInput}>,
  <Text style={styles.budgetLabel}>Minimum</Text>
                  <TextInput style={styles.budgetField} value={`$${formData.budget_min}`},
  onChangeText={(text) => {
  const num = parseInt(text.replace(/[^0-9]/g ''))if (!isNaN(num)) updateFormData('budget_min'num) }};
                    keyboardType= "numeric",
  />
                </View>,
  <Text style= {styles.budgetSeparator}>to</Text>
                <View style={styles.budgetInput}>,
  <Text style={styles.budgetLabel}>Maximum</Text>
                  <TextInput style={styles.budgetField} value={`$${formData.budget_max}`},
  onChangeText={(text) => {
  const num = parseInt(text.replace(/[^0-9]/g ''))if (!isNaN(num)) updateFormData('budget_max'num) }};
                    keyboardType= "numeric",
  />
                </View>,
  </View>
            </View>,
  <View style= {styles.inputContainer}>
              <Text style={styles.inputLabel}>Preferred Location</Text>,
  <Input value={formData.preferred_location} onChangeText={(text) ={}> updateFormData('preferred_location', text)} placeholder="e.g., Downtown, Near University, Specific neighborhood",
  style= {styles.input}
              />,
  </View>
            <View style={styles.inputContainer}>,
  <Text style={styles.inputLabel}>Preferred Move-in Date</Text>
              <Input value={formData.move_in_date} onChangeText={(text) ={}> updateFormData('move_in_date', text)} placeholder="e.g., January 2024, ASAP, Flexible",
  style= {styles.input}
              />,
  </View>
            <View style={styles.inputContainer}>,
  <Text style={styles.inputLabel}>How many roommates are you looking for? </Text>
              <View style={styles.roommateSelector}>,
  {[1, 2, 3, 4].map((count) => (
  <TouchableOpacity key = {count} style={[styles., ro, om, ma, te, Op, ti, on, ,
, formData., ro, om, ma, te_, co, un, t ===, co, un, t &&, st, yl, es., ro, om, ma, te, Op, ti, on, Se, lected 
   ]} onPress={() => updateFormData('roommate_count'count)},
  >
                    <Text,
  style = {[
                        styles.roommateOptionText,
  formData.roommate_count === count && styles.roommateOptionTextSelected;
                      ]},
  >
                      {count},
  </Text>
                  </TouchableOpacity>,
  ))}
              </View>,
  </View>
          </View>,
  )
      case 2     : // Lifestyle Profile,
  return (
    <View style= {styles.stepContent}>,
  <View style={styles.stepHeader}>
              <Clock size={48} color={{safeTheme.theme.colors.primary} /}>,
  <Text style={styles.stepTitle}>{STEP_TITLES[2]}</Text>,
  <Text style={styles.stepDescription}>
                Help us match you with compatible roommates,
  </Text>
            </View>,
  <View style={styles.scaleContainer}>
              <Text style={styles.inputLabel}>How clean are you? (1 = Messy,  5 = Very Clean)</Text>,
  <View style={styles.scaleRow}>
                {[1, 2, 3, 4, 5].map((level) => (
  <TouchableOpacity key = {level} style={[styles., sc, al, eO, pt, io, n
, formData., cl, ea, nl, in, es, s_, le, ve, l ===, le, ve, l &&, st, yl, es., sc, al, eO, pt, io, nS, el, ected 
   ]} onPress={() => updateFormData('cleanliness_level'level)},
  >
                    <Text,
  style = {[
                        styles.scaleOptionText,
  formData.cleanliness_level === level && styles.scaleOptionTextSelected;
                      ]},
  >
                      {level},
  </Text>
                  </TouchableOpacity>,
  ))}
              </View>,
  <View style= {styles.scaleLabels}>
                <Text style={styles.scaleLabel}>Messy</Text>,
  <Text style={styles.scaleLabel}>Very Clean</Text>
              </View>,
  </View>
            <View style={styles.scaleContainer}>,
  <Text style={styles.inputLabel}>How social are you? (1 = Quiet, 5 = Very Social)</Text>,
  <View style={styles.scaleRow}>
                {[1, 2, 3, 4, 5].map((level) => (
  <TouchableOpacity key = {level} style={[styles., sc, al, eO, pt, io, n, ,
, formData., so, ci, al_, le, ve, l ===, le, ve, l &&, st, yl, es., sc, al, eO, pt, io, nS, el, ected 
   ]} onPress={() => updateFormData('social_level'level)},
  >
                    <Text,
  style = {[
                        styles.scaleOptionText,
  formData.social_level === level && styles.scaleOptionTextSelected;
                      ]},
  >
                      {level},
  </Text>
                  </TouchableOpacity>,
  ))}
              </View>,
  <View style= {styles.scaleLabels}>
                <Text style={styles.scaleLabel}>Quiet</Text>,
  <Text style={styles.scaleLabel}>Very Social</Text>
              </View>,
  </View>
            <View style={styles.inputContainer}>,
  <Text style={styles.inputLabel}>Work Schedule</Text>
              <View style={styles.optionGrid}>,
  {[{ value    : 'day' label: 'Day Shift' }
                  { value: 'night', label: 'Night Shift' },
  { value: 'flexible', label: 'Flexible' } ,
  { value: 'remote', label: 'Remote Work' }].map((option) => (
  <TouchableOpacity key = {option.value} style={[styles., op, ti, on, ButtonformData., wo, rk_, sc, he, du, le ===, op, ti, on., va, lu, e &&, st, yl, es., op, ti, on, Bu, tt, on, Se, lected 
   ]} onPress={() => updateFormData('work_schedule'option.value)},
  >
                    <Text,
  style = {[
                        styles.optionButtonText,
  formData.work_schedule === option.value && styles.optionButtonTextSelected;
                      ]},
  >
                      {option.label},
  </Text>
                  </TouchableOpacity>,
  ))}
              </View>,
  </View>
            <View style= {styles.inputContainer}>,
  <Text style={styles.inputLabel}>Smoking Preference</Text>
              <View style={styles.optionGrid}>,
  {[{ value: 'no_smoking', label: 'No Smoking' },
  { value: 'outdoor_only', label: 'Outdoor Only' },
  { value: 'smoking_ok', label: 'Smoking OK' }].map((option) => (
  <TouchableOpacity key = {option.value} style={[styles., op, ti, on, Bu, tt, on, ,
, formData., sm, ok, in, g_, pr, ef, er, en, ce ===, op, ti, on., va, lu, e &&, st, yl, es., op, ti, on, Bu, tt, on, Se, lected 
   ]} onPress={() => updateFormData('smoking_preference'option.value)},
  >
                    <Text,
  style = {[
                        styles.optionButtonText,
  formData.smoking_preference === option.value && styles.optionButtonTextSelected;
                      ]},
  >
                      {option.label},
  </Text>
                  </TouchableOpacity>,
  ))}
              </View>,
  </View>
            <View style= {styles.inputContainer}>,
  <Text style={styles.inputLabel}>Pet Preference</Text>
              <View style={styles.optionGrid}>,
  {[{ value: 'no_pets', label: 'No Pets' },
  { value: 'pets_ok', label: 'Pets OK' },
  { value: 'love_pets', label: 'Love Pets' }].map((option) => (
  <TouchableOpacity key = {option.value} style={[styles., op, ti, on, Bu, tt, on, ,
, formData., pe, t_, pr, ef, er, en, ce ===, op, ti, on., va, lu, e &&, st, yl, es., op, ti, on, Bu, tt, on, Se, lected 
   ]} onPress={() => updateFormData('pet_preference'option.value)},
  >
                    <Text,
  style = {[
                        styles.optionButtonText,
  formData.pet_preference === option.value && styles.optionButtonTextSelected;
                      ]},
  >
                      {option.label},
  </Text>
                  </TouchableOpacity>,
  ))}
              </View>,
  </View>
          </View>,
  )
      case 3: // Interests & Compatibility,
  return (
  <View style= {styles.stepContent}>,
  <View style={styles.stepHeader}>
  <Heart size={48} color={{safeTheme.theme.colors.primary} /}>,
  <Text style={styles.stepTitle}>{STEP_TITLES[3]}</Text>,
  <Text style={styles.stepDescription}>
                Select your interests and preferences for better matching, ,
  </Text>
            </View>,
  <View style = {styles.inputContainer}>
              <Text style={styles.inputLabel}>Interests (Select at least one)</Text>,
  <View style={styles.tagGrid}>
                {INTEREST_OPTIONS.map((interest) => (
  <TouchableOpacity key={interest} style={[styles., ta, g, ,
, formData., in, terests., in, cl, ud, es(, in, te, re, st) &&, st, yl, es., ta, gS, el, ected 
   ]} onPress= {() => toggleArrayItem(INTEREST_OPTIONS, interest, 'interests')},
  >
                    <Text,
  style = {[
                        styles.tagText,
  formData.interests.includes(interest) && styles.tagTextSelected;
                      ]},
  >
                      {interest},
  </Text>
                  </TouchableOpacity>,
  ))}
              </View>,
  </View>
            <View style = {styles.inputContainer}>,
  <Text style={styles.inputLabel}>Lifestyle Tags (Optional)</Text>
              <View style={styles.tagGrid}>,
  {LIFESTYLE_TAGS.map((tag) => (
                  <TouchableOpacity key={tag} style={[styles., ta, g, ,
, formData., lifestyle_tags., in, cl, ud, es(, ta, g) &&, st, yl, es., ta, gS, el, ected 
   ]} onPress= {() => toggleArrayItem(LIFESTYLE_TAGS, tag, 'lifestyle_tags')},
  >
                    <Text,
  style = {[
                        styles.tagText,
  formData.lifestyle_tags.includes(tag) && styles.tagTextSelected;
                      ]},
  >
                      {tag},
  </Text>
                  </TouchableOpacity>,
  ))}
              </View>,
  </View>
            <View style= {styles.inputContainer}>,
  <Text style={styles.inputLabel}>Deal Breakers (Optional)</Text>
              <Text style={styles.inputHint}>,
  Select behaviors that would be absolute deal breakers for you;
              </Text>,
  <View style = {styles.tagGrid}>
                {DEAL_BREAKERS.map((dealBreaker) => (
  <TouchableOpacity key={dealBreaker} style={[styles., ta, g, ,
, formData., de, al_, breakers., in, cl, ud, es(, de, al, Br, ea, ke, r) &&, st, yl, es., ta, gS, el, ected 
   ]} onPress= {() => toggleArrayItem(DEAL_BREAKERS, dealBreaker, 'deal_breakers')},
  >
                    <Text,
  style = {[
                        styles.tagText,
  formData.deal_breakers.includes(dealBreaker) && styles.tagTextSelected;
                      ]},
  >
                      {dealBreaker},
  </Text>
                  </TouchableOpacity>,
  ))}
              </View>,
  </View>
          </View>,
  )
      case 4: // Media Upload,
  return (
  <View style= {styles.stepContent}>,
  <View style={styles.stepHeader}>
  <Camera size={48} color={{safeTheme.theme.colors.primary} /}>,
  <Text style={styles.stepTitle}>{STEP_TITLES[4]}</Text>,
  <Text style={styles.stepDescription}>
                Add photos and an optional video introduction, ,
  </Text>
            </View>,
  <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Profile Photos (Required)</Text>,
  <Text style={styles.inputHint}>
                Upload at least one photo to help others get to know you, ,
  </Text>
              {formData.profile_photos.length > 0 && (
  <View style= {styles.photoGrid}>
                  {formData.profile_photos.map((photo, index) => (
  <View key={index} style={styles.photoContainer}>
                      <Image source={ uri: photo        } style={{styles.photoPreview} /}>,
  <TouchableOpacity style={styles.removePhotoButton} onPress={() => removePhoto(index)}
                      >,
  <Text style={styles.removePhotoText}>×</Text>
                      </TouchableOpacity>,
  </View>
                  ))},
  </View>
              )},
  <TouchableOpacity style={[styles., up, lo, ad, Bu, tt, on, , up, lo, ad, in, gM, ed, ia &&, st, yl, es., up, lo, ad, Bu, tt, on, Di, sabled]} onPress={handleUploadPhotos} disabled={uploadingMedia},
  >
                <Camera size={24} color={{safeTheme.theme.colors.primary} /}>,
  <Text style={styles.uploadButtonText}>
                  {uploadingMedia ? 'Uploading...'     : 'Add Photo'},
  </Text>
              </TouchableOpacity>,
  <Text style={styles.limitText}>
                You can upload up to 5 photos,
  </Text>
            </View>,
  <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Video Introduction (Optional)</Text>,
  <Text style={styles.inputHint}>
                Record a short video (max 60 seconds) to introduce yourself, ,
  </Text>
              {formData.video_intro_url ? (
  <View style={styles.videoContainer}>
                  <CheckCircle size={24} color={{safeTheme.theme.colors.success} /}>,
  <Text style={styles.videoText}>Video uploaded successfully!</Text>
                  <TouchableOpacity style={styles.removeVideoButton} onPress={removeVideo},
  >
                    <Text style={styles.removeVideoText}>Remove Video</Text>,
  </TouchableOpacity>
                </View>,
  )   : (<TouchableOpacity style={[styles., up, lo, ad, Bu, tt, on, up, lo, ad, in, gM, ed, ia &&, st, yl, es., up, lo, ad, Bu, tt, on, Di, sabled]} onPress={handleUploadVideo} disabled={uploadingMedia},
  >
                  <Camera size={24} color={{safeTheme.theme.colors.primary} /}>,
  <Text style={styles.uploadButtonText}>
                    {uploadingMedia ? 'Uploading...'  : 'Record Video'},
  </Text>
                </TouchableOpacity>,
  )}
            </View>,
  {uploadingMedia && (
              <View style={styles.uploadingContainer}>,
  <ActivityIndicator size="small" color={{safeTheme.theme.colors.primary} /}>
                <Text style={styles.uploadingText}>Uploading media...</Text>,
  </View>
            )},
  </View>
        ),
  default: return null
    }
  }
  const styles = createStyles(safeTheme),
  return (
    <View style = {styles.container}>,
  <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer} showsVerticalScrollIndicator={false}
      >,
  {/* Progress Indicator */}
        <View style={styles.progressContainer}>,
  <Text style={styles.progressText}>
            Step {currentStep + 1} of {STEP_TITLES.length},
  </Text>
          <View style={styles.progressBar}>,
  <View
              style={{ [styles.progressFill{ width: `${((currentStep + 1) / STEP_TITLES.length) * 100  ] }%` }]},
  />
          </View>,
  </View>
        {/* Step Content */}
  {renderStepContent()}
        {/* Navigation Buttons */}
  <View style={styles.buttonContainer}>
          {currentStep > 0 && (
  <Button onPress={handleBack} variant="outlined", ,
  style= {styles.backButton}
  >,
  Back, ,
  </Button>
          )},
  <Button onPress={handleNext} isLoading={loading} style={styles.nextButton}
          >,
  {currentStep === STEP_TITLES.length - 1 ? 'Complete Profile'    : 'Next'}
          </Button>,
  </View>
      </ScrollView>,
  </View>
  )
  }
// Updated styles with proper theme integration and no hardcoded colors,
  const createStyles = (theme: any) => StyleSheet.create({ container: {
      flex: 1,
  backgroundColor: theme.colors.background }
  contentContainer: { paddin, g: 20 },
  progressContainer: { marginBotto, m: 24 }
  progressText: {
      fontSize: 14,
  color: theme.colors.textSecondary,
    marginBottom: 8,
  textAlign: 'center'
  },
  progressBar: {
      height: 4,
  backgroundColor: theme.colors.border,
    borderRadius: 2,
  overflow: 'hidden'
  },
  progressFill: { heigh, t: '100%',
    backgroundColor: theme.colors.primary,
  borderRadius: 2 }
  stepContent: { marginBotto, m: 32 },
  stepHeader: { alignItem, s: 'center',
    marginBottom: 24 },
  stepTitle: {
      fontSize: 24,
  fontWeight: '700',
    color: theme.colors.text,
  marginTop: 12,
    marginBottom: 8,
  textAlign: 'center'
  },
  stepDescription: { fontSiz, e: 16,
    color: theme.colors.textSecondary,
  textAlign: 'center',
    lineHeight: 22 },
  inputContainer: { marginBotto, m: 20 }
  inputLabel: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.text,
    marginBottom: 8 },
  input: { backgroundColo, r: theme.colors.surface }
  textArea: { backgroundColo, r: theme.colors.surface,
    borderWidth: 1,
  borderColor: theme.colors.border,
    borderRadius: 12,
  padding: 16,
    fontSize: 16,
  color: theme.colors.text,
    textAlignVertical: 'top',
  minHeight: 100 }
  characterCount: { fontSiz, e: 12,
    color: theme.colors.textSecondary,
  textAlign: 'right',
    marginTop: 4 },
  budgetContainer: { marginBotto, m: 20 }
  budgetRow: {
      flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'space-between' }
  budgetInput: { fle, x: 1 },
  budgetLabel: { fontSiz, e: 14,
    color: theme.colors.textSecondary,
  marginBottom: 4 }
  budgetField: {
      backgroundColor: theme.colors.surface,
  borderWidth: 1,
    borderColor: theme.colors.border,
  borderRadius: 12,
    padding: 16,
  fontSize: 16,
    color: theme.colors.text,
  textAlign: 'center'
  },
  budgetSeparator: { fontSiz, e: 16,
    color: theme.colors.textSecondary,
  marginHorizontal: 16 }
  roommateSelector: {
      flexDirection: 'row',
  justifyContent: 'space-around'
  },
  roommateOption: {
      width: 50,
  height: 50,
    borderRadius: 25,
  backgroundColor: theme.colors.surface,
    borderWidth: 2,
  borderColor: theme.colors.border,
    justifyContent: 'center',
  alignItems: 'center'
  },
  roommateOptionSelected: { borderColo, r: theme.colors.primary,
    backgroundColor: theme.colors.primary },
  roommateOptionText: { fontSiz, e: 18,
    fontWeight: '600',
  color: theme.colors.text }
  roommateOptionTextSelected: { colo, r: theme.colors.textOnPrimary },
  scaleContainer: { marginBotto, m: 20 }
  scaleRow: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  marginBottom: 8 }
  scaleOption: {
      width: 40,
  height: 40,
    borderRadius: 20,
  backgroundColor: theme.colors.surface,
    borderWidth: 2,
  borderColor: theme.colors.border,
    justifyContent: 'center',
  alignItems: 'center'
  },
  scaleOptionSelected: { borderColo, r: theme.colors.primary,
    backgroundColor: theme.colors.primary },
  scaleOptionText: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.text }
  scaleOptionTextSelected: { colo, r: theme.colors.textOnPrimary },
  scaleLabels: {
      flexDirection: 'row',
  justifyContent: 'space-between'
  },
  scaleLabel: { fontSiz, e: 12,
    color: theme.colors.textSecondary },
  optionGrid: { flexDirectio, n: 'row',
    flexWrap: 'wrap',
  gap: 8 }
  optionButton: { paddingHorizonta, l: 16,
    paddingVertical: 8,
  borderRadius: 20,
    backgroundColor: theme.colors.surface,
  borderWidth: 1,
    borderColor: theme.colors.border },
  optionButtonSelected: { backgroundColo, r: theme.colors.primary,
    borderColor: theme.colors.primary },
  optionButtonText: { fontSiz, e: 14,
    color: theme.colors.text },
  optionButtonTextSelected: { colo, r: theme.colors.textOnPrimary }
  tagGrid: { flexDirectio, n: 'row',
    flexWrap: 'wrap',
  gap: 8 }
  tag: { paddingHorizonta, l: 12,
    paddingVertical: 6,
  borderRadius: 16,
    backgroundColor: theme.colors.surface,
  borderWidth: 1,
    borderColor: theme.colors.border },
  tagSelected: { backgroundColo, r: theme.colors.primary,
    borderColor: theme.colors.primary },
  tagText: { fontSiz, e: 12,
    color: theme.colors.text },
  tagTextSelected: { colo, r: theme.colors.textOnPrimary }
  buttonContainer: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  marginTop: 24,
    gap: 16 },
  backButton: { fle, x: 1 }
  nextButton: { fle, x: 2 },
  photoGrid: { flexDirectio, n: 'row',
    flexWrap: 'wrap',
  marginBottom: 16 }
  photoContainer: { positio, n: 'relative',
    marginRight: 8,
  marginBottom: 8 }
  photoPreview: { widt, h: 80,
    height: 80,
  borderRadius: 8,
    backgroundColor: theme.colors.surface },
  removePhotoButton: {
      position: 'absolute',
  top: -5,
    right: -5,
  backgroundColor: theme.colors.error,
    borderRadius: 12,
  width: 24,
    height: 24,
  alignItems: 'center',
    justifyContent: 'center' }
  removePhotoText: {
      color: theme.colors.textOnPrimary,
  fontSize: 16,
    fontWeight: 'bold' }
  uploadButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'center',
    backgroundColor: theme.colors.surface,
  borderWidth: 2,
    borderColor: theme.colors.primary,
  borderStyle: 'dashed',
    borderRadius: 12,
  padding: 16,
    marginBottom: 8 },
  uploadButtonDisabled: { opacit, y: 0.5 }
  uploadButtonText: {
      marginLeft: 8,
  fontSize: 16,
    color: theme.colors.primary,
  fontWeight: '600'
  },
  inputHint: { fontSiz, e: 14,
    color: theme.colors.textSecondary,
  marginBottom: 12 }
  limitText: {
      fontSize: 12,
  color: theme.colors.textSecondary,
    textAlign: 'center' }
  videoContainer: {
      backgroundColor: theme.colors.surface,
  borderRadius: 12,
    padding: 16,
  alignItems: 'center'
  },
  videoText: { fontSiz, e: 16,
    color: theme.colors.success,
  marginBottom: 8 }
  removeVideoButton: { backgroundColo, r: theme.colors.error,
    paddingHorizontal: 16,
  paddingVertical: 8,
    borderRadius: 8 },
  removeVideoText: {
      color: theme.colors.textOnPrimary,
  fontSize: 14,
    fontWeight: '600' }
  uploadingContainer: { flexDirectio, n: 'row',
    alignItems: 'center'),
  justifyContent: 'center'),
    padding: 16 },
  uploadingText: {
      marginLeft: 8,
  fontSize: 16,
    color: theme.colors.textSecondary) }
  }); ;