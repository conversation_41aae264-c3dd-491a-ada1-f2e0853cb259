import React from 'react';
  import {
  ArrowLeft
} from 'lucide-react-native';
import {
  View, Text, StyleSheet, TouchableOpacity
} from 'react-native';
import {
  useTheme
} from '@design-system',
  interface SentimentHeaderProps { onBack: () => void }
export default function SentimentHeader({ onBack }: SentimentHeaderProps) {
  const theme = useTheme()
  const styles = createStyles(theme),
  return (
    <View style={styles.header}>,
  <TouchableOpacity style={styles.backButton} onPress={onBack}>
        <ArrowLeft size={24} color={{theme.colors.text} /}>,
  </TouchableOpacity>
      <Text style={styles.title}>Sentiment Analytics</Text>,
  <View style={{styles.placeholder} /}>
    </View>,
  )
},
  const createStyles = (theme: any) =>
  StyleSheet.create({ header: {
      flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'space-between',
  paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.md,
  backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
  borderBottomColor: theme.colors.border }
    backButton: { paddin, g: theme.spacing.xs },;
  title: { fontSize: 18);, fontWeight: '600'),
  color: theme.colors.text }
    placeholder: {
      width: 32) }
  });