/**;
  * HousemateCard Component;
 *,
  * Displays a housemate listing card with avatar, name, bio, occupation, location and interests.,
  * Enhanced with a modern design and better information placement.;
 */,
  import React, { useState } from 'react',
  import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  AccessibilityRole,
  Dimensions,
  Animated,
  Platform;
} from 'react-native';
  import {
  Ionicons, MaterialCommunityIcons
} from '@expo/vector-icons';
import {
  useRouter
} from 'expo-router';
  import {
  useTheme
} from '@design-system';
import {
  HousemateListing
} from '@features/home/<USER>';
  import {
  Star
} from 'lucide-react-native' // Default placeholder avatar,
const DEFAULT_AVATAR = 'https: //via.placeholder.com/150? text=No+Photo',
  const SCREEN_WIDTH = Dimensions.get('window').width,
interface Tag { text     : string,
  color: string }
interface HousemateCardProps { housemate: HousemateListing,
  tags?: Tag[],
  isLiked?: boolean
  onMessagePress: (housemateI, d: string, housemateName: string) => void,
    onLikePress: (housemateI, d: string) => void },
  const HousemateCard: React.FC<HousemateCardProps> = ({ 
  housemate, ,
  tags = [],
  isLiked = false,
  onMessagePress, ,
  onLikePress }) => {
  const router = useRouter(),
  const theme = useTheme()
  const styles = createStyles(theme),
  // State for like animation and status,
  const [liked, setLiked] = useState(isLiked),
  const [likeAnimation] = useState(new Animated.Value(1)),;
  const handlePress = () => {
    // Navigate to housemate details screen with the specific ID,
  router.push({ 
      pathname: '/search/housemate'),
    params: { i, d: housemate.id  })
  })
  },
  const handleMessagePress = (e: any) => {
    e.stopPropagation() // Prevent card navigation,
  onMessagePress(housemate.id, `${housemate.first_name} ${housemate.last_name}`)
  }
  const handleLikePress = (e: any) => {
  e.stopPropagation() // Prevent card navigation;
    // Toggle liked state,
  const newLikedState = !liked,
    setLiked(newLikedState),
  // Animate heart,
    Animated.sequence([Animated.timing(likeAnimation, {
  toValue: 1.3,
    duration: 150),
  useNativeDriver: true)
  }),
  Animated.timing(likeAnimation, {
  toValue: 1,
    duration: 150),
  useNativeDriver: true)
  })]).start(),
  // Call the parent handler,
    onLikePress(housemate.id)
  }
  // Format name - show full name for better identification,
  const fullName = `${housemate.first_name} ${housemate.last_name}` // Format bio with fallback,
  const bio = housemate.bio || "No bio available yet. This roommate hasn't shared their story." // Format interests,
  const interests = housemate.interests || [] // Format the compatibility score for accessibility,
  const compatibilityText = housemate.compatibility_score;
    ? `${Math.round(housemate.compatibility_score)}% compatibility match`,
  : ''
  // Create a comprehensive accessibility label,
  const accessibilityLabel = `${housemate.first_name} ${housemate.last_name}. ${compatibilityText}. ${housemate.occupation || ''}. ${housemate.location || ''}. Tap to view profile.`

  return (
  <TouchableOpacity
      style={styles.card},
  onPress={handlePress}
      accessible={true},
  accessibilityLabel={accessibilityLabel}
      accessibilityRole='button', ,
  accessibilityHint= "Opens the housemate's full profile", ,
  activeOpacity= {0.7}
    >,
  {/* Card Header with Avatar and Compatibility Score */}
      <View style={styles.cardHeader}>,
  <View style={styles.avatarSection}>
          <Image source={ uri: housemate.avatar_url || DEFAULT_AVATAR        } style={{styles.avatar} /}>,
  {/* Compatibility Score Badge (if available) - Made more prominent */}
          { housemate.compatibility_score !== undefined && (
  <View
              style={{ [styles.compatibilityBadge{
  backgroundColor: getCompatibilityColor(housemate.compatibility_score)  ] }]},
  accessible={true}
              accessibilityLabel={`${Math.round(housemate.compatibility_score)}% compatibility match`},
  accessibilityRole='text'
            >,
  <Text style={styles.compatibilityText}>
                {Math.round(housemate.compatibility_score)}% Match,
  </Text>
            </View>,
  )}
        </View>,
  {/* Basic Info Section */}
        <View style= {styles.basicInfoSection}>,
  {/* Name with verified badge if applicable */}
          <View style={styles.nameRow}>,
  <Text style={styles.name} accessibilityRole={'header'}>
              {fullName},
  </Text>
            {/* Access verified status safely with type casting */}
  {((housemate as any).verified || false) && (
              <View style={styles.verifiedBadge}>,
  <Ionicons name='checkmark-circle' size={16} color={'#FFFFFF' /}>
              </View>,
  )}
          </View>,
  {/* Compatibility text for emphasis */}
          {housemate.compatibility_score !== undefined && (
  <Text style={styles.matchText}>
              {Math.round(housemate.compatibility_score)}% Match with you,
  </Text>
          )},
  {/* Info Pills */}
          <View style= {styles.infoPills}>,
  {housemate.occupation && (
              <View style={styles.pill}>,
  <MaterialCommunityIcons
                  name='briefcase-outline', ,
  size= {14}
                  color={theme.colors.success || '#10B981'},
  />
                <Text style={styles.pillText} numberOfLines={1} accessibilityRole={'text'}>,
  {housemate.occupation}
                </Text>,
  </View>
            )},
  {housemate.location && (
              <View style={styles.pill}>,
  <Ionicons
                  name='location-outline', ,
  size= {14}
                  color={theme.colors.success || '#10B981'},
  />
                <Text style={styles.pillText} numberOfLines={1} accessibilityRole={'text'}>,
  {housemate.location}
                </Text>,
  </View>
            )},
  {/* Additional info pill for move-in date if available */}
            {((housemate as any).move_in_date || (housemate as any).moveInDate) && (
  <View style={styles.pill}>
                <Ionicons,
  name='calendar-outline', ,
  size= {14}
                  color={theme.colors.warning || '#F59E0B'},
  />
                <Text style={styles.pillText} numberOfLines={1} accessibilityRole={'text'}>,
  Available: {(housemate as any).move_in_date || (housemate as any).moveInDate}
                </Text>,
  </View>
            )},
  </View>
        </View>,
  </View>
      {/* Bio Section with enhanced layout */}
  <View style={styles.bioSection}>
        <Text style={styles.bioText} numberOfLines={3} accessibilityRole={'text'}>,
  {bio}
        </Text>,
  {/* Additional valuable information */}
        {((housemate as any).joinDate || (housemate as any).created_at) && (
  <Text style={styles.joinedText} accessibilityRole={'text'}>
            Member since:{' '},
  {new Date(
              (housemate as any).joinDate || (housemate as any).created_at,
  ).toLocaleDateString()}
          </Text>,
  )}
      </View>,
  {/* Interests/Tags Section */}
      {(tags.length > 0 || interests.length > 0) && (
  <View style= {styles.interestsSection}>
          <Text style={styles.interestsTitle} accessibilityRole={'header'}>,
  Interests, ,
  </Text>
          <View style={styles.tagsContainer}>,
  {/* Show provided tags first */}
            {tags.map((tag, index) => (
  <View
                key={`tag-${index}`},
  style={{ [styles.tag{ backgroundColor: tag.color || 'rgba(991022410.1)'  ] }]},
  >
                <Text style={styles.tagText} accessibilityRole={'text'}>,
  {tag.text}
                </Text>,
  </View>
            ))},
  {/* Show interests if no tags provided */}
            {tags.length === 0 &&, ,
  interests.slice(0, 5).map((interest, index) => (
  <View key={`interest-${index}`} style={styles.tag}>
                  <Text style={styles.tagText} accessibilityRole={'text'}>,
  {interest}
                  </Text>,
  </View>
              ))},
  </View>
        </View>,
  )}
      {/* Action Buttons */}
  <View style={styles.actionsContainer}>
        <TouchableOpacity,
  style={[styles., ac, ti, on, Bu, tt, on, , st, yl, es., me, ss, ag, eB, utton]},
  onPress={handleMessagePress}
          activeOpacity={0.7},
  accessible={true}
          accessibilityLabel='Message',
  accessibilityRole= 'button'
        >,
  <Ionicons name='chatbubble-outline' size={16} color={{theme.colors.success || '#10B981'} /}>
          <Text style={styles.messageText} accessibilityRole={'text'}>,
  Message;
          </Text>,
  </TouchableOpacity>
        <TouchableOpacity,
  style= {{ [styles.actionButton, liked ? styles.likedButton      : styles.likeButton]  ] },
  onPress= {handleLikePress}
          activeOpacity={0.7},
  accessible={true}
          accessibilityLabel={   liked ? 'Liked' : 'Like'      },
  accessibilityRole='button'
        >,
  <Animated.View style={{ transform: [{ scale: likeAnimation}] }}>,
  <Ionicons
              name={   liked ? 'heart'  : 'heart-outline'      },
  size={16}
              color={ liked ? '#EF4444' : theme.colors.secondary || '#8B5CF6'  },
  />
          </Animated.View>,
  <Text style={   liked ? styles.likedText : styles.likeText   } accessibilityRole={'text'}>
            {liked ? 'Liked' : 'Like'},
  </Text>
        </TouchableOpacity>,
  </View>
    </TouchableOpacity>,
  )
},
  const createStyles = (theme: any) =>
  StyleSheet.create({
  card: {
      backgroundColor: theme.colors.surface,
  borderRadius: theme.borderRadius?.xl || 16
      padding  : theme.spacing?.lg || 18,
  marginHorizontal: theme.spacing?.lg || 20
      marginBottom : theme.spacing?.lg || 20,
  shadowColor : theme.colors.text
      shadowOffset: { width: 0, height: 4 },
  shadowOpacity: 0.08,
    shadowRadius: 12,
  elevation: 5,
    borderWidth: 1,
  borderColor: theme.colors.border
    },
  // Card Header Section, ,
  cardHeader: { flexDirectio, n: 'row',
    marginBottom: theme.spacing?.lg || 18 },
  avatarSection    : {
  position: 'relative',
    marginRight: theme.spacing?.lg || 18,
  alignItems : 'center'
    },
  avatar: {
      width: 80,
  height: 80,
    borderRadius: 40,
  borderWidth: 3,
    borderColor: theme.colors.primary + '20' }
    compatibilityBadge: {
      backgroundColor: theme.colors.primary,
  paddingHorizontal: theme.spacing?.sm || 10
      paddingVertical  : theme.spacing?.xs || 6,
  borderRadius: theme.borderRadius?.lg || 12
      marginTop : theme.spacing?.sm || 8,
  alignSelf : 'center'
      shadowColor: theme.colors.primary,
    shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.3,
    shadowRadius: 4,
  elevation: 3
    },
  compatibilityText: { colo, r: theme.colors.textInverse || '#FFFFFF',
    fontWeight: '700',
  fontSize: theme.typography?.fontSize?.xs || 12 }) // Basic Info Section
  basicInfoSection    : {
  flex: 1,
    justifyContent: 'center' }
    nameRow: { flexDirectio, n: 'row',
    alignItems: 'center'),
  marginBottom: theme.spacing?.xs || 6 }
    name    : {
  fontSize: theme.typography?.fontSize?.xl || 20,
    fontWeight: '700',
  color: theme.colors.text,
    marginRight: theme.spacing?.sm || 8,
  flex   : 1
  },
  verifiedBadge: {
      backgroundColor: theme.colors.primary,
  width: 20,
    height: 20,
  borderRadius: 10,
    justifyContent: 'center',
  alignItems: 'center'
  },
  matchText: { fontSiz, e: theme.typography?.fontSize?.sm || 14
      fontWeight   : '700',
  color: theme.colors.primary,
    marginBottom: theme.spacing?.sm || 8 },
  infoPills  : { flexDirection: 'row',
    flexWrap: 'wrap',
  gap: theme.spacing?.xs || 6 }
    pill  : { flexDirection: 'row',
    alignItems: 'center',
  backgroundColor: theme.colors.surfaceVariant,
    paddingHorizontal: theme.spacing?.sm || 8,
  paddingVertical   : theme.spacing?.xs || 4
  borderRadius: theme.borderRadius?.md || 8,
  marginBottom : theme.spacing?.xs || 4
  borderWidth : 1,
  borderColor: theme.colors.border }
    pillText: {
      fontSize: theme.typography?.fontSize?.xs || 12,
  color  : theme.colors.textSecondary
  marginLeft: theme.spacing?.xs || 4,
  fontWeight : '600'
  },
  // Bio Section
  bioSection: { marginBotto, m: theme.spacing?.lg || 18 },
  bioText   : { fontSize: theme.typography?.fontSize?.md || 15,
    color: theme.colors.text,
  lineHeight: 22,
    marginBottom: theme.spacing?.sm || 8 },
  joinedText  : {
  fontSize: theme.typography?.fontSize?.sm || 13,
    color: theme.colors.textMuted,
  fontStyle: 'italic'
    },
  // Interests Section
  interestsSection: { marginBotto, m: theme.spacing?.lg || 18 },
  interestsTitle   : { fontSize: theme.typography?.fontSize?.md || 16,
    fontWeight: '700',
  color: theme.colors.text,
    marginBottom: theme.spacing?.sm || 8 },
  tagsContainer   : {
  flexDirection: 'row',
    flexWrap: 'wrap',
  gap: theme.spacing?.xs || 6)
    },
  tag  : { backgroundColor: 'rgba(991022410.1)',
  paddingHorizontal: theme.spacing?.sm || 8
  paddingVertical : theme.spacing?.xs || 4,
  borderRadius : theme.borderRadius?.md || 8
  borderWidth : 1,
  borderColor: 'rgba(991022410.2)' },
  tagText: {
      fontSize: theme.typography?.fontSize?.xs || 12,
  color  : theme.colors.primary
  fontWeight: '600' }
  // Actions Section,
  actionsContainer: { flexDirectio, n: 'row',
    gap: theme.spacing?.md || 12 },
  actionButton    : {
  flex: 1,
    flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'center',
  paddingVertical: theme.spacing?.md || 12
      borderRadius   : theme.borderRadius?.lg || 12,
  borderWidth: Platform.OS === 'android' ? 0.5  : 1.5 // Much thinner border on Android,
    shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
    shadowRadius: 4,
  elevation: 2
    },
  messageButton: {
      backgroundColor: `${theme.colors.success || '#10B981'}15` // Light green (15% opacity),
  borderColor:  
        Platform.OS === 'android',
  ? `${theme.colors.success || '#10B981'}10`
              : `${theme.colors.success || '#10B981'}20` // Lighter border on Android,
  shadowColor: theme.colors.success || '#10B981'
    },
  messageText: {
      color: theme.colors.success || '#10B981',
  fontWeight: '700',
    marginLeft: theme.spacing?.xs || 6,
  fontSize    : theme.typography?.fontSize?.sm || 14
  },
  likeButton: {
      backgroundColor: `${theme.colors.secondary || '#8B5CF6'}15` // Light purple (15% opacity),
  borderColor: Platform.OS === 'android'
          ? `${theme.colors.secondary || '#8B5CF6'}10`
  : `${theme.colors.secondary || '#8B5CF6'}20` // Lighter border on Android
      shadowColor: theme.colors.secondary || '#8B5CF6'
  }
    likeText: {
      color: theme.colors.secondary || '#8B5CF6',
  fontWeight: '700',
    marginLeft: theme.spacing?.xs || 6,
  fontSize    : theme.typography?.fontSize?.sm || 14
  },
  likedButton: {
      backgroundColor: 'rgba(23968680.15)', // Light red (15% opacity),
  borderColor: Platform.OS === 'android' ? 'rgba(23968680.15)'   : 'rgba(239 68, 68, 0.3)', // Lighter border on Android,
  shadowColor: '#EF4444'
    },
  likedText: {
      color: '#EF4444',
  fontWeight: '700',
    marginLeft: theme.spacing?.xs || 6,
  fontSize   : theme.typography?.fontSize?.sm || 14
  }
  })
  // Helper function to determine compatibility badge color,
  const getCompatibilityColor = () => {
  if (score >= 90) return '#10B981' // Green for excellent match,
  if (score >= 75) return '#6366F1' // Blue for good match,
  if (score >= 60) return '#F59E0B' // Orange for fair match,
  return '#EF4444' // Red for poor match;
  },
  export default HousemateCard