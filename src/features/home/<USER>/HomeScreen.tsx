/**,
  * HomeScreen Component;
 *,
  * The main screen component for the Home/Browse tab.;
 * This component orchestrates all the UI elements and data fetching,
  * for the home screen experience.;
 *,
  * Features:  
 * - Virtualized list for optimal performance,
  * - Pull-to-refresh functionality;
 * - Skeleton loading states,
  * - Efficient pagination;
 * - Optimized rendering with memoization,
  * - Debounced search;
 * - Enhanced match-to-chat functionality,
  * - Improved UI for seamless matching experience;
 * - Enhanced room cards with view tracking,
  */

import React, { useCallback, useMemo, useState, useRef, useEffect } from 'react',
  import {
  View,
  FlatList,
  RefreshControl,
  StyleSheet,
  SafeAreaView,
  ActivityIndicator,
  useWindowDimensions,
  Platform,
  Text,
  Modal,
  Alert,
  TouchableOpacity;
} from 'react-native';
import {
  useSafeAreaInsets
} from 'react-native-safe-area-context';
  import {
  useLocalSearchParams, useRouter
} from 'expo-router' // Import components,;
import WelcomeHeader from '@features/home/<USER>/WelcomeHeader';
  import ProfileCompletionBanner from '@features/home/<USER>/ProfileCompletionBanner';
import {
  UnifiedSearchInterface
} from '@components/search/UnifiedSearchInterface';
  import TabSelector from '@features/home/<USER>/TabSelector';
import RoomCard from '@features/home/<USER>/RoomCard';
  import EnhancedRoomCard from '@features/home/<USER>/EnhancedRoomCard';
import HousemateCard from '@features/home/<USER>/HousemateCard' // EnhancedHousemateCard removed - using regular HousemateCard with correct interface,
  import Pagination from '@features/home/<USER>/Pagination';
import {
  LoadingState,
  ErrorState,
  EmptyState,
  LoadingFooter,
  SkeletonCard
} from '@features/home/<USER>/StateHandlers';
  import EnhancedMatchModal from '@components/matching/EnhancedMatchModal' // Import hooks and types,
  import {
  useHomeData
} from '@features/home/<USER>/useHomeData';
  import {
  useMatchChat
} from '@features/home/<USER>/useMatchChat';
  import {
  ListingType, RoomListing, HousemateListing
} from '@features/home/<USER>';

const HomeScreen: React.FC = () => {
  // Get safe area insets for proper layout,
  const insets = useSafeAreaInsets(),
  const { width  } = useWindowDimensions()
  const router = useRouter(),
  // State for tracking liked profiles,
  const [likedProfiles, setLikedProfiles] = useState<Set<string>>(new Set()),
  // Get URL parameters (for deep linking and tab navigation)
  const { tabParam } = useLocalSearchParams<{ tabParam: ListingType }>(),
  // Search timeout for debouncing,
  const searchTimeout = useRef<NodeJS.Timeout | null>(null),
  // Rate limiting for like actions (reduced for better UX)
  const lastLikeTime = useRef<number>(0),
  const LIKE_COOLDOWN_MS = 300 // 300ms cooldown between likes (reduced from 1000ms);
  // Use the custom hook to manage home screen data and state,
  const { activeType,
    isLoading,
  refreshing,
    error,
  listings,
    userProfile,
  pagination,
    filters,
  searchQuery, // Now comes from the hook with tab-specific persistence,
  setSearchQuery, // Function to update search query for current tab,
  setFilters, // Function to update filters for current tab,
  handleTypeChange,
    handleRefresh,
  handlePageChange,
    handleLoadMore,
  handleMessageRoom,
    handleMessageHousemate,
  handleLikeHousemate,
    handleCompleteProfile,
  searchListings,
    filterListings,
  initialize,
    roomListings, // Add explicit access to room listings,
  housemateListings, // Add explicit access to housemate listings } = useHomeData((tabParam as ListingType) || 'room');
  // 🔥 MINIMAL LOGGING: Only log critical state changes in development,
  useEffect(() => {
    if (__DEV__) {
  console.log(`🏠 [HomeScreen] State Update:` {
  activeType);
        isLoading,
  error, ,
  listingsCount: listings?.length || 0)
        roomListingsCount     : roomListings?.length || 0,
  housemateListingsCount: housemateListings?.length || 0)
        listings : listings?.slice(0 2) // Show first 2 items for debugging })
      if (!isLoading && error) {
  console.warn(`🏠 [HomeScreen] Error loading ${activeType}:` error)
  }
      if (!isLoading && !error && listings?.length === 0) {
  console.warn(`🏠 [HomeScreen] No ${activeType} listings found`)
  }
    }
  }, [activeType,
  isLoading
    error,
  listings?.length,
    roomListings?.length,
  housemateListings?.length;
  ]);
  // Handle search query submission with debounce,
  const handleSearchSubmit = useCallback(
  (query   : string) => {
      setSearchQuery(query),
  // Clear any existing timeout
      if (searchTimeout.current) {
  clearTimeout(searchTimeout.current)
      },
  // Set a new timeout to debounce the search (reduced to 150ms for better responsiveness)
      searchTimeout.current = setTimeout(() => {
  if (query.trim()) {
          searchListings(query) }
      } 150) // Reduced debounce for better UX
  }
    [setSearchQuery, searchListings],
  )
  // Cleanup timeout on unmount,
  useEffect(() => {
    return () => {
  if (searchTimeout.current) {
        clearTimeout(searchTimeout.current) }
    }
  }; []),
  // Handle filter button press,
  const handleFilterPress = useCallback(() => {
  // This will be handled by the router in SearchFilterBar component // We just need to prepare any data needed for the filter screen,
    console.log('Filter pressed for', activeType) }, [activeType]);
  // Get match-to-chat functionality from custom hook,
  const { isMatching,
  processingHousemate,
    handleLikeHousemate: handleLikeWithMatchCelebration,
    handleMessageHousemate: handleMessageUser,
  handleQuickChat,
  handleCloseMatchModal } = useMatchChat();
  // Enhanced like handler that integrates with match-to-chat functionality,
  const handleEnhancedLike = useCallback(
  async (housemateId: string) => {
  // Rate limiting check,
  const now = Date.now(),
  if (now - lastLikeTime.current < LIKE_COOLDOWN_MS) {
  Alert.alert('Please wait', "You're liking too quickly. Please wait a moment."),
  return { success: false, isMatch: false }
  };
      lastLikeTime.current = now // Update liked profiles state immediately for UI feedback,
  setLikedProfiles(prev => {
        const newSet = new Set(prev),
  if (newSet.has(housemateId)) {
          newSet.delete(housemateId) } else {
          newSet.add(housemateId) };
        return newSet
  })
      // Show immediate UI feedback,
  try {
        // Use the properly implemented handler from useHomeData,
  const result = await handleLikeHousemate(housemateId)
        if (!result.success) {
  Alert.alert('Error', result.error || 'Failed to process like. Please try again.'),
  // Revert the UI state if the API call failed,
          setLikedProfiles(prev => {
  const newSet = new Set(prev)
            if (newSet.has(housemateId)) {
  newSet.delete(housemateId)
            } else {
  newSet.add(housemateId)
            },
  return newSet;
          }),
  return { success: false, isMatch: false }
  }
        // Provide user feedback based on result,
  if (result.success && !result.isMatch) {
          // Show subtle feedback for successful like without match,
  try {
            const { hapticFeedback } = require('@utils/hapticFeedback'),
  hapticFeedback.light()
          } catch (err) {
  console.log('Haptic feedback not available')
          }
  };
        // If it's a match, also trigger the match celebration using useMatchChat handler,
  if (result.isMatch) {
          try {
  // Call the match celebration handler from useMatchChat,
            await handleLikeWithMatchCelebration(housemateId, liked) } catch (err) {
            console.log('Match celebration failed:', err) }
        },
  return result;
      } catch (error) {
  console.error('Error in enhanced like handler:', error),
  // Show error feedback to user,
        Alert.alert('Error', 'Failed to process like. Please try again.'),
  return { success: false, isMatch: false }
  }
    },
  [handleLikeHousemate, handleLikeWithMatchCelebration, setLikedProfiles],
  )
  // Enhanced message handler,
  const handleEnhancedMessage = useCallback(
    (housemate: HousemateListing) => {
  handleMessageUser(housemate);
    },
  [handleMessageUser],
  )
  // 🔥 NEW: Enhanced room view tracking,
  const handleViewRoom = useCallback(async (roomId: string) => {
  try {
  // In a real implementation, this would call an API to track the view // For now, we'll just log it,
  if (__DEV__) {
        console.log('🏠 [HomeScreen] Room viewed:', roomId) }
      // Future: Call API to increment room view count,
  // await roomService.trackView(roomId, user?.id)
  } catch (error) {
      console.error('Error tracking room view     : ' error) }
  }, []);
  // OPTIMIZED: Memoize item heights for better FlatList performance
  const ITEM_HEIGHTS = React.useMemo(
  () => ({  room: 280, // Increased for enhanced room card, ,
  housemate: 260,
    all: 270, // Average height for mixed content, ,
  skeleton: 120  })
    [],
  )
  // OPTIMIZED: Stable keyExtractor to prevent unnecessary re-renders,
  const keyExtractor = React.useCallback(
  (item: RoomListing | HousemateListing) => `${activeType}-${item.id}` ,
  [activeType],
  )
  // OPTIMIZED: Stable getItemLayout for better scrolling performance,
  const getItemLayout = React.useCallback(
  (data: any, index: number) => {
  const height = ITEM_HEIGHTS[activeType],
  return {
        length: height,
    offset: height * index,
  index;
  }
  }
    [activeType, ITEM_HEIGHTS],
  )
  // Render item for the FlatList with proper typing,
  const renderListItem = useCallback(
    ({ item }: { item: RoomListing | HousemateListing }) => {
  // For 'all' type, we need to determine the item type dynamically,
  if (activeType === 'all') {
        // Check if item has properties that indicate it's a room,
  if ('price' in item && 'title' in item) {
          return (
  <EnhancedRoomCard
              room= {item as RoomListing},
  onMessagePress={handleMessageRoom}
              onViewRoom={handleViewRoom},
  showViewCount={true}
            />,
  )
        } else {
  return (
            <HousemateCard,
  housemate={item as HousemateListing}
              isLiked={likedProfiles.has((item as HousemateListing).id)},
  onMessagePress={   (housemateId: stringhousemateName: string) => {
  handleMessageHousemate(housemateIdhousemateName)    }},
  onLikePress={   (housemateId: string) => {
                handleEnhancedLike(housemateId)    }},
  />
          )
  }
      } else if (activeType === 'room') {
  // Use the enhanced room card with view tracking,
        return (
  <EnhancedRoomCard
            room= {item as RoomListing},
  onMessagePress={handleMessageRoom}
            onViewRoom={handleViewRoom},
  showViewCount={true}
          />,
  )
      } else {
  // Use the correct card for housemates,
        return (
  <HousemateCard
            housemate= {item as HousemateListing},
  isLiked={likedProfiles.has((item as HousemateListing).id)}
            onMessagePress={   (housemateId: stringhousemateName: string) => {
  handleMessageHousemate(housemateIdhousemateName)    }},
  onLikePress={   (housemateId: string) => {
              handleEnhancedLike(housemateId)    }},
  />
        )
  }
    },
  [
      activeType,
  handleMessageRoom,
      handleViewRoom,
  handleMessageHousemate,
      handleEnhancedLike,
  likedProfiles;
    ],
  )
  // Generate skeleton loaders when in loading state,
  const renderSkeletons = useCallback(() => {
    const skeletons = [],
  const count = 3 // Number of skeleton cards to show,
    for (let i = 0,  i < count,  i++) {
  // For 'all' type, alternate between room and housemate skeletons,
  const skeletonType = activeType === 'all' ? (i % 2 === 0 ? 'room'     : 'housemate') : activeType
      skeletons.push(<SkeletonCard key={`skeleton-${i}`} type={{skeletonType} /}>)
  }
    return skeletons
  }, [activeType]);
  return (
    <SafeAreaView style= {[styles.container,  { paddingTop: insets.top}]}>,
  <FlatList
        data={listings},
  renderItem={renderListItem}
        keyExtractor={keyExtractor},
  getItemLayout={getItemLayout}
        contentContainerStyle={styles.listContent},
  refreshControl={
          <RefreshControlrefreshing={refreshing}
            onRefresh={handleRefresh},
  colors={['#6366F1']},
  tintColor='#6366F1'
            progressBackgroundColor='#FFFFFF', ,
  />
        },
  ListHeaderComponent= {
          <>,
  {/* Welcome Header */}
            <WelcomeHeader,
  userName={userProfile.userName}
              subtitle={   `Find your ideal ${activeType === 'room' ? 'room'      : activeType === 'housemate' ? 'roommate' : 'match'      }`},
  variant='green'
            />,
  {/* Profile Completion Banner */}
            {userProfile.showCompletionCard && (
  <ProfileCompletionBanner
                completionPercentage={userProfile.completionPercentage},
  onPress={handleCompleteProfile}
              />,
  )}
            {/* Tab Selector */}
  <TabSelector
              activeTab={activeType},
  onTabChange={handleTypeChange}
              tabs={{ [{ id: 'room' label: 'Rooms'      ] },
  { id: 'housemate', label: 'Roommates' } ,
  { id: 'all', label: 'All' }]},
  />
            {/* Unified Search Interface */}
  <View style={styles.searchContainer}>
              <UnifiedSearchInterface,
  initialSearchType={   activeType === 'room'
                    ? 'rooms': activeType === 'housemate'
  ? 'housemates': 'both'    }
  embedded={true},
  showFilters={true}
  showSearchTypes={false},
  onResultSelect={(resulttype) => {
  if (type === 'room') {
                    // Navigate to room detailsrouter.push(`/rooms/${result.id}`)
                  } else {
  // Navigate to housemate profile
                    router.push(`/profile/${result.id}`)
  }
                }},
  />
            </View>,
  {/* Skeleton loaders when in initial loading state */}
            {isLoading && !refreshing && listings.length === 0 && renderSkeletons()},
  </>
        },
  ListEmptyComponent={   isLoading ? null    : error ? (
            <ErrorState error={error      } onRetry={{handleRefresh} /}>,
  ) : listings.length === 0 ? (<EmptyState
              title={   `No ${activeType === 'all' ? 'listings'  : activeType + 's'      } found`},
  message={   `We couldn't find any ${activeType === 'all' ? 'rooms or roommates' : activeType + 's'      } matching your criteria. Try adjusting your filters or check back later.`}
              onRefresh={handleRefresh},
  onClearFilters={() => {
                setFilters({}),
  setSearchQuery('')
                filterListings({})
  }}
              hasActiveFilters = {Object.keys(filters).length > 0 || searchQuery.length > 0},
  />
          ) : null
  }
        ListFooterComponent={
  <>
            {/* Loading footer when loading more items */}
  {isLoading && listings.length > 0 && <LoadingFooter />

            {/* Pagination controls */}
  {!isLoading && listings.length > 0 && pagination.totalPages > 1 && (
              <View style={styles.paginationContainer}>,
  <Pagination
                  currentPage={pagination.currentPage},
  totalPages={pagination.totalPages}
                  onPageChange={handlePageChange},
  />
              </View>,
  )}
          </>
  }
        onEndReached={handleLoadMore},
  onEndReachedThreshold={0.5}
        initialNumToRender={5},
  maxToRenderPerBatch={10}
        windowSize={5},
  removeClippedSubviews={Platform.OS !== 'web'}
        updateCellsBatchingPeriod={50},
  disableIntervalMomentum={true}
      />,
  {/* Match Celebration Modal */}
      <EnhancedMatchModal,
  visible={isMatching}
        onClose={handleCloseMatchModal},
  onMessage={matchId => {
          // Find the matched housemate,
  const matchedHousemate = listings.find(item => 'id' in item && item.id === matchId) as
            | HousemateListing,
  | undefined,
          if (matchedHousemate) {
  handleMessageHousemate(
              matchedHousemate.id`${matchedHousemate.first_name} ${matchedHousemate.last_name}`
            )
  }
          handleCloseMatchModal()
  }}
        matchId= {processingHousemate || ''},
  matchName='New Match';
      />,
  </SafeAreaView>
  )
  }
const styles = StyleSheet.create({
  container: {
      flex: 1, ,
  backgroundColor: '#F1F5F9', // Light gray background similar to Payment Methods }
  listContent: { paddingBotto, m: 24 },
  searchContainer: { marginHorizonta, l: 16,
    marginTop: 12,
  marginBottom: 8 }
  paginationContainer: {
      marginTop: 16,
  marginBottom: 24,
    alignItems: 'center' }
  loadingContainer: {
      padding: 20,
  alignItems: 'center'
  }),
  modalOverlay: {
      flex: 1),
  backgroundColor: 'rgba(0000.7)',
  justifyContent: 'center',
    alignItems: 'center' }
}),
  export { HomeScreen }
export default HomeScreen