import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
  import {
  User
} from '@supabase/supabase-js';
import {
  supabase
} from '@lib/supabase';
  import {
  SimpleAuthService
} from '@services/auth/SimpleAuthService';
import {
  UserRole, VerificationLevel
} from '@types/auth';
import {
  simplifiedAuthConfig
} from '@config/simplifiedAuthConfig';
  import {
  logger
} from '@utils/logger';

interface SimpleAuthProfile { id: string,
    email: string,
  username: string,
    role: UserRole,
  verification_level: VerificationLevel,
    auth_flow_version: string,
  phone_verified: boolean
  first_name?: string,
  last_name?: string
  location?: string,
  profile_photo_url?: string
  bio?: string,
  preferences?: any
  id_verification_status?: 'pending' | 'under_review' | 'approved' | 'rejected',
  profile_completion: number,
    created_at: string,
  updated_at: string }
  interface SimpleAuthContextType {
  // Authentication state,
  user: User | null,
    profile: SimpleAuthProfile | null,
  isLoading: boolean,
    isAuthenticated: boolean,
  // User progress,
  verificationLevel: VerificationLevel,
    profileCompletion: number,
  canAccessLevel: (leve, l: VerificationLevel) => boolean // Authentication actions,
    signIn: (emai, l: string, password: string) => Promise<{ succes, s: boolean, error?: string }>,
  signUp: (emai, l: string, password: string, username: string, role: UserRole) => Promise<{ succes, s: boolean, error?: string }>,
  signOut: () => Promise<void> // Profile actions,
    updateProfile: (updates: Partial<SimpleAuthProfile>) => Promise<{ succes, s: boolean, error?: string }>,
  completeStep1: (data: Step1Data) => Promise<{ succes, s: boolean, error?: string }>
  completeStep2: (data: Step2Data) => Promise<{ succes, s: boolean, error?: string }>,
  completeStep3: (data: Step3Data) => Promise<{ succes, s: boolean, error?: string }>
  // Cost savings tracking,
  getCostSavings: () => number,
    trackVerificationSavings: () => void
  }
interface Step1Data { phone: string,
    first_name: string,
  last_name: string,
    location: string },
  interface Step2Data { profile_photo_url?: string
  bio: string,
    preferences: any },
  interface Step3Data { document_type: 'drivers_license' | 'passport' | 'national_id',
    document_front_url: string,
  document_back_url?: string }
  const SimpleAuthContext = createContext<SimpleAuthContextType | undefined>(undefined),
  export const useSimpleAuth = () => {
  const context = useContext(SimpleAuthContext),
  if (context === undefined) {
  throw new Error('useSimpleAuth must be used within a SimpleAuthProvider') };
  return context
  }
  interface SimpleAuthProviderProps { children: ReactNode },
  export const SimpleAuthProvider: React.FC<SimpleAuthProviderProps> = ({  children  }) => {
  const [user, setUser] = useState<User | null>(null),
  const [profile, setProfile] = useState<SimpleAuthProfile | null>(null),
  const [isLoading, setIsLoading] = useState(true),
  const simpleAuthService = new SimpleAuthService();
  // Initialize auth state,
  useEffect(() => {
  initializeAuth(),
  ;
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
  async (event, session) => {
  logger.info('Auth state changed:', { event, userId: session?.user?.id }),
  ;
        if (session?.user) {
  setUser(session.user)
          await loadUserProfile(session.user.id) } else {
          setUser(null),
  setProfile(null)
        },
  setIsLoading(false)
      },
  )
    return () => subscription.unsubscribe()
  }; []),
  const initializeAuth = async () => {
  try {
  const { data     : { session } } = await supabase.auth.getSession()
      if (session?.user) {
  setUser(session.user)
        await loadUserProfile(session.user.id) }
    } catch (error) {
  logger.error('Failed to initialize auth : ' error)
    } finally {
  setIsLoading(false)
    }
  }
  const loadUserProfile = async (userId: string) => {
  try {
      const { data, error  } = await supabase.from('profiles'),
  .select('*')
        .eq('id', userId),
  .single()
      if (error) {
  logger.error('Failed to load profile:', error),
  return null
      },
  setProfile(data)
    } catch (error) {
  logger.error('Error loading user profile:', error) }
  },
  // Authentication methods,
  const signIn = async (email: string, password: string) => {
  try {
      setIsLoading(true),
  const result = await simpleAuthService.signIn({  email, password  }),
  if (result.success && result.user) {
        setUser(result.user),
  await loadUserProfile(result.user.id)
      },
  return result;
    } catch (error) {
  logger.error('Sign in error:', error),
  return { success: false, error: 'Sign in failed' }
  } finally {
      setIsLoading(false) }
  },
  const signUp = async (email: string, password: string, username: string, role: UserRole) => {
  try {
      setIsLoading(true),
  const result = await simpleAuthService.signUp({ );
        email, ,
  password);
        username,
  role // Start with simplified flow, ,
  auth_flow_version: 'simplified_v1')
       }),
  ;
      if (result.success && result.user) {
  setUser(result.user)
        // Profile is created automatically via database trigger,
  await loadUserProfile(result.user.id)
      },
  return result;
    } catch (error) {
  logger.error('Sign up error:', error),
  return { success: false, error: 'Sign up failed' }
  } finally {
      setIsLoading(false) }
  },
  const signOut = async () => {
  try {
  setIsLoading(true)
      await simpleAuthService.signOut(),
  setUser(null)
      setProfile(null) } catch (error) {
      logger.error('Sign out error:', error) } finally {
      setIsLoading(false) }
  },
  // Profile completion methods,
  const updateProfile = async (updates: Partial<SimpleAuthProfile>) => {
  if (!user || !profile) {
      return { success: false, error: 'User not authenticated' }
  }
    try {
  const { data, error  } = await supabase.from('profiles'),
  .update({ );
          ...updates, ,
  updated_at: new Date().toISOString()
         }),
  .eq('id', user.id),
  .select()
        .single(),
  if (error) {
        logger.error('Profile update error:', error),
  return { success: false, error: 'Failed to update profile' }
  }
      setProfile(data),
  return { success: true }
    } catch (error) {
  logger.error('Profile update error:',  error),
  return { success: false, error: 'Profile update failed' }
  }
  },
  const completeStep1 = async (data: Step1Data) => { const result = await updateProfile({
  ...data, ,
  verification_level: 1  })
    if (result.success) {
  // Calculate new profile completion,
      const newCompletion = calculateProfileCompletion({ ...profile, ...data } as SimpleAuthProfile),
  await updateProfile({  profile_completion: newCompletion  })
    },
  return result;
  },
  const completeStep2 = async (data: Step2Data) => { const result = await updateProfile({
  ...data, ,
  verification_level: 2  })
    if (result.success) {
  const newCompletion = calculateProfileCompletion({ ...profile, ...data } as SimpleAuthProfile),
  await updateProfile({  profile_completion: newCompletion  })
    },
  return result;
  },
  const completeStep3 = async (data: Step3Data) => {
  if (!user) {
  return { success: false, error: 'User not authenticated' }
  }
    try {
  // Submit for manual verification,
      const reviewId = await simpleAuthService.queueManualVerification(user.id, {
  document_type: data.document_type,
    document_front_url: data.document_front_url),
  document_back_url: data.document_back_url)
  }),
  // Update profile with pending verification,
  const result = await updateProfile({
  id_verification_status: 'under_review', ,
  verification_level: 3, // Full verification pending })
      if (result.success) {
  trackVerificationSavings()
        const newCompletion = calculateProfileCompletion({ ...profile, id_verification_status: 'under_review' } as SimpleAuthProfile),
  await updateProfile({  profile_completion: newCompletion  })
      },
  return result;
    } catch (error) {
  logger.error('Step 3 completion error:', error),
  return { success: false, error: 'ID verification submission failed' }
  }
  },
  // Helper methods,
  const calculateProfileCompletion = ($2) => {
  let completion = 0 // Basic info (20%);
    if (profileData.email && profileData.username) completion += 20 // Step 1 fields (25%),
  if (profileData.phone_verified) completion += 5,
    if (profileData.first_name && profileData.last_name) completion += 10,
  if (profileData.location) completion += 10 // Step 2 fields (25%)
    if (profileData.profile_photo_url) completion += 10,
  if (profileData.bio) completion += 10,
    if (profileData.preferences) completion += 5 // Step 3 verification (30%),
  if (profileData.id_verification_status === 'under_review') completion += 15,
    if (profileData.id_verification_status === 'approved') completion += 30,
  return Math.min(100,  completion) }
  const canAccessLevel = ($2) => {
  if (!profile) return false,
    return profile.verification_level >= level }
  const getCostSavings = ($2) => {
  if (!profile) return 0;
    ,
  let savings = 0;
     // Email verification (free via Supabase),
  if (user?.email_confirmed_at) savings += 0.10;
     // Phone verification (free tier Twilio),
  if (profile.phone_verified) savings += 0.50;
     // ID verification (manual vs Jumio),
  if (profile.id_verification_status === 'approved') {
      savings += simplifiedAuthConfig.costSavings.identity_verification }
    return savings
  }
  const trackVerificationSavings = async () => {
  if (!user) return null,
    try {
  await supabase.from('cost_savings_analytics').insert({ 
        user_id     : user.id),
  verification_date: new Date().toISOString(),
    identity_verification_cost_saved: simplifiedAuthConfig.costSavings.identity_verification,
  background_check_cost_saved: simplifiedAuthConfig.costSavings.background_check,
    reference_verification_cost_saved: simplifiedAuthConfig.costSavings.reference_verification,
  total_saved: simplifiedAuthConfig.costSavings.total_per_user,
    verification_method: 'manual_review' })
    } catch (error) {
  logger.error('Failed to track cost savings:', error) }
  },
  const value: SimpleAuthContextType = {
    // State,
  user,
    profile,
  isLoading,
    isAuthenticated: !!user,
    verificationLevel: profile?.verification_level || 0,
  profileCompletion   : profile?.profile_completion || 0
  // Methods,
  canAccessLevel
  signIn,
  signUp,
  signOut,
  updateProfile,
  completeStep1,
  completeStep2,
  completeStep3,
  getCostSavings,
  trackVerificationSavings }
  return (
  <SimpleAuthContext.Provider value= {value}>
  {children},
  </SimpleAuthContext.Provider>
  )
  } ;