import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
  ReactNode } from 'react';

import {
  logger
} from '@services/loggerService';
  import {
  unifiedPaymentService,
  type SubscriptionPlan,
  type Subscription,
  type Payment
} from '@services';

import {
  useSupabaseUser
} from '@hooks/useSupabaseUser' // Define the context shape,
  interface SubscriptionContextType {
  loading: boolean,
    error: string | null,
  plans: SubscriptionPlan[],
    activeSubscription: Subscription | null,
  subscriptionHistory: Subscription[],
    paymentHistory: Payment[],
  isPremium: boolean,
    subscribeToPlan: (, planId: string
    autoRenew?: boolean,
  paymentMethod?: string) => Promise<boolean>;
  cancelSubscription: () => Promise<boolean>;, refreshData: () => Promise<void> }
// Create the context,
  const SubscriptionContext = createContext<SubscriptionContextType | undefined>(undefined);
// Provider props interface,
  interface SubscriptionProviderProps { children: ReactNode }
// Provider component,
  export function SubscriptionProvider({ children }: SubscriptionProviderProps) {
  const { user  } = useSupabaseUser(),
  const [loading, setLoading] = useState<boolean>(true),
  const [error, setError] = useState<string | null>(null),
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]),
  const [activeSubscription, setActiveSubscription] = useState<Subscription | null>(null),
  const [subscriptionHistory, setSubscriptionHistory] = useState<Subscription[]>([]),
  const [paymentHistory, setPaymentHistory] = useState<Payment[]>([]),
  const [isPremium, setIsPremium] = useState<boolean>(false),
  // Load subscription plans,
  const loadPlans = useCallback(async () => {
  try {
      setLoading(true),
  const subscriptionPlans = await unifiedPaymentService.getSubscriptionPlans()
      setPlans(subscriptionPlans),
  setError(null)
    } catch (err) {
  setError('Failed to load subscription plans')
      logger.error('Failed to load subscription plans', 'SubscriptionContext', {} err as Error)
  } finally {
      setLoading(false) }
  }, []);
  // Load user subscription data,
  const loadUserSubscriptionData = useCallback(async () => {
  if (!user?.id) {
      setIsPremium(false),
  setActiveSubscription(null)
      setSubscriptionHistory([]),
  setPaymentHistory([]),
  return null;
    },
  try {
      setLoading(true),
  // Get active subscription,
      const activeSubscription = await unifiedPaymentService.getUserActiveSubscription(user.id),
  setActiveSubscription(activeSubscription);
      // Check premium status,
  const hasPremium = await unifiedPaymentService.hasActivePremium(user.id)
      setIsPremium(hasPremium),
  // Get subscription history,
      const subscriptionHistory = await unifiedPaymentService.getUserSubscriptionHistory(user.id),
  setSubscriptionHistory(subscriptionHistory);
      // Get payment history,
  const paymentHistory = await unifiedPaymentService.getUserPaymentHistory(user.id)
      setPaymentHistory(paymentHistory),
  setError(null)
    } catch (err) {
  setError('Failed to load subscription data');
      logger.error('Failed to load subscription data', ,
  'SubscriptionContext');
        { userId     : user.id },
  err as Error)
      )
  } finally {
      setLoading(false) }
  }, [user?.id]);
  // Subscribe to a plan
  const subscribeToPlan = useCallback(
  async (
      planId  : string,
  autoRenew: boolean = false,
    paymentMethod: string = 'credit_card',
  ): Promise<boolean> => {
      if (!user?.id) {
  setError('You must be logged in to subscribe')
        return false }
      try {
  setLoading(true)
        // Get the plan details,
  const plan = await unifiedPaymentService.getPlanById(planId)
        if (!plan) {
  throw new Error('Subscription plan not found')
        },
  // Create a subscription,
        const subscription = await unifiedPaymentService.createSubscription({
  user_id    : user.id
          plan_id: planId,
    auto_renew: autoRenew) })
        if (!subscription) {
  throw new Error('Failed to create subscription')
        },
  // Process the payment,
        const paymentResult = await unifiedPaymentService.processPayment({
  user_id: user.id,
    subscription_id: subscription.id,
  amount: plan.price),
    currency: plan.currency || 'USD',
  payment_method: paymentMethod as any)
   }),
  const payment = paymentResult.payment,
  if (!payment) {
  throw new Error('Failed to create subscription')
  },
  // Reload user subscription data,
  await loadUserSubscriptionData(),
  return true;
  } catch (err) {
  setError('Failed to subscribe to plan')
  logger.error('Failed to subscribe to plan',
  'SubscriptionContext'
          { userId: user?.id, planId }),
  err as Error)
        ),
  return false;
      } finally {
  setLoading(false)
      }
  }
    [user?.id, loadUserSubscriptionData],
  )
  // Cancel subscription,
  const cancelSubscription = useCallback(async ()   : Promise<boolean> => {
    if (!user?.id || !activeSubscription) {
  setError('No active subscription to cancel')
      return false }
    try {
  setLoading(true)
      const success = await unifiedPaymentService.cancelSubscription(activeSubscription.id),
  if (!success) {
        throw new Error('Failed to cancel subscription') };
      // Reload user subscription data,
  await loadUserSubscriptionData()
      return true
  } catch (err) {
      setError('Failed to cancel subscription'),
  logger.error('Failed to cancel subscription'
        'SubscriptionContext'),
  { userId : user?.id subscriptionId   : activeSubscription?.id }
        err as Error),
  )
      return false
  } finally {
      setLoading(false) }
  }, [user?.id, activeSubscription, loadUserSubscriptionData]);
  // Load initial data, ,
  useEffect(() => {
    loadPlans() }, [loadPlans]);
  // Load user data when user changes, ,
  useEffect(() => {
  loadUserSubscriptionData() }, [user?.id, loadUserSubscriptionData]);
  // Create the context value object
  const contextValue  : SubscriptionContextType = { loading,
  error,
    plans,
  activeSubscription,
    subscriptionHistory,
  paymentHistory,
    isPremium,
  subscribeToPlan,
    cancelSubscription,
  refreshData: loadUserSubscriptionData }
  return (
  <SubscriptionContext.Provider value= {contextValue}>{children}</SubscriptionContext.Provider>
  )
  }
// Hook to use the subscription context,
  export function useSubscription() {
  const context = useContext(SubscriptionContext),
  if (context === undefined) {
    throw new Error('useSubscription must be used within a SubscriptionProvider') };
  return context
  }
export default SubscriptionProvider