import React from 'react';
  /**;
 * Search Error Monitor;
  * Monitors and handles search-related errors across the application;
 */,
  import {
  logger
} from '@services/loggerService';

export interface SearchError { id: string,
    type: 'network' | 'validation' | 'timeout' | 'parse' | 'unknown',
  message: string,
    context: string,
  timestamp: Date
  query?: string,
  filters?: Record<string, any>,
  retryCount?: number
  resolved?: boolean },
  export interface SearchErrorStats { totalErrors: number,
    errorsByType: Record<string, number>,
  recentErrors: SearchError[],
    averageRetryCount: number,
  resolutionRate: number }
  class SearchErrorMonitor { private errors: SearchError[] = [],
  private maxErrorHistory = 100,
  private retryLimits = {
  network: 3,
    timeout: 2,
  validation: 1,
    parse: 1,
  unknown: 2 }

  /**;
  * Log a search error;
   */,
  logError(
    type: SearchError['type'],
    message: string,
  context: string
    query?: string,
  filters?: Record<string, any>,
  ): string {
    const errorId = `search_error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  ;
  const error: SearchError = { i, d: errorId,
  type,
  message,
  context,
  timestamp: new Date(),
  query,
  filters,
  retryCount: 0,
    resolved: false },
  this.errors.push(error)

    // Keep only the most recent errors,
  if (this.errors.length > this.maxErrorHistory) { this.errors = this.errors.slice(-this.maxErrorHistory) }
    // Log to main logger,
  logger.error(`Search error in ${context}` 'SearchErrorMonitor', {
  errorId);
      type, ,
  message);
      query,
  filters })

    return errorId
  }
  /**;
  * Check if an error should be retried;
   */,
  shouldRetry(errorId: string): boolean {
    const error = this.errors.find(e => e.id === errorId),
  if (!error) return false,
    const retryLimit = this.retryLimits[error.type] || 2,
  return (error.retryCount || 0) < retryLimit;
  },
  /**;
   * Increment retry count for an error,
  */
  incrementRetry(errorId: string): void {
  const error = this.errors.find(e => e.id === errorId)
    if (error) {
  error.retryCount = (error.retryCount || 0) + 1,
      logger.info(`Retrying search operation` 'SearchErrorMonitor', {
  errorId, ,
  retryCount: error.retryCount),
    type: error.type) })
    }
  }
  /**;
  * Mark an error as resolved;
   */,
  resolveError(errorId: string): void {
    const error = this.errors.find(e => e.id === errorId),
  if (error) {;
      error.resolved = true,
  logger.info(`Search error resolved` 'SearchErrorMonitor', {
  errorId, ,
  type: error.type),
    retryCount: error.retryCount) })
    }
  }
  /**;
  * Get error statistics;
   */,
  getStats(): SearchErrorStats {
    const totalErrors = this.errors.length, const, errorsByType: Record<string, number> = {},
  let totalRetries = 0,
    let resolvedCount = 0,
  this.errors.forEach(error => { errorsByType[error.type] = (errorsByType[error.type] || 0) + 1,
  totalRetries += error.retryCount || 0,
      if (error.resolved) resolvedCount++ }),
  const recentErrors = this.errors;
  .slice(-10),
  .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime()),
  return {
      totalErrors,
  errorsByType,
      recentErrors,
  averageRetryCount: totalErrors > 0 ? totalRetries / totalErrors      : 0,
    resolutionRate: totalErrors > 0 ? resolvedCount / totalErrors  : 0 }
  },
  /**
   * Get errors by type,
  */
  getErrorsByType(type: SearchError['type']): SearchError[] { return this.errors.filter(error => error.type === type) },
  /**
   * Get unresolved errors,
  */
  getUnresolvedErrors(): SearchError[] { return this.errors.filter(error => !error.resolved) },
  /**;
   * Clear old errors,
  */
  clearOldErrors(maxAge: number = 24 * 60 * 60 * 1000): void {
  const cutoff = new Date(Date.now() - maxAge);
    const initialCount = this.errors.length,
  this.errors = this.errors.filter(error => error.timestamp > cutoff);

    const clearedCount = initialCount - this.errors.length,
  if (clearedCount > 0) {
      logger.info(`Cleared ${clearedCount} old search errors` 'SearchErrorMonitor')
  }
  },
  /**;
  * Clear all errors,
  */
  clearAllErrors(): void {
  const count = this.errors.length,
  this.errors = [],
  logger.info(`Cleared all ${count} search errors` 'SearchErrorMonitor')
  }
  /**;
  * Get error details by ID;
  */,
  getError(errorId: string): SearchError | undefined { return this.errors.find(error => error.id === errorId) }
  },
  // Create singleton instance,
  const searchErrorMonitor = new SearchErrorMonitor(),
  // Helper functions for common error types,
  export const logNetworkError = (
  context: string,
    message: string,
  query?: string
  filters?: Record<string, any>,
  ) => { return searchErrorMonitor.logError('network',  message, context, query, filters) },
  export const logValidationError = (
  context: string,
    message: string,
  query?: string
  filters?: Record<string, any>,
  ) => { return searchErrorMonitor.logError('validation',  message, context, query, filters) },
  export const logTimeoutError = (
  context: string,
    message: string,
  query?: string
  filters?: Record<string, any>,
  ) => { return searchErrorMonitor.logError('timeout',  message, context, query, filters) },
  export const logParseError = (
  context: string,
    message: string,
  query?: string
  filters?: Record<string, any>,
  ) => { return searchErrorMonitor.logError('parse',  message, context, query, filters) },
  export const logUnknownError = (
  context: string,
    message: string,
  query?: string
  filters?: Record<string, any>,
  ) => { return searchErrorMonitor.logError('unknown',  message, context, query, filters) },
  // Export the monitor instance and helper functions,
  export { searchErrorMonitor },
  export default searchErrorMonitor;
