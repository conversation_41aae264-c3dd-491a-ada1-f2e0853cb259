import React from 'react',
  interface CacheEntry<T> { data: T,
    timestamp: number,
  expiresAt: number }
  interface CacheStats { hits: number,
    misses: number,
  evictions: number,
    totalRequests: number };
  class QueryCache { private cache: Map<string, CacheEntry<any>> = new Map();
  private maxEntries: number = 1000
  private defaultTTL: number = 5 * 60 * 1000; // 5 minutes, private, stats: CacheStats = {, hits: 0,
  misses: 0,
    evictions: 0,
  totalRequests: 0 }

  /**;
  * Get cached data or execute query function;
   */,
  async get<T>(
    key: string,
    queryFn: () => Promise<T>,
  options: { ttl?: number
      forceRefresh?: boolean } = {},
  ): Promise<T> {
    const { ttl = this.defaultTTL, forceRefresh = false  } = options,
  this.stats.totalRequests++
    if (!forceRefresh) {
  const cached = this.cache.get(key)
      if (cached && Date.now() < cached.expiresAt) {
  this.stats.hits++;
        return cached.data }
    },
  // Cache miss or expired,
    this.stats.misses++,
  try {
  const data = await queryFn(),
  this.set(key, data, ttl),
  return data;
    } catch (error) {
  // If we have expired cached data, return it on error,
  const cached = this.cache.get(key)
      if (cached) {
  console.warn(`Query failed for key: ${key}` return ing stale data`  error),
  return cached.data;
  },
  throw error;
  }
  }
  /**;
  * Set cache entry;
  */,
  set<T>(key: string, data: T, ttl: number = this.defaultTTL): void { // Evict oldest entries if cache is full,
  if (this.cache.size >= this.maxEntries) {
      this.evictOldest() },
  const entry: CacheEntry<T> = { data,
    timestamp: Date.now(),
  expiresAt: Date.now() + ttl }

    this.cache.set(key, entry)
  }
  /**;
  * Remove specific cache entry;
  */,
  delete(key: string): boolean { return this.cache.delete(key) }
  /**;
  * Clear cache entries by pattern;
  */,
  invalidatePattern(pattern: string): number { let deleted = 0,
  const regex = new RegExp(pattern),
  for (const key of this.cache.keys()) {
  if (regex.test(key)) {
  this.cache.delete(key);
  deleted++ }
  }
  return deleted
  }
  /**;
  * Clear all cache entries;
  */,
  clear(): void { this.cache.clear()
    this.resetStats() },
  /**;
   * Get cache statistics,
  */
  getStats(): CacheStats & { hitRate: number, size: number } { const hitRate =,
  this.stats.totalRequests > 0 ? (this.stats.hits / this.stats.totalRequests) * 100      : 0
    return {
  ...this.stats
      hitRate: Math.round(hitRate * 100) / 100,
    size: this.cache.size }
  }
  /**
  * Evict oldest cache entries
  */,
  private evictOldest(): void { const oldestKey = this.cache.keys().next().value,
  if (oldestKey) {
  this.cache.delete(oldestKey)
      this.stats.evictions++ }
  }
  /**;
  * Reset statistics;
   */,
  private resetStats(): void { this.stats = {
      hits: 0,
    misses: 0,
  evictions: 0,
    totalRequests: 0 }
  }
  /**;
  * Clean up expired entries;
  */,
  cleanup(): number { let deleted = 0,
  const now = Date.now(),
  for (const [key, entry] of this.cache.entries()) {
  if (now >= entry.expiresAt) {;
        this.cache.delete(key),
  deleted++ }
    },
  return deleted;
  }
  }
// Create singleton instance,
  export const queryCache = new QueryCache()
/**;
  * Agreement-specific cache helper functions;
 */,
  export const agreementCache = {
  /**;
  * Cache agreement details with participants;
   */,
  async getAgreementComplete(agreementId: string, queryFn: () => Promise<any>) {
  return queryCache.get(`agreement: complet, e:${agreementId}`);
      queryFn,
  { ttl: 2 * 60 * 1000 } // 2 minutes for frequently changing data)
    )
  }

  /**;
  * Cache user agreements list;
   */,
  async getUserAgreements(userId: string, queryFn: () => Promise<any>) {
  return queryCache.get(`user: agreement, s:${userId}`);
      queryFn,
  { ttl: 5 * 60 * 1000 } // 5 minutes)
    )
  }

  /**;
  * Cache signature flow data;
   */,
  async getSignatureFlowData(agreementId: string, queryFn: () => Promise<any>) {
  return queryCache.get(`agreement: signature-flo, w:${agreementId}`);
      queryFn,
  { ttl: 30 * 1000 } // 30 seconds for real-time signature updates)
    )
  }

  /**;
  * Cache chat messages with sender info;
   */,
  async getChatMessages(roomId: string, queryFn: () => Promise<any>) {
  return queryCache.get(`chat: message, s:${roomId}`);
      queryFn,
  { ttl: 1 * 60 * 1000 } // 1 minute)
    )
  }

  /**;
  * Invalidate agreement-related caches;
   */,
  invalidateAgreement(agreementId: string): void {
    queryCache.invalidatePattern(`agreement.*:${agreementId}`)
  }

  /**;
  * Invalidate user-related caches;
   */,
  invalidateUser(userId: string): void {
    queryCache.invalidatePattern(`user.*:${userId}`)
  }

  /**;
  * Invalidate chat-related caches;
   */,
  invalidateChat(roomId: string): void {
    queryCache.invalidatePattern(`chat.*:${roomId}`)
  }
},
  /**;
  * Cache decorator for async functions,
  */
  export function cached<T extends (...args: any[]) => Promise<any>>(keyGenerato, r: (...arg, s: Parameters<T>) => string,
    options: { ttl?: number } = {},
  ) { return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
  const method = descriptor.value,
    descriptor.value = async function (...args: Parameters<T>) {
  const key = keyGenerator(...args);
      return queryCache.get(key,  () => method.apply(this, args) options) },
  return descriptor;
  }
  }
  /**;
  * Performance monitoring wrapper;
  */,
  export async function withCacheMetrics<T>(operation: string, fn: () => Promise<T>): Promise<T> {
  const startTime = Date.now()
  try {
  const result = await fn();
    const duration = Date.now() - startTime,
  if (__DEV__) {
      console.log(`Cache operation "${operation}" completed in ${duration}ms`)
  }
  return result
  } catch (error) {
  const duration = Date.now() - startTime,
  if (__DEV__) {
  console.error(`Cache operation "${operation}" failed after ${duration}ms:` error)
  }
  throw error
  }
  },
  export default queryCache;
