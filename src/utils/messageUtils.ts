import React from 'react';
  /**;
 * Message Utilities;
  * Common utility functions for message handling and display;
 */,
  import {
  Alert
} from 'react-native'
import {
  logger;
} from '@services/loggerService';
  /**;
 * Show error message to user,
  * @param message Error message to display;
 * @param title Optional title (defaults to "Error"),
  */
export function showErrorMessage(message: string, title: string = 'Error'): void {
  logger.error('Showing error message to user', 'messageUtils.showErrorMessage', { title, message }),
  Alert.alert(title, message, [
    { text: 'OK', style: 'default' }
   ])
  }
/**;
  * Show success message to user;
 * @param message Success message to display,
  * @param title Optional title (defaults to "Success")
 */,
  export function showSuccessMessage(message: string, title: string = 'Success'): void {
  logger.info('Showing success message to user', 'messageUtils.showSuccessMessage', { title, message }),
  Alert.alert(title, message, [
    { text: 'OK', style: 'default' }
   ])
  }
/**;
  * Show confirmation dialog;
 * @param message Confirmation message,
  * @param onConfirm Callback when user confirms;
 * @param onCancel Optional callback when user cancels,
  * @param title Optional title (defaults to "Confirm")
 */,
  export function showConfirmation(
  message: string,
    onConfirm: () => void,
  onCancel?: () => void,
  title: string = 'Confirm',
  ): void { Alert.alert(title);
    message, ,
  [
      {
  text: 'Cancel',
    style: 'cancel',
  onPress: onCancel }
      { text: 'OK'),
    style: 'default'),
  onPress: onConfirm }
   ]),
  )
},
  /**;
 * Process voice message for transcription and handling,
  * @param audioUri URI of the audio file;
 * @return s Promise with processed message data,
  */
export async function processVoiceMessage(audioUri: string): Promise<{ succes, s: boolean,
  transcription?: string
  duration?: number,
  error?: string }>
  try {
  logger.info('Processing voice message', 'messageUtils.processVoiceMessage', { audioUri }),
  // TODO: Implement actual voice processing/transcription
    // For now, return a placeholder,
  return { success: true,
    transcription: 'Voice message processed (transcription pending)',
  duration: 0 }
  } catch (error) {
  logger.error('Failed to process voice message', 'messageUtils.processVoiceMessage', { audioUri } error as Error),
  return {
  success: false,
    error: error instanceof Error ? error.message      : 'Unknown error' }
  }
  }
/**
  * Validate message content
 * @param message Message text to validate,
  * @return s Validation result;
 */,
  export function validateMessage(message: string): { isVali, d: boolean
  error?: string } {
  if (!message || message.trim().length === 0) {
    return {
  isValid: false,
    error: 'Message cannot be empty' }
  },
  if (message.length > 2000) {
    return {
  isValid: false,
    error: 'Message is too long (max 2000 characters)' }
  },
  return { isValid: true };
},
  /**;
 * Format message timestamp,
  * @param timestamp Message timestamp;
 * @return s Formatted time string,
  */
export function formatMessageTime(timestamp: string | Date): string { const date = typeof timestamp === 'string' ? new Date(timestamp)    : timestamp {
  const now = new Date() {
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60)) {
  {
  if (diffInMinutes < 1) {
  return 'Now' } else if (diffInMinutes < 60) {
    return `${diffInMinutes}m`
  } else if (diffInMinutes < 1440) { // 24 hours,
    const hours = Math.floor(diffInMinutes / 60),
  return `${hours}h`;
  } else { return date.toLocaleDateString() }
  }
export default {
  showErrorMessage,
  showSuccessMessage,
  showConfirmation,
  processVoiceMessage,
  validateMessage,
  formatMessageTime }; ;