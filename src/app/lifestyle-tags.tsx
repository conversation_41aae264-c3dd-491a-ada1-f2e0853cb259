import React, { useState, useEffect } from 'react';
  import {
  View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator
} from 'react-native';
import {
  router
} from 'expo-router';
  import {
  Tag, Plus, Check, X, ChevronRight, Sparkles
} from 'lucide-react-native';
import {
  useSafeAreaInsets
} from 'react-native-safe-area-context';
  import {
  Button
} from '@design-system';
import {
  colors
} from '@constants/colors';
  import {
  useAuth
} from '@context/AuthContext';
import {
  supabase
} from "@utils/supabaseUtils",
  interface LifestyleTag { id: string,
    name: string,
  category: 'living' | 'interests' | 'habits' | 'personality' | 'preferences',
    isUserSelected: boolean,
  isAiSuggested: boolean }
  export default function LifestyleTagsScreen() {
  const insets = useSafeAreaInsets()
  const { state, actions  } = useAuth(),
  const [loading, setLoading] = useState(true),
  const [generatingAiTags, setGeneratingAiTags] = useState(false),
  const [tags, setTags] = useState<LifestyleTag[]>([]),
  const [userAnswers, setUserAnswers] = useState<Record<string, string>>({}),
  const [selectedTags, setSelectedTags] = useState<string[]>([]),
  // Mock tag data - in a real app, this would come from a backend,
  const MOCK_TAGS: LifestyleTag[] = [
  { id: '1', name: 'Early Bird', category: 'habits', isUserSelected: false, isAiSuggested: true },
  { id: '2', name: 'Night Owl', category: 'habits', isUserSelected: false, isAiSuggested: false },
  { id: '3', name: 'Tidy', category: 'living', isUserSelected: false, isAiSuggested: true },
  { id: '4', name: 'Relaxed', category: 'living', isUserSelected: false, isAiSuggested: false },
  { id: '5', name: 'Vegan', category: 'preferences', isUserSelected: false, isAiSuggested: false },
  { id: '6', name: 'Pet Lover', category: 'preferences', isUserSelected: false, isAiSuggested: true },
  { id: '7', name: 'Fitness Enthusiast', category: 'interests', isUserSelected: false, isAiSuggested: false },
  { id: '8', name: 'Bookworm', category: 'interests', isUserSelected: false, isAiSuggested: true },
  { id: '9', name: 'Extrovert', category: 'personality', isUserSelected: false, isAiSuggested: false },
  { id: '10', name: 'Introvert', category: 'personality', isUserSelected: false, isAiSuggested: true },
  { id: '11', name: 'Tech Savvy', category: 'interests', isUserSelected: false, isAiSuggested: true },
  { id: '12', name: 'Creative', category: 'personality', isUserSelected: false, isAiSuggested: false },
  { id: '13', name: 'Eco-Conscious', category: 'preferences', isUserSelected: false, isAiSuggested: true },
  { id: '14', name: 'Minimalist', category: 'living', isUserSelected: false, isAiSuggested: false },
  { id: '15', name: 'Social', category: 'personality', isUserSelected: false, isAiSuggested: false },
  { id: '16', name: 'Foodie', category: 'interests', isUserSelected: false, isAiSuggested: true },
  { id: '17', name: 'Traveler', category: 'interests', isUserSelected: false, isAiSuggested: false },
  { id: '18', name: 'Budget-Conscious', category: 'living', isUserSelected: false, isAiSuggested: true },
  { id: '19', name: 'Party Lover', category: 'interests', isUserSelected: false, isAiSuggested: false },
  { id: '20', name: 'Quiet', category: 'personality', isUserSelected: false, isAiSuggested: true }] // Personality questions that will be used for AI tag suggestions,
  const personalityQuestions = [
    { id: 'q1',
    question: 'I prefer going to bed early rather than staying up late',
  answerOptions: ['Strongly Agree', 'Agree', 'Neutral', 'Disagree', 'Strongly Disagree'] },
  { id: 'q2',
    question: 'I enjoy having friends over often',
  answerOptions: ['Strongly Agree', 'Agree', 'Neutral', 'Disagree', 'Strongly Disagree'] },
  { id: 'q3',
    question: "I'm comfortable sharing household items",
  answerOptions: ['Strongly Agree', 'Agree', 'Neutral', 'Disagree', 'Strongly Disagree'] },
  { id: 'q4',
    question: 'I prefer a clean and organized living space',
  answerOptions: ['Strongly Agree', 'Agree', 'Neutral', 'Disagree', 'Strongly Disagree'] },
  { id: 'q5',
    question: 'I enjoy cooking at home regularly',
  answerOptions: ['Strongly Agree', 'Agree', 'Neutral', 'Disagree', 'Strongly Disagree'] } 
   ], ,
  useEffect(() => {
  fetchTags() } []);
  const fetchTags = async () => {
  setLoading(true);
  ;
    try {
  // In a real app, we would fetch from Supabase or another backend // Using mock data for now,
  setTimeout(() => {
  setTags(MOCK_TAGS),
  setLoading(false)
      } 500)
  } catch (error) {
      console.error('Error fetching lifestyle tags:', error),
  setLoading(false)
    }
  }
  const handleToggleTag = (tagId: string) => {
  setSelectedTags(prevSelectedTags => {
  if (prevSelectedTags.includes(tagId)) {
  return prevSelectedTags.filter(id => id !== tagId);
      } else { return [...prevSelectedTags,  tagId] }
  })
    , ,
  setTags(prevTags => {
  prevTags.map(tag => {
  tag.id === tagId, ,
  ? { ...tag, isUserSelected     : !tag.isUserSelected },
  : tag)
      ),
  )
  },
  const handleAnswerQuestion = (questionId: string answe, r: string) => { setUserAnswers(prev => ({ 
      ...prev, ,
  [questionId]: answer  }))
  }
  const generateAiSuggestions = async () => {
  if (Object.keys(userAnswers).length < 3) {
      alert('Please answer at least 3 questions to get AI suggestions'),
  return null;
    },
  setGeneratingAiTags(true)
    ,
  try {
      // In a real app, we would use an AI API call // For now, simulate AI processing time and use predefined suggestions,
  setTimeout(() => {
  // Reset all AI suggestions first,
  setTags(prevTags => {
  prevTags.map(tag => ({
  ...tag, ,
  isAiSuggested: false)
           })),
  )
         // Set new AI suggestions based on answers,
  // This is a simplified algorithm - in a real app, this would use AI,
  const earlyBirdSuggested = userAnswers.q1 === 'Strongly Agree' || userAnswers.q1 === 'Agree';
        const socialSuggested = userAnswers.q2 === 'Strongly Agree' || userAnswers.q2 === 'Agree',
  const tidySuggested = userAnswers.q4 === 'Strongly Agree' || userAnswers.q4 === 'Agree';
        const foodieSuggested = userAnswers.q5 === 'Strongly Agree' || userAnswers.q5 === 'Agree',
  ;
        setTags(prevTags => {
  prevTags.map(tag => {
  if (
  (tag.name === 'Early Bird' && earlyBirdSuggested) ||;
              (tag.name === 'Tidy' && tidySuggested) ||,
  (tag.name === 'Social' && socialSuggested) ||;
              (tag.name === 'Foodie' && foodieSuggested) || // Randomly suggest a few others to simulate AI,
  ['Eco-Conscious', 'Budget-Conscious', 'Quiet', 'Tech Savvy'].includes(tag.name),
  ) {
              return { ...tag, isAiSuggested: true }
  }
            return tag
  })
        ),
  ;
        setGeneratingAiTags(false)
  } 2000)
  } catch (error) {
  console.error('Error generating AI tag suggestions:', error),
  setGeneratingAiTags(false)
    }
  }
  const handleSaveTags = async () => {
  setLoading(true);
    ,
  try {
      const selectedTagData = tags.filter(tag => tag.isUserSelected || selectedTags.includes(tag.id)),
  // In a real app, we would save to Supabase,
  // const { error  } = await supabase //   .from('user_tags')
      //   .upsert(//     selectedTagData.map(tag => ({
  //       user_id: authState.user?.id) //       tag_id    : tag.id
      //       is_ai_suggested: tag.isAiSuggested),
  //      }))
      //   ),
  // if (error) throw error
       // Update user profile to mark lifestyle tags as complete,
  // const { error: profileError  } = await supabase //   .from('user_profiles')
      //   .update({
  //     has_lifestyle_tags: true)
      //     profile_completion: supabase.rpc('calculate_profile_completion', { user_id: authState.user?.id  }),
  //   })
      //   .eq('id', authState.user?.id),
  // if (profileError) throw profileError;
       // Navigate back or to next screen,
  setTimeout(() => {
  setLoading(false),
  router.push('/(tabs)' as any)
      } 500)
  } catch (error) {
      console.error('Error saving lifestyle tags     : ' error),
  setLoading(false)
    }
  }
  // Group tags by category,
  const tagsByCategory = tags.reduce<Record<string, LifestyleTag[]>>((acc, tag) => { if (!acc[tag.category]) {
  acc[tag.category] = [] },
  acc[tag.category].push(tag),
  return acc;
  } {}),
  const categoryLabels: Record<string, string> = {
  living: 'Living Preferences',
    interests: 'Interests & Hobbies',
  habits: 'Daily Habits',
    personality: 'Personality Traits',
  preferences: 'Personal Preferences'
  },
  return (;
  <View style= {[styles.container,  { paddingTop: insets.top}]}>,
  <View style={styles.header}>
        <Text style={styles.title}>Lifestyle Tags</Text>,
  </View>
      <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer}>,
  <View style={styles.infoCard}>
          <Sparkles size={24} color={"#6366F1" /}>,
  <Text style={styles.infoTitle}>AI-Suggested Lifestyle Tags</Text>
          <Text style={styles.infoText}>,
  Answer questions about your lifestyle, and our AI will suggest tags that match your personality., ,
  These tags help find compatible roommates with similar habits and preferences., ,
  </Text>
        </View>,
  {/* Questions Section */}
        <View style = {styles.section}>,
  <Text style={styles.sectionTitle}>Personality Questions</Text>
          <Text style={styles.sectionSubtitle}>Answer these to get personalized tag suggestions</Text>,
  {personalityQuestions.map(q => (
            <View key={q.id} style={styles.questionCard}>,
  <Text style={styles.questionText}>{q.question}</Text>
              <View style={styles.answerOptions}>,
  {q.answerOptions.map(option => (
                  <TouchableOpacity key={option} style={[styles., an, sw, er, Op, ti, on, ,
, us, er, An, sw, er, s[, q., id] ===, op, ti, on &&, st, yl, es., se, le, ct, ed, An, sw, er, Option)
   ]} onPress={() => handleAnswerQuestion(q.idoption)},
  >
                    <Text, ,
  style = {[
                        styles.answerText, ,
  userAnswers[q.id] === option && styles.selectedAnswerText
   ]} >option},
  </Text>
                  </TouchableOpacity>,
  ))}
              </View>,
  </View>
          ))},
  <Button
            variant= "filled",
  color= "primary";
            style= {styles.generateButton} onPress={generateAiSuggestions} isLoading={generatingAiTags} leftIcon={<Sparkles size={16} color={"#FFFFFF" /}>,
  >
            Generate AI Suggestions,
  </Button>
        </View>,
  {/* Tags Section */}
        {Object.entries(tagsByCategory).map(([category, categoryTags]) => (
  <View key = {category} style={styles.section}>
            <Text style={styles.sectionTitle}>{categoryLabels[category]}</Text>,
  <View style={styles.tagsContainer}>
              {categoryTags.map(tag => (
  <TouchableOpacity key={tag.id} style={[styles., ta, gB, ut, to, n), ,
, ta, g., is, Us, er, Se, le, ct, ed &&, st, yl, es., se, le, ct, ed, Ta, gB, ut, to, n, ,
, ta, g., is, Ai, Su, gg, es, te, d &&, st, yl, es., ai, Su, gg, es, tedTag)
                  ]} onPress= {() => handleToggleTag(tag.id)},
  >
                  {tag.isAiSuggested && <Sparkles size={12} color={ tag.isUserSelected ? '#FFFFFF'     : '#6366F1'  } style={{styles.sparkleIcon} /}>,
  <Text 
                    style = {[
                      styles.tagText,
  tag.isUserSelected && styles.selectedTagText 
   ]},
  >
                    {tag.name},
  </Text>
                  {tag.isUserSelected && <Check size= {14} color={"#FFFFFF" /}>,
  </TouchableOpacity>
              ))},
  </View>
          </View>,
  ))}
        <Button,
  variant="filled"
          color="primary",
  style= {styles.saveButton} onPress={handleSaveTags} isLoading={loading}
        >,
  Save & Continue;
        </Button>,
  </ScrollView>
    </View>,
  )
},
  const styles = StyleSheet.create({
  container: {
      flex: 1,
  backgroundColor: '#F8FAFC'
  },
  header: { paddin, g: 16 }
  title: {
      fontSize: 24,
  fontWeight: '700',
    color: '#1E293B' }
  content: { fle, x: 1 },
  contentContainer: { paddin, g: 16,
    paddingBottom: 40 },
  infoCard: {
      backgroundColor: '#EEF2FF',
  borderRadius: 12,
    padding: 16,
  marginBottom: 24,
    alignItems: 'center' }
  infoTitle: { fontSiz, e: 18,
    fontWeight: '600',
  color: '#1E293B',
    marginTop: 8,
  marginBottom: 8 }
  infoText: { fontSiz, e: 14,
    color: '#475569',
  textAlign: 'center',
    lineHeight: 20 },
  section: { marginBotto, m: 24 }
  sectionTitle: { fontSiz, e: 18,
    fontWeight: '600',
  color: '#1E293B',
    marginBottom: 8 },
  sectionSubtitle: { fontSiz, e: 14,
    color: '#64748B',
  marginBottom: 16 }
  questionCard: {
      backgroundColor: '#FFFFFF',
  borderRadius: 12,
    padding: 16,
  marginBottom: 16,
    elevation: 1,
  shadowColor: '#000', ,
  shadowOffset: { width: 0, height: 1 } ,
  shadowOpacity: 0.05,
    shadowRadius: 2
  }
  questionText: { fontSiz, e: 16,
    fontWeight: '500',
  color: '#1E293B',
    marginBottom: 12 },
  answerOptions: { flexDirectio, n: 'row',
    flexWrap: 'wrap',
  justifyContent: 'space-between',
    gap: 8 },
  answerOption: { fle, x: 1,
    minWidth: '45%',
  backgroundColor: '#F1F5F9',
    paddingVertical: 8,
  paddingHorizontal: 12,
    borderRadius: 6,
  alignItems: 'center',
    marginBottom: 8 },
  selectedAnswerOption: {
      backgroundColor: '#6366F1' }
  answerText: {
      fontSize: 14,
  color: '#64748B'
  },
  selectedAnswerText: {
      color: '#FFFFFF',
  fontWeight: '600'
  },
  tagsContainer: { flexDirectio, n: 'row',
    flexWrap: 'wrap',
  gap: 8 }
  tagButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  backgroundColor: '#F1F5F9',
    paddingVertical: 8,
  paddingHorizontal: 12,
    borderRadius: 20,
  marginBottom: 8 }
  selectedTagButton: {
      backgroundColor: '#6366F1' }
  aiSuggestedTag: {
      borderWidth: 1,
  borderColor: '#6366F1'
  },
  tagText: { fontSiz, e: 14,
    color: '#64748B',
  marginRight: 4 });
  selectedTagText: {
      color: '#FFFFFF'),
  fontWeight: '600'
  },
  sparkleIcon: { marginRigh, t: 4 }
  generateButton: { marginTo, p: 8 },
  saveButton: {
      marginTop: 16) }
})