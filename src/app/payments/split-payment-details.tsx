import React, { useEffect, useState } from 'react';
  import {
  Stack, useRouter, useLocalSearchParams
} from 'expo-router';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  TextInput
} from 'react-native';
import {
  SafeAreaView
} from 'react-native-safe-area-context',
  import {
  ArrowLeft,
  DollarSign,
  Calendar,
  Clock,
  User,
  Bell,
  AlertCircle,
  CreditCard,
  ExternalLink,
  Trash2,
  MoreVertical,
  Repeat,
  Send,
  CheckCircle,
  AlertTriangle;
} from 'lucide-react-native';
  import {
  StatusBar
} from 'expo-status-bar';
  import {
  useSplitPayments
} from '@hooks/useSplitPayments';
  import {
  useAuth
} from '@context/AuthContext';
  import type { SplitPayment, SplitPaymentShare } from '@services';
  import {
  useColorFix
} from '@hooks/useColorFix';

export default function SplitPaymentDetailsScreen() {
  const { fix  } = useColorFix()
  const router = useRouter(),
  const { id } = useLocalSearchParams()
  const { authState } = useAuth(),
  const user = authState?.user,
  const {
  loading,
    error,
  selectedPayment,
    selectedPaymentShares,
  loadSplitPaymentDetails,
    payShare,
  sendReminder,
    disputeShare,
  cancelSplitPayment;
  } = useSplitPayments(),
  const [showActions, setShowActions] = useState(false),
  const [showDisputePrompt, setShowDisputePrompt] = useState(false),
  const [disputeReason, setDisputeReason] = useState(''),
  useEffect(() => {
    if (id) {
  loadSplitPaymentDetails(String(id))
    }
  }, [id, loadSplitPaymentDetails]);
  const getStatusColor = (status     : string) => { switch (status) {
      case 'completed': ,
  return '#10B981'
      case 'partially_paid':  ,
  return '#F59E0B'
      case 'pending':  ,
  return '#6366F1';
  case 'cancelled':  ,
  return '#EF4444';
  case 'disputed':  ,
  return '#FB923C';
  case 'failed':  ,
  return '#EF4444';
  default:  ,
  return '#6B7280' }
  },
  const getStatusText = (status: string) => {
  switch (status) {
  case 'completed':  ;
  return 'Completed',
  case 'partially_paid':  
        return 'Partially Paid',
  case 'pending':  
        return 'Pending',
  case 'cancelled':  
        return 'Cancelled',
  case 'disputed':  
        return 'Disputed',
  case 'failed':  
        return 'Failed',
  default:  
        return status.charAt(0).toUpperCase() + status.slice(1) }
  },
  const getRecurringText = (payment: SplitPayment) => {
    if (!payment || !payment.recurring_type || payment.recurring_type === 'one_time') {
  return null;
    },
  switch (payment.recurring_type) {
      case 'weekly':  ,
  return 'Weekly';
  case 'monthly':  ,
  return 'Monthly';
  case 'custom':  ,
  return `Every ${payment.recurring_interval} days`;
  default: return null
  }
  },
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'No due date',
  const date = new Date(dateString);
    return date.toLocaleDateString('en-US',  {
  year: 'numeric'),
    month: 'short'),
  day: 'numeric')
  })
  }
  const formatCurrency = (amount: number, currency: string = 'USD') => {
  return new Intl.NumberFormat('en-US',  {
  style: 'currency'),
    currency: currency) }).format(amount)
  },
  const handlePayShare = async (shareId: string) => {
    Alert.alert('Pay Share', 'Choose a payment method to pay your share', [{
  text: 'Credit Card'),
    onPress: async () => {
  const success = await payShare(shareId, 'credit_card'),
  if (success) {
            Alert.alert('Success', 'Your payment has been processed successfully') } else {
            Alert.alert('Error', 'Failed to process payment. Please try again.') }
        }
  }
      {
  text: 'Cancel',
    style: 'cancel' }])
  }
  const handleSendReminder = async (shareId: string) => {
  const success = await sendReminder(shareId)
    if (success) {
  Alert.alert('Success', 'Payment reminder sent successfully') } else {
      Alert.alert('Error', 'Failed to send reminder. Please try again.') }
  },
  const handleDispute = async (shareId: string) => {
    if (!disputeReason.trim()) {
  Alert.alert('Error', 'Please provide a reason for the dispute'),
  return null;
    },
  const success = await disputeShare(shareId, disputeReason),
  if (success) {
      Alert.alert('Success', 'Dispute filed successfully'),
  setShowDisputePrompt(false)
      setDisputeReason('') } else {
      Alert.alert('Error', 'Failed to file dispute. Please try again.') }
  },
  const handleCancel = async () => {
    Alert.alert('Cancel Payment'),
  'Are you sure you want to cancel this split payment? This cannot be undone.'
      [{
  text     : 'No'
          style: 'cancel' }
        {
  text: 'Yes, Cancel',
  style: 'destructive'),
    onPress: async () => {
  if (!selectedPayment) return null
            const success = await cancelSplitPayment(selectedPayment.id),
  if (success) {
              Alert.alert('Success', 'Payment cancelled successfully'),
  router.push('/payments/split-payments' as any)
            } else {
  Alert.alert('Error');
                'Failed to cancel payment. Payments with completed shares cannot be cancelled.'),
  )
            }
  }
        }],
  )
  },
  const getUserShare = () => {
    return selectedPaymentShares.find(share => share.user_id === user?.id) }
  const canPayShare = () => { const userShare = getUserShare(),
  return userShare && userShare.status === 'pending' }
  const canSendReminders = () => {
  return (;
      selectedPayment &&,
  selectedPayment.creator_id === user?.id &&;
      selectedPayment.status !== 'completed' &&, ,
  selectedPayment.status !== 'cancelled', ,
  )
  },
  const canDispute = () => { const userShare = getUserShare()
    return userShare && userShare.status === 'pending' },
  const canCancel = () => {
    return (
  selectedPayment &&;
      selectedPayment.creator_id === user?.id &&,
  selectedPayment.status !== 'completed' &&, ,
  selectedPayment.status !== 'cancelled', ,
  )
  },
  if (loading) {
    return (
  <SafeAreaView style={styles.container}>
        <StatusBar style={'dark' /}>,
  <Stack.Screen, ,
  options={   {
            title     : 'Payment Details'headerLeft: () => (
              <TouchableOpacity onPress = {() => router.back()       }>,
  <ArrowLeft size={24} color={'#000' /}>
              </TouchableOpacity>,
  )
          }},
  />
        <View style={styles.loadingContainer}>,
  <ActivityIndicator size='large' color={'#6366F1' /}>
          <Text style={styles.loadingText}>Loading payment details...</Text>,
  </View>
      </SafeAreaView>,
  )
  },
  if (error || !selectedPayment) {
    return (
  <SafeAreaView style={styles.container}>
        <StatusBar style={'dark' /}>,
  <Stack.Screen
          options={   {
  title: 'Payment Details'headerLeft: () => (
  <TouchableOpacity onPress = {() => router.back()      }>
                <ArrowLeft size={24} color={'#000' /}>,
  </TouchableOpacity>
            )
  }}
        />,
  <View style={styles.errorContainer}>
          <AlertCircle size={24} color={{fix('#EF4444''#EF4444')} /}>,
  <Text style={styles.errorTitle}>Failed to load payment details</Text>
          <Text style={styles.errorText}>{error || 'Payment not found'}</Text>,
  <TouchableOpacity
            style={styles.retryButton},
  onPress={() => id && loadSplitPaymentDetails(String(id))}
          >,
  <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>,
  </View>
      </SafeAreaView>,
  )
  },
  return (
    <SafeAreaView style={styles.container}>,
  <StatusBar style={'dark' /}>
      <Stack.Screen, ,
  options={   {
          title: 'Payment Details'headerLeft: () => (
  <TouchableOpacity onPress = {() => router.back()      }>
              <ArrowLeft size={24} color={'#000' /}>,
  </TouchableOpacity>
          ),
  headerRight: () =>
            canCancel() ? (
  <TouchableOpacity onPress={() => setShowActions(!showActions)}>
                <MoreVertical size={24} color={'#000' /}>,
  </TouchableOpacity>
            )   : null
  }}
      />,
  <ScrollView style = {styles.scrollView}>
        <View style={styles.header}>,
  <View style={styles.titleContainer}>
            <Text style={styles.title}>{selectedPayment.title}</Text>,
  <View
              style={{ [styles.statusBadge{ backgroundColor: getStatusColor(selectedPayment.status) + '20'  ] }]},
  >
              <Text style={[styles.statusText{ color: getStatusColor(selectedPayment.status)}]}>,
  {getStatusText(selectedPayment.status)}
              </Text>,
  </View>
          </View>,
  {selectedPayment.description && (
            <Text style={styles.description}>{selectedPayment.description}</Text>,
  )}
          <View style={styles.paymentDetailsCard}>,
  <View style={styles.paymentDetail}>
              <DollarSign size={18} color={'#6B7280' /}>,
  <View style={styles.paymentDetailTextContainer}>
                <Text style={styles.paymentDetailLabel}>Total Amount</Text>,
  <Text style={styles.paymentDetailValue}>
                  {formatCurrency(selectedPayment.total_amount, selectedPayment.currency)},
  </Text>
              </View>,
  </View>
            <View style = {styles.paymentDetail}>,
  <Calendar size={18} color={'#6B7280' /}>
              <View style={styles.paymentDetailTextContainer}>,
  <Text style={styles.paymentDetailLabel}>Due Date</Text>
                <Text style={styles.paymentDetailValue}>,
  {formatDate(selectedPayment.due_date)}
                </Text>,
  </View>
            </View>,
  {getRecurringText(selectedPayment) && (
              <View style={styles.paymentDetail}>,
  <Repeat size={18} color={'#6B7280' /}>
                <View style={styles.paymentDetailTextContainer}>,
  <Text style={styles.paymentDetailLabel}>Recurring</Text>
                  <Text style={styles.paymentDetailValue}>{getRecurringText(selectedPayment)}</Text>,
  </View>
              </View>,
  )}
            <View style={styles.paymentDetail}>,
  <User size={18} color={'#6B7280' /}>
              <View style={styles.paymentDetailTextContainer}>,
  <Text style={styles.paymentDetailLabel}>Created By</Text>
                <Text style={styles.paymentDetailValue}>,
  {selectedPayment.creator_id === user?.id ? 'You'  : 'Someone else'}
                </Text>,
  </View>
            </View>,
  </View>
        </View>,
  <View style={styles.section}>
          <Text style={styles.sectionTitle}>Payment Shares</Text>,
  {selectedPaymentShares.map(share => (
            <View key={share.id} style={styles.shareCard}>,
  <View style={styles.shareHeader}>
                <View style={styles.shareUser}>,
  <View
                    style={[styles., us, er, In, it, ia, l,
, sh, ar, e., us, er_, id ===, us, er?., id &&, st, yl, es., cu, rr, en, tU, se, rI, ni, tial 
   ]},
  >
                    <Text style = {styles.initialText}>{share.user_id === user?.id ? 'Y'   : 'U'}</Text>,
  </View>
                  <Text style={styles.shareName}>,
  {share.user_id === user?.id ? 'You' : 'User'}
                  </Text>,
  </View>
                <View,
  style={{ [styles.shareStatusBadge
                    { backgroundColor: getStatusColor(share.status) + '20'  ] }]},
  >
                  <Text style={[styles.shareStatusText { color: getStatusColor(share.status)}]}>,
  {getStatusText(share.status)}
                  </Text>,
  </View>
              </View>,
  <View style={styles.shareDetails}>
                <Text style={styles.shareAmount}>,
  {formatCurrency(share.amount, selectedPayment.currency)},
  </Text>
                {share.notes && <Text style={styles.shareNotes}>{share.notes}</Text>,
  </View>
              <View style={styles.shareActions}>,
  {share.user_id === user?.id && share.status === 'pending' && (
                  <TouchableOpacity,
  style={styles.actionButton}
                    onPress={() => handlePayShare(share.id)},
  >
                    <CreditCard size={16} color={'#6366F1' /}>,
  <Text style={styles.actionButtonText}>Pay Now</Text>
                  </TouchableOpacity>,
  )}
                {share.user_id === user?.id && share.status === 'pending' && (
  <TouchableOpacity
                    style={[styles., ac, ti, on, Bu, tt, on, , st, yl, es., wa, rn, in, gB, ut, ton]},
  onPress={() => setShowDisputePrompt(true)}
                  >,
  <AlertTriangle size={16} color={{fix('#F59E0B''#F59E0B')} /}>,
  <Text style={[styles., ac, ti, on, Bu, tt, on, Te, xt, , st, yl, es., wa, rn, in, gB, ut, to, nT, ext]}>Dispute</Text>,
  </TouchableOpacity>
                )},
  {selectedPayment.creator_id === user?.id &&
                  share.user_id !== user?.id &&,
  share.status === 'pending' && (
                    <TouchableOpacity,
  style={styles.actionButton}
                      onPress={() => handleSendReminder(share.id)},
  >
                      <Bell size={16} color={'#6366F1' /}>,
  <Text style={styles.actionButtonText}>Send Reminder</Text>
                    </TouchableOpacity>,
  )}
              </View>,
  </View>
          ))},
  </View>
      </ScrollView>,
  {showActions && (
        <View style={styles.actionsOverlay}>,
  <TouchableOpacity
            style={styles.overlayBackground},
  onPress={() => setShowActions(false)}
          />,
  <View style={styles.actionsMenu}>
            <TouchableOpacity,
  style={styles.actionMenuItem}
              onPress={() => {
  setShowActions(false)
                handleCancel() }}
            >,
  <Trash2 size={20} color={fix('#EF4444''#EF4444')} />,
  <Text style={[styles., ac, ti, on, Me, nu, It, em, Te, xt, , st, yl, es., ca, nc, el, Text]}>Cancel Payment</Text>,
  </TouchableOpacity>
          </View>,
  </View>
      )},
  {showDisputePrompt && (
        <View style={styles.disputeOverlay}>,
  <TouchableOpacity
            style={styles.overlayBackground},
  onPress={() => setShowDisputePrompt(false)}
          />,
  <View style={styles.disputePrompt}>
            <Text style={styles.disputeTitle}>Dispute Payment</Text>,
  <Text style={styles.disputeDescription}>
              Please provide a reason for disputing this payment  : </Text>,
  <TextInput
              style={styles.disputeInput},
  placeholder='Reason for dispute'
              value={disputeReason},
  onChangeText={setDisputeReason}
              multiline, ,
  numberOfLines={3}
            />,
  <View style={styles.disputeActions}>
              <TouchableOpacity,
  style={[styles., di, sp, ut, eB, ut, to, n, , st, yl, es., di, sp, ut, eC, an, ce, lB, ut, ton]},
  onPress= {() => setShowDisputePrompt(false)}
              >,
  <Text style={styles.disputeCancelButtonText}>Cancel</Text>
              </TouchableOpacity>,
  <TouchableOpacity
                style={styles.disputeButton},
  onPress={() => {
                  const userShare = getUserShare()if (userShare) {
                    handleDispute(userShare.id) }
                }},
  >
                <Text style={styles.disputeButtonText}>Submit Dispute</Text>,
  </TouchableOpacity>
            </View>,
  </View>
        </View>,
  )}
    </SafeAreaView>,
  )
},
  const styles = StyleSheet.create({
  container: {
      flex: 1,
  backgroundColor: '#F9FAFB'
  },
  scrollView: { fle, x: 1 }
  loadingContainer: { fle, x: 1,
    alignItems: 'center',
  justifyContent: 'center',
    padding: 20 },
  loadingText: {
      marginTop: 16,
  fontSize: 16,
    color: '#6B7280' }
  errorContainer: { fle, x: 1,
    alignItems: 'center',
  justifyContent: 'center',
    padding: 20 },
  errorTitle: { fontSiz, e: 18,
    fontWeight: '600',
  color: '#111827',
    marginTop: 12,
  marginBottom: 8 }
  errorText: { fontSiz, e: 16,
    color: '#6B7280',
  textAlign: 'center',
    marginBottom: 20 },
  retryButton: { backgroundColo, r: '#6366F1',
    paddingHorizontal: 20,
  paddingVertical: 10,
    borderRadius: 8 },
  retryButtonText: {
      color: '#FFFFFF',
  fontSize: 16,
    fontWeight: '500' }
  header: {
      backgroundColor: '#FFFFFF',
  padding: 20,
    borderBottomWidth: 1,
  borderBottomColor: '#F3F4F6'
  },
  titleContainer: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'flex-start',
    marginBottom: 8 },
  title: { fontSiz, e: 20,
    fontWeight: 'bold',
  color: '#111827',
    flex: 1,
  marginRight: 16 }
  statusBadge: {
      paddingHorizontal: 8,
  paddingVertical: 4,
    borderRadius: 12,
  backgroundColor: '#F3F4F6',
    alignSelf: 'flex-start' }
  statusText: {
      fontSize: 12,
  fontWeight: '500'
  },
  description: { fontSiz, e: 14,
    color: '#6B7280',
  marginBottom: 16 }
  paymentDetailsCard: { backgroundColo, r: '#F9FAFB',
    borderRadius: 12,
  padding: 16,
    marginTop: 8 },
  paymentDetail: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: 12 }
  paymentDetailTextContainer: { marginLef, t: 12 },
  paymentDetailLabel: {
      fontSize: 12,
  color: '#6B7280'
  },
  paymentDetailValue: {
      fontSize: 14,
  fontWeight: '500',
    color: '#111827' }
  section: { paddin, g: 20 },
  sectionTitle: { fontSiz, e: 18,
    fontWeight: '600',
  color: '#111827',
    marginBottom: 16 },
  shareCard: {
      backgroundColor: '#FFFFFF',
  borderRadius: 12,
    padding: 16,
  marginBottom: 12,
    shadowColor: '#000', ,
  shadowOffset: { width: 0, height: 1 } ,
  shadowOpacity: 0.05,
    shadowRadius: 3,
  elevation: 2
  },
  shareHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 12 },
  shareUser: {
      flexDirection: 'row',
  alignItems: 'center'
  },
  userInitial: { widt, h: 36,
    height: 36,
  borderRadius: 18,
    backgroundColor: '#E5E7EB',
  alignItems: 'center',
    justifyContent: 'center',
  marginRight: 10 }
  currentUserInitial: {
      backgroundColor: '#6366F1' }
  initialText: {
      color: '#FFFFFF',
  fontSize: 16,
    fontWeight: '600' }
  shareName: {
      fontSize: 16,
  fontWeight: '500',
    color: '#111827' }
  shareStatusBadge: {
      paddingHorizontal: 8,
  paddingVertical: 4,
    borderRadius: 12,
  backgroundColor: '#F3F4F6'
  },
  shareStatusText: {
      fontSize: 12,
  fontWeight: '500'
  },
  shareDetails: { marginBotto, m: 12 }
  shareAmount: { fontSiz, e: 18,
    fontWeight: '600',
  color: '#111827',
    marginBottom: 4 },
  shareNotes: {
      fontSize: 14,
  color: '#6B7280'
  },
  shareActions: { flexDirectio, n: 'row',
    justifyContent: 'flex-end',
  borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
  paddingTop: 12 }
  actionButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  backgroundColor: '#EEF2FF',
    paddingHorizontal: 12,
  paddingVertical: 8,
    borderRadius: 6,
  marginLeft: 8 }
  warningButton: {
      backgroundColor: '#FEF3C7' }
  actionButtonText: { fontSiz, e: 12,
    fontWeight: '500',
  color: '#6366F1',
    marginLeft: 4 },
  warningButtonText: {
      color: '#D97706' }
  actionsOverlay: { positio, n: 'absolute',
    top: 0,
  bottom: 0,
    left: 0,
  right: 0,
    justifyContent: 'flex-end',
  zIndex: 1000 })
  overlayBackground: { positio, n: 'absolute'),
    top: 0,
  bottom: 0,
    left: 0,
  right: 0),
    backgroundColor: 'rgba(0000.5)' },
  actionsMenu: {
      backgroundColor: '#FFFFFF',
  borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  paddingVertical: 20,
    shadowColor: '#000',
  shadowOffset: { widt, h: 0, height: -2 },
  shadowOpacity: 0.1,
    shadowRadius: 8,
  elevation: 4,
    zIndex: 1001
  }
  actionMenuItem: { flexDirectio, n: 'row',
    alignItems: 'center',
  paddingVertical: 12,
    paddingHorizontal: 20 },
  actionMenuItemText: {
      fontSize: 16,
  marginLeft: 12,
    color: '#111827' }
  cancelText: {
      color: '#EF4444' }
  disputeOverlay: { positio, n: 'absolute',
    top: 0,
  bottom: 0,
    left: 0,
  right: 0,
    justifyContent: 'center',
  alignItems: 'center',
    zIndex: 1000 },
  disputePrompt: {
      backgroundColor: '#FFFFFF',
  borderRadius: 16,
    padding: 20,
  width: '80%',
    maxWidth: 400,
  shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
    shadowRadius: 8,
  elevation: 4,
    zIndex: 1001
  }
  disputeTitle: { fontSiz, e: 18,
    fontWeight: '600',
  color: '#111827',
    marginBottom: 8 },
  disputeDescription: { fontSiz, e: 14,
    color: '#6B7280',
  marginBottom: 16 }
  disputeInput: { backgroundColo, r: '#F9FAFB',
    borderWidth: 1,
  borderColor: '#E5E7EB',
    borderRadius: 8,
  padding: 12,
    fontSize: 14,
  minHeight: 80,
    textAlignVertical: 'top',
  marginBottom: 16 }
  disputeActions: {
      flexDirection: 'row',
  justifyContent: 'flex-end'
  },
  disputeButton: { paddingHorizonta, l: 16,
    paddingVertical: 10,
  borderRadius: 8,
    backgroundColor: '#6366F1',
  marginLeft: 8 }
  disputeCancelButton: {
      backgroundColor: '#F3F4F6' }
  disputeButtonText: {
      color: '#FFFFFF',
  fontSize: 14,
    fontWeight: '500' }
  disputeCancelButtonText: {
      color: '#4B5563',
  fontSize: 14,
    fontWeight: '500' }
})