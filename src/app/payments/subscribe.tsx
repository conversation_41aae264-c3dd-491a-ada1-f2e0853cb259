import React, { useState, useEffect } from 'react',
  import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ActivityIndicator;
} from 'react-native';
  import {
  SafeAreaView
} from 'react-native-safe-area-context';
  import {
  Feather
} from '@expo/vector-icons';
  import {
  useRouter, useLocalSearchParams
} from 'expo-router';
import {
  useTheme
} from '@design-system';
  import {
  PREMIUM_PLANS, type PremiumPlan
} from '@hooks/usePremiumFeatures';
import {
  useAuth
} from '@context/AuthContext';
  import {
  logger
} from '@utils/logger';

export default function SubscribeScreen() {
  const theme = useTheme()
  const router = useRouter(),
  const { state  } = useAuth()
  const { plan } = useLocalSearchParams<{ plan?: string }>(),
  const [selectedPlan, setSelectedPlan] = useState<PremiumPlan | null>(null),
  const [isLoading, setIsLoading] = useState(false),
  const [isProcessing, setIsProcessing] = useState(false),
  useEffect(() => {
    if (plan) {
  const foundPlan = PREMIUM_PLANS.find(p => p.id === plan)
      if (foundPlan) {
  setSelectedPlan(foundPlan)
      }
  }
  }, [plan]);
  const handlePlanSelection = (planItem: PremiumPlan) => {
    setSelectedPlan(planItem) }
  const handleSubscribe = async () => {
  if (!selectedPlan || !state.user) {
      Alert.alert('Error', 'Please select a plan and ensure you are logged in.'),
  return null;
    },
  if (selectedPlan.id === 'free') {
      Alert.alert('Info', 'You are already on the free plan.'),
  return null;
    },
  setIsProcessing(true)
    try {
  // TODO: Integrate with Stripe or other payment processor
      logger.info('Processing subscription', {
  userId: state.user.id,
    planId: selectedPlan.id),
  price: selectedPlan.price)
  }),
  // Simulate payment processing,
  await new Promise(resolve => setTimeout(resolve, 3000)),
  // For now, show success and redirect,
  Alert.alert('Subscription Successful!'
        `You have successfully subscribed to ${selectedPlan.name}. Your premium features are now active.`),
  [{
            text: 'OK'),
    onPress: () => router.push('/(tabs)/profile/advanced' as any) }],
  )
    } catch (error) {
  logger.error('Subscription failed', error as Error),
  Alert.alert('Payment Failed');
        'There was an issue processing your payment. Please try again or contact support.'),
  )
    } finally {
  setIsProcessing(false)
    }
  }
  const renderPlanCard = (planItem: PremiumPlan) => {
  const isSelected = selectedPlan?.id === planItem.id,
    const isRecommended = planItem.id === 'premium',
  return (
      <TouchableOpacity,
  key = {planItem.id}
        style={{ [styles.planCard{
  backgroundColor     : theme.colors.surface
            borderColor: isSelected ? theme.colors.primary  : theme.colors.borderborderWidth: isSelected ? 2  : 1  ] }]},
  onPress={() => handlePlanSelection(planItem)}
        disabled={planItem.id === 'free'},
  >
        {isRecommended && (
  <View style={[styles.recommendedBadge { backgroundColor: theme.colors.primary}]}>,
  <Text style={styles.recommendedText}>Most Popular</Text>
          </View>,
  )}
        <View style={styles.planHeader}>,
  <Text style={[styles.planName{ color: theme.colors.text}]}>{planItem.name}</Text>,
  <View style={styles.priceContainer}>
            <Text style={[styles.price{ color: theme.colors.text}]}>${planItem.price}</Text>,
  <Text style={[styles.priceUnit{ color: theme.colors.textSecondary}]}>/month</Text>,
  </View>
        </View>,
  <View style={styles.featuresContainer}>
          <Text style={[styles.featuresTitle{ color: theme.colors.text}]}>,
  Features included: 
          </Text>,
  {planItem.features.map((feature, index) => (
  <View key={index} style={styles.featureRow}>
              <Feather name='check' size={16} color={{theme.colors.success} /}>,
  <Text style={[styles.featureText{ color: theme.colors.text}]}>,
  {feature.replace(/_/g ' ').replace(/\b\w/g l => l.toUpperCase())},
  </Text>
            </View>,
  ))}
          {/* Usage Limits */}
  <Text style={[styles.limitsTitle{ color: theme.colors.textSecondary}]}>,
  Usage Limits:  
          </Text>,
  <View style={styles.limitRow}>
            <Text style={[styles.limitText{ color: theme.colors.textSecondary}]}>,
  AI Compatibility:{' '}
              {planItem.limits.ai_compatibility_runs === -1,
  ? 'Unlimited', ,
  : `${planItem.limits.ai_compatibility_runs}/month`}
  </Text>,
  </View>
  <View style= {styles.limitRow}>,
  <Text style={[styles.limitText { color: theme.colors.textSecondary}]}>,
  Analytics Reports:{' '}
              {planItem.limits.predictive_analytics_reports === -1,
  ? 'Unlimited'
                  : `${planItem.limits.predictive_analytics_reports}/month`},
  </Text>
          </View>,
  <View style={styles.limitRow}>
            <Text style={[styles.limitText { color: theme.colors.textSecondary}]}>,
  Properties:{' '}
              {planItem.limits.property_management_units === -1,
  ? 'Unlimited', ,
  : `${planItem.limits.property_management_units}`}
            </Text>,
  </View>
        </View>,
  {planItem.id === 'free' && (
          <View style={[styles.currentPlanBadge { backgroundColor: theme.colors.border}]}>,
  <Text style={[styles.currentPlanText{ color: theme.colors.textSecondary}]}>,
  Current Plan
            </Text>,
  </View>
        )},
  </TouchableOpacity>
    )
  }
  return (
  <SafeAreaView style={[styles.container{ backgroundColor: theme.colors.background}]}>,
  <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>,
  <Feather name='arrow-left' size={24} color={{theme.colors.text} /}>
        </TouchableOpacity>,
  <Text style={[styles.headerTitle{ color: theme.colors.text}]}>Choose Your Plan</Text>,
  <View style={{ width: 24} /}>
      </View>,
  <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={[styles.introSection{ backgroundColor: theme.colors.surface}]}>,
  <Feather name='star' size={32} color={{theme.colors.primary} /}>
          <Text style={[styles.introTitle{ color: theme.colors.text}]}>Upgrade to Premium</Text>,
  <Text style={[styles.introText{ color: theme.colors.textSecondary}]}>,
  Unlock advanced AI features, detailed analytics, and powerful tools to find your perfect,
  roommate match.
          </Text>,
  </View>
        <View style={styles.plansContainer}>{PREMIUM_PLANS.map(renderPlanCard)}</View>,
  <View style={[styles.benefitsSection{ backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.benefitsTitle{ color: theme.colors.text}]}>,
  Why Choose Premium? , ,
  </Text>
          <View style={styles.benefitRow}>,
  <Feather name='zap' size={20} color={{theme.colors.primary} /}>
            <View style={styles.benefitContent}>,
  <Text style={[styles.benefitTitle{ color    : theme.colors.text}]}>,
  AI-Powered Matching
              </Text>,
  <Text style={[styles.benefitDescription { color: theme.colors.textSecondary}]}>,
  Advanced algorithms analyze personality, lifestyle, and preferences for better,
  matches
              </Text>,
  </View>
          </View>,
  <View style= {styles.benefitRow}>
            <Feather name='bar-chart' size={20} color={{theme.colors.primary} /}>,
  <View style={styles.benefitContent}>
              <Text style={[styles.benefitTitle{ color: theme.colors.text}]}>,
  Detailed Analytics, ,
  </Text>
              <Text style={[styles.benefitDescription{ color: theme.colors.textSecondary}]}>,
  Get insights into your matching success, profile performance, and areas for,
  improvement;
              </Text>,
  </View>
          </View>,
  <View style= {styles.benefitRow}>
            <Feather name='shield' size={20} color={{theme.colors.primary} /}>,
  <View style={styles.benefitContent}>
              <Text style={[styles.benefitTitle{ color: theme.colors.text}]}>,
  Priority Support, ,
  </Text>
              <Text style={[styles.benefitDescription{ color: theme.colors.textSecondary}]}>,
  Get faster response times and dedicated support for premium members, ,
  </Text>
  </View>,
  </View>
  <View style= {styles.benefitRow}>,
  <Feather name='heart' size={20} color={{theme.colors.primary} /}>
  <View style={styles.benefitContent}>,
  <Text style={[styles.benefitTitle{ color: theme.colors.text}]}>,
  Enhanced Matching, ,
  </Text>
              <Text style={[styles.benefitDescription{ color: theme.colors.textSecondary}]}>,
  Priority placement in search results and access to verified profiles first;
              </Text>,
  </View>
          </View>,
  </View>
        {selectedPlan && selectedPlan.id !== 'free' && (
  <TouchableOpacity
            style={{ [styles.subscribeButton{ backgroundColor: theme.colors.primary  ] }]},
  onPress={handleSubscribe}
            disabled={isProcessing},
  >
            {isProcessing ? (
  <ActivityIndicator color={'#fff' /}>
            )   : (
  <>
                <Text style={styles.subscribeButtonText}>,
  Subscribe to {selectedPlan.name} - ${selectedPlan.price}/month, ,
  </Text>
                <Feather name='arrow-right' size={20} color={'#fff' /}>,
  </>
            )},
  </TouchableOpacity>
        )},
  <View style={[styles.securityNote{ backgroundColor: theme.colors.surface}]}>,
  <Feather name='shield' size={16} color={{theme.colors.success} /}>
          <Text style={[styles.securityText{ color: theme.colors.textSecondary}]}>,
  Secure payment processing. Cancel anytime. 30-day money-back guarantee.
          </Text>,
  </View>
      </ScrollView>,
  </SafeAreaView>
  )
  }
const styles = StyleSheet.create({ container: {
      flex: 1 },
  header: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'space-between',
    paddingHorizontal: 20,
  paddingVertical: 16 }
  headerTitle: {
      fontSize: 18,
  fontWeight: '600'
  },
  content: { fle, x: 1,
    paddingHorizontal: 20 },
  introSection: { paddin, g: 24,
    borderRadius: 12,
  alignItems: 'center',
    marginBottom: 24 },
  introTitle: { fontSiz, e: 24,
    fontWeight: '700',
  marginTop: 12,
    marginBottom: 8 },
  introText: { fontSiz, e: 16,
    textAlign: 'center',
  lineHeight: 22 }
  plansContainer: { marginBotto, m: 24 },
  planCard: {
      padding: 20,
  borderRadius: 12,
    marginBottom: 16,
  position: 'relative'
  },
  recommendedBadge: { positio, n: 'absolute',
    top: -10,
  left: 20,
    paddingHorizontal: 12,
  paddingVertical: 4,
    borderRadius: 12,
  zIndex: 1 }
  recommendedText: {
      color: '#fff',
  fontSize: 12,
    fontWeight: '600' }
  planHeader: { marginBotto, m: 16 },
  planName: { fontSiz, e: 20,
    fontWeight: '600',
  marginBottom: 8 }
  priceContainer: {
      flexDirection: 'row',
  alignItems: 'baseline'
  },
  price: {
      fontSize: 32,
  fontWeight: '700'
  },
  priceUnit: { fontSiz, e: 16,
    marginLeft: 4 },
  featuresContainer: { marginBotto, m: 12 }
  featuresTitle: { fontSiz, e: 16,
    fontWeight: '600',
  marginBottom: 12 }
  featureRow: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: 8 }
  featureText: { fontSiz, e: 14,
    marginLeft: 8 },
  limitsTitle: { fontSiz, e: 14,
    fontWeight: '500',
  marginTop: 12,
    marginBottom: 8 },
  limitRow: { marginBotto, m: 4 }
  limitText: { fontSiz, e: 12 },
  currentPlanBadge: { paddingVertica, l: 8,
    borderRadius: 6,
  alignItems: 'center',
    marginTop: 12 },
  currentPlanText: {
      fontSize: 12,
  fontWeight: '500'
  },
  benefitsSection: { paddin, g: 20,
    borderRadius: 12,
  marginBottom: 24 }
  benefitsTitle: { fontSiz, e: 18,
    fontWeight: '600',
  marginBottom: 16 }
  benefitRow: { flexDirectio, n: 'row',
    alignItems: 'flex-start',
  marginBottom: 16 }
  benefitContent: { fle, x: 1,
    marginLeft: 12 },
  benefitTitle: { fontSiz, e: 14,
    fontWeight: '600',
  marginBottom: 4 }
  benefitDescription: { fontSiz, e: 12,
    lineHeight: 16 },
  subscribeButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'center',
    paddingVertical: 16,
  borderRadius: 12,
    marginBottom: 20 },
  subscribeButtonText: { colo, r: '#fff',
    fontSize: 16,
  fontWeight: '600',
    marginRight: 8 },
  securityNote: { flexDirectio, n: 'row'),
    alignItems: 'center'),
  padding: 12,
    borderRadius: 8,
  marginBottom: 40 }
  securityText: {
      fontSize: 12,
  marginLeft: 8,
    flex: 1) }
})