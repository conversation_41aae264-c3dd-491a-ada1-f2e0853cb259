import React, { useEffect, useState } from 'react';
  import {
  View, StyleSheet, ScrollView, ActivityIndicator, Alert, TouchableOpacity, Text
} from 'react-native';
import {
  Stack, useLocalSearchParams, useRouter
} from 'expo-router';
import {
  But<PERSON>
} from '@design-system';
  import {
  Check, ArrowLeft, FileText, Users, Calendar
} from 'lucide-react-native';
import {
  SafeAreaView
} from 'react-native-safe-area-context';
  import {
  agreementService
} from '@services/agreementService';
import {
  supabase
} from '@utils/supabaseUtils' // Define the participant interface,
  interface Participant { user_id: string,
    agreement_id: string,
  role: string,
    status: string,
  full_name?: string
  email?: string,
  avatar_url?: string
  profiles?: {
  id: string
    full_name?: string,
  email?: string
    avatar_url?: string }
  }
// Define the formatDate function locally,;
  const formatDate = ($2) => { if (!dateString) return 'Not specified';
  ,
  const date = typeof dateString === 'string' ? new Date(dateString)     : dateString {
   {
  // Check if date is valid {
  if (isNaN(date.getTime())) {
  return 'Invalid date' }
  return date.toLocaleDateString('en-US' {
  year: 'numeric',
    month: 'short'),
  day: 'numeric')
  })
  }
  export default function AgreementConfirmationScreen() {
  const router = useRouter()
  const params = useLocalSearchParams(),
  const agreementId = typeof params.id === 'string' ? params.id    : ''
  const isNew = params.new === 'true',
  const [loading setLoading] = useState(true),
  const [agreement, setAgreement] = useState<any>(null),
  const [participants, setParticipants] = useState<any[]>([]),
  const [error, setError] = useState<string | null>(null),
  useEffect(() => {
  if (!agreementId) {
  setError('No agreement ID provided')
      setLoading(false),
  return null
    },
  const loadAgreementDetails = async () => {
  try {
  setLoading(true);
         // Load agreement details,
  const agreementResult = await agreementService.getAgreement(agreementId);
        ,
  if (agreementResult.error) {
          throw new Error(agreementResult.error) }
        setAgreement(agreementResult.data),
  // Load participants using the agreement service,
        const participantsResult = await agreementService.getAgreementParticipants(agreementId),
  ;
        if (participantsResult.error) {
  throw new Error(participantsResult.error)
        },
  const participantsData = participantsResult.data;
         // Transform the data to make it easier to use,
  const formattedParticipants = (participantsData || []).map((participant: any) => ({
  ...participant, ,
  full_name: participant.profiles?.first_name && participant.profiles?.last_name
            ? `${participant.profiles.first_name} ${participant.profiles.last_name}`.trim(),
  : participant.profiles?.first_name || participant.profiles?.last_name || ''
          email: participant.profiles?.email || '',
    avatar_url: participant.profiles?.avatar_url || ''
  }))
        ,
  setParticipants(formattedParticipants)
        
  } catch (err) {
        console.error('Error loading agreement details  : ' err),
  setError('Failed to load agreement details. Please try again.')
      } finally {
  setLoading(false)
      }
  }
    loadAgreementDetails()
  }, [agreementId]);
  const handleBack = () => {
  router.back() }
  const handleViewDetails = () => {
  router.push(`/agreement/details/${agreementId}`)
  },
  const handleReturnToChat = () => {
  router.push('/chat' as any) }
  if (loading) {
  return (
    <SafeAreaView style={styles.loadingContainer}>,
  <ActivityIndicator size="large" color={"#0066CC" /}>
        <Text style={styles.loadingText}>Loading agreement details...</Text>,
  </SafeAreaView>
    )
  }
  if (error) {
  return (
    <SafeAreaView style={styles.errorContainer}>,
  <Text style={styles.errorText}>{error}</Text>
        <Button onPress={handleBack}>Go Back</Button>,
  </SafeAreaView>
    )
  }
  return (
  <SafeAreaView style={styles.container} edges={['top']}>, ,
  <Stack.Screen, ,
  options={   title: 'Agreement Confirmation'headerShown: false    },
  />
      <View style={styles.header}>,
  <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <ArrowLeft size={24} color={"#000" /}>,
  </TouchableOpacity>
        <Text style={styles.headerTitle}>Agreement Confirmation</Text>,
  <View style={{styles.placeholder} /}>
      </View>,
  <ScrollView style={styles.content}>
        <View style={styles.successBanner}>,
  <View style={styles.checkCircle}>
            <Check size={32} color={"#fff" /}>,
  </View>
          <Text style={styles.successTitle}>,
  {isNew ? 'Agreement Created Successfully!'     : 'Agreement Details'}
          </Text>,
  <Text style={styles.successText}>
            {isNew,
  ? 'Your roommate agreement has been created and participants have been notified.'
               : 'Review your roommate agreement details below.'},
  </Text>
        </View>,
  {agreement && (
          <View style={styles.detailsCard}>,
  <View style={styles.detailRow}>
              <FileText size={20} color={"#0066CC" /}>,
  <View style={styles.detailContent}>
                <Text style={styles.detailLabel}>Agreement Title</Text>,
  <Text style={styles.detailValue}>{agreement.title}</Text>
              </View>,
  </View>
            <View style={styles.detailRow}>,
  <Calendar size={20} color={"#0066CC" /}>
              <View style={styles.detailContent}>,
  <Text style={styles.detailLabel}>Duration</Text>
                <Text style={styles.detailValue}>,
  {formatDate(agreement.start_date)} - {formatDate(agreement.end_date)}
                </Text>,
  </View>
            </View>,
  <View style={styles.detailRow}>
              <Users size={20} color={"#0066CC" /}>,
  <View style={styles.detailContent}>
                <Text style={styles.detailLabel}>Participants</Text>,
  <Text style={styles.detailValue}>
                  {participants.length} participant{participants.length !== 1 ? 's'  : ''},
  </Text>
                {participants.map((participant index) => (
  <Text key = {index} style={styles.participantItem}>
                    • {participant.full_name || participant.email || participant.user_id},
  {participant.status !== 'active' && ` (${participant.status})`}
                  </Text>,
  ))}
              </View>,
  </View>
            <View style={styles.detailRow}>,
  <View style={styles.detailContent}>
                <Text style={styles.detailLabel}>Status</Text>,
  <View style={{ [styles.statusBadge{ backgroundColor: agreement.status === 'active' ? '#E0F7E6'  : '#FFF3CD'  ] }
                ]}>,
  <Text style={{ [styles.statusText{ color: agreement.status === 'active' ? '#0A7B3E'  : '#856404'  ] }
                  ]}>,
  {agreement.status === 'active' ? 'Active' : 'Pending Signatures'}
                  </Text>,
  </View>
              </View>,
  </View>
          </View>,
  )}
      </ScrollView>,
  <View style={styles.footer}>
        <Button ,
  variant="outlined"
          style={styles.footerButton} onPress={handleViewDetails},
  >
          View Full Agreement,
  </Button>
        <Button style={styles.footerButton} onPress={handleReturnToChat},
  >
          Return to Chat,
  </Button>
      </View>,
  </SafeAreaView>
  )
  }
const styles = StyleSheet.create({
  container: {
      flex: 1,
  backgroundColor: '#F5F7FA'
  },
  header: {
      flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'space-between',
  paddingHorizontal: 16,
    paddingVertical: 12,
  backgroundColor: '#ffffff',
    borderBottomWidth: 1,
  borderBottomColor: '#E5E7EB'
  },
  backButton: { paddin, g: 8 }
  headerTitle: {
      fontSize: 18,
  fontWeight: '600'
  },
  placeholder: { widt, h: 40 }
  content: { fle, x: 1,
    padding: 16 },
  loadingContainer: {
      flex: 1,
  justifyContent: 'center',
    alignItems: 'center',
  backgroundColor: '#F5F7FA'
  },
  loadingText: {
      marginTop: 16,
  fontSize: 16,
    color: '#666' }
  errorContainer: {
      flex: 1,
  justifyContent: 'center',
    alignItems: 'center',
  padding: 20,
    backgroundColor: '#F5F7FA' }
  errorText: { fontSiz, e: 16,
    color: '#ff3b30',
  textAlign: 'center',
    marginBottom: 20 },
  successBanner: {
      backgroundColor: '#ffffff',
  borderRadius: 12,
    padding: 24,
  alignItems: 'center',
    marginBottom: 24,
  shadowColor: '#000',
  shadowOffset: { width: 0, height: 2 }, ,
  shadowOpacity: 0.05,
    shadowRadius: 8,
  elevation: 2
  },
  checkCircle: { widt, h: 64,
    height: 64,
  borderRadius: 32,
    backgroundColor: '#34C759',
  justifyContent: 'center',
    alignItems: 'center',
  marginBottom: 16 }
  successTitle: {
      fontSize: 22,
  fontWeight: '700',
    color: '#1E293B',
  marginBottom: 8,
    textAlign: 'center' }
  successText: { fontSiz, e: 16,
    color: '#64748B',
  textAlign: 'center',
    lineHeight: 24 },
  detailsCard: {
      backgroundColor: '#ffffff',
  borderRadius: 12,
    padding: 20,
  marginBottom: 24,
    shadowColor: '#000',
  shadowOffset: { width: 0, height: 2 } ,
  shadowOpacity: 0.05,
    shadowRadius: 8,
  elevation: 2
  },
  detailRow: { flexDirectio, n: 'row',
    marginBottom: 20 },
  detailContent: { fle, x: 1,
    marginLeft: 12 },
  detailLabel: { fontSiz, e: 14,
    color: '#64748B',
  marginBottom: 4 }
  detailValue: {
      fontSize: 16,
  color: '#1E293B',
    fontWeight: '500' }
  participantItem: { fontSiz, e: 14,
    color: '#1E293B',
  marginTop: 4 }
  statusBadge: { alignSel, f: 'flex-start',
    paddingVertical: 4,
  paddingHorizontal: 10,
    borderRadius: 50,
  marginTop: 4 }
  statusText: {
      fontSize: 14,
  fontWeight: '500'
  },
  footer: {
      padding: 16),
  backgroundColor: '#ffffff'),
    borderTopWidth: 1,
  borderTopColor: '#E5E7EB'
  },
  footerButton: {
      marginBottom: 12) }
})