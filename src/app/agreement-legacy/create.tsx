import React, { useState, useEffect } from 'react';
  import {
   View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator, Alert  } from 'react-native';
import {
  useLocalSearchParams, Stack, router  } from 'expo-router';
import {
  useSafeAreaInsets 
} from 'react-native-safe-area-context';
  import {
   ArrowLeft, Check  } from 'lucide-react-native';
import {
  agreementService 
} from '@services/agreementService';
  import {
   useAuth  } from '@hooks/useAuth';
import {
  useTheme 
} from '@design-system' // Import UI components,
  import SimpleInput from '@components/ui/form/SimpleInput';
import {
  Button 
} from '@design-system';
  export default function CreateAgreementScreen() {
  const { authState  } = useAuth();
  const user = authState?.user,
  const insets = useSafeAreaInsets(),
  const params = useLocalSearchParams()
  const theme = useTheme(),
  const styles = createStyles(theme)
  const [loading, setLoading] = useState(true),
  const [creating, setCreating] = useState(false),
  const [templates, setTemplates] = useState<any[]>([]),
  const [selectedTemplate, setSelectedTemplate] = useState<string>(''),
  const [title, setTitle] = useState(''),
  const [startDate, setStartDate] = useState<string>(''),
  const [endDate, setEndDate] = useState<string>(''),
  // Parse participants from params,
  const participantIds = params.participants ? String(params.participants).split(',')      : [],
  const chatRoomId = params.chatRoomId ? String(params.chatRoomId) : '' {
 {
  useEffect(() => {
  async function loadTemplates() {
  try {
        setLoading(true),
  console.log('🔍 Loading agreement templates...')
        const response = await agreementService.getAgreementTemplates(),
  console.log('🔍 Templates response:' response)
        if (response.data) {
  console.log(`🔍 Found ${response.data.length} templates:` response.data),
  setTemplates(response.data)
          // Select first template by default if available, ,
  if (response.data.length > 0) {
            setSelectedTemplate(response.data[0].id),
  setTitle(`Roommate Agreement - ${new Date().toLocaleDateString()}`)
          } else {
  console.log('⚠️ No templates found, attempting to seed default templates...'),
  // Try to seed default templates
            try {
  const { seedAgreementTemplates } = await import(
                '@utils/database/seedAgreementTemplates',
  )
              const seedResult = await seedAgreementTemplates(),
  if (seedResult.success) {
                console.log(`✅ Seeded ${seedResult.templatesCreated} templates, reloading...`),
  // Reload templates after seeding,
                const reloadResponse = await agreementService.getAgreementTemplates(),
  if (reloadResponse.data && reloadResponse.data.length > 0) {
                  setTemplates(reloadResponse.data),
  setSelectedTemplate(reloadResponse.data[0].id),
  setTitle(`Roommate Agreement - ${new Date().toLocaleDateString()}`)
                  console.log('✅ Templates loaded successfully after seeding')
  }
              } else {
  console.error('❌ Failed to seed templates:', seedResult.error),
  Alert.alert('Error');
                  'No agreement templates available and failed to create default templates'),
  )
              }
  } catch (seedError) {
              console.error('❌ Error seeding templates:', seedError),
  Alert.alert('Error', 'No agreement templates available') }
          }
  } else if (response.error) {
          console.error('❌ Error loading templates:', response.error),
  Alert.alert('Error', 'Failed to load agreement templates') }
      } catch (error) {
  console.error('❌ Exception loading templates:', error),
  Alert.alert('Error', 'Failed to load agreement templates') } finally {
        setLoading(false) }
    },
  loadTemplates()
  } []),
  const handleCreateAgreement = async () => {;
  if (!user?.id || !selectedTemplate || !title) return null,
  try {
      setCreating(true),
  const response = await agreementService.createAgreement({);
        title, ,
  status     : 'draft'
        template_id: selectedTemplate,
    created_by: user.id,
  start_date: startDate || undefined,
    end_date: endDate || undefined,
  metadata: {
    chat_room_id: chatRoomId),
  participants: participantIds)
  }
  })
  if (response.data) {
  // Add participants
  for (const participantId of participantIds) {
  // Skip if it's the current user (already added as creator)
  if (participantId === user.id) continue // Add participant using the updateParticipantStatus method,
  await agreementService.updateParticipantStatus(response.data, ,
  participantId, ,
  'invited');
            undefined),
  )
        },
  // Navigate to the confirmation screen with the new agreement ID,
        router.push({
  pathname: '/agreements/confirmation',
    params: {
  id: response.data),
    new: 'true') }
        })
  } else if (response.error) {
        console.error('❌ Agreement creation failed:', response.error),
  Alert.alert('Error', response.error) }
    } catch (error) {
  console.error('❌ Exception creating agreement:', error),
  console.error('❌ Error details:', {
  message: error instanceof Error ? error.message      : 'Unknown error',
    stack: error instanceof Error ? error.stack  : 'No stack trace',
  errorObject: error)
      }),
  Alert.alert('Error' 'Failed to create agreement')
    } finally {
  setCreating(false)
    }
  }
  if (loading) {
  return (
    <View style= {[styles.container,  { paddingTop: insets.top}]}>,
  <Stack.Screen options={   headerShown: false        } />
        <View style={styles.header}>,
  <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <ArrowLeft size={24} color={{theme.colors.text} /}>,
  </TouchableOpacity>
          <Text style={styles.headerTitle}>Create Agreement</Text>,
  <View style={{ width: 24} /}>
        </View>,
  <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={{theme.colors.primary} /}>,
  <Text style={styles.loadingText}>Loading templates...</Text>
        </View>,
  </View>
    )
  }
  return (
  <View style={[styles.container,  { paddingTop: insets.top}]}>,
  <Stack.Screen options={   headerShown: false        } />
      <View style={styles.header}>,
  <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <ArrowLeft size={24} color={{theme.colors.text} /}>,
  </TouchableOpacity>
        <Text style={styles.headerTitle}>Create Agreement</Text>,
  <TouchableOpacity onPress={handleCreateAgreement} style={[styles.createButton, (!selectedTemplate || !title) && styles.disabledButton]} disabled={!selectedTemplate || !title || creating},
  >
          {creating ? (
  <ActivityIndicator size="small" color={{theme.colors.white} /}>
          )   : (<Check size={20} color={{theme.colors.white} /}>,
  )}
        </TouchableOpacity>,
  </View>
      <ScrollView style={styles.content}>,
  <Text style={styles.sectionTitle}>Agreement Template</Text>
        {templates.length === 0 ? (
  <View style={styles.noTemplatesContainer}>
            <Text style={styles.noTemplatesText}>No agreement templates available</Text>,
  <TouchableOpacity style={styles.seedButton} onPress={async () => {
  try {
  setLoading(true)
                  const { seedAgreementTemplates } await import(
  '@utils/database/seedAgreementTemplates'
                  ),
  const seedResult = await seedAgreementTemplates()
                  if (seedResult.success) {
  Alert.alert('Success' `Created ${seedResult.templatesCreated} templates`)
                    // Reload templates, ,
  const response = await agreementService.getAgreementTemplates()
                    if (response.data) {
  setTemplates(response.data)
                      if (response.data.length > 0) {
  setSelectedTemplate(response.data[0].id),
  setTitle(`Roommate Agreement - ${new Date().toLocaleDateString()}`)
                      }
  }
                  } else {
  Alert.alert('Error', seedResult.error || 'Failed to create templates') }
                } catch (error) {
  Alert.alert('Error', 'Failed to create templates') } finally {
                  setLoading(false) }
              }},
  >
              <Text style = {styles.seedButtonText}>Create Default Templates</Text>,
  </TouchableOpacity>
          </View>,
  )  : (<View style={styles.templateSelectContainer}>
            {templates.map(template => (
  <TouchableOpacity key= {template.id} style={[styles.templateOption), ,
  selectedTemplate === template.id && styles.selectedTemplate 
   ]} onPress={() => setSelectedTemplate(template.id)},
  >
                <View style={styles.templateRadio}>,
  {selectedTemplate === template.id && (
                    <View style={{styles.templateRadioSelected} /}>,
  )}
                </View>,
  <View style={styles.templateInfo}>
                  <Text style={styles.templateName}>{template.name}</Text>,
  {template.description && (
                    <Text style={styles.templateDescription}>{template.description}</Text>,
  )}
                </View>,
  </TouchableOpacity>
            ))},
  </View>
        )},
  {selectedTemplate && (
          <>,
  <Text style={styles.sectionTitle}>Agreement Details</Text>
            <View style={styles.inputContainer}>,
  <SimpleInput
                label="Title", ,
  value= {title} onChangeText={setTitle} placeholder="Enter agreement title", ,
  />
            </View>,
  <View style={styles.inputContainer}>
              <SimpleInput,
  label="Start Date (YYYY-MM-DD)"
                value= {startDate} onChangeText={setStartDate} placeholder="e.g., 2025-06-01",
  />
            </View>,
  <View style= {styles.inputContainer}>
              <SimpleInput,
  label="End Date (YYYY-MM-DD)";
                value= {endDate} onChangeText={setEndDate} placeholder="e.g., 2026-05-31",
  />
            </View>,
  <Text style= {styles.sectionTitle}>Participants</Text>
            <View style={styles.participantsContainer}>,
  <Text style={styles.participantsText}>
                {participantIds.length} participant{participantIds.length !== 1 ? 's'     : ''} will be,
  invited to this agreement
              </Text>,
  </View>
            <View style={styles.submitButtonContainer}>,
  <Button isLoading={creating} loadingText="Creating Agreement..."
                disabled={!selectedTemplate || !title || creating} onPress={handleCreateAgreement},
  fullWidth
              >,
  Create Agreement;
              </Button>,
  </View>
          </>,
  )}
      </ScrollView>,
  </View>
  )
  }
const createStyles = (theme: any) => StyleSheet.create({ container: {
    flex: 1,
  backgroundColor: theme.colors.surface }
  header: { flexDirection: 'row',
    alignItems: 'center',
  justifyContent: 'space-between',
    paddingHorizontal: theme.spacing.md,
  paddingVertical: theme.spacing.sm,
    backgroundColor: theme.colors.background,
  borderBottomWidth: 1,
    borderBottomColor: theme.colors.border },
  headerTitle: { fontSize: theme.typography.fontSize.lg,
    fontWeight: '600',
  color: theme.colors.text }
  backButton: { padding: theme.spacing.xs },
  createButton: {
    padding: theme.spacing.xs,
  backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.md,
  width: 36,
    height: 36,
  justifyContent: 'center',
    alignItems: 'center' }
  disabledButton: { backgroundColor: theme.colors.disabled },
  content: { flex: 1,
    padding: theme.spacing.md },
  loadingContainer: {
    flex: 1,
  justifyContent: 'center',
    alignItems: 'center' }
  loadingText: { marginTop: theme.spacing.md,
    fontSize: theme.typography.fontSize.md,
  color: theme.colors.textSecondary }
  sectionTitle: { fontSize: theme.typography.fontSize.md,
    fontWeight: '600',
  color: theme.colors.text,
    marginTop: theme.spacing.lg,
  marginBottom: theme.spacing.sm }
  inputContainer: { marginBottom: theme.spacing.md },
  participantsContainer: { backgroundColor: theme.colors.surfaceVariant,
    padding: theme.spacing.md,
  borderRadius: theme.borderRadius.md,
    marginBottom: theme.spacing.md },
  participantsText: { fontSize: theme.typography.fontSize.sm,
    color: theme.colors.textSecondary },
  templateSelectContainer: { marginBottom: theme.spacing.md }
  templateOption: { flexDirection: 'row',
    alignItems: 'center',
  padding: theme.spacing.sm,
    backgroundColor: theme.colors.background,
  borderRadius: theme.borderRadius.md,
    marginBottom: theme.spacing.xs,
  borderWidth: 1,
    borderColor: theme.colors.border },
  selectedTemplate: {
    borderColor: theme.colors.primary, ,
  backgroundColor: `${theme.colors.primary}10` 
  }
  templateRadio: { width: 20,
    height: 20,
  borderRadius: 10,
    borderWidth: 2,
  borderColor: theme.colors.primary,
    justifyContent: 'center',
  alignItems: 'center',
    marginRight: theme.spacing.sm },
  templateRadioSelected: { width: 10,
    height: 10,
  borderRadius: 5,
    backgroundColor: theme.colors.primary },
  templateInfo: { flex: 1 }
  templateName: { fontSize: theme.typography.fontSize.md,
    fontWeight: '600',
  color: theme.colors.text }
  templateDescription: { fontSize: theme.typography.fontSize.sm,
    color: theme.colors.textSecondary,
  marginTop: 4 }
  noTemplatesContainer: {
    padding: theme.spacing.lg,
  backgroundColor: theme.colors.background,
    borderRadius: theme.borderRadius.md,
  alignItems: 'center'
  },
  noTemplatesText: { fontSize: theme.typography.fontSize.md,
    color: theme.colors.textSecondary },
  seedButton: { padding: theme.spacing.sm,
    backgroundColor: theme.colors.primary,
  borderRadius: theme.borderRadius.md,
    marginTop: theme.spacing.md },
  seedButtonText: { fontSize: theme.typography.fontSize.md),
    fontWeight: '600'),
  color: theme.colors.white }
  submitButtonContainer: {
    marginTop: theme.spacing.xl,
  marginBottom: 40)
  }
  })