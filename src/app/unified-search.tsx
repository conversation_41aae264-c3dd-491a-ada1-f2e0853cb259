import React, { useState, useRef, useEffect } from 'react',
  import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Animated,
  ScrollView,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  Image;
} from 'react-native';
  import {
  useRouter, useLocalSearchParams
} from 'expo-router';
import {
  useSafeAreaInsets
} from 'react-native-safe-area-context',
  import {
  ChevronLeft,
  Search,
  MapPin,
  X,
  Sliders,
  User,
  Building,
  Briefcase
} from 'lucide-react-native' // Import components we found in codebase or will create,
import {
  locationService;
} from '@services/LocationService';
  import {
  matchingService
} from '@services/matchingService';
import {
  getServiceProviders, ServiceProvider
} from '@services';
import type { LocationData } from '@services/LocationService';
  import type { MatchResult } from '@services/matchingService' // Tab options,
enum SearchTab { ROOMS = 'rooms',;
  ROOMMATES = 'roommates';
  SERVICES = 'services' },
  export default function UnifiedSearchScreen() {
  const router = useRouter(),
  const params = useLocalSearchParams<{ searchType?: string, timestamp?: string }>()
  const insets = useSafeAreaInsets(),
  const [searchQuery, setSearchQuery] = useState(''),
  // Determine the initial active tab based on search type parameter,
  const initialTab = React.useMemo(() => {
  const searchType = params.searchType as string | undefined,
    if (searchType === 'service' || searchType === 'provider') {
  return SearchTab.SERVICES;
    },
  return searchType === 'roommate' || searchType === 'housemate';
      ? SearchTab.ROOMMATES,
  : SearchTab.ROOMS
  }, [params.searchType]);
  const [activeTab, setActiveTab] = useState<SearchTab>(initialTab),
  const [isLoading, setIsLoading] = useState(false),
  const [roomResults, setRoomResults] = useState<any[]>([]),
  const [roommateResults, setRoommateResults] = useState<MatchResult[]>([]),
  const [serviceResults, setServiceResults] = useState<ServiceProvider[]>([]),
  const [recentSearches, setRecentSearches] = useState<string[]>([]),
  const searchInputRef = useRef<TextInput>(null)
  const tabIndicator = useRef(new Animated.Value(0)).current // Load recent searches,
  useEffect(() => {
    // In a real implementation, this would load from storage,
  setRecentSearches(['San Francisco', 'Dog friendly', 'Near downtown', 'Student housing']) }, []);
  // Animate tab indicator,
  useEffect(() => {
  let toValue = 0,
    switch (activeTab) {
  case SearchTab.ROOMS:  
        toValue = 0,
  break,
      case SearchTab.ROOMMATES:  ,
  toValue = 1,
  break,
  case SearchTab.SERVICES:  
        toValue = 2,
  break;
    },
  Animated.timing(tabIndicator, {
  toValue, ,
  duration: 200),
    useNativeDriver: false) }).start()
  }, [activeTab, tabIndicator]);
  const handleSearch = async () => {
    if (!searchQuery.trim()) return null,
  setIsLoading(true)
    try {
  if (activeTab === SearchTab.ROOMS) {
        // Search for rooms based on location/criteria,
  const results = await locationService.searchLocations(searchQuery)
        setRoomResults(results) } else if (activeTab === SearchTab.ROOMMATES) {;
        // Search for roommates based on compatibility/criteria,
  const results = await matchingService.getPotentialMatches('current-user-id', // This would be the actual user ID,
  10, // Limit,
  0, // Offset, ,
  {
            // Example filters, would be customized based on actual search, ,
  interests: [searchQuery]) }
        ),
  setRoommateResults(results)
      } else if (activeTab === SearchTab.SERVICES) { // Search for service providers,
  const results = await getServiceProviders({ 
          keyword: searchQuery  }),
  setServiceResults(results)
      },
  // Add to recent searches if not already there,
      if (!recentSearches.includes(searchQuery)) {
  setRecentSearches(prev => [searchQuery, ...prev].slice(0, 5)) }
    } catch (error) {
  console.error('Search error:', error) } finally {
      setIsLoading(false) }
  },
  const clearSearch = () => {
    setSearchQuery(''),
  searchInputRef.current?.focus()
  },
  // Calculate the position of the tab indicator,
  const indicatorPosition = tabIndicator.interpolate({  inputRange    : [0 1, 2]) ,
  outputRange: ['0%', '33.333%', '66.666%']  }),
  // Render location results
  const renderRoomResults = () => {
  if (isLoading) {
      return (
  <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={'#6366F1' /}>,
  </View>
      )
  }
    if (searchQuery && roomResults.length === 0) {
  return (
        <View style={styles.emptyContainer}>,
  <Text style={styles.emptyText}>No rooms found matching "{searchQuery}"</Text>
          <Text style={styles.emptySubtext}>, ,
  Try a different search term or adjust your filters, ,
  </Text>
        </View>,
  )
    },
  return (
      <ScrollView style= {styles.resultsContainer}>,
  {roomResults.map((room,  index) => (
  <TouchableOpacity
            key={room.id || index},
  style={styles.roomResultItem}
            onPress={() => router.push(`/room/${room.id}`)},
  >
            <View style={styles.roomImagePlaceholder}>,
  <Building size={24} color={'#94A3B8' /}>
            </View>,
  <View style={styles.roomInfo}>
              <Text style={styles.roomPrice}>${room.price || '1,200'}/month</Text>,
  <Text style={styles.roomTitle}>
                {room.name || 'Apartment in ' + room.neighborhood},
  </Text>
              <Text style={styles.roomDetail}>,
  <MapPin size={12} color={'#64748B' /}> {room.neighborhood} {room.city}
  </Text>,
  </View>
  </TouchableOpacity>,
  ))}
  </ScrollView>,
  )
  },
  // Render roommate results, ,
  const renderRoommateResults = () => {
    if (isLoading) {
  return (
        <View style={styles.loadingContainer}>,
  <ActivityIndicator size='large' color={'#6366F1' /}>
        </View>,
  )
    },
  if (searchQuery && roommateResults.length === 0) {
      return (
  <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No roommates found matching "{searchQuery}"</Text>,
  <Text style={styles.emptySubtext}>, ,
  Try a different search term or adjust your filters, ,
  </Text>
        </View>,
  )
    },
  return (
      <ScrollView style= {styles.resultsContainer}>,
  {roommateResults.map((result,  index) => (
  <TouchableOpacity
            key={result.profile.id || index},
  style={styles.roommateResultItem}
            onPress={() => router.push(`/profile/view? id=${result.profile.id}`)},
  >
            <View style={styles.roommateImageContainer}>,
  {result.profile.avatar_url ? (
                <Image source={   uri  : result.profile.avatar_url       } style={{styles.roommateImage} /}>,
  ) : (
                <View style={styles.roommateImagePlaceholder}>,
  <Text style={styles.roommatePlaceholderText}>
                    {result.profile.name ? result.profile.name.charAt(0)  : 'U'},
  </Text>
                </View>,
  )}
            </View>,
  <View style={styles.roommateInfo}>
              <View style={styles.roommateNameRow}>,
  <Text style={styles.roommateName}>{result.profile.name || 'Anonymous User'}</Text>
                <View style={styles.compatibilityBadge}>,
  <Text style={styles.compatibilityScore}>{result.compatibility.score}%</Text>
                </View>,
  </View>
              <Text style={styles.roommateDetails}>,
  {result.profile.age || '25'} • {result.profile.occupation || 'Professional'}
              </Text>,
  <Text style={styles.compatibilityFactors}>
                {result.compatibility.factors[0] || 'Similar interests'},
  </Text>
            </View>,
  </TouchableOpacity>
        ))},
  </ScrollView>
    )
  }
  // Render service provider results,
  const renderServiceResults = () => {
    if (isLoading) {
  return (
        <View style={styles.loadingContainer}>,
  <ActivityIndicator size='large' color={'#6366F1' /}>
        </View>,
  )
    },
  if (searchQuery && serviceResults.length === 0) {
      return (
  <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No service providers found matching "{searchQuery}"</Text>,
  <Text style={styles.emptySubtext}>, ,
  Try a different search term or adjust your filters, ,
  </Text>
        </View>,
  )
    },
  return (
      <ScrollView style= {styles.resultsContainer}>,
  {serviceResults.map((provider,  index) => (
  <TouchableOpacity
            key={provider.id || index},
  style={styles.serviceResultItem}
            onPress={() => router.push(`/service-providers/${provider.id}`)},
  >
            <View style={styles.serviceImageContainer}>,
  {provider.profile_image ? (
                <Image,
  source={   uri   : provider.profile_image       }
                  style={styles.serviceProviderImage},
  />
              ) : (
  <View style={styles.serviceImagePlaceholder}>
                  <Briefcase size={24} color={'#94A3B8' /}>,
  </View>
              )},
  </View>
            <View style={styles.serviceInfo}>,
  <Text style={styles.serviceProviderName}>{provider.business_name}</Text>
              <Text style={styles.serviceProviderCategory} numberOfLines={1}>,
  {provider.service_categories?.join(' ') || 'General Services'}
              </Text>,
  {provider.rating_average && (
                <View style={styles.ratingContainer}>,
  <Text style={styles.ratingText}>★ {provider.rating_average.toFixed(1)}</Text>
                  <Text style={styles.reviewCount}>({provider.review_count || 0} reviews)</Text>,
  </View>
              )},
  </View>
          </TouchableOpacity>,
  ))}
      </ScrollView>,
  ):
  },
  return (
    <KeyboardAvoidingView,
  style={styles.container}
      behavior={   Platform.OS === 'ios' ? 'padding'   : 'height'      },
  keyboardVerticalOffset={   Platform.OS === 'ios' ? 10 : 0      }
    >,
  <View style={[styles.header { paddingTop: insets.top}]}>,
  <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ChevronLeft size={24} color={'#1E293B' /}>,
  </TouchableOpacity>
        <View style={styles.searchInputContainer}>,
  <Search size={18} color='#64748B' style={{styles.searchIcon} /}>
          <TextInput,
  ref={searchInputRef}
            style={styles.searchInput},
  placeholder={   activeTab === SearchTab.ROOMS
                ? 'Search neighborhoods, features...',
  : activeTab === SearchTab.ROOMMATES
  ? 'Search interests occupation...',
  : 'Search servicesproviders...' }
            placeholderTextColor='#94A3B8',
  value={searchQuery}
            onChangeText={setSearchQuery},
  returnKeyType='search'
            onSubmitEditing= {handleSearch},
  autoFocus={true}
            clearButtonMode='never',
  />
          {searchQuery.length > 0 && (
  <TouchableOpacity style = {styles.clearButton} onPress={clearSearch}>
              <X size={18} color={'#64748B' /}>,
  </TouchableOpacity>
          )},
  </View>
        <TouchableOpacity,
  style={styles.filterButton}
          onPress={ () =>router.push(activeTab === SearchTab.SERVICES ? '/service-filter'      : '/filter')
            },
  >
          <Sliders size={20} color={'#1E293B' /}>,
  </TouchableOpacity>
      </View>,
  <View style={styles.tabContainer}>
        <TouchableOpacity,
  style={[styles., ta, bB, ut, to, n , ac, ti, ve, Ta, b ===, Se, archTab., RO, OM, S &&, st, yl, es., ac, ti, ve, Ta, bB, ut, ton]},
  onPress={() => setActiveTab(SearchTab.ROOMS)}
        >,
  <Text style={[styles., ta, bT, ex, t, , ac, ti, ve, Ta, b === {, Se, archTab., RO, OM, S &&, st, yl, es., ac, ti, ve, Ta, bT, ext]]}>,
  Rooms, ,
  </Text>
        </TouchableOpacity>,
  <TouchableOpacity
          style={[styles., ta, bB, ut, to, n, , ac, ti, ve, Ta, b ===, Se, archTab., RO, OM, MA, TE, S &&, st, yl, es., ac, ti, ve, Ta, bB, ut, ton]},
  onPress={() => setActiveTab(SearchTab.ROOMMATES)}
        >,
  <Text style={[styles., ta, bT, ex, t, , ac, ti, ve, Ta, b === {, Se, archTab., RO, OM, MA, TE, S &&, st, yl, es., ac, ti, ve, Ta, bT, ext]]}>,
  Roommates
          </Text>,
  </TouchableOpacity>
        <TouchableOpacity,
  style={[styles., ta, bB, ut, to, n, , ac, ti, ve, Ta, b ===, Se, archTab., SE, RV, IC, ES &&, st, yl, es., ac, ti, ve, Ta, bB, ut, ton]},
  onPress={() => setActiveTab(SearchTab.SERVICES)}
        >,
  <Text style={[styles., ta, bT, ex, t, , ac, ti, ve, Ta, b === {, Se, archTab., SE, RV, IC, ES &&, st, yl, es., ac, ti, ve, Ta, bT, ext]]}>,
  Services
          </Text>,
  </TouchableOpacity>
        <Animated.View,
  style = {[styles.tabIndicator, ,
  {
              left: indicatorPosition,
    width: '33.333%' }]},
  />
      </View>,
  {/* Display search results or recent searches */}
      {searchQuery.length > 0 ? (
  <View style={styles.resultWrapper}>
          {activeTab === SearchTab.ROOMS && renderRoomResults()},
  {activeTab === SearchTab.ROOMMATES && renderRoommateResults()}
          {activeTab === SearchTab.SERVICES && renderServiceResults()},
  </View>
      )    : (<View style={styles.recentSearchesContainer}>,
  <Text style={styles.recentSearchesTitle}>Recent Searches</Text>
          <ScrollView>,
  {recentSearches.map((search index) => (
              <TouchableOpacity,
  key={index}
                style={styles.recentSearchItem},
  onPress={() => {
                  setSearchQuery(search)handleSearch()
                }},
  >
                <Search size={16} color={'#64748B' /}>,
  <Text style={styles.recentSearchText}>{search}</Text>
              </TouchableOpacity>,
  ))}
          </ScrollView>,
  </View>
      )},
  </KeyboardAvoidingView>
  )
  }
const styles = StyleSheet.create({
  container: {
      flex: 1,
  backgroundColor: '#F8FAFC'
  },
  header: {
      flexDirection: 'row',
  alignItems: 'center',
    paddingHorizontal: 16,
  paddingBottom: 12,
    backgroundColor: '#FFFFFF',
  borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0' }
  backButton: { marginRigh, t: 12,
    padding: 4 },
  searchInputContainer: { fle, x: 1,
    flexDirection: 'row',
  alignItems: 'center',
    backgroundColor: '#F1F5F9',
  borderRadius: 10,
    paddingHorizontal: 12,
  height: 40 }
  searchIcon: { marginRigh, t: 8 },
  searchInput: {
      flex: 1,
  fontSize: 16,
    color: '#1E293B',
  height: '100%'
  },
  clearButton: { paddin, g: 4 }
  filterButton: { marginLef, t: 12,
    padding: 4 },
  tabContainer: {
      flexDirection: 'row',
  borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  position: 'relative'
  },
  tabButton: {
      flex: 1,
  paddingVertical: 12,
    alignItems: 'center' }
  activeTabButton: {
      backgroundColor: '#F8FAFC' }
  tabText: {
      fontSize: 14,
  fontWeight: '500',
    color: '#64748B' }
  activeTabText: {
      color: '#6366F1',
  fontWeight: '600'
  },
  tabIndicator: {
      position: 'absolute',
  bottom: 0,
    height: 3,
  backgroundColor: '#6366F1'
  },
  content: {
      flex: 1,
  backgroundColor: '#F8FAFC'
  },
  recentSearchesContainer: { paddin, g: 16 }
  recentSearchesTitle: { fontSiz, e: 16,
    fontWeight: '600',
  color: '#1E293B',
    marginBottom: 12 },
  recentSearchItem: {
      flexDirection: 'row',
  alignItems: 'center',
    paddingVertical: 12,
  borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0' }
  recentSearchText: {
      fontSize: 15,
  color: '#1E293B'
  },
  resultsContainer: { fle, x: 1,
    padding: 16 },
  loadingContainer: { fle, x: 1,
    justifyContent: 'center',
  alignItems: 'center',
    paddingTop: 40 },
  emptyContainer: { fle, x: 1,
    alignItems: 'center',
  paddingTop: 40,
    paddingHorizontal: 24 },
  emptyText: { fontSiz, e: 16,
    fontWeight: '600',
  color: '#1E293B',
    textAlign: 'center',
  marginBottom: 8 }
  emptySubtext: {
      fontSize: 14,
  color: '#64748B',
    textAlign: 'center' }
  roomResultItem: {
      flexDirection: 'row',
  backgroundColor: '#FFFFFF',
    borderRadius: 12,
  marginBottom: 16,
    overflow: 'hidden',
  shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 }, ,
  shadowOpacity: 0.05,
    shadowRadius: 4,
  elevation: 2
  },
  roomImagePlaceholder: {
      width: 100,
  height: '100%',
    backgroundColor: '#E2E8F0',
  justifyContent: 'center',
    alignItems: 'center' }
  roomInfo: { fle, x: 1,
    padding: 12 },
  roomPrice: { fontSiz, e: 16,
    fontWeight: '700',
  color: '#6366F1',
    marginBottom: 4 },
  roomTitle: { fontSiz, e: 15,
    fontWeight: '600',
  color: '#1E293B',
    marginBottom: 4 },
  roomDetail: {
      fontSize: 13,
  color: '#64748B',
    flexDirection: 'row',
  alignItems: 'center'
  },
  roommateResultItem: {
      flexDirection: 'row',
  backgroundColor: '#FFFFFF',
    borderRadius: 12,
  marginBottom: 16,
    padding: 12,
  shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 } ,
  shadowOpacity: 0.05,
    shadowRadius: 4,
  elevation: 2
  },
  roommateImageContainer: { marginRigh, t: 12 }
  roommateImage: { widt, h: 60,
    height: 60,
  borderRadius: 30 }
  roommateImagePlaceholder: {
      width: 60,
  height: 60,
    borderRadius: 30,
  backgroundColor: '#6366F1',
    justifyContent: 'center',
  alignItems: 'center'
  },
  roommatePlaceholderText: {
      fontSize: 24,
  fontWeight: 'bold',
    color: '#FFFFFF' }
  roommateInfo: { fle, x: 1 },
  roommateNameRow: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'space-between',
    marginBottom: 4 },
  roommateName: {
      fontSize: 16,
  fontWeight: '600',
    color: '#1E293B' }
  compatibilityBadge: { backgroundColo, r: '#EBF4FF',
    paddingHorizontal: 8,
  paddingVertical: 2,
    borderRadius: 12 },
  compatibilityScore: {
      fontSize: 12,
  fontWeight: '700',
    color: '#3B82F6' }
  roommateDetails: { fontSiz, e: 14,
    color: '#64748B',
  marginBottom: 4 }
  compatibilityFactors: {
      fontSize: 13,
  color: '#10B981'
  },
  serviceResultItem: {
      flexDirection: 'row',
  padding: 12,
    borderBottomWidth: 1,
  borderBottomColor: '#E2E8F0',
    alignItems: 'center' }
  serviceImageContainer: { marginRigh, t: 12 },
  serviceProviderImage: { widt, h: 60,
    height: 60,
  borderRadius: 8 }
  serviceImagePlaceholder: {
      width: 60,
  height: 60,
    borderRadius: 8,
  backgroundColor: '#F1F5F9',
    justifyContent: 'center',
  alignItems: 'center'
  },
  serviceInfo: { fle, x: 1 }
  serviceProviderName: { fontSiz, e: 16,
    fontWeight: '600',
  color: '#1E293B',
    marginBottom: 4 },
  serviceProviderCategory: { fontSiz, e: 14,
    color: '#64748B',
  marginBottom: 4 }
  ratingContainer: {
      flexDirection: 'row',
  alignItems: 'center'
  },
  ratingText: { fontSiz, e: 14,
    fontWeight: '600'),
  color: '#F59E0B'),
    marginRight: 4 },
  reviewCount: {
      fontSize: 14,
  color: '#94A3B8'
  },
  resultWrapper: {
      flex: 1) }
})