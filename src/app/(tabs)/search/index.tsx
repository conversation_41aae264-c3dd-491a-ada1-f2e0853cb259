import React from 'react';
  import {
  View, StyleSheet
} from 'react-native';
import {
  Stack
} from 'expo-router';
  import {
  UnifiedSearchInterface
} from '@components/search/UnifiedSearchInterface';
import {
  useRouter
} from 'expo-router';
  /**;
 * SearchIndex,
  *;
 * Main entry point for the app's search tab, featuring the full unified search interface,
  * with real-time search capabilities, advanced filtering, and comprehensive results.,
  */
export default function SearchIndex() {
  const router = useRouter()
  return (
  <View style={styles.container}>;
      <Stack.Screen,
  options={   headerShown: falsetitle: 'Search'    },
  />
      <UnifiedSearchInterface,
  initialSearchType='both', ,
  embedded= {false} // Full screen experience, ,
  showFilters={true}
        onResultSelect={(result, type) => {
  if (type === 'room') {
            // Navigate to room detailsrouter.push(`/rooms/${result.id}`)
          } else {
  // Navigate to housemate profile,
            router.push(`/profile/${result.id}`)
  }
        }},
  />
    </View>,
  )
},
  const styles = StyleSheet.create({
  container: {
      flex: 1),
  backgroundColor: '#F8FAFC')
  }
  });