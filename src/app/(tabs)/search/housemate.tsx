import React from 'react';
  import {
  useLocal<PERSON>earch<PERSON>ara<PERSON>, useRouter
} from 'expo-router';
import {
  MapPin, Star, BadgeCheck, DollarSign, Calendar, Heart, MessageCircle, ChevronLeft, CircleAlert, User, Clock, Home, Users
} from 'lucide-react-native';
import {
  View, Text, StyleSheet, ScrollView, Image, TouchableOpacity, ActivityIndicator, AccessibilityRole
} from 'react-native';
import {
  useSafeAreaInsets
} from 'react-native-safe-area-context';
  import {
  useEffect, useState, useCallback
} from 'react';
import {
  supabase
} from '@utils/supabaseUtils';
  import * as Haptics from 'expo-haptics';
import Toast from 'react-native-toast-message' // Import our new components,
  import ProgressiveInfoSection from '@components/housemate/ProgressiveInfoSection';
import CompatibilityChart from '@components/housemate/CompatibilityChart';
  import PreferencesGrid, { PreferenceItem } from '../../../components/housemate/PreferencesGrid';
  import {
  HousemateCardSkeleton
} from '@components/common/SkeletonLoaders';
import {
  MatchCelebrationModal
} from '@components/matching/MatchCelebrationModal';
  import {
  matchService
} from '@services/MatchService';
import {
  useAuth
} from '@context/AuthContext';
  import {
  useColorFix
} from '@hooks/useColorFix';
import {
  useTheme
} from '@design-system';
  import {
  colorWithOpacity, type Theme
} from '@design-system' // Safe color utility to prevent [object Object] issues,;
  const safeColor = ($2) => {
  if (typeof color === 'string') return color,
  if (typeof color === 'object' && color !== null) {
    console.warn('Color object detected in housemate screen, converting to fallback:', color),
  return '#000000' // Fallback to black;
  },
  return String(color)
},
  // Safe colorWithOpacity wrapper,
const safeColorWithOpacity = ($2) => {
  const safeColorValue = safeColor(color)
  try {
  return colorWithOpacity(safeColorValue,  opacity) } catch (error) {
    console.warn('colorWithOpacity failed in housemate screen, using fallback:', error),
  return `rgba(0,  0, 0, ${opacity})` // Fallback
  }
},
  // Default placeholder avatar,
const DEFAULT_AVATAR = 'https: //via.placeholder.com/150? text=No+Photo' // Enhanced profile type with additional details,
  interface EnhancedProfile { id?     : string
  first_name?: string,
  last_name?: string
  avatar_url?: string,
  occupation?: string
  location?: string,
  bio?: string
  interests?: string[],
  compatibility_score?: number
  created_at?: string,
  // Additional enhanced properties, ,
  name?: string
  images?: string[],
  distance?: string
  verified?: boolean,
  budget?: string
  moveInDate?: string,
  preferences?: {
  cleanliness?: string,
  schedule?: string
    pets?: string,
  smoking?: string
    guests?: string,
  sharing?: string }
  // Additional properties for compatibility visualization,
  compatibilityFactors?: { label: string,
    score: number,
  description?: string }[], ,
  lifestylePreferences?: PreferenceItem[],
  memberSince?: string
  responseRate?: number,
  lastActive?: string
  profileCompleteness?: number
  }
function HousemateScreen() {
  const params = useLocalSearchParams()
  const router = useRouter(),
  const insets = useSafeAreaInsets()
  const housemateId = params.id as string,
  const { authState  } = useAuth();
  const user = authState?.user,
  const theme = useTheme()
  const styles = createStyles(theme),
  const [isLoading, setIsLoading] = useState(true),
  const [error, setError] = useState<string | null>(null),
  const [profile, setProfile] = useState<EnhancedProfile>({}),
  const [matchModalVisible, setMatchModalVisible] = useState(false),
  const [matchData, setMatchData] = useState<any>(null),
  const [isLiking, setIsLiking] = useState(false),
  // Fetch the housemate data when the component mounts,
  useEffect(() => {
  if (housemateId) {
      fetchHousemate() }
  }, [housemateId]);
  // Function to fetch the housemate data,
  const fetchHousemate = async (retryCount = 0) => {
  try {
      setIsLoading(true),
  setError(null)
      if (!housemateId) {
  throw new Error('Housemate ID is missing')
      },
  // Fetch the housemate from Supabase,
      const { data     : housemateData error: fetchError } = await supabase.from('user_profiles').select('id, first_name, last_name, avatar_url, occupation, location, bio, preferences, created_at'),
  ).eq('id', housemateId),
  .single()
      if (fetchError) {
  throw new Error(`Failed to fetch housemate: ${fetchError.message}`)
      },
  if (!housemateData) {
        throw new Error('Housemate not found') }
      // Extract interests from preferences if they exist,
  const interests = housemateData.preferences?.interests || [],
  ;
      // Generate mock compatibility factors based on a base score,
  const baseScore = 75 // Default base score if not available,
      const mockCompatibilityFactors = [{ label    : 'Lifestyle',
  score: Math.min(100 baseScore + Math.random() * 10 - 5),
    description: 'Daily routines, sleep schedule, and social activities' },
  {
  label: 'Cleanliness',
    score: Math.min(100, baseScore + Math.random() * 15 - 7),
  description: 'Cleaning habits and organization preferences'
  },
  {
  label: 'Communication',
    score: Math.min(100, baseScore + Math.random() * 12 - 6),
  description: 'Communication style and conflict resolution'
  },
  {
  label: 'Values',
    score: Math.min(100, baseScore + Math.random() * 8 - 4),
  description: 'Personal values and priorities'
  },
  { label: 'Interests',
    score: Math.min(100, baseScore - 5 + Math.random() * 10), ,
  description: 'Hobbies, activities, and social interests' }] // Generate mock lifestyle preferences for enhanced visualization,
  const mockLifestylePreferences: PreferenceItem[] = [
  {
          id: 'sleep_schedule',
    label: 'Sleep Schedule',
  value: 'Early bird',
    icon: 'early_bird',
  importance: 'high'
  },
  {
  id: 'cleanliness',
    label: 'Cleanliness',
  value: 'Very clean',
    icon: 'quiet',
  importance: 'high'
  },
  {
  id: 'noise_level',
    label: 'Noise Level',
  value: 'Quiet',
    icon: 'quiet',
  importance: 'medium'
  },
  {
  id: 'smoking',
    label: 'Smoking',
  value: false,
    icon: 'smoking',
  importance: 'high'
  },
  {
  id: 'drinking',
    label: 'Drinking',
  value: 'Socially',
    icon: 'drinking',
  importance: 'medium'
  },
  {
  id: 'pets',
    label: 'Pets',
  value: 'Loves dogs',
    icon: 'pets_dog',
  importance: 'medium'
  },
  {
  id: 'guests',
    label: 'Guests',
  value: 'Occasionally',
    importance: 'low' }
        {
  id: 'sharing',
    label: 'Sharing',
  value: 'Private bathroom preferred',
    importance: 'medium' }] // Enhance the profile with additional mock data,
  const enhancedProfile: EnhancedProfile = {, id: housemateData.id,
  first_name: housemateData.first_name,
    last_name: housemateData.last_name,
  avatar_url: housemateData.avatar_url,
    occupation: housemateData.occupation,
  location: housemateData.location,
    bio: housemateData.bio,
  interests: interests,
    created_at: housemateData.created_at,
  compatibility_score: 85, // Mock compatibility score,
  name: `${housemateData.first_name} ${housemateData.last_name}`;
        images: housemateData.avatar_url ? [housemateData.avatar_url]      : [],
    distance: '2.5 miles away',
  verified: true,
    budget: '$800 - $1,200 / month',
  moveInDate: 'Flexible, from June 2023',
  preferences: {, cleanliness: 'Very clean',
  schedule: 'Early bird',
    pets: 'Loves pets',
  smoking: 'Non-smoker',
    guests: 'Occasional guests',
  sharing: 'Prefers private bathroom'
  },
  // Add our new enhanced data
  compatibilityFactors: mockCompatibilityFactors,
    lifestylePreferences: mockLifestylePreferences,
  memberSince: new Date(
          housemateData.created_at || Date.now() - 90 * 24 * 60 * 60 * 1000,
  ).toLocaleDateString()
        responseRate: 92,
    lastActive: '2 days ago',
  profileCompleteness: 85
      },
  setProfile(enhancedProfile)
    } catch (err) {
  console.error('Error fetching housemate:', err),
  // If we've retried less than 3 times and it's a network error, retry,
  if (retryCount < 3 && err instanceof Error && err.message.includes('network')) {
        setTimeout(() => fetchHousemate(retryCount + 1) 1000),
  return null;
      },
  setError(err instanceof Error ? err.message      : 'Failed to load housemate profile')
    } finally {
  setIsLoading(false)
    }
  }
  // Handle navigation back to search,
  const handleBackPress = useCallback(() => {
  router.back() }, [router]);
  // Handle message button press,
  const handleMessagePress = useCallback(async () => {
  if (!user?.id || !housemateId) {
      router.push('/login?redirectTo=%2Fsearch%2Fhousemate' as any),
  return null;
    },
  try {
      // Provide haptic feedback,
  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium)
      // Use the match service to start a conversation with the housemate,
  const roomId = await matchService.startConversationWithMatch(user.id);
        housemateId, ,
  `Hi ${profile.first_name || 'there'}! I saw your profile and thought we might be a good match as roommates. Would you like to chat?`)
      ),
  if (roomId) {
         // Show success feedback,
  Toast.show({
           type   : 'success',
  text1: 'Conversation started!',
    text2: `Opening chat with ${profile.first_name || 'housemate'}`
  position: 'bottom'),
    visibilityTime: 2000)
  })
         // Navigate to the chat screen with the new room ID,
  const queryParams = new URLSearchParams()
         queryParams.set('roomId', roomId),
  queryParams.set('recipientId', housemateId),
  queryParams.set('recipientName', profile.name || `${profile.first_name || ''} ${profile.last_name || ''}`.trim()),
  queryParams.set('fromProfile', 'true'),
  router.push(`/chat? ${queryParams.toString()}`)
       } else {
  // Fallback     : try to find existing chat or show error
         console.error('Failed to create chat room'),
  Toast.show({ 
           type: 'error',
    text1: 'Unable to start conversation',
  text2: 'Please try again later.'),
    position: 'bottom') })
       }
  } catch (error) {
       console.error('Error starting conversation:', error),
  Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error)
       Toast.show({
  type: 'error',
    text1: 'Failed to start conversation'),
  text2: 'Please check your connection and try again.'),
    position: 'bottom') })
     }
  }, [router, housemateId, user?.id, profile]);
  // Handle like button press
  const handleLikePress = useCallback(async () => { if (!user?.id || !housemateId || isLiking) return null,
  try {
      setIsLiking(true),
  // Provide haptic feedback when the user taps the like button,
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium),
  // Call the match service to like the profile,
      const result = await matchService.likeProfile(user.id, housemateId),
  if (result.isMatch) {;
        // It's a match! Show celebration modal,
  Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success)
        // Prepare match data for the modal,
  const matchDataForModal = {
          id     : result.matchId,
  matchedUser: {, id: housemateId,
  name: profile.name || 'Roommate',
    firstName: profile.first_name,
  avatar: profile.avatar_url,
    compatibility: profile.compatibility_score },
  currentUser: { i, d: user.id,
    avatar: user.user_metadata?.avatar_url }
  }
  setMatchData(matchDataForModal),
  // Show the match celebration modal
  setMatchModalVisible(true),
  // Also register the match with the notification service for future reference // This will allow the match to appear in the notifications and match list,
  try {
  const { matchNotificationService, MatchEventType  } = await import(
  '../../../services/MatchNotificationService', ,
  )
          await matchNotificationService.initialize(user.id),
  // Create a match data object to emit through the event system,
          const matchData = { matchId   : result.matchId,
  userId: user.id,
    matchedUserId: housemateId,
  matchedUserName: profile.name || 'Roommate',
    matchedUserAvatar: profile.avatar_url,
  compatibility: profile.compatibility_score || 0,
    matchedAt: new Date().toISOString(),
  isViewed: true, // Since we're showing the modal, it's viewed,
  isMessaged: false }
          // Use the event emitter to notify about the new match,
  matchNotificationService.on(MatchEventType.NEW_MATCH, () => {
  // This is just to register interest in match events,
            console.log('Match event registered') })
          // Show a match notification (this is a public method),
  if (result.matchId) {
            matchNotificationService.showMatchNotification(result.matchId) }
        } catch (notificationError) {
  console.warn('Failed to register match with notification service:', notificationError),
  // Non-critical error, continue with the flow }
      } else {
  // No match yet, but like was recorded,
  Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success)
      }
  } catch (err) {
      console.error('Error liking profile:', err),
  Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error)
    } finally {
  setIsLiking(false)
    }
  }, [user?.id, housemateId, profile, isLiking]);
  // Handle starting a conversation from the match modal,
  const handleStartMessaging = useCallback(
  async (matchedUserId     : string name: string) => {
  try {
  // Close the modal
        setMatchModalVisible(false),
  // Provide haptic feedback
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium),
  if (!user?.id || !matchData?.id) {
          console.error('Missing user or match data'),
  return null;
        },
  // Use the match service to start a conversation,
        const roomId = await matchService.startConversationWithMatch(user.id, matchedUserId),
  if (roomId) {;
          // Navigate to the chat screen with the new room ID - use standardized main chat path,
  const queryParams = new URLSearchParams()
          queryParams.set('roomId', roomId),
  queryParams.set('recipientId', matchedUserId),
  queryParams.set('recipientName', name),
  queryParams.set('fromMatch', 'true') // Add fromMatch parameter to enable match-specific features,
  router.push(`/chat? ${queryParams.toString()}`)
        } else {
  // Fallback to the new message screen if conversation creation fails,
          router.push(`/chat/new?recipient= ${matchedUserId}`)
  }
      } catch (error) {
  console.error('Error starting conversation     : ' error)
        // Fallback to the new message screen,
  router.push(`/chat/new? recipient= ${matchedUserId}`)
      }
  }
    [router, user?.id, matchData],
  )
  // Handle viewing a profile from the match modal,
  const handleViewProfile = useCallback((userId    : string) => {
  // Close the modal,
  setMatchModalVisible(false)
    // We're already on the profile so no need to navigate }, []);
  // Render loading state with skeletons, ,
  if (isLoading) {
  return (
  <View style= {[styles.container,  { paddingTop: insets.top}]}>,
  <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={handleBackPress} accessible={true} accessibilityLabel="Go back", ,
  accessibilityRole= "button"
          >,
  <ChevronLeft size= {24} color={"#1E293B" /}>
          </TouchableOpacity>,
  <Text style={styles.headerTitle}>Housemate Profile</Text>
        </View>,
  <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer} showsVerticalScrollIndicator={false}
        >,
  <HousemateCardSkeleton />
          <HousemateCardSkeleton />,
  </ScrollView>
      </View>,
  )
  },
  // Render error state,
  if (error) {
  return (
    <View style= {[styles.container,  { paddingTop: insets.top}]}>,
  <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={handleBackPress} accessible={true} accessibilityLabel="Go back", ,
  accessibilityRole= "button"
          >,
  <ChevronLeft size= {24} color={"#1E293B" /}>
          </TouchableOpacity>,
  <Text style={styles.headerTitle}>Housemate Profile</Text>
        </View>,
  <View style={styles.notFoundContainer}>
                      <CircleAlert size={64} color={"#EF4444" /}>,
  <Text style={styles.notFoundTitle}>Oops! Something went wrong</Text>
          <Text style={styles.notFoundText}>{error}</Text>,
  <TouchableOpacity style={styles.backToSearchButton} onPress={handleBackPress} accessible={true} accessibilityLabel="Back to search", ,
  accessibilityRole= "button"
          >,
  <Text style={styles.backToSearchText}>Back to Search</Text>
          </TouchableOpacity>,
  </View>
      </View>,
  )
  },
  // Calculate overall compatibility score (average of factors)
  const overallCompatibilityScore = profile.compatibilityFactors?.length,
  ? profile.compatibilityFactors.reduce((sum, factor) => sum + factor.score, 0) /,
  profile.compatibilityFactors.length;
         : 0,
  // Render the profile
  return (
  <View style= {[styles.container,  { paddingTop: insets.top}]}>,
  {/* Header */}
      <View style={styles.header}>,
  <TouchableOpacity style={styles.backButton} onPress={handleBackPress} accessible={true} accessibilityLabel="Go back"
          accessibilityRole="button",
  >
          <ChevronLeft size= {24} color={"#1E293B" /}>,
  </TouchableOpacity>
        <Text style={styles.headerTitle}>,
  {profile.name || `${profile.first_name || ''} ${profile.last_name || ''}`}
        </Text>,
  {profile.compatibilityFactors && profile.compatibilityFactors.length > 0 && (
          <View style={styles.compatibilityBadgeHeader}>,
  <Star size={16} color={"#FFFFFF" /}>
            <Text style={styles.compatibilityBadgeText}>,
  {Math.round(overallCompatibilityScore)}%
            </Text>,
  </View>
        )},
  </View>
      <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer} showsVerticalScrollIndicator={false},
  >
        {/* Profile Image */}
  <View style={styles.imageContainer}>
          <Image,
  source={ uri: profile.images?.[0] || profile.avatar_url || DEFAULT_AVATAR        },
  style={styles.profileImage} accessible={true} accessibilityLabel={`Profile picture of ${profile.name || 'housemate'}`}
            accessibilityRole="image",
  />
          {profile.verified && (
  <View style= {styles.verifiedBadge}>
              <BadgeCheck size={16} color={"#FFFFFF" /}>,
  <Text style={styles.verifiedText}>Verified</Text>
            </View>,
  )}
        </View>,
  {/* Basic Info Section */}
        <View style={styles.basicInfoSection}>,
  <View style={styles.nameRow}>
            <Text style={styles.nameText} accessibilityRole={"header"}>,
  {profile.name || `${profile.first_name || ''} ${profile.last_name || ''}`}
            </Text>,
  </View>
          <View style={styles.infoRow}>,
  {profile.occupation && <Text style={styles.occupationText}>{profile.occupation}</Text>

            {profile.distance && (
  <View style={styles.distanceContainer}>
                <MapPin size={14} color={"#64748B" /}>,
  <Text style={styles.distanceText}>{profile.distance}</Text>
              </View>,
  )}
          </View>,
  <View style={styles.memberInfoRow}>
            <Text style={styles.memberSinceText}>,
  Member since    : {' '}
              {profile.memberSince || new Date(profile.created_at || '').toLocaleDateString()},
  </Text>
            {profile.responseRate && (
  <Text style={styles.responseRateText}>{profile.responseRate}% response rate</Text>
            )},
  </View>
        </View>,
  {/* Compatibility Section with Progressive Disclosure */}
        {profile.compatibilityFactors && profile.compatibilityFactors.length > 0 && (
  <ProgressiveInfoSection
            title="Compatibility",
  initiallyExpanded={true} importance="high"
            accessibilityLabel={   `Compatibility section. Overall compatibility: ${Math.round(overallCompatibilityScore)      }%`},
  >
            <CompatibilityChart factors={profile.compatibilityFactors} overallScore={overallCompatibilityScore},
  />
          </ProgressiveInfoSection>,
  )}
        {/* Bio Section */}
  {profile.bio && (
          <ProgressiveInfoSection title="About Me" initiallyExpanded={true} importance={"high"}>,
  <Text style={styles.bioText} accessibilityRole={"text"}>
              {profile.bio},
  </Text>
          </ProgressiveInfoSection>,
  )}
        {/* Interests Section */}
  {profile.interests && profile.interests.length > 0 && (
          <ProgressiveInfoSection title="Interests" initiallyExpanded={false} importance={"medium"}>,
  {profile.interests && profile.interests.length > 0 && (
              <View style={styles.interestsContainer}>,
  {profile.interests.map((interest: string inde, x: number) => (
                  <View key= {`interest-${index}`} style={styles.interestTag}>,
  <Text style={styles.interestText}>{interest}</Text>
                  </View>,
  ))}
              </View>,
  )}
          </ProgressiveInfoSection>,
  )}
        {/* Lifestyle Preferences Section */}
  {profile.lifestylePreferences && profile.lifestylePreferences.length > 0 && (
          <ProgressiveInfoSection,
  title="Lifestyle Preferences", ,
  initiallyExpanded= {false} importance="high"
          >,
  <PreferencesGrid preferences= {profile.lifestylePreferences} columns={{2} /}>
          </ProgressiveInfoSection>,
  )}
        {/* Housing Preferences */}
  <ProgressiveInfoSection
          title="Housing Preferences",
  initiallyExpanded= {false} importance="medium"
        >,
  <View style={styles.housingPreferencesContainer}>
            {profile.budget && (
  <View style={styles.infoRow}>
                <View style={styles.infoIcon}>,
  <DollarSign size={18} color={"#4F46E5" /}>
                </View>,
  <Text style={styles.infoLabel}>Budget:</Text>
                <Text style={styles.infoText}>{profile.budget}</Text>,
  </View>
            )},
  {profile.moveInDate && (
              <View style={styles.infoRow}>,
  <View style={styles.infoIcon}>
                  <Calendar size={18} color={"#4F46E5" /}>,
  </View>
                <Text style={styles.infoLabel}>Move-in Date:</Text>,
  <Text style={styles.infoText}>{profile.moveInDate}</Text>
              </View>,
  )}
            {/* Additional housing preferences can be added here */}
  </View>
        </ProgressiveInfoSection>,
  {/* Action Buttons */}
        <View style={styles.actionContainer}>,
  <TouchableOpacity style={styles.messageButton} onPress={handleMessagePress} accessible={true} accessibilityLabel={`Message ${profile.name || 'housemate'}`}
            accessibilityRole="button",
  accessibilityHint= "Opens messaging screen"
          >,
  <MessageCircle size= {20} color={"#FFFFFF" /}>
            <Text style={styles.messageButtonText}>Message</Text>,
  </TouchableOpacity>
          <TouchableOpacity style={styles.likeButton} onPress={handleLikePress} accessible={true} accessibilityLabel={`Like ${profile.name || 'housemate'}`},
  accessibilityRole="button";
            accessibilityHint= "Adds this housemate to your liked profiles",
  >
            <Heart size= {20} color={"#FFFFFF" /}>,
  <Text style={styles.likeButtonText}>Like</Text>
          </TouchableOpacity>,
  </View>
      </ScrollView>,
  {/* Match Celebration Modal */}
      {matchData && (
  <MatchCelebrationModal visible={matchModalVisible} onClose={() => setMatchModalVisible(false)} matchedUser={matchData.matchedUser} currentUser={matchData.currentUser} onStartMessaging={handleStartMessaging} onViewProfile={handleViewProfile}
        />,
  )}
    </View>,
  )
},
  const createStyles = (theme: Theme) => {
  // Ensure theme colors are safe strings,
  const safeTheme = {
    colors: {, background: safeColor(theme.colors?.background),
  surface     : safeColor(theme.colors?.surface)
      text: safeColor(theme.colors?.text),
  textSecondary : safeColor(theme.colors?.textSecondary)
      primary: safeColor(theme.colors?.primary),
  success : safeColor(theme.colors?.success)
      error: safeColor(theme.colors?.error) }
  },
  return StyleSheet.create({ container : {
      flex: 1,
    backgroundColor: safeTheme.theme.colors.background },
  header: { flexDirectio, n: 'row',
    alignItems: 'center',
  padding: 16 }
    headerTitle: { fontSiz, e: 18,
    fontWeight: '600',
  color: safeTheme.theme.colors.text,
    flex: 1 },
  backButton: {, width: 40,
  height: 40,
    borderRadius: 20,
  backgroundColor: safeTheme.theme.colors.surface,
    justifyContent: 'center',
  alignItems: 'center',
    marginRight: 12,
  shadowColor: safeTheme.theme.colors.text,
    shadowOffset: { widt, h: 0, height: 1 } ,
  shadowOpacity: 0.1,
    shadowRadius: 2,
  elevation: 2
    },
  notFoundContainer: { fle, x: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 24,
  backgroundColor: safeTheme.theme.colors.background }
    notFoundTitle: { fontSiz, e: 24,
    fontWeight: '700',
  color: safeTheme.theme.colors.text,
    marginTop: 16,
  marginBottom: 8 }
    notFoundText: { fontSiz, e: 16,
    color: safeTheme.theme.colors.textSecondary,
  textAlign: 'center',
    marginBottom: 24 },
  loadingContainer: { fle, x: 1,
    justifyContent: 'center',
  alignItems: 'center',
    backgroundColor: safeTheme.theme.colors.background },
  loadingText: { marginTo, p: 16,
    fontSize: 16,
  color: safeTheme.theme.colors.textSecondary }
    errorContainer: { fle, x: 1,
    justifyContent: 'center',
  alignItems: 'center',
    paddingHorizontal: 16,
  backgroundColor: safeTheme.theme.colors.background }
    errorText: { fontSiz, e: 16,
    color: safeTheme.theme.colors.textSecondary,
  textAlign: 'center',
    marginBottom: 24 },
  backToSearchButton: { paddingVertica, l: 12,
    paddingHorizontal: 24,
  backgroundColor: safeTheme.theme.colors.primary,
    borderRadius: 8 },
  backToSearchText: { fontSiz, e: 16,
    fontWeight: '600',
  color: safeTheme.theme.colors.background }
    content: { fle, x: 1 },
  contentContainer: { paddingBotto, m: 32,
    paddingHorizontal: 16 },
  imageContainer: {, width: '100%',
  height: 250,
    backgroundColor: safeTheme.theme.colors.surface,
  marginBottom: 16,
    position: 'relative',
  borderRadius: 12,
    overflow: 'hidden' }
    profileImage: {, width: '100%',
  height: '100%',
    resizeMode: 'cover' }
    verifiedBadge: { positio, n: 'absolute',
    bottom: 12,
  right: 12,
    backgroundColor: safeTheme.theme.colors.success,
  flexDirection: 'row',
    alignItems: 'center',
  paddingHorizontal: 8,
    paddingVertical: 4,
  borderRadius: 12 }
    verifiedText: { colo, r: safeTheme.theme.colors.background,
    fontWeight: '600',
  fontSize: 12,
    marginLeft: 4 },
  basicInfoSection: { marginBotto, m: 16 }
    nameRow: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: 4 }
    nameText: { fontSiz, e: 24,
    fontWeight: '700',
  color: safeTheme.theme.colors.text }
    infoRow: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: 8 }
    occupationText: { fontSiz, e: 16,
    color: safeTheme.theme.colors.textSecondary,
  marginRight: 12 }
    distanceContainer: {, flexDirection: 'row',
  alignItems: 'center'
  },
  distanceText: { fontSiz, e: 14,
    color: safeTheme.theme.colors.textSecondary,
  marginLeft: 4 }
    memberInfoRow: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  marginTop: 4 }
    memberSinceText: { fontSiz, e: 14,
    color: safeTheme.theme.colors.textSecondary },
  responseRateText: { fontSiz, e: 14,
    color: safeTheme.theme.colors.textSecondary },
  bioText: { fontSiz, e: 16,
    lineHeight: 24,
  color: safeTheme.theme.colors.text }
    interestsContainer: {, flexDirection: 'row'),
  flexWrap: 'wrap'),
    marginTop: 8) }
    interestTag: { backgroundColo, r: safeColorWithOpacity(safeTheme.theme.colors.primary, 0.1),
  paddingHorizontal: 12,
    paddingVertical: 6,
  borderRadius: 16,
    marginRight: 8,
  marginBottom: 8 }
    interestText: {, fontSize: 14,
  color: safeTheme.theme.colors.primary,
    fontWeight: '500' }
    housingPreferencesContainer: { marginTo, p: 8 },
  infoIcon: { widt, h: 32,
    height: 32,
  borderRadius: 16,
    backgroundColor: safeColorWithOpacity(safeTheme.theme.colors.primary, 0.1),
  justifyContent: 'center',
    alignItems: 'center',
  marginRight: 8 }
    infoLabel: { fontSiz, e: 14,
    fontWeight: '600',
  color: safeTheme.theme.colors.textSecondary,
    marginRight: 4,
  width: 100 }
    infoText: { fontSiz, e: 14,
    color: safeTheme.theme.colors.textSecondary,
  flex: 1 }
    compatibilityBadgeHeader: { flexDirectio, n: 'row',
    alignItems: 'center',
  backgroundColor: safeTheme.theme.colors.primary,
    paddingHorizontal: 8,
  paddingVertical: 4,
    borderRadius: 12,
  marginLeft: 8 }
    compatibilityBadgeText: { colo, r: safeTheme.theme.colors.background,
    fontWeight: '700',
  fontSize: 12,
    marginLeft: 4 },
  actionContainer: { flexDirectio, n: 'row',
    marginTop: 24 },
  messageButton: { fle, x: 1,
    backgroundColor: safeTheme.theme.colors.primary,
  paddingVertical: 12,
    borderRadius: 8,
  flexDirection: 'row',
    justifyContent: 'center',
  alignItems: 'center',
    marginRight: 8,
  minHeight: 48 }
    messageButtonText: { colo, r: safeTheme.theme.colors.background,
    fontWeight: '600',
  marginLeft: 8 }
    likeButton: { fle, x: 1,
    backgroundColor: '#EC4899', // Pink color for like/heart button,
  paddingVertical: 12,
    borderRadius: 8,
  flexDirection: 'row',
    justifyContent: 'center',
  alignItems: 'center',
    marginLeft: 8,
  minHeight: 48 }
    likeButtonText: { colo, r: safeTheme.theme.colors.background,
    fontWeight: '600',
  marginLeft: 8 }
  })
  }
export default HousemateScreen