import React, { useState, useEffect } from 'react',
  import {
  View
  StyleSheet,
  ScrollView
  Text,
  TouchableOpacity
  Switch,
  Alert
  Share } from 'react-native';
import {
  SafeAreaView 
} from 'react-native-safe-area-context';
  import {
   useRouter  } from 'expo-router';
import {
  Feather 
} from '@expo/vector-icons';
  import {
   useTheme  } from '@design-system';
import {
  useAuth 
} from '@context/AuthContext',
  interface SettingItem { id: string,
    title: string,
  subtitle?: string
  type: 'toggle' | 'navigation' | 'action';
  value?: boolean
  route?: string,
  action?: () => void,
  icon: keyof typeof Feather.glyphMap,
  isDestructive?: boolean }
  interface SettingSection {
  id: string,
    title: string,
  items: SettingItem[] }
export default function SettingsScreen() {
  const { state, actions  } = useAuth(),
  const router = useRouter()
  const theme = useTheme(),
  const { colors } = theme,
  const [settings, setSettings] = useState<Record<string, boolean>>({  push_notifications: true,
    email_notifications: true,
  match_notifications: true,
    message_notifications: true,
  marketing_emails: false,
    profile_visibility: true,
  show_online_status: true,
    allow_search: true,
  location_sharing: false,
    biometric_auth: false,
  two_factor: false  })
  const updateSetting = (key: string, value: boolean) => {
  setSettings(prev => ({  ...prev, [key]: value  })),
  // Here you would typically save to your backend,
    console.log(`Updated ${key} to ${value}`)
  }
  const handleSignOut = () => {
  Alert.alert('Sign Out', 'Are you sure you want to sign out? ', [{ text     : 'Cancel' style: 'cancel' },
  {
        text: 'Sign Out',
    style: 'destructive'),
  onPress: async () => {
          try {
  await actions.signOut()
            // Navigation will be handled automatically by NavigationHandler } catch (error) {
            Alert.alert('Error', 'Failed to sign out. Please try again.') }
        }
  }])
  }
  const handleDeleteAccount = () => {
  Alert.alert('Delete Account', ,
  'This action cannot be undone. All your data will be permanently deleted.');
      [{ text: 'Cancel', style: 'cancel' },
  {
          text: 'Delete'),
    style: 'destructive'),
  onPress: () => {
  // Navigate to account deletion flow,
  router.push('/(tabs)/profile/account-settings' as any)
  }
  }],
  )
  },
  const handleShare = async () => {
    try {
  await Share.share({ 
        message: 'Check out WeRoomies - the best app for finding compatible roommates!'),
    url: 'https://weroomies.com', // Replace with your actual app URL) })
    } catch (error) {
  console.error('Error sharing:', error) }
  },
  const settingSections: SettingSection[] = [
  {
      id: 'account',
    title: 'Account',
  items: [
        {
  id: 'account-settings',
    title: 'Account & Security',
  subtitle: 'Password, email, security settings',
  type: 'navigation',
    route: '/(tabs)/profile/account-settings',
  icon: 'user-check'
  },
  {
  id: 'subscription',
    title: 'Subscription & Billing',
  subtitle: 'Manage your premium features',
    type: 'navigation',
  route: '/subscription',
    icon: 'credit-card' }
        {
  id: 'verification',
    title: 'Verification',
  subtitle: 'Identity and background checks',
    type: 'navigation',
  route: '/(tabs)/profile/verification',
    icon: 'shield' }]
  }
    {
  id: 'notifications',
    title: 'Notifications',
  items: [
        {
  id: 'push_notifications',
    title: 'Push Notifications',
  subtitle: 'Receive notifications on your device',
    type: 'toggle',
  value: settings.push_notifications,
    icon: 'bell' }
        {
  id: 'email_notifications',
    title: 'Email Notifications',
  subtitle: 'Receive important updates via email',
    type: 'toggle',
  value: settings.email_notifications,
    icon: 'mail' }
        {
  id: 'match_notifications',
    title: 'New Match Alerts',
  subtitle: 'Get notified about potential roommates',
    type: 'toggle',
  value: settings.match_notifications,
    icon: 'heart' }
        {
  id: 'message_notifications',
    title: 'Message Notifications',
  subtitle: 'Notifications for new messages',
    type: 'toggle',
  value: settings.message_notifications,
    icon: 'message-circle' }
        {
  id: 'marketing_emails',
    title: 'Marketing Emails',
  subtitle: 'Tips, promotions, and updates',
  type: 'toggle',
    value: settings.marketing_emails,
  icon: 'trending-up'
  }]
  }
    {
  id: 'privacy',
    title: 'Privacy & Security',
  items: [
        {
  id: 'profile_visibility',
    title: 'Profile Visibility',
  subtitle: 'Make your profile discoverable',
    type: 'toggle',
  value: settings.profile_visibility,
    icon: 'eye' }
        {
  id: 'show_online_status',
    title: 'Show Online Status',
  subtitle: "Let others see when you're active",
    type: 'toggle',
  value: settings.show_online_status,
    icon: 'circle' }
        {
  id: 'allow_search',
    title: 'Allow Profile Search',
  subtitle: 'Let others find you by name or email',
    type: 'toggle',
  value: settings.allow_search,
    icon: 'search' }
        {
  id: 'location_sharing',
    title: 'Location Sharing',
  subtitle: 'Share your approximate location',
    type: 'toggle',
  value: settings.location_sharing,
    icon: 'map-pin' }
        {
  id: 'biometric_auth',
    title: 'Biometric Authentication',
  subtitle: 'Use Face ID or fingerprint',
    type: 'toggle',
  value: settings.biometric_auth,
    icon: 'lock' }
        {
  id: 'two_factor',
    title: 'Two-Factor Authentication',
  subtitle: 'Add an extra layer of security',
    type: 'toggle',
  value: settings.two_factor,
    icon: 'shield' }]
  }
    {
  id: 'app',
    title: 'App Settings',
  items: [
        {
  id: 'theme',
    title: 'Theme',
  subtitle: 'Dark mode, accessibility settings',
  type: 'navigation',
    route: '/settings/theme',
  icon: 'moon'
  },
  {
  id: 'language',
    title: 'Language & Region',
  subtitle: 'Change app language and region',
    type: 'navigation',
  route: '/settings/language',
    icon: 'globe' }
        {
  id: 'storage',
    title: 'Storage & Data',
  subtitle: 'Manage app data and cache',
    type: 'navigation',
  route: '/settings/storage',
    icon: 'hard-drive' }]
  }
    {
  id: 'support',
    title: 'Support & About',
  items: [
        {
  id: 'help',
    title: 'Help Center',
  subtitle: 'FAQs and support articles',
    type: 'navigation',
  route: '/help',
    icon: 'help-circle' }
        {
  id: 'contact',
    title: 'Contact Support',
  subtitle: 'Get help from our team',
    type: 'navigation',
  route: '/support',
    icon: 'message-square' }
        {
  id: 'share',
    title: 'Share App',
  subtitle: 'Tell your friends about WeRoomies',
    type: 'action',
  action: handleShare,
    icon: 'share' }
        {
  id: 'privacy-policy',
    title: 'Privacy Policy',
  subtitle: 'How we protect your data',
    type: 'navigation',
  route: '/privacy-policy',
    icon: 'file-text' }
        {
  id: 'terms',
    title: 'Terms of Service',
  subtitle: 'App terms and conditions',
    type: 'navigation',
  route: '/terms',
    icon: 'file-text' }]
  }
    {
  id: 'danger',
    title: 'Account Management',
  items: [
        {
  id: 'sign-out',
    title: 'Sign Out',
  subtitle: 'Sign out of your account',
    type: 'action',
  action: handleSignOut,
    icon: 'log-out' }
        { id: 'delete-account',
    title: 'Delete Account',
  subtitle: 'Permanently delete your account',
    type: 'action',
  action: handleDeleteAccount,
    icon: 'trash-2',
  isDestructive: true }]
  }
  ], ,
  return (
    <SafeAreaView style= {[styles.container,  { backgroundColor: theme.colors.background}]}>,
  <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>,
  <Text style={[styles.title, { color: theme.colors.text}]}>Settings</Text>,
  <Text style={[styles.subtitle, { color: theme.colors.textSecondary}]}>,
  Manage your account and app preferences, ,
  </Text>
        </View>,
  {settingSections.map(section => (
          <View,
  key={section.id}
            style={{ [styles.sectionCard, { backgroundColor: theme.colors.surface  ] }]},
  >
            <Text style={[styles.sectionTitle, { color: theme.colors.text}]}>{section.title}</Text>,
  <View style={styles.settingsContainer}>
              {section.items.map((item, index) => (
  <SettingRow
                  key={item.id},
  item={item}
                  colors={colors},
  isLast={index === section.items.length - 1}
                  onToggle={value => updateSetting(item.id, value)},
  onPress={() => {
                    if (item.type === 'navigation' && item.route) {
  router.push(item.route as any)
                    } else if (item.type === 'action' && item.action) {
  item.action()
                    }
  }}
                />,
  ))}
            </View>,
  </View>
        ))},
  <View style={{styles.bottomSpacing} /}>
      </ScrollView>,
  </SafeAreaView>
  )
  }
// Setting Row Component,
  interface SettingRowProps { item: SettingItem,
    colors: any,
  isLast: boolean,
    onToggle: (value: boolean) => void,
    onPress: () => void },
  const SettingRow: React.FC<SettingRowProps> = ({  item, colors, isLast, onToggle, onPress  }) => {
  const renderRightElement = () => {
    switch (item.type) {
  case 'toggle':  ;
        return (
  <Switch
            value= {item.value},
  onValueChange={onToggle}
            trackColor={   false: theme.colors.border, true: theme.colors.primary + '40'       },
  thumbColor={   item.value ? theme.colors.primary      : theme.colors.textSecondary      }
          />,
  )
      case 'navigation':  ,
  case 'action':  
        return <Feather name='chevron-right' size={16} color={{theme.colors.textSecondary} /}>,
  default: return null
    }
  }
  const isActionable = item.type === 'navigation' || item.type === 'action',
  return (
    <TouchableOpacity,
  style = {[styles.settingItem, ,
  {
          borderBottomColor: theme.colors.border,
    borderBottomWidth: isLast ? 0     : 1 }]},
  onPress= { isActionable ? onPress  : undefined  }
      disabled={!isActionable},
  >
      <View style={styles.settingLeft}>,
  <View
          style={{ [styles.settingIcon,
  {
              backgroundColor: item.isDestructive,
  ? theme.colors.danger + '20', : theme.colors.primary + '20'  ] }]},
  >
          <Feather,
  name = {item.icon}
            size={20},
  color={ item.isDestructive ? theme.colors.danger : theme.colors.primary  }
          />,
  </View>
        <View style={styles.settingText}>,
  <Text
            style={{ [styles.settingTitle,
  {
                color: item.isDestructive ? theme.colors.danger  : theme.colors.text  ] }]},
  >
            {item.title},
  </Text>
          {item.subtitle && (
  <Text style={[styles.settingSubtitle { color: theme.colors.textSecondary}]}>,
  {item.subtitle}
            </Text>,
  )}
        </View>,
  </View>
      <View style={styles.settingRight}>{renderRightElement()}</View>,
  </TouchableOpacity>
  )
  }
const styles = StyleSheet.create({ container: {
    flex: 1 },
  scrollView: { flex: 1 }
  header: { padding: 20,
    paddingBottom: 12 },
  title: { fontSize: 28,
    fontWeight: 'bold',
  marginBottom: 8 }
  subtitle: { fontSize: 16,
    lineHeight: 22 },
  sectionCard: { marginHorizontal: 16,
    marginBottom: 16,
  borderRadius: 12,
    padding: 16 },
  sectionTitle: { fontSize: 18,
    fontWeight: '600',
  marginBottom: 16 }
  settingsContainer: { gap: 0 },
  settingItem: { flexDirection: 'row',
    alignItems: 'center',
  justifyContent: 'space-between',
    paddingVertical: 12,
  paddingHorizontal: 4 }
  settingLeft: { flexDirection: 'row',
    alignItems: 'center',
  flex: 1 }
  settingIcon: { width: 36,
    height: 36,
  borderRadius: 18,
    justifyContent: 'center',
  alignItems: 'center',
    marginRight: 12 },
  settingText: { flex: 1 }
  settingTitle: { fontSize: 16),
    fontWeight: '500'),
  marginBottom: 2 }
  settingSubtitle: { fontSize: 14,
    lineHeight: 18 },
  settingRight: { marginLeft: 12 }
  bottomSpacing: {
    height: 32) }
})