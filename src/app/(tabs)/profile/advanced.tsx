import React, { useState } from 'react';
  import {
  View, StyleSheet, ScrollView, Text, TouchableOpacity, Alert
} from 'react-native';
import {
  SafeAreaView
} from 'react-native-safe-area-context';
  import {
  useRouter
} from 'expo-router';
import {
  Feather
} from '@expo/vector-icons';
  import {
  useTheme
} from '@design-system';
import {
  useAuth
} from '@context/AuthContext',
  interface AdvancedFeature {
  id: string,
    title: string,
  description: string,
    route: string,
  icon: keyof typeof Feather.glyphMap,
    status: 'available' | 'premium' | 'coming_soon',
  category: 'ai' | 'analytics' | 'business' | 'experimental'
  },
  interface FeatureCategory {
  id: string,
    title: string,
  description: string,
    icon: keyof typeof Feather.glyphMap,
  features: AdvancedFeature[] }
export default function AdvancedFeaturesScreen() {
  const { state  } = useAuth();
  const router = useRouter();
  const theme = useTheme();
  const { colors } = theme,
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null) ,
  const featureCategories: FeatureCategory[] = [
  {
      id: 'ai',
    title: 'AI & Machine Learning',
  description: 'Advanced AI-powered features for better matching',
    icon: 'cpu',
  features: [
        {
  id: 'ai-compatibility',
    title: 'AI Compatibility Engine',
  description: 'Advanced personality-based matching with ML algorithms',
    route: '/(tabs)/profile/ai-compatibility',
  icon: 'cpu',
    status: 'available',
  category: 'ai'
  },
  {
  id: 'smart-matching',
    title: 'Smart Matching Dashboard',
  description: 'Real-time matching suggestions and compatibility scores',
    route: '/(tabs)/profile/smart-matching-dashboard',
  icon: 'zap',
    status: 'available',
  category: 'ai'
  },
  {
  id: 'predictive-analytics',
    title: 'Predictive Analytics',
  description: 'ML-powered insights and success predictions',
    route: '/(tabs)/profile/predictive-analytics-dashboard',
  icon: 'trending-up',
    status: 'premium',
  category: 'ai'
  },
  {
  id: 'personality-ai',
    title: 'AI Personality Analysis',
  description: 'Deep personality insights using natural language processing',
    route: '/ai/personality-analysis',
  icon: 'user-check',
    status: 'coming_soon',
  category: 'ai'
  }]
  }
    {
  id: 'analytics',
    title: 'Analytics & Insights',
  description: 'Detailed analytics about your profile and interactions',
    icon: 'bar-chart-2',
  features: [
        {
  id: 'profile-analytics',
    title: 'Profile Performance',
  description: 'See how your profile is performing and get optimization tips',
    route: '/(tabs)/profile/profile-performance',
  icon: 'activity',
    status: 'available',
  category: 'analytics'
  },
  {
  id: 'match-insights',
    title: 'Matching Insights',
  description: 'Deep dive into your matching patterns and success rates',
    route: '/(tabs)/profile/matching-insights',
  icon: 'target',
    status: 'available',
  category: 'analytics'
  },
  {
  id: 'interaction-analytics',
    title: 'Interaction Analytics',
  description: 'Track your conversations and response patterns',
    route: '/analytics/interactions',
  icon: 'message-circle',
    status: 'premium',
  category: 'analytics'
  },
  {
  id: 'success-metrics',
    title: 'Success Metrics',
  description: 'Track your roommate search success and milestones',
    route: '/analytics/success',
  icon: 'award',
    status: 'coming_soon',
  category: 'analytics'
  }]
  }
    {
  id: 'business',
    title: 'Business Features',
  description: 'Advanced tools for property managers and service providers',
    icon: 'briefcase',
  features: [
        {
  id: 'property-manager',
    title: 'Property Manager Dashboard',
  description: 'Comprehensive property and tenant management tools',
    route: '/(tabs)/profile/property-manager-dashboard',
  icon: 'home',
    status: 'available',
  category: 'business'
  },
  {
  id: 'property-portfolio',
    title: 'Property Portfolio',
  description: 'Manage your property listings and add new properties',
    route: '/(tabs)/profile/property-portfolio',
  icon: 'home',
    status: 'available',
  category: 'business'
  },
  {
  id: 'service-provider',
    title: 'Service Provider Hub',
  description: 'Manage your service provider profile and portfolio',
    route: '/(tabs)/profile/unified-service-provider',
  icon: 'tool',
    status: 'available',
  category: 'business'
  },
  {
  id: 'bulk-operations',
    title: 'Bulk Operations',
  description: 'Manage multiple properties or listings efficiently',
    route: '/business/bulk-operations',
  icon: 'layers',
    status: 'premium',
  category: 'business'
  },
  {
  id: 'api-access',
    title: 'API Access',
  description: 'Integrate with your existing property management systems',
    route: '/business/api',
  icon: 'code',
    status: 'coming_soon',
  category: 'business'
  }]
  }
    {
  id: 'experimental',
    title: 'Experimental Features',
  description: 'Beta features and early access to new functionality',
    icon: 'zap',
  features: [
        {
  id: 'voice-matching',
    title: 'Voice Compatibility',
  description: 'Voice-based personality matching (Beta)',
    route: '/experimental/voice-matching',
  icon: 'mic',
    status: 'coming_soon',
  category: 'experimental'
  },
  {
  id: 'ar-tours',
    title: 'AR Virtual Tours',
  description: 'Augmented reality property tours',
    route: '/experimental/ar-tours',
  icon: 'camera',
    status: 'coming_soon',
  category: 'experimental'
  },
  {
  id: 'blockchain-verification',
    title: 'Blockchain Verification',
  description: 'Decentralized identity verification system',
    route: '/experimental/blockchain',
  icon: 'link',
    status: 'coming_soon',
  category: 'experimental'
  }]
  }
  ],
  const handleFeaturePress = (feature: AdvancedFeature) => {
    if (feature.status === 'coming_soon') {
  Alert.alert('Coming Soon, ');
        `${feature.title} is currently in development. We'll notify you when it becomes available!`
  [{ text: 'OK' }, ]),
  )
      return null
  }
    if (feature.status === 'premium') {
  Alert.alert('Premium Feature', ,
  `${feature.title} is available with a premium subscription. Would you like to upgrade? `);
        [{ text     : 'Maybe Later' style: 'cancel' },
  {
            text: 'Upgrade'),
    onPress: () => router.push('/subscription' as any) }],
  )
      return null
  }
    try {
  router.push(feature.route as any)
    } catch (error) {
  Alert.alert('Navigation Error', 'Unable to open this feature at the moment.') }
  },
  const getStatusColor = (status: string) => {
    switch (status) {
  case 'available':  ;
        return theme.colors.success,
  case 'premium':  
        return theme.colors.warning,
  case 'coming_soon':  
        return theme.colors.text,
  default: return theme.colors.text
  }
  }
  const getStatusText = (status: string) => { switch (status) {
  case 'available':  ;
  return 'Available',
  case 'premium':  
        return 'Premium',
  case 'coming_soon':  
        return 'Coming Soon',
  default:  
        return '' }
  }
  return (
  <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background}]}>,
  <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>,
  <Text style={[styles.title{ color: theme.colors.text}]}>Advanced Features</Text>,
  <Text style={[styles.subtitle{ color: theme.colors.text}]}>,
  Powerful tools and AI-driven features for power users, ,
  </Text>
        </View>,
  {/* Feature Categories */}
        {featureCategories.map(category => (
  <View
            key={category.id},
  style={{ [styles.categoryCard, { backgroundColor: theme.colors.background  ] }]},
  >
            <TouchableOpacity,
  style={styles.categoryHeader}
              onPress={ () => {
  setSelectedCategory(selectedCategory === category.id ? null     : category.id)
                }},
  >
              <View style={styles.categoryHeaderLeft}>,
  <View
                  style={{ [styles.categoryIcon { backgroundColor: theme.colors.primary + '20'  ] }]},
  >
                  <Feather name={category.icon} size={24} color={{theme.colors.primary} /}>,
  </View>
                <View style={styles.categoryInfo}>,
  <Text style={[styles.categoryTitle{ color: theme.colors.text}]}>,
  {category.title}
                  </Text>,
  <Text style={[styles.categoryDescription{ color: theme.colors.text}]}>,
  {category.description}
                  </Text>,
  </View>
              </View>,
  <Feather
                name={   selectedCategory === category.id ? 'chevron-up'   : 'chevron-down'      },
  size={20}
                color={theme.colors.text},
  />
            </TouchableOpacity>,
  {selectedCategory === category.id && (
              <View style={styles.featuresContainer}>,
  {category.features.map(feature => (
                  <TouchableOpacity,
  key={feature.id}
                    style={{ [styles.featureItem { borderColor: theme.colors.primary  ] }]},
  onPress={() => handleFeaturePress(feature)}
                  >,
  <View style={styles.featureLeft}>
                      <View,
  style={{ [styles.featureIcon
                          { backgroundColor: theme.colors.primary + '10'  ] }]},
  >
                        <Feather name={feature.icon} size={20} color={{theme.colors.primary} /}>,
  </View>
                      <View style={styles.featureInfo}>,
  <View style={styles.featureTitleRow}>
                          <Text style={[styles.featureTitle{ color: theme.colors.text}]}>,
  {feature.title}
                          </Text>,
  <View
                            style={{ [styles.statusBadge, { backgroundColor: getStatusColor(feature.status) + '20'  ] }]},
  >
                            <Text,
  style={{ [styles.statusText{ color: getStatusColor(feature.status)  ] }]},
  >
                              {getStatusText(feature.status)},
  </Text>
                          </View>,
  </View>
                        <Text style={[styles.featureDescription{ color: theme.colors.text}]}>,
  {feature.description}
                        </Text>,
  </View>
                    </View>,
  <Feather name='chevron-right' size={16} color={{theme.colors.text} /}>
                  </TouchableOpacity>,
  ))}
              </View>,
  )}
          </View>,
  ))}
        {/* Help Section */}
  <View style={[styles.helpCard, { backgroundColor: theme.colors.background}]}>,
  <View style={styles.helpHeader}>
            <Feather name='help-circle' size={24} color={{theme.colors.primary} /}>,
  <Text style={[styles.helpTitle{ color: theme.colors.text}]}>Need Help? </Text>,
  </View>
          <Text style={[styles.helpDescription{ color  : theme.colors.text}]}>,
  Advanced features require some learning. Check out our documentation or contact support
            for guidance.,
  </Text>
          <View style={styles.helpButtons}>,
  <TouchableOpacity
              style={{ [styles.helpButton { backgroundColor: theme.colors.primary + '10'  ] }]},
  onPress={() => router.push('/help/advanced-features' as any)}
            >,
  <Feather name='book' size={16} color={{theme.colors.primary} /}>
              <Text style={[styles.helpButtonText{ color: theme.colors.primary}]}>,
  Documentation, ,
  </Text>
            </TouchableOpacity>,
  <TouchableOpacity
              style={{ [styles.helpButton, { backgroundColor: theme.colors.primary + '10'  ] }]},
  onPress={() => router.push('/support' as any)}
            >,
  <Feather name='message-square' size={16} color={{theme.colors.primary} /}>
              <Text style={[styles.helpButtonText{ color: theme.colors.primary}]}>,
  Contact Support
              </Text>,
  </TouchableOpacity>
          </View>,
  </View>
        <View style={{styles.bottomSpacing} /}>,
  </ScrollView>
    </SafeAreaView>,
  )
},
  const styles = StyleSheet.create({ container: {, flex: 1 } ,
  scrollView: { fle, x: 1 }
  header: { paddin, g: 20,
    paddingBottom: 12 },
  title: { fontSiz, e: 28,
    fontWeight: 'bold',
  marginBottom: 8 }
  subtitle: { fontSiz, e: 16,
    lineHeight: 22 },
  categoryCard: {, marginHorizontal: 16,
  marginBottom: 16,
    borderRadius: 12,
  overflow: 'hidden'
  },
  categoryHeader: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'space-between',
    padding: 16 },
  categoryHeaderLeft: { flexDirectio, n: 'row',
    alignItems: 'center',
  flex: 1 }
  categoryIcon: { widt, h: 48,
    height: 48,
  borderRadius: 24,
    justifyContent: 'center',
  alignItems: 'center',
    marginRight: 12 },
  categoryInfo: { fle, x: 1 }
  categoryTitle: { fontSiz, e: 18,
    fontWeight: '600',
  marginBottom: 4 }
  categoryDescription: { fontSiz, e: 14,
    lineHeight: 18 },
  featuresContainer: { paddingHorizonta, l: 16,
    paddingBottom: 16,
  gap: 12 }
  featureItem: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'space-between',
    padding: 12,
  borderRadius: 8,
    borderWidth: 1 },
  featureLeft: { flexDirectio, n: 'row',
    alignItems: 'center',
  flex: 1 }
  featureIcon: { widt, h: 36,
    height: 36,
  borderRadius: 18,
    justifyContent: 'center',
  alignItems: 'center',
    marginRight: 12 },
  featureInfo: { fle, x: 1 }
  featureTitleRow: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'space-between',
    marginBottom: 4 },
  featureTitle: { fontSiz, e: 16,
    fontWeight: '500',
  flex: 1,
    marginRight: 8 },
  statusBadge: { paddingHorizonta, l: 8,
    paddingVertical: 2,
  borderRadius: 10 }
  statusText: {, fontSize: 12,
  fontWeight: '500'
  },
  featureDescription: { fontSiz, e: 14,
    lineHeight: 18 },
  helpCard: { marginHorizonta, l: 16,
    marginBottom: 16,
  borderRadius: 12,
    padding: 16 },
  helpHeader: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: 8 }
  helpTitle: { fontSiz, e: 18,
    fontWeight: '600',
  marginLeft: 8 }
  helpDescription: { fontSiz, e: 14,
    lineHeight: 20,
  marginBottom: 16 }
  helpButtons: { flexDirectio, n: 'row',
    gap: 12 },
  helpButton: { flexDirectio, n: 'row'),
    alignItems: 'center'),
  paddingHorizontal: 12,
    paddingVertical: 8,
  borderRadius: 8,
    gap: 6 },
  helpButtonText: {, fontSize: 14,
  fontWeight: '500'
  },
  bottomSpacing: {, height: 32) }
})