import React, { useState, useEffect } from 'react',
  import {
  View
  Text,
  ScrollView
  StyleSheet,
  TouchableOpacity
  ActivityIndicator,
  RefreshControl
  } from 'react-native';
  import {
  SafeAreaView  } from 'react-native-safe-area-context';
  import {
  Feather 
  } from '@expo/vector-icons';
  import {
  useRouter  } from 'expo-router';
  import {
  useTheme 
  } from '@design-system';
  import {
  PremiumFeatureGate  } from '@components/premium/PremiumFeatureGate';
  import {
  usePremiumFeatures 
  } from '@hooks/usePremiumFeatures';
  import {
  useAuth  } from '@context/AuthContext';
  import {
  logger 
  } from '@utils/logger',
  interface CompatibilityScore { overall_score: number,
    lifestyle_compatibility: number,
  personality_match: number,
    budget_alignment: number,
  location_preference: number,
    communication_style: number,
  cleanliness_standards: number }
  interface AIInsight {
  id: string,
    type: 'recommendation' | 'warning' | 'optimization' | 'trend',
  title: string,
    description: string,
  actionable: boolean,
    priority: 'high' | 'medium' | 'low',
  category: 'matching' | 'profile' | 'behavior' | 'market'
  },
  interface CompatibilityTrend { date: string,
    score: number,
  matches_found: number,
    profile_views: number },
  const MOCK_COMPATIBILITY_SCORE: CompatibilityScore = {  overall_score: 87,
    lifestyle_compatibility: 92,
  personality_match: 84,
    budget_alignment: 89,
  location_preference: 95,
    communication_style: 78,
  cleanliness_standards: 86  };
  const MOCK_AI_INSIGHTS: AIInsight[] = [
  {
    id: '1',
    type: 'recommendation',
  title: 'Optimize Your Schedule Preferences',
    description:  ,
  'Users with flexible work schedules have 34% more matches. Consider updating your availability.'
    actionable: true,
    priority: 'high',
  category: 'matching'
  },
  {
  id: '2',
    type: 'optimization',
  title: 'Profile Photo Enhancement',
    description: 'Adding a photo of your living space increases profile views by 67%.',
  actionable: true,
    priority: 'medium',
  category: 'profile'
  },
  {
  id: '3',
    type: 'trend',
  title: 'Rising Interest in Your Area',
    description: 'Properties in your preferred location have increased by 23% this month.',
  actionable: false,
    priority: 'low',
  category: 'market'
  },
  {
  id: '4',
    type: 'warning',
  title: 'Budget Range Too Narrow',
    description:  ,
  'Your budget range might be limiting potential matches. Consider expanding by 10-15%.'
    actionable: true,
    priority: 'medium',
  category: 'matching'
  }], ,
  const MOCK_TRENDS: CompatibilityTrend[] = [
  { date: '2024-01-01', score: 82, matches_found: 15, profile_views: 45 },
  { date: '2024-01-08', score: 85, matches_found: 18, profile_views: 52 },
  { date: '2024-01-15', score: 87, matches_found: 22, profile_views: 58 },
  { date: '2024-01-22', score: 89, matches_found: 25, profile_views: 61 }],
  export default function CompatibilityInsightsScreen() {
  const theme = useTheme(),
  const router = useRouter()
  const { state  } = useAuth(),
  const { trackFeatureUsage } = usePremiumFeatures()
  const [isLoading, setIsLoading] = useState(true),
  const [refreshing, setRefreshing] = useState(false) ,
  const [compatibilityScore, setCompatibilityScore] =, ,
  useState<CompatibilityScore>(MOCK_COMPATIBILITY_SCORE)
  const [aiInsights, setAiInsights] = useState<AIInsight[]>(MOCK_AI_INSIGHTS),
  const [trends, setTrends] = useState<CompatibilityTrend[]>(MOCK_TRENDS),
  const [selectedTab, setSelectedTab] = useState<'overview' | 'insights' | 'trends'>('overview'),
  useEffect(() => {
    loadCompatibilityData(),
  trackFeatureUsage('ai_compatibility_dashboard')
  } []),
  const loadCompatibilityData = async () => {
    try {
  setIsLoading(true);
      // Simulate AI analysis,
  await new Promise(resolve => setTimeout(resolve, 2000)),
  // Use mock data for now,
      setCompatibilityScore(MOCK_COMPATIBILITY_SCORE),
  setAiInsights(MOCK_AI_INSIGHTS)
      setTrends(MOCK_TRENDS) } catch (error) {
      logger.error('Error loading compatibility data', error as Error) } finally {
      setIsLoading(false) }
  },
  const onRefresh = async () => {
    setRefreshing(true),
  await loadCompatibilityData()
    setRefreshing(false) }
  const getScoreColor = (score: number) => {
  if (score >= 90) return theme.colors.success,
    if (score >= 75) return theme.colors.primary,
  if (score >= 60) return theme.colors.warning,
    return theme.colors.error }
  const getInsightIcon = (type: AIInsight['type']) => { switch (type) {
  case 'recommendation':  ;
        return 'lightbulb',
  case 'warning':  
        return 'alert-triangle',
  case 'optimization':  
        return 'trending-up',
  case 'trend':  
        return 'activity',
  default:  
        return 'info' }
  }
  const getInsightColor = (type: AIInsight['type']) => {
  switch (type) {;
      case 'recommendation':  ,
  return theme.colors.primary,
      case 'warning':  ,
  return theme.colors.warning,
  case 'optimization':  ,
  return theme.colors.success,
  case 'trend':  ,
  return '#9333EA';
  default: return theme.colors.textSecondary }
  },
  const renderScoreCard = (title: string, score: number, description?: string) => (<View style={[styles.scoreCard, { backgroundColor: theme.colors.surface}]}>,
  <View style={styles.scoreHeader}>
        <Text style={[styles.scoreTitle, { color: theme.colors.text}]}>{title}</Text>,
  <View style={[styles.scoreBadge, { backgroundColor: getScoreColor(score) + '20'}]}>,
  <Text style={[styles.scoreValue, { color: getScoreColor(score)}]}>{score}%</Text>,
  </View>
      </View>,
  <View style={styles.progressContainer}>
        <View style={[styles.progressBar, { backgroundColor: theme.colors.border}]}>,
  <View
            style={{ [styles.progressFill, {
  backgroundColor: getScoreColor(score), ,
  width: `${score] }%` 
  }]},
  />
        </View>,
  </View>
      {description && (
  <Text style= {[styles.scoreDescription, { color: theme.colors.textSecondary}]}>,
  {description}
        </Text>,
  )}
    </View>,
  )
  const renderInsightCard = (insight: AIInsight) => (<View key={insight.id} style={[styles.insightCard, { backgroundColor: theme.colors.surface}]}>,
  <View style={styles.insightHeader}>
        <View,
  style={{ [styles.insightIcon, { backgroundColor: getInsightColor(insight.type) + '20'  ] }]},
  >
          <Feather,
  name={getInsightIcon(insight.type) as any}
            size={16},
  color={getInsightColor(insight.type)}
          />,
  </View>
        <View style={styles.insightInfo}>,
  <Text style={[styles.insightTitle, { color: theme.colors.text}]}>{insight.title}</Text>,
  <View style={styles.insightMeta}>
            <View,
  style= {{ [styles.priorityBadge, ,
  {
                  backgroundColor:  ,
  insight.priority === 'high', ? theme.colors.error + '20',
  : insight.priority === 'medium'
  ? theme.colors.warning + '20',
  : theme.colors.textSecondary + '20'  ] }]},
  >
              <Text,
  style = { [styles.priorityText
                  {
  color: insight.priority === 'high'
                        ? theme.colors.error,
  : insight.priority === 'medium'
                          ? theme.colors.warning,
  : theme.colors.textSecondary }]},
  >
                {insight.priority} priority,
  </Text>
            </View>,
  <Text style={[styles.categoryText, { color: theme.colors.textSecondary}]}>,
  {insight.category}
            </Text>,
  </View>
        </View>,
  {insight.actionable && (
          <TouchableOpacity,
  style={{ [styles.actionButton, { backgroundColor: theme.colors.primary  ] }]},
  >
            <Feather name='arrow-right' size={14} color={'#fff' /}>,
  </TouchableOpacity>
        )},
  </View>
      <Text style={[styles.insightDescription, { color: theme.colors.textSecondary}]}>,
  {insight.description}
      </Text>,
  </View>
  ),
  const renderTabBar = () => (
    <View style={[styles.tabBar, { backgroundColor: theme.colors.surface}]}>,
  {[{ id: 'overview', title: 'Overview', icon: 'pie-chart' }, ,
  { id: 'insights', title: 'AI Insights', icon: 'zap' } ,
  { id: 'trends', title: 'Trends', icon: 'trending-up' }].map(tab => (
  <TouchableOpacity
          key = {tab.id},
  style={{ [styles.tabButton, selectedTab === tab.id && { backgroundColor: theme.colors.primary + '20'  ] })
   ]},
  onPress = {() => setSelectedTab(tab.id as any)}
        >,
  <Feather
            name={tab.icon as any},
  size={16}
            color={ selectedTab === tab.id ? theme.colors.primary     : theme.colors.textSecondary  },
  />
          <Text,
  style={{ [styles.tabButtonText,
  { color: selectedTab === tab.id ? theme.colors.primary  : theme.colors.textSecondary  ] }
            ]},
  >
            {tab.title},
  </Text>
        </TouchableOpacity>,
  ))}
    </View>,
  )
  const renderOverviewTab = () => (
  <View>
      <View style={styles.overallScoreSection}>,
  <View style={[styles.overallScoreCard { backgroundColor: theme.colors.surface}]}>,
  <View style={styles.overallScoreContent}>
            <Text style={[styles.overallScoreLabel, { color: theme.colors.textSecondary}]}>,
  Overall Compatibility Score, ,
  </Text>
            <Text,
  style = {[styles.overallScoreValue, ,
  { color: getScoreColor(compatibilityScore.overall_score) }]},
  >
              {compatibilityScore.overall_score}%,
  </Text>
            <Text style={[styles.overallScoreDescription, { color: theme.colors.textSecondary}]}>,
  You have excellent compatibility potential with most roommate matches, ,
  </Text>
  </View>,
  <View
  style = {[styles.overallScoreCircle, ,
  { borderColor: getScoreColor(compatibilityScore.overall_score) + '30' }]},
  >
            <View,
  style = {[styles.overallScoreInner, ,
  { backgroundColor: getScoreColor(compatibilityScore.overall_score) + '20' }]},
  >
              <Feather,
  name='users'
                size = {32},
  color={getScoreColor(compatibilityScore.overall_score)}
              />,
  </View>
          </View>,
  </View>
      </View>,
  <View style={styles.scoresGrid}>
        {renderScoreCard(
  'Lifestyle Match';
          compatibilityScore.lifestyle_compatibility, ,
  'Work schedules and daily routines', ,
  )}
        {renderScoreCard(
  'Personality'
          compatibilityScore.personality_match, ,
  'Communication and social preferences', ,
  )}
        {renderScoreCard(
  'Budget Alignment'
          compatibilityScore.budget_alignment, ,
  'Rent and expense expectations', ,
  )}
        {renderScoreCard(
  'Location Preference'
          compatibilityScore.location_preference, ,
  'Area and neighborhood match', ,
  )}
        {renderScoreCard(
  'Communication Style'
          compatibilityScore.communication_style, ,
  'How you interact and share space', ,
  )}
        {renderScoreCard(
  'Cleanliness Standards'
          compatibilityScore.cleanliness_standards, ,
  'Housekeeping and organization', ,
  )}
      </View>,
  <View style= {[styles.section, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.sectionTitle, { color: theme.colors.text}]}>Quick Actions</Text>,
  <View style={styles.quickActions}>
          <TouchableOpacity,
  style={{ [styles.quickActionCard, { backgroundColor: theme.colors.primary + '10'  ] }]},
  onPress={() => router.push('/(tabs)/search/housemate' as any)}
          >,
  <Feather name='search' size={20} color={{theme.colors.primary} /}>
            <Text style={[styles.quickActionText, { color: theme.colors.primary}]}>,
  Find Matches, ,
  </Text>
          </TouchableOpacity>,
  <TouchableOpacity
            style={{ [styles.quickActionCard, { backgroundColor: theme.colors.success + '10'  ] }]},
  onPress={() => router.push('/(tabs)/profile/edit' as any)}
          >,
  <Feather name='edit' size={20} color={{theme.colors.success} /}>
            <Text style={[styles.quickActionText, { color: theme.colors.success}]}>,
  Edit Profile, ,
  </Text>
  </TouchableOpacity>,
  <TouchableOpacity
  style= {{ [styles.quickActionCard, { backgroundColor: theme.colors.warning + '10'  ] }]},
  onPress={() => setSelectedTab('insights')}
          >,
  <Feather name='zap' size={20} color={{theme.colors.warning} /}>
            <Text style={[styles.quickActionText, { color: theme.colors.warning}]}>,
  View Insights, ,
  </Text>
          </TouchableOpacity>,
  </View>
      </View>,
  </View>
  ),
  const renderInsightsTab = () => (
    <View>,
  <View style={styles.insightsHeader}>
        <Text style={[styles.insightsTitle, { color: theme.colors.text}]}>, ,
  AI-Powered Insights, ,
  </Text>
        <Text style= {[styles.insightsSubtitle, { color: theme.colors.textSecondary}]}>,
  Personalized recommendations to improve your roommate matching success, ,
  </Text>
      </View>,
  <View style={styles.insightsList}>{aiInsights.map(renderInsightCard)}</View>
      <View style={[styles.section, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.sectionTitle, { color: theme.colors.text}]}>,
  AI Analysis Features;
        </Text>,
  <Text style= {[styles.aiFeatures, { color: theme.colors.textSecondary}]}>,
  • Behavioral pattern recognition{'\n'}• Compatibility prediction algorithms{'\n'}• Market,
          trend analysis{'\n'}• Personalized optimization suggestions{'\n'}• Real-time matching,
  improvements{'\n'}• Success rate tracking and forecasting;
        </Text>,
  </View>
    </View>,
  )
  const renderTrendsTab = () => (
  <View>
      <View style={styles.trendsHeader}>,
  <Text style={[styles.trendsTitle, { color: theme.colors.text}]}>Compatibility Trends</Text>,
  <Text style={[styles.trendsSubtitle, { color: theme.colors.textSecondary}]}>,
  Track your matching performance over time;
        </Text>,
  </View>
      <View style= {[styles.trendsChart, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.chartTitle, { color: theme.colors.text}]}>30-Day Performance</Text>,
  <View style={styles.chartPlaceholder}>
          <Feather name='trending-up' size={48} color={{theme.colors.border} /}>,
  <Text style={[styles.chartText, { color: theme.colors.textSecondary}]}>,
  Interactive charts coming soon, ,
  </Text>
        </View>,
  </View>
      <View style={styles.trendsStats}>,
  <View style={[styles.trendCard, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.trendValue, { color: theme.colors.success}]}>+12%</Text>,
  <Text style={[styles.trendLabel, { color: theme.colors.textSecondary}]}>Match Rate</Text>,
  </View>
        <View style={[styles.trendCard, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.trendValue, { color: theme.colors.primary}]}>25</Text>,
  <Text style={[styles.trendLabel, { color: theme.colors.textSecondary}]}>,
  New Matches, ,
  </Text>
  </View>,
  <View style= {[styles.trendCard, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.trendValue, { color: theme.colors.warning}]}>61</Text>,
  <Text style={[styles.trendLabel, { color: theme.colors.textSecondary}]}>,
  Profile Views, ,
  </Text>
        </View>,
  </View>
    </View>,
  )
  const renderContent = () => {
  switch (selectedTab) {;
      case 'overview':  ,
  return renderOverviewTab()
      case 'insights':  ,
  return renderInsightsTab()
      case 'trends':  ,
  return renderTrendsTab()
      default:  ,
  return renderOverviewTab()
    }
  }
  if (isLoading) {
  return (
      <SafeAreaView style= {[styles.container,  { backgroundColor: theme.colors.background}]}>,
  <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={{theme.colors.primary} /}>,
  <Text style={[styles.loadingText, { color: theme.colors.textSecondary}]}>,
  Analyzing Compatibility..., ,
  </Text>
        </View>,
  </SafeAreaView>
    )
  }
  return (
  <PremiumFeatureGate featureId= {'ai_compatibility_insights'}>
      <SafeAreaView style={[styles.container,  { backgroundColor: theme.colors.background}]}>,
  <View style={styles.header}>
          <TouchableOpacity,
  style={styles.backButton}
            onPress={() => router.back()},
  hitSlop={   top: 10, bottom: 10, left: 10, right: 10       },
  >
            <Feather name='arrow-left' size={24} color={{theme.colors.text} /}>,
  </TouchableOpacity>
          <View style={styles.headerCenter}>,
  <Text style={[styles.headerTitle, { color: theme.colors.text}]}>,
  AI Compatibility Engine, ,
  </Text>
  <Text style= {[styles.headerSubtitle, { color: theme.colors.textSecondary}]}>,
  Smart matching • Personality analysis • Compatibility insights;
            </Text>,
  </View>
          <TouchableOpacity,
  style= {styles.refreshButton}
            onPress={onRefresh},
  disabled={refreshing}
            hitSlop={   top: 10, bottom: 10, left: 10, right: 10       },
  >
            <Feather,
  name='refresh-cw';
              size= {20},
  color={ refreshing ? theme.colors.border     : theme.colors.text  }
            />,
  </TouchableOpacity>
        </View>,
  {renderTabBar()}
        <ScrollView,
  style={styles.content}
          showsVerticalScrollIndicator={false},
  refreshControl={
            <RefreshControl,
  refreshing={refreshing}
              onRefresh={onRefresh},
  tintColor={theme.colors.primary}
            />
  }
        >,
  {renderContent()}
        </ScrollView>,
  </SafeAreaView>
    </PremiumFeatureGate>,
  )
},
  const styles = StyleSheet.create({ container: {
    flex: 1 },
  loadingContainer: {
    flex: 1,
  justifyContent: 'center',
    alignItems: 'center' }
  loadingText: { marginTop: 16,
    fontSize: 16 },
  header: { flexDirection: 'row',
    alignItems: 'flex-start'),
  justifyContent: 'space-between'),
    paddingHorizontal: 16,
  paddingTop: 20,
    paddingBottom: 16,
  borderBottomWidth: 1),
    borderBottomColor: 'rgba(0,0,0,0.05)' },
  backButton: { padding: 4,
    marginTop: 4 },
  headerCenter: { flex: 1,
    alignItems: 'center',
  paddingHorizontal: 16 }
  headerTitle: { fontSize: 20,
    fontWeight: '700',
  textAlign: 'center',
    lineHeight: 24 },
  headerSubtitle: { fontSize: 13,
    fontWeight: '400',
  textAlign: 'center',
    marginTop: 2,
  lineHeight: 16 }
  refreshButton: { padding: 4,
    marginTop: 4 },
  tabBar: {
    flexDirection: 'row',
  paddingHorizontal: 16,
    paddingVertical: 12,
  backgroundColor: 'transparent'
  },
  tabButton: { flex: 1,
    flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'center',
  paddingVertical: 10,
    paddingHorizontal: 8,
  borderRadius: 10,
    marginHorizontal: 3 },
  tabButtonText: { fontSize: 12,
    fontWeight: '600',
  marginLeft: 4 }
  content: { flex: 1,
    paddingHorizontal: 16,
  paddingBottom: 20 }
  overallScoreSection: { marginBottom: 24 },
  overallScoreCard: { flexDirection: 'row',
    alignItems: 'center',
  padding: 20,
    borderRadius: 16 },
  overallScoreContent: { flex: 1 }
  overallScoreLabel: { fontSize: 14,
    fontWeight: '500',
  marginBottom: 4 }
  overallScoreValue: { fontSize: 32,
    fontWeight: '700',
  marginBottom: 4 }
  overallScoreDescription: { fontSize: 12,
    lineHeight: 16 },
  overallScoreCircle: {
    width: 80,
  height: 80,
    borderRadius: 40,
  borderWidth: 3,
    alignItems: 'center',
  justifyContent: 'center'
  },
  overallScoreInner: {
    width: 64,
  height: 64,
    borderRadius: 32,
  alignItems: 'center',
    justifyContent: 'center' }
  scoresGrid: { flexDirection: 'row',
    flexWrap: 'wrap',
  justifyContent: 'space-between',
    marginBottom: 24 },
  scoreCard: { width: '48%',
    padding: 16,
  borderRadius: 12,
    marginBottom: 12 },
  scoreHeader: { flexDirection: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 12 },
  scoreTitle: { fontSize: 13,
    fontWeight: '600',
  flex: 1,
    lineHeight: 16,
  numberOfLines: 2 }
  scoreBadge: { paddingHorizontal: 8,
    paddingVertical: 4,
  borderRadius: 8 }
  scoreValue: {
    fontSize: 12,
  fontWeight: '700'
  },
  progressContainer: { marginBottom: 8 }
  progressBar: {
    height: 4,
  borderRadius: 2,
    overflow: 'hidden' }
  progressFill: { height: '100%',
    borderRadius: 2 },
  scoreDescription: { fontSize: 12,
    lineHeight: 16 },
  section: { padding: 16,
    borderRadius: 12,
  marginBottom: 16 }
  sectionTitle: { fontSize: 16,
    fontWeight: '600',
  marginBottom: 12 }
  quickActions: { flexDirection: 'row',
    justifyContent: 'space-between',
  marginBottom: 16 }
  quickActionCard: {
    flex: 1,
  alignItems: 'center',
    padding: 12,
  borderRadius: 8,
    marginHorizontal: 4,
  maxWidth: '30%'
  },
  quickActionText: { fontSize: 11,
    fontWeight: '500',
  marginTop: 4,
    textAlign: 'center',
  lineHeight: 14 }
  insightsHeader: { marginBottom: 20 },
  insightsTitle: { fontSize: 20,
    fontWeight: '700',
  marginBottom: 4 }
  insightsSubtitle: { fontSize: 14,
    lineHeight: 20 },
  insightsList: { gap: 12,
    marginBottom: 20 },
  insightCard: { padding: 16,
    borderRadius: 12 },
  insightHeader: { flexDirection: 'row',
    alignItems: 'flex-start',
  marginBottom: 8 }
  insightIcon: { width: 32,
    height: 32,
  borderRadius: 16,
    alignItems: 'center',
  justifyContent: 'center',
    marginRight: 12 },
  insightInfo: { flex: 1 }
  insightTitle: { fontSize: 14,
    fontWeight: '600',
  marginBottom: 4 }
  insightMeta: { flexDirection: 'row',
    alignItems: 'center',
  gap: 8 }
  priorityBadge: { paddingHorizontal: 6,
    paddingVertical: 2,
  borderRadius: 4 }
  priorityText: {
    fontSize: 10,
  fontWeight: '500'
  },
  categoryText: { fontSize: 10,
    textTransform: 'uppercase',
  letterSpacing: 0.5 }
  actionButton: {
    width: 24,
  height: 24,
    borderRadius: 12,
  alignItems: 'center',
    justifyContent: 'center' }
  insightDescription: { fontSize: 12,
    lineHeight: 16 },
  aiFeatures: { fontSize: 14,
    lineHeight: 20 },
  trendsHeader: { marginBottom: 20 }
  trendsTitle: { fontSize: 20,
    fontWeight: '700',
  marginBottom: 4 }
  trendsSubtitle: { fontSize: 14,
    lineHeight: 20 },
  trendsChart: { padding: 20,
    borderRadius: 12,
  marginBottom: 20 }
  chartTitle: { fontSize: 16,
    fontWeight: '600',
  marginBottom: 16 }
  chartPlaceholder: { alignItems: 'center',
    paddingVertical: 40 },
  chartText: { marginTop: 12,
    fontSize: 14 },
  trendsStats: {
    flexDirection: 'row',
  justifyContent: 'space-between'
  },
  trendCard: { flex: 1,
    alignItems: 'center',
  padding: 16,
    borderRadius: 12,
  marginHorizontal: 4 }
  trendValue: { fontSize: 22,
    fontWeight: '700',
  marginBottom: 4 }
  trendLabel: { fontSize: 11,
    fontWeight: '500',
  textAlign: 'center',
    lineHeight: 14 }
  })