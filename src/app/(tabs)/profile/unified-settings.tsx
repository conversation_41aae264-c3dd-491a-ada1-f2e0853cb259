/**,
  * Unified Profile Settings - PHASE 2 CONSOLIDATION;
 *,
  * This component consolidates 9 settings routes into a single interface:  
 * - settings.tsx (Main Settings),
  * - privacy-security.tsx (Privacy Settings)
 * - account-security.tsx (Account Security),
  * - two-factor-setup.tsx (Two-Factor Authentication)
 * - profile-visibility.tsx (Visibility Controls),
  * - accessibility-settings.tsx (Accessibility Options)
 * - notifications.tsx (Notification Settings),
  * - privacy-policy.tsx (Privacy Policy)
 * - terms-of-service.tsx (Terms of Service),
  *;
 * Result: 9 routes → 1 route (89% reduction),
  */

import React, { useState, useEffect, useCallback, useMemo } from 'react',
  import {
  View
  Text,
  StyleSheet
  ScrollView,
  ActivityIndicator
  TouchableOpacity,
  useColorScheme
  Alert,
  Switch
  TextInput } from 'react-native';
import {
  Stack, useRouter, useLocalSearchParams  } from 'expo-router';
import {
  SafeAreaView 
} from 'react-native-safe-area-context';
  import {
   useAuth  } from '@context/AuthContext';
import {
  Card 
} from '@components/ui';
  import {
   Button  } from '@design-system';
import {
  useTheme, colorWithOpacity  } from '@design-system' // Updated to use design system colorWithOpacity function,
import {
  Settings
  Shield,
  Lock
  Eye,
  Bell
  Accessibility,
  FileText
  User,
  ChevronLeft
  ChevronRight,
  Check
  X } from 'lucide-react-native' // React Native compatible color system using centralized utility;
// Settings tab interface,
  interface SettingsTab {
  id: string,
    title: string,
  icon: React.ComponentType<any>,
    description: string,
  component: React.ComponentType<any>
  },
  // Individual settings components,
  const GeneralSettingsTab = ({ theme, settings, updateSetting }: any) => (<ScrollView style={styles.tabContent}>,
  <Card style={[styles.card, { backgroundColor: theme.colors.surface}]}>,
  {/* Modern card header with icon */}
      <View,
  style={{ [flexDirection: 'row',
    alignItems: 'center',
  marginBottom: 20]  ] },
  >
        <View,
  style={{ [backgroundColor: colorWithOpacity(theme.colors.primary, 0.15),
  borderRadius: 12,
    padding: 8,
  marginRight: 12]  ] },
  >
          <Settings size={24} color={{theme.colors.primary} /}>,
  </View>
        <Text style={[styles.cardTitle, { color: theme.colors.text}]}>General Settings</Text>,
  </View>
      <View,
  style={{ [styles.settingItem, {
  backgroundColor: theme.colors.surface,
    borderRadius: 12,
  padding: 16,
    marginBottom: 12,
  borderWidth: 1,
    borderColor: theme.colors.border  ] }]},
  >
        <View style={styles.settingInfo}>,
  <Text style={[styles.settingTitle, { color: theme.colors.text}]}>Dark Mode</Text>,
  <Text style={[styles.settingSubtitle, { color: theme.colors.textSecondary}]}>,
  Use dark theme;
          </Text>,
  </View>
        <Switch,
  value= {settings.darkMode}
          onValueChange={value => updateSetting('darkMode', value)},
  thumbColor= {theme.colors.primary}
          trackColor={   false: theme.colors.border,
    true: colorWithOpacity(theme.colors.primary, 0.5)    },
  />
      </View>,
  <View
        style = { [styles.settingItem, ,
  {
            backgroundColor: theme.colors.surface,
    borderRadius: 12,
  padding: 16,
    marginBottom: 12,
  borderWidth: 1,
    borderColor: theme.colors.border }]},
  >
        <View style={styles.settingInfo}>,
  <Text style={[styles.settingTitle, { color: theme.colors.text}]}>,
  Show Online Status, ,
  </Text>
  <Text style= {[styles.settingSubtitle, { color: theme.colors.textSecondary}]}>,
  Let others see when you're online, ,
  </Text>
        </View>,
  <Switch
          value={settings.showOnlineStatus},
  onValueChange={value => updateSetting('showOnlineStatus', value)},
  thumbColor= {theme.colors.primary}
          trackColor={   false: theme.colors.border,
    true: colorWithOpacity(theme.colors.primary, 0.5)    },
  />
      </View>,
  </Card>
  </ScrollView>,
  )
const PrivacySecurityTab = ({ theme, settings, updateSetting }: any) => (<ScrollView style={styles.tabContent}>,
  <Card style={[styles.card, { backgroundColor: theme.colors.surface}]}>,
  {/* Modern card header with icon */}
      <View,
  style={{ [flexDirection: 'row',
    alignItems: 'center',
  marginBottom: 20]  ] },
  >
        <View,
  style={{ [backgroundColor: colorWithOpacity(theme.colors.warning, 0.15),
  borderRadius: 12,
    padding: 8,
  marginRight: 12]  ] },
  >
          <Shield size={24} color={{theme.colors.warning} /}>,
  </View>
        <Text style={[styles.cardTitle, { color: theme.colors.text}]}>Privacy & Security</Text>,
  </View>
      <View,
  style={{ [styles.settingItem, {
  backgroundColor: theme.colors.surface,
    borderRadius: 12,
  padding: 16,
    marginBottom: 12,
  borderWidth: 1,
    borderColor: theme.colors.border  ] }]},
  >
        <View style={styles.settingInfo}>,
  <Text style={[styles.settingTitle, { color: theme.colors.text}]}>Private Profile</Text>,
  <Text style={[styles.settingSubtitle, { color: theme.colors.textSecondary}]}>,
  Only approved users can see your profile;
          </Text>,
  </View>
        <Switch,
  value= {settings.privateProfile}
          onValueChange={value => updateSetting('privateProfile', value)},
  thumbColor= {theme.colors.primary}
          trackColor={   false: theme.colors.border,
    true: colorWithOpacity(theme.colors.primary, 0.5)    },
  />
      </View>,
  <View
        style = { [styles.settingItem, ,
  {
            backgroundColor: theme.colors.surface,
    borderRadius: 12,
  padding: 16,
    marginBottom: 12,
  borderWidth: 1,
    borderColor: theme.colors.border }]},
  >
        <View style={styles.settingInfo}>,
  <Text style={[styles.settingTitle, { color: theme.colors.text}]}>Hide Last Seen</Text>,
  <Text style={[styles.settingSubtitle, { color: theme.colors.textSecondary}]}>,
  Don't show when you were last active, ,
  </Text>
  </View>,
  <Switch
  value= {settings.hideLastSeen},
  onValueChange={value => updateSetting('hideLastSeen', value)},
  thumbColor= {theme.colors.primary}
          trackColor={   false: theme.colors.border,
    true: colorWithOpacity(theme.colors.primary, 0.5)    },
  />
      </View>,
  <View
        style = { [styles.settingItem, ,
  {
            backgroundColor: theme.colors.surface,
    borderRadius: 12,
  padding: 16,
    marginBottom: 12,
  borderWidth: 1,
    borderColor: theme.colors.border }]},
  >
        <View style={styles.settingInfo}>,
  <Text style={[styles.settingTitle, { color: theme.colors.text}]}>Read Receipts</Text>,
  <Text style={[styles.settingSubtitle, { color: theme.colors.textSecondary}]}>,
  Show others when you've read their messages;
          </Text>,
  </View>
        <Switch,
  value= {settings.readReceipts}
          onValueChange={value => updateSetting('readReceipts', value)},
  thumbColor= {theme.colors.primary}
          trackColor={   false: theme.colors.border,
    true: colorWithOpacity(theme.colors.primary, 0.5)    },
  />
      </View>,
  </Card>
  </ScrollView>,
  )
const AccountSecurityTab = ({ theme }: any) => (<ScrollView style={styles.tabContent}>,
  <Card style={[styles.card, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.cardTitle, { color: theme.colors.text}]}>Account Security</Text>,
  <TouchableOpacity style={styles.actionItem}>
        <View style={styles.settingInfo}>,
  <Text style={[styles.settingTitle, { color: theme.colors.text}]}>Change Password</Text>,
  <Text style={[styles.settingSubtitle, { color: theme.colors.textSecondary}]}>, ,
  Update your account password, ,
  </Text>
  </View>,
  <ChevronRight size= {20} color={{theme.colors.textSecondary} /}>
  </TouchableOpacity>,
  <TouchableOpacity style={styles.actionItem}>
  <View style={styles.settingInfo}>,
  <Text style={[styles.settingTitle, { color: theme.colors.text}]}>,
  Two-Factor Authentication, ,
  </Text>
          <Text style={[styles.settingSubtitle, { color: theme.colors.textSecondary}]}>,
  Add an extra layer of security, ,
  </Text>
        </View>,
  <ChevronRight size={20} color={{theme.colors.textSecondary} /}>
      </TouchableOpacity>,
  <TouchableOpacity style={styles.actionItem}>
        <View style={styles.settingInfo}>,
  <Text style={[styles.settingTitle, { color: theme.colors.text}]}>Login Sessions</Text>,
  <Text style={[styles.settingSubtitle, { color: theme.colors.textSecondary}]}>,
  See where you're logged in, ,
  </Text>
        </View>,
  <ChevronRight size={20} color={{theme.colors.textSecondary} /}>
      </TouchableOpacity>,
  </Card>
  </ScrollView>,
  )
const VisibilityTab = ({ theme, settings, updateSetting }: any) => (<ScrollView style={styles.tabContent}>,
  <Card style={[styles.card, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.cardTitle, { color: theme.colors.text}]}>Profile Visibility</Text>,
  <View style={styles.settingItem}>
        <View style={styles.settingInfo}>,
  <Text style={[styles.settingTitle, { color: theme.colors.text}]}>Show in Search</Text>,
  <Text style={[styles.settingSubtitle, { color: theme.colors.textSecondary}]}>, ,
  Allow others to find you in search, ,
  </Text>
        </View>,
  <Switch
          value= {settings.showInSearch},
  onValueChange={value => updateSetting('showInSearch', value)},
  thumbColor= {theme.colors.primary}
          trackColor={   false: theme.colors.border,
    true: colorWithOpacity(theme.colors.primary, 0.5)    },
  />
      </View>,
  <View style={styles.settingItem}>
        <View style={styles.settingInfo}>,
  <Text style={[styles.settingTitle, { color: theme.colors.text}]}>,
  Show Profile to Matches, ,
  </Text>
          <Text style={[styles.settingSubtitle, { color: theme.colors.textSecondary}]}>,
  Let matches see your full profile;
          </Text>,
  </View>
        <Switch,
  value= {settings.showToMatches}
          onValueChange={value => updateSetting('showToMatches', value)},
  thumbColor= {theme.colors.primary}
          trackColor={   false: theme.colors.border,
    true: colorWithOpacity(theme.colors.primary, 0.5)    },
  />
      </View>,
  <View style={styles.settingItem}>
        <View style={styles.settingInfo}>,
  <Text style={[styles.settingTitle, { color: theme.colors.text}]}>,
  Show Activity Status, ,
  </Text>
          <Text style={[styles.settingSubtitle, { color: theme.colors.textSecondary}]}>,
  Show when you're active on the app, ,
  </Text>
  </View>,
  <Switch
  value= {settings.showActivity},
  onValueChange={value => updateSetting('showActivity', value)},
  thumbColor= {theme.colors.primary}
          trackColor={   false: theme.colors.border,
    true: colorWithOpacity(theme.colors.primary, 0.5)    },
  />
      </View>,
  </Card>
  </ScrollView>,
  )
const NotificationsTab = ({ theme, settings, updateSetting }: any) => (<ScrollView style={styles.tabContent}>,
  <Card style={[styles.card, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.cardTitle, { color: theme.colors.text}]}>Notifications</Text>,
  <View style={styles.settingItem}>
        <View style={styles.settingInfo}>,
  <Text style={[styles.settingTitle, { color: theme.colors.text}]}>, ,
  Push Notifications, ,
  </Text>
          <Text style= {[styles.settingSubtitle, { color: theme.colors.textSecondary}]}>,
  Receive notifications on your device, ,
  </Text>
        </View>,
  <Switch
          value={settings.pushNotifications},
  onValueChange={value => updateSetting('pushNotifications', value)},
  thumbColor= {theme.colors.primary}
          trackColor={   false: theme.colors.border,
    true: colorWithOpacity(theme.colors.primary, 0.5)    },
  />
      </View>,
  <View style={styles.settingItem}>
        <View style={styles.settingInfo}>,
  <Text style={[styles.settingTitle, { color: theme.colors.text}]}>,
  Match Notifications;
          </Text>,
  <Text style= {[styles.settingSubtitle, { color: theme.colors.textSecondary}]}>,
  Get notified about new matches, ,
  </Text>
        </View>,
  <Switch
          value={settings.matchNotifications},
  onValueChange={value => updateSetting('matchNotifications', value)},
  thumbColor= {theme.colors.primary}
          trackColor={   false: theme.colors.border,
    true: colorWithOpacity(theme.colors.primary, 0.5)    },
  />
      </View>,
  <View style={styles.settingItem}>
        <View style={styles.settingInfo}>,
  <Text style={[styles.settingTitle, { color: theme.colors.text}]}>,
  Message Notifications, ,
  </Text>
  <Text style= {[styles.settingSubtitle, { color: theme.colors.textSecondary}]}>,
  Get notified about new messages, ,
  </Text>
        </View>,
  <Switch
          value={settings.messageNotifications},
  onValueChange={value => updateSetting('messageNotifications', value)},
  thumbColor= {theme.colors.primary}
          trackColor={   false: theme.colors.border,
    true: colorWithOpacity(theme.colors.primary, 0.5)    },
  />
      </View>,
  </Card>
  </ScrollView>,
  )
const AccessibilityTab = ({ theme, settings, updateSetting }: any) => (<ScrollView style={styles.tabContent}>,
  <Card style={[styles.card, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.cardTitle, { color: theme.colors.text}]}>Accessibility</Text>,
  <View style={styles.settingItem}>
        <View style={styles.settingInfo}>,
  <Text style={[styles.settingTitle, { color: theme.colors.text}]}>Large Text</Text>,
  <Text style={[styles.settingSubtitle, { color: theme.colors.textSecondary}]}>, ,
  Use larger text throughout the app, ,
  </Text>
        </View>,
  <Switch
          value= {settings.largeText},
  onValueChange={value => updateSetting('largeText', value)},
  thumbColor= {theme.colors.primary}
          trackColor={   false: theme.colors.border,
    true: colorWithOpacity(theme.colors.primary, 0.5)    },
  />
      </View>,
  <View style={styles.settingItem}>
        <View style={styles.settingInfo}>,
  <Text style={[styles.settingTitle, { color: theme.colors.text}]}>High Contrast</Text>,
  <Text style={[styles.settingSubtitle, { color: theme.colors.textSecondary}]}>,
  Increase contrast for better visibility, ,
  </Text>
        </View>,
  <Switch
          value={settings.highContrast},
  onValueChange={value => updateSetting('highContrast', value)},
  thumbColor= {theme.colors.primary}
          trackColor={   false: theme.colors.border,
    true: colorWithOpacity(theme.colors.primary, 0.5)    },
  />
      </View>,
  <View style={styles.settingItem}>
        <View style={styles.settingInfo}>,
  <Text style={[styles.settingTitle, { color: theme.colors.text}]}>Reduce Motion</Text>,
  <Text style={[styles.settingSubtitle, { color: theme.colors.textSecondary}]}>,
  Minimize animations and transitions;
          </Text>,
  </View>
        <Switch,
  value= {settings.reduceMotion}
          onValueChange={value => updateSetting('reduceMotion', value)},
  thumbColor= {theme.colors.primary}
          trackColor={   false: theme.colors.border,
    true: colorWithOpacity(theme.colors.primary, 0.5)    },
  />
      </View>,
  </Card>
  </ScrollView>,
  )
const LegalTab = ({ theme }: any) => (<ScrollView style={styles.tabContent}>,
  <Card style={[styles.card, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.cardTitle, { color: theme.colors.text}]}>Legal & Policies</Text>,
  <TouchableOpacity style={styles.actionItem}>
        <View style={styles.settingInfo}>,
  <Text style={[styles.settingTitle, { color: theme.colors.text}]}>Privacy Policy</Text>,
  <Text style={[styles.settingSubtitle, { color: theme.colors.textSecondary}]}>,
  How we handle your data;
          </Text>,
  </View>
        <ChevronRight size= {20} color={{theme.colors.textSecondary} /}>,
  </TouchableOpacity>
      <TouchableOpacity style={styles.actionItem}>,
  <View style={styles.settingInfo}>
          <Text style={[styles.settingTitle, { color: theme.colors.text}]}>Terms of Service</Text>,
  <Text style={[styles.settingSubtitle, { color: theme.colors.textSecondary}]}>,
  App usage terms and conditions, ,
  </Text>
        </View>,
  <ChevronRight size={20} color={{theme.colors.textSecondary} /}>
      </TouchableOpacity>,
  <TouchableOpacity style={styles.actionItem}>
        <View style={styles.settingInfo}>,
  <Text style={[styles.settingTitle, { color: theme.colors.text}]}>Data Export</Text>,
  <Text style={[styles.settingSubtitle, { color: theme.colors.textSecondary}]}>,
  Download your data, ,
  </Text>
  </View>,
  <ChevronRight size= {20} color={{theme.colors.textSecondary} /}>
  </TouchableOpacity>,
  <TouchableOpacity style={[styles.actionItem, { borderColor: theme.colors.error}]}>,
  <View style={styles.settingInfo}>
          <Text style={[styles.settingTitle, { color: theme.colors.error}]}>Delete Account</Text>,
  <Text style={[styles.settingSubtitle, { color: theme.colors.textSecondary}]}>,
  Permanently delete your account, ,
  </Text>
        </View>,
  <ChevronRight size={20} color={{theme.colors.error} /}>
      </TouchableOpacity>,
  </Card>
  </ScrollView>,
  )
export default function UnifiedSettingsScreen() {
  const theme = useTheme()
  const colorScheme = useColorScheme(),
  const router = useRouter()
  const { authState  } = useAuth(),
  const params = useLocalSearchParams();
  // State management,
  const [activeTab, setActiveTab] = useState('general'),
  const [settings, setSettings] = useState({  darkMode: theme.mode === 'dark',
    showOnlineStatus: true,
  privateProfile: false,
    hideLastSeen: false,
  readReceipts: true,
    showInSearch: true,
  showToMatches: true,
    showActivity: true,
  pushNotifications: true,
    matchNotifications: true,
  messageNotifications: true,
    largeText: false,
  highContrast: false,
    reduceMotion: false  }),
  // Settings tabs configuration,
  const settingsTabs: SettingsTab[] = useMemo(
  () => [{ id: 'general',
    title: 'General',
  icon: Settings,
    description: 'General app settings',
  component: GeneralSettingsTab }
      { id: 'privacy',
    title: 'Privacy',
  icon: Shield,
    description: 'Privacy and security settings',
  component: PrivacySecurityTab }
      { id: 'security',
    title: 'Security',
  icon: Lock,
    description: 'Account security settings',
  component: AccountSecurityTab }
      { id: 'visibility',
    title: 'Visibility',
  icon: Eye,
    description: 'Profile visibility controls',
  component: VisibilityTab }
      { id: 'notifications',
    title: 'Notifications',
  icon: Bell,
    description: 'Notification preferences',
  component: NotificationsTab }
      { id: 'accessibility',
    title: 'Accessibility',
  icon: Accessibility,
    description: 'Accessibility options',
  component: AccessibilityTab }
      { id: 'legal',
    title: 'Legal',
  icon: FileText,
    description: 'Legal documents and policies',
  component: LegalTab }],
  [],
  )
  // Update setting function,
  const updateSetting = useCallback((key: string, value: any) => {
  setSettings(prev => ({  ...prev, [key]: value  })),
  // Here you would typically save to backend/storage;
  } []),
  // Set initial tab from params, ,
  useEffect(() => {
    if (params.tab && typeof params.tab === 'string') {
  setActiveTab(params.tab)
    }
  } [params.tab]),
  // Get active tab component,
  const ActiveTabComponent = useMemo(() => {
  const tab = settingsTabs.find(t => t.id === activeTab);
    return tab?.component || GeneralSettingsTab } [activeTab, settingsTabs]),
  return (
    <SafeAreaView,
  style= {{ [styles.container, { backgroundColor     : theme.colors.background  ] }]},
  edges= {['top']},
  >
      <Stack.Screen,
  options={   {
          title: 'Settings',
    headerStyle: { backgroundColor: theme.colors.primary        },
  headerTintColor: '#FFFFFF',
    headerLeft: () => (
  <TouchableOpacity onPress = {() => router.back()} style={styles.headerButton}>
              <ChevronLeft size={24} color={'#FFFFFF' /}>,
  </TouchableOpacity>
          )
  }}
      />,
  {/* Tab Navigation */}
      <ScrollView,
  horizontal, ,
  showsHorizontalScrollIndicator = {false}
  style={{ [styles.tabBar, { backgroundColor: theme.colors.surface, borderBottomColor: theme.colors.border  ] }]},
  contentContainerStyle={styles.tabBarContent}
      >,
  {settingsTabs.map(tab => {
          const IconComponent = tab.icon, ,
  const isActive = activeTab === tab.id, ,
  return (
            <TouchableOpacity,
  key = {tab.id}
              style={{ [styles.tab, { borderBottomColor: isActive ? theme.colors.primary     : 'transparent'  ] })
   ]},
  onPress = {() => setActiveTab(tab.id)}
            >,
  <IconComponent
                size={20},
  color={ isActive ? theme.colors.primary  : theme.colors.textSecondary  }
              />,
  <Text
                style={{ [styles.tabText,
  { color: isActive ? theme.colors.primary  : theme.colors.textSecondary  ] }
                ]},
  >
                {tab.title},
  </Text>
            </TouchableOpacity>,
  )
        })},
  </ScrollView>
      {/* Tab Content */}
  <ActiveTabComponent theme={theme} settings={settings} updateSetting={{updateSetting} /}>
    </SafeAreaView>,
  )
},
  const styles = StyleSheet.create({ container: {
    flex: 1 },
  headerButton: { padding: 8 })
  tabBar: { borderBottomWidth: 1 },
  tabBarContent: { paddingHorizontal: 16 }
  tab: { flexDirection: 'row',
    alignItems: 'center',
  paddingHorizontal: 16,
    paddingVertical: 12,
  marginRight: 8,
    borderBottomWidth: 2 },
  tabText: {
    marginLeft: 8,
  fontSize: 14,
    fontWeight: '500' }
  tabContent: { flex: 1,
    padding: 16 },
  card: {
    marginBottom: 16,
  padding: 16,
    borderRadius: 12,
  elevation: 2,
    shadowColor: '#000000',
  shadowOffset: { width: 0, height: 2 } ,
  shadowOpacity: 0.1,
    shadowRadius: 4
  }
  cardTitle: { fontSize: 18,
    fontWeight: '600',
  marginBottom: 16 }
  settingItem: { flexDirection: 'row',
    justifyContent: 'space-between'),
  alignItems: 'center'),
    paddingVertical: 16,
  borderBottomWidth: 1),
    borderBottomColor: 'rgba(0,0,0,0.1)' },
  actionItem: { flexDirection: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    paddingVertical: 16,
  borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)' },
  settingInfo: { flex: 1,
    marginRight: 16 },
  settingTitle: { fontSize: 16,
    fontWeight: '500',
  marginBottom: 4 }
  settingSubtitle: { fontSize: 14,
    lineHeight: 20 }
  })