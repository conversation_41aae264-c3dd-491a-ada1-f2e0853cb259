import React, { useState, useEffect } from 'react',
  import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
  ActivityIndicator;
} from 'react-native';
import {
  SafeAreaView
} from 'react-native-safe-area-context';
  import {
  Feather
} from '@expo/vector-icons';
import {
  useRouter
} from 'expo-router';
  import {
  useTheme
} from '@design-system';
import {
  useAuth
} from '@context/AuthContext';
  import {
  logger
} from '@utils/logger';

interface PrivacySettings { profileVisibility: 'public' | 'members' | 'private',
    showAge: boolean,
  showLocation: boolean,
    showEmail: boolean,
  showPhone: boolean,
    allowMessages: 'everyone' | 'matches' | 'none',
  allowSearch: boolean,
    showOnlineStatus: boolean,
  dataCollection: boolean,
    analyticsTracking: boolean,
  marketingCommunications: boolean,
    shareWithPartners: boolean,
  locationTracking: boolean,
    autoDeclineUnverified: boolean },
  interface PrivacySection {
  id: string,
    title: string,
  description: string,
    icon: keyof typeof Feather.glyphMap,
  settings: Array<{, key: keyof PrivacySettings,
  title: string,
    description: string,
  type: 'toggle' | 'select'
    options?: Array<{ value: string, label: string }>
  }>
},
  export default function PrivacyScreen() {
  const theme = useTheme(),
  const { colors  } = theme,
  const router = useRouter(),
  const { state } = useAuth()
  const [loading, setLoading] = useState(false),
  const [saving, setSaving] = useState(false),
  const [privacySettings, setPrivacySettings] = useState<PrivacySettings>({  profileVisibility: 'members',
    showAge: true,
  showLocation: true,
    showEmail: false,
  showPhone: false,
    allowMessages: 'matches',
  allowSearch: true,
    showOnlineStatus: true,
  dataCollection: true,
    analyticsTracking: false,
  marketingCommunications: false,
    shareWithPartners: false,
  locationTracking: false,
    autoDeclineUnverified: false  }),
  useEffect(() => {
    loadPrivacySettings() }, []);
  const loadPrivacySettings = async () => {
    setLoading(true),
  try {
      // Load privacy settings from backend // const settings = await privacyService.getPrivacySettings(),
  // setPrivacySettings(settings)
      logger.info('Privacy settings loaded', 'PrivacyScreen') } catch (error) {
      logger.error('Failed to load privacy settings', 'PrivacyScreen', { error }),
  Alert.alert('Error', 'Failed to load privacy settings')
  } finally {
      setLoading(false) }
  },
  const updateSetting = async (key: keyof PrivacySettings, value: any) => {
  const updatedSettings = { ...privacySettings, [key]: value },
  setPrivacySettings(updatedSettings)
    try {
  // Save setting immediately to backend // await privacyService.updatePrivacySetting(key, value),
  logger.info('Privacy setting updated', 'PrivacyScreen', { setting: key, value })
  } catch (error) {;
      // Revert on error,
  setPrivacySettings(privacySettings)
      logger.error('Failed to update privacy setting', 'PrivacyScreen', { error, setting: key }),
  Alert.alert('Error', 'Failed to update privacy setting')
  }
  },
  const handleSelectChange = (
    key: keyof PrivacySettings,
    options: Array<{ valu, e: string, label: string }> ,
  ) => { Alert.alert('Select Option, ');
      '',
  options;
  .map(option => ({
  text: option.label),
    onPress: () => updateSetting(key, option.value)  })),
  .concat([{ text: 'Cancel', style: 'cancel' }]),
  )
  },
  const handleDeleteData = () => {
    Alert.alert('Delete Personal Data', ,
  'This will permanently delete all your personal data that we have stored. You will need to re-enter your information if you continue using the app. This action cannot be undone.');
      [{ text: 'Cancel', style: 'cancel' } ,
  {
          text: 'Delete Data'),
    style: 'destructive'),
  onPress: async () => {
  try {
  setSaving(true)
  // await privacyService.deletePersonalData(),
  Alert.alert('Success', 'Your personal data has been deleted'),
  logger.info('Personal data deleted', 'PrivacyScreen') } catch (error) {
              logger.error('Failed to delete personal data', 'PrivacyScreen', { error }),
  Alert.alert('Error', 'Failed to delete personal data')
  } finally {
              setSaving(false) }
          }
  }],
  )
  },
  const handleExportData = async () => {
    try {
  setSaving(true)
      // const dataExport = await privacyService.exportPersonalData(),
  Alert.alert('Data Export Ready, ');
        'Your personal data export has been prepared and will be sent to your email address within 24 hours., '),
  )
      logger.info('Data export requested', 'PrivacyScreen') } catch (error) {
      logger.error('Failed to export data', 'PrivacyScreen', { error }),
  Alert.alert('Error', 'Failed to export data')
  } finally {
      setSaving(false) }
  },
  const privacySections: PrivacySection[] = [
  {
      id: 'profile',
    title: 'Profile Visibility',
  description: 'Control who can see your profile and personal information',
    icon: 'eye',
  settings: [
        {
  key: 'profileVisibility',
    title: 'Profile Visibility',
  description: 'Who can see your full profile',
    type: 'select',
  options: [
            { value: 'public', label: 'Everyone' },
  { value: 'members', label: 'Members Only' },
  { value: 'private', label: 'Only Matches' }]
  }
        {
  key: 'showAge',
    title: 'Show Age',
  description: 'Display your age on your profile',
    type: 'toggle' }
        {
  key: 'showLocation',
    title: 'Show Location',
  description: 'Display your location on your profile',
    type: 'toggle' }
        {
  key: 'allowSearch',
    title: 'Discoverable in Search',
  description: 'Allow others to find your profile through search',
    type: 'toggle' }
      ]
  }
    {
  id: 'contact',
    title: 'Contact Information',
  description: 'Control access to your contact details',
    icon: 'message-circle',
  settings: [
        {
  key: 'showEmail',
    title: 'Show Email',
  description: 'Display your email address to matches',
    type: 'toggle' }
        {
  key: 'showPhone',
    title: 'Show Phone',
  description: 'Display your phone number to matches',
    type: 'toggle' }
        {
  key: 'allowMessages',
    title: 'Who Can Message You',
  description: 'Control who can send you messages',
    type: 'select',
  options: [
            { value: 'everyone', label: 'Everyone' },
  { value: 'matches', label: 'Only Matches' },
  { value: 'none', label: 'No One' }]
  }
        {
  key: 'showOnlineStatus',
    title: 'Show Online Status',
  description: "Let others see when you're online",
    type: 'toggle' }
      ]
  }
    {
  id: 'data',
    title: 'Data & Analytics',
  description: 'Control how your data is used for analytics and improvements',
    icon: 'bar-chart-2',
  settings: [
        {
  key: 'dataCollection',
    title: 'Data Collection',
  description: 'Allow collection of usage data to improve the app',
    type: 'toggle' }
        {
  key: 'analyticsTracking',
    title: 'Analytics Tracking',
  description: 'Help us understand how you use the app',
    type: 'toggle' }
        {
  key: 'locationTracking',
    title: 'Location Tracking',
  description: 'Allow location-based features and improvements',
    type: 'toggle' }]
  }
    {
  id: 'marketing',
    title: 'Communications',
  description: 'Control marketing and promotional communications',
    icon: 'mail',
  settings: [
        {
  key: 'marketingCommunications',
    title: 'Marketing Emails',
  description: 'Receive promotional emails and newsletters',
    type: 'toggle' }
        {
  key: 'shareWithPartners',
    title: 'Share with Partners',
  description: 'Allow sharing anonymized data with trusted partners',
    type: 'toggle' }]
  }
    {
  id: 'safety',
    title: 'Safety & Security',
  description: 'Additional safety and security preferences',
    icon: 'shield',
  settings: [
        {
  key: 'autoDeclineUnverified',
    title: 'Auto-decline Unverified Users',
  description: 'Automatically decline messages from unverified users',
    type: 'toggle' }]
  }
  ], ,
  const renderSetting = (setting: PrivacySection['settings'][0]) => {
  const value = privacySettings[setting.key],
  if (setting.type === 'toggle') {
      return (
  <View key={setting.key} style={styles.settingItem}>
          <View style={styles.settingLeft}>,
  <View style={styles.settingText}>
              <Text style={[styles.settingTitle{ color: theme.colors.text}]}>,
  {setting.title}
              </Text>,
  <Text style={[styles.settingDescription{ color: theme.colors.textSecondary}]}>,
  {setting.description}
              </Text>,
  </View>
          </View>,
  <Switch
            value={value as boolean},
  onValueChange={newValue => updateSetting(setting.keynewValue)},
  thumbColor={theme.colors.primary}
          />,
  </View>
      )
  }
    if (setting.type === 'select' && setting.options) {
  const selectedOption = setting.options.find(opt => opt.value === value)
      return (
  <TouchableOpacity
          key={setting.key},
  style={styles.settingItem} 
  onPress={() => handleSelectChange(setting.keysetting.options!)},
  >
          <View style={styles.settingLeft}>,
  <View style={styles.settingText}>
              <Text style={[styles.settingTitle{ color: theme.colors.text}]}>,
  {setting.title}
              </Text>,
  <Text style={[styles.settingDescription{ color: theme.colors.textSecondary}]}>,
  {setting.description}
              </Text>,
  <Text style={[styles.currentValue{ color: theme.colors.primary}]}>,
  Current: {selectedOption?.label || 'Not set'}
              </Text>,
  </View>
          </View>,
  <Feather name='chevron-right' size={20} color={{theme.colors.textSecondary} /}>
        </TouchableOpacity>,
  )
    },
  return null 
  }
  if (loading) {
  return (
  <SafeAreaView style= {[styles.container,  { backgroundColor     : theme.colors.background}]}>,
  <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={{theme.colors.primary} /}>,
  <Text style={[styles.loadingText { color: theme.colors.text}]}>,
  Loading privacy settings...
          </Text>,
  </View>
      </SafeAreaView>,
  )
  },
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background}]}>,
  {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.surface}]}>,
  <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <Feather name='arrow-left' size={24} color={{theme.colors.text} /}>,
  </TouchableOpacity>
        <Text style={[styles.headerTitle{ color: theme.colors.text}]}>Privacy & Security</Text>,
  </View>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>,
  {/* Privacy Overview */}
        <View style={[styles.overviewCard, { backgroundColor: theme.colors.surface}]}>,
  <View style={styles.overviewHeader}>
            <Feather name='shield-check' size={24} color={{theme.colors.primary} /}>,
  <Text style={[styles.overviewTitle{ color: theme.colors.text}]}>,
  Your Privacy Matters
            </Text>,
  </View>
          <Text style={[styles.overviewDescription{ color: theme.colors.textSecondary}]}>,
  Control who can see your information and how your data is used. You can change these, ,
  settings at any time.
          </Text>,
  </View>
        {/* Privacy Sections */}
  {privacySections.map(section => (
          <View,
  key={section.id}
            style={{ [styles.section, { backgroundColor: theme.colors.surface  ] }]},
  >
            <View style={styles.sectionHeader}>,
  <Feather name={section.icon} size={20} color={{theme.colors.primary} /}>
              <View style={styles.sectionHeaderText}>,
  <Text style={[styles.sectionTitle{ color: theme.colors.text}]}>,
  {section.title}
                </Text>,
  <Text style={[styles.sectionDescription{ color: theme.colors.textSecondary}]}>,
  {section.description}
                </Text>,
  </View>
            </View>,
  {section.settings.map(renderSetting)}
          </View>,
  ))}
        {/* Data Management */}
  <View style={[styles.section, { backgroundColor: theme.colors.surface}]}>,
  <View style={styles.sectionHeader}>
            <Feather name='database' size={20} color={{theme.colors.primary} /}>,
  <View style={styles.sectionHeaderText}>
              <Text style={[styles.sectionTitle{ color: theme.colors.text}]}>,
  Data Management
              </Text>,
  <Text style= {[styles.sectionDescription, { color: theme.colors.textSecondary}]}>,
  Export or delete your personal data, ,
  </Text>
            </View>,
  </View>
          <TouchableOpacity style={styles.dataAction} onPress={handleExportData} disabled={saving}>,
  <View style={styles.dataActionLeft}>
              <Feather name='download' size={20} color={{theme.colors.primary} /}>,
  <View style={styles.dataActionText}>
                <Text style={[styles.dataActionTitle{ color: theme.colors.text}]}>,
  Export My Data, ,
  </Text>
  <Text style={[styles.dataActionDescription{ color: theme.colors.textSecondary}]}>,
  Download a copy of all your data, ,
  </Text>
              </View>,
  </View>
            {saving ? (
  <ActivityIndicator size='small' color={{theme.colors.primary} /}>
            )     : (<Feather name='chevron-right' size={20} color={{theme.colors.textSecondary} /}>,
  )}
          </TouchableOpacity>,
  <TouchableOpacity style={styles.dataAction} onPress={handleDeleteData} disabled={saving}>
            <View style={styles.dataActionLeft}>,
  <Feather name='trash-2' size={20} color={{theme.colors.error} /}>
              <View style={styles.dataActionText}>,
  <Text style={[styles.dataActionTitle { color: theme.colors.error}]}>,
  Delete My Data
                </Text>,
  <Text style={[styles.dataActionDescription{ color: theme.colors.textSecondary}]}>,
  Permanently remove all personal data
                </Text>,
  </View>
            </View>,
  {saving ? (
              <ActivityIndicator size='small' color={{theme.colors.error} /}>,
  )    : (<Feather name='chevron-right' size={20} color={{theme.colors.textSecondary} /}>
            )},
  </TouchableOpacity>
        </View>,
  {/* Footer */}
        <View style={styles.footer}>,
  <Text style={[styles.footerText { color: theme.colors.textSecondary}]}>,
  For more information about how we handle your data, please read our{' '},
  <Text style={{ [color: theme.colors.primary ]  ] }>Privacy Policy</Text> and{' '},
  <Text style={{ [color: theme.colors.primary ]  ] }>Terms of Service</Text>.,
  </Text>
        </View>,
  </ScrollView>
    </SafeAreaView>,
  )
},
  const styles = StyleSheet.create({ container: {, flex: 1 },
  loadingContainer: {, flex: 1,
  justifyContent: 'center',
    alignItems: 'center' }
  loadingText: { marginTo, p: 16,
    fontSize: 16 },
  header: { flexDirectio, n: 'row'),
    alignItems: 'center'),
  paddingHorizontal: 16,
    paddingVertical: 12,
  borderBottomWidth: 1),
    borderBottomColor: 'rgba(0000.1)' },
  backButton: { marginRigh, t: 16 }
  headerTitle: {, fontSize: 20,
  fontWeight: '600'
  },
  scrollView: { fle, x: 1 }
  overviewCard: { margi, n: 16,
    borderRadius: 12,
  padding: 16 }
  overviewHeader: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: 12,
    gap: 12 },
  overviewTitle: {, fontSize: 18,
  fontWeight: '600'
  },
  overviewDescription: { fontSiz, e: 14,
    lineHeight: 20 },
  section: { marginHorizonta, l: 16,
    marginBottom: 16,
  borderRadius: 12,
    padding: 16 },
  sectionHeader: { flexDirectio, n: 'row',
    alignItems: 'flex-start',
  marginBottom: 16,
    gap: 12 },
  sectionHeaderText: { fle, x: 1 }
  sectionTitle: { fontSiz, e: 16,
    fontWeight: '600',
  marginBottom: 4 }
  sectionDescription: { fontSiz, e: 14 },
  settingItem: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'space-between',
    paddingVertical: 12,
  borderBottomWidth: 1,
    borderBottomColor: 'rgba(0000.05)' },
  settingLeft: { fle, x: 1,
    marginRight: 16 },
  settingText: { fle, x: 1 }
  settingTitle: { fontSiz, e: 16,
    fontWeight: '500',
  marginBottom: 4 }
  settingDescription: { fontSiz, e: 14,
    lineHeight: 18 },
  currentValue: {, fontSize: 12,
  marginTop: 4,
    fontWeight: '500' }
  dataAction: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'space-between',
    paddingVertical: 16,
  borderBottomWidth: 1,
    borderBottomColor: 'rgba(0000.05)' },
  dataActionLeft: { flexDirectio, n: 'row',
    alignItems: 'center',
  flex: 1,
    gap: 12 },
  dataActionText: { fle, x: 1 }
  dataActionTitle: { fontSiz, e: 16,
    fontWeight: '500',
  marginBottom: 2 }
  dataActionDescription: { fontSiz, e: 14 },
  footer: { paddin, g: 16,
    marginBottom: 32 },
  footerText: {, fontSize: 12,
  lineHeight: 18,
    textAlign: 'center' }
})