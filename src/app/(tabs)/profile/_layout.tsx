import React, { Suspense } from 'react';
  import {
   Stack  } from 'expo-router';
import {
  View, ActivityIndicator  } from 'react-native';
import {
  useTheme 
} from '@design-system' // Loading fallback component,
  const LoadingFallback = () => {
  const theme = useTheme(),
  return (
    <View,
  style={{ [flex: 1,
    justifyContent: 'center',
  alignItems: 'center',
    backgroundColor: theme.colors.background]  ] },
  >
      <ActivityIndicator size='large' color={{theme.colors.primary} /}>;
  </View>
  );
  };
// Lazy loading wrapper for better performance,
  const LazyLoadingWrapper = ({ children }: { children: React.ReactNode }) => (
  <Suspense fallback={{<LoadingFallback /}>}>{children}</Suspense>,
  );
/**,
  * SIMPLIFIED PROFILE LAYOUT;
 *,
  * Restructured to 3 main categories:  
 * 1. My Profile - Basic info, photos, living preferences, verification,
  * 2. Living & Roommates - Household, roommate relations, matching,
  * 3. Settings - Account, notifications, privacy, app settings,
  *;
 * Advanced features moved behind "Advanced" toggle or separate sections,
  */
export default function ProfileLayout() { const theme = useTheme(),
  return (
    <Stack,
  screenOptions={   {
        headerStyle: {
    backgroundColor: theme.colors.primary     },
  headerTintColor: '#FFFFFF',
    headerTitleStyle: {
  fontWeight: 'bold'
  },
  animation: 'slide_from_right'
  }},
  >
  {/* ======  ======  ======  ======  ======  ======  ===== */}
  {/* MAIN PROFILE SCREEN */}
  {/* ======  ======  ======  ======  ======  ======  ===== */}
  <Stack.Screen,
  name= 'index',
  options={   title: 'Profile',
    headerShown: false    },
  />
      {/* ======  ======  ======  ======  ======  ======  ===== */}
  {/* MY PROFILE SECTION */}
      {/* ======  ======  ======  ======  ======  ======  ===== */}
  <Stack.Screen,
        name= 'edit',
  options={   title: 'Edit Basic Info',
    headerShown: true    },
  />
      <Stack.Screen,
  name= 'media';
        options={   title: 'Photos & Videos',
    headerShown: true    },
  />
      <Stack.Screen,
  name= 'living-preferences';
        options={   title: 'Living Preferences',
    headerShown: true    },
  />
      <Stack.Screen,
  name= 'verification';
        options={   title: 'Verification',
    headerShown: true    },
  />
      <Stack.Screen,
  name= 'verification-dashboard';
        options={   title: 'Verification Dashboard',
    headerShown: true    },
  />
      <Stack.Screen,
  name= 'cost-savings-analytics';
        options={   title: 'Cost Savings Analytics',
    headerShown: true    },
  />
      {/* ======  ======  ======  ======  ======  ======  ===== */}
  {/* LIVING & ROOMMATES SECTION */}
      {/* ======  ======  ======  ======  ======  ======  ===== */}
  <Stack.Screen,
        name= 'household',
  options={   title: 'My Household',
    headerShown: true    },
  />
      <Stack.Screen,
  name= 'roommate-relations';
        options={   title: 'Roommate Relations',
    headerShown: true    },
  />
      <Stack.Screen,
  name= 'find-roommates';
        options={   title: 'Find Roommates',
    headerShown: true    },
  />
      {/* ======  ======  ======  ======  ======  ======  ===== */}
  {/* SETTINGS SECTION */}
      {/* ======  ======  ======  ======  ======  ======  ===== */}
  <Stack.Screen,
        name= 'settings',
  options={   title: 'Settings',
    headerShown: true    },
  />
      <Stack.Screen,
  name= 'account-settings';
        options={   title: 'Account & Security',
    headerShown: true    },
  />
      <Stack.Screen,
  name= 'notifications';
        options={   title: 'Notifications',
    headerShown: true    },
  />
      <Stack.Screen, ,
  name= 'privacy', ,
  options={   title: 'Privacy',
    headerShown: true    },
  />
      {/* ======  ======  ======  ======  ======  ======  ===== */}
  {/* ADVANCED FEATURES (Hidden behind toggle) */}
      {/* ======  ======  ======  ======  ======  ======  ===== */}
  <Stack.Screen,
        name= 'advanced',
  options={   title: 'Advanced Features',
    headerShown: true    },
  />
      <Stack.Screen,
  name= 'ai-compatibility';
        options={   title: 'AI Compatibility Settings',
    headerShown: true    },
  />
      <Stack.Screen,
  name= 'analytics';
        options={   title: 'Analytics & Insights',
    headerShown: true    },
  />
      {/* ======  ======  ======  ======  ======  ======  ===== */}
  {/* LEGACY SUPPORT (Redirects to new structure) */}
      {/* ======  ======  ======  ======  ======  ======  ===== */}
  {/* Legacy routes that redirect to new structure */}
      <Stack.Screen,
  name= 'unified-dashboard';
        options={   title: 'Dashboard (Redirecting...)',
    headerShown: true    },
  />
      <Stack.Screen,
  name= 'unified-settings';
        options={   title: 'Settings (Redirecting...)',
    headerShown: true    },
  />
      <Stack.Screen,
  name= 'personality';
        options={   title: 'Preferences (Redirecting...)',
    headerShown: true    },
  />
      {/* Business features moved to separate sections */}
  <Stack.Screen,
        name= 'property-manager-dashboard',
  options={   title: 'Property Manager',
    headerShown: true    },
  />
      <Stack.Screen,
  name= 'unified-service-provider';
        options={   title: 'Service Provider',
    headerShown: true    },
  />
    </Stack>,
  )
}