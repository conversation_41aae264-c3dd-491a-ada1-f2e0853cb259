import React, { Suspense } from 'react';
  import {
  Stack
} from 'expo-router';
import {
  View, ActivityIndicator
} from 'react-native';
import {
  useTheme
} from '@design-system' // Loading fallback component,
  const LoadingFallback = () => {
  const theme = useTheme(),
  return (
    <View,
  style={{ [flex: 1,
    justifyContent: 'center'alignItem, s: 'center'backgroundColo, r: theme.colors.background]  ] },
  >;
      <ActivityIndicator size='large' color={{theme.colors.primary} /}>;
  </View>
  );
  };
// Lazy loading wrapper for better performance,
  const LazyLoadingWrapper = ({ children }: { children: React.ReactNode }) => (
  <Suspense fallback={{<LoadingFallback /}>}>{children}</Suspense>,
  );
/**,
  * SIMPLIFIED PROFILE LAYOUT;
 *,
  * Restructured to 3 main categories:  
 * 1. My Profile - Basic info, photos, living preferences, verification,
  * 2. Living & Roommates - Household, roommate relations, matching,
  * 3. Settings - Account, notifications, privacy, app settings,
  *;
 * Advanced features moved behind "Advanced" toggle or separate sections,
  */
export default function ProfileLayout() { const theme = useTheme(),
  return (
    <Stack,
  screenOptions={   {
        headerStyle: {, backgroundColor: theme.colors.primary     },
  headerTintColor: '#FFFFFF',
    headerTitleStyle: {, fontWeight: 'bold'
  },
  animation: 'slide_from_right'
  }},
  >
  {/* ======  ======  ======  ======  ======  ======  ===== */}
  {/* MAIN PROFILE SCREEN */}
  {/* ======  ======  ======  ======  ======  ======  ===== */}
  <Stack.Screen,
  name= 'index',
  options={   title: 'Profile'headerShow, n: false    },
  />
      {/* ======  ======  ======  ======  ======  ======  ===== */}
  {/* MY PROFILE SECTION */}
      {/* ======  ======  ======  ======  ======  ======  ===== */}
  <Stack.Screen,
        name= 'edit',
  options={   title: 'Edit Basic Info'headerShow, n: true    },
  />
      <Stack.Screen,
  name= 'media';
        options={   title: 'Photos & Videos'headerShow, n: true    },
  />
      <Stack.Screen,
  name= 'living-preferences';
        options={   title: 'Living Preferences'headerShow, n: true    },
  />
      <Stack.Screen,
  name= 'verification';
        options={   title: 'Verification'headerShow, n: true    },
  />
      <Stack.Screen,
  name= 'verification-dashboard';
        options={   title: 'Verification Dashboard'headerShow, n: true    },
  />
      <Stack.Screen,
  name= 'cost-savings-analytics';
        options={   title: 'Cost Savings Analytics'headerShow, n: true    },
  />
      {/* ======  ======  ======  ======  ======  ======  ===== */}
  {/* LIVING & ROOMMATES SECTION */}
      {/* ======  ======  ======  ======  ======  ======  ===== */}
  <Stack.Screen,
        name= 'household',
  options={   title: 'My Household'headerShow, n: true    },
  />
      <Stack.Screen,
  name= 'roommate-relations';
        options={   title: 'Roommate Relations'headerShow, n: true    },
  />
      <Stack.Screen,
  name= 'find-roommates';
        options={   title: 'Find Roommates'headerShow, n: true    },
  />
      {/* ======  ======  ======  ======  ======  ======  ===== */}
  {/* SETTINGS SECTION */}
      {/* ======  ======  ======  ======  ======  ======  ===== */}
  <Stack.Screen,
        name= 'settings',
  options={   title: 'Settings'headerShow, n: true    },
  />
      <Stack.Screen,
  name= 'account-settings';
        options={   title: 'Account & Security'headerShow, n: true    },
  />
      <Stack.Screen,
  name= 'notifications';
        options={   title: 'Notifications'headerShow, n: true    },
  />
      <Stack.Screen, ,
  name= 'privacy', ,
  options={   title: 'Privacy'headerShow, n: true    },
  />
      {/* ======  ======  ======  ======  ======  ======  ===== */}
  {/* ADVANCED FEATURES (Hidden behind toggle) */}
      {/* ======  ======  ======  ======  ======  ======  ===== */}
  <Stack.Screen,
        name= 'advanced',
  options={   title: 'Advanced Features'headerShow, n: true    },
  />
      <Stack.Screen,
  name= 'ai-compatibility';
        options={   title: 'AI Compatibility Settings'headerShow, n: true    },
  />
      <Stack.Screen,
  name= 'analytics';
        options={   title: 'Analytics & Insights'headerShow, n: true    },
  />
      {/* ======  ======  ======  ======  ======  ======  ===== */}
  {/* LEGACY SUPPORT (Redirects to new structure) */}
      {/* ======  ======  ======  ======  ======  ======  ===== */}
  {/* Legacy routes that redirect to new structure */}
      <Stack.Screen,
  name= 'unified-dashboard';
        options={   title: 'Dashboard (Redirecting...)'headerShow, n: true    },
  />
      <Stack.Screen,
  name= 'unified-settings';
        options={   title: 'Settings (Redirecting...)'headerShow, n: true    },
  />
      <Stack.Screen,
  name= 'personality';
        options={   title: 'Preferences (Redirecting...)'headerShow, n: true    },
  />
      {/* Business features moved to separate sections */}
  <Stack.Screen,
        name= 'property-manager-dashboard',
  options={   title: 'Property Manager'headerShow, n: true    },
  />
      <Stack.Screen,
  name= 'unified-service-provider';
        options={   title: 'Service Provider'headerShow, n: true    },
  />
    </Stack>,
  )
}