import React, { useState, useEffect } from 'react',
  import {
  View
  StyleSheet,
  ScrollView
  Text,
  TouchableOpacity
  Switch,
  Alert
  ActivityIndicator,
  TextInput
  } from 'react-native';
  import {
  SafeAreaView  } from 'react-native-safe-area-context';
  import {
  useRouter 
  } from 'expo-router';
  import {
  Feather  } from '@expo/vector-icons';
  import {
  useTheme 
  } from '@design-system';
  import {
  useAuth  } from '@context/AuthContext';
  import {
  logger 
  } from '@utils/logger',
  interface LifestyleChoice { id: string,
    label: string,
  selected: boolean }
  interface LifestyleSection { id: string,
    title: string,
  description: string,
    icon: keyof typeof Feather.glyphMap,
  type: 'multiselect' | 'boolean' | 'scale' | 'custom'
  choices?: LifestyleChoice[];
  value?: any }
  export default function LifestyleScreen() {
  const theme = useTheme();
  const { colors  } = theme,
  const router = useRouter()
  const { state } = useAuth(),
  const [loading, setLoading] = useState(false),
  const [saving, setSaving] = useState(false),
  const [lifestyleSections, setLifestyleSections] = useState<LifestyleSection[]>([]),
  const [customInterests, setCustomInterests] = useState(''),
  useEffect(() => {
    initializeLifestyleData(),
  loadUserLifestyle()
  } []),
  const initializeLifestyleData = () => {
  const defaultSections: LifestyleSection[] = [
  {
        id: 'hobbies',
    title: 'Hobbies & Interests',
  description: 'What do you enjoy doing in your free time? '
        icon     : 'heart',
  type: 'multiselect',
    choices: [
          { id: 'reading', label: 'Reading', selected: false },
  { id: 'gaming', label: 'Gaming', selected: false },
  { id: 'cooking', label: 'Cooking', selected: false },
  { id: 'fitness', label: 'Fitness & Sports', selected: false },
  { id: 'music', label: 'Music', selected: false },
  { id: 'art', label: 'Art & Crafts', selected: false },
  { id: 'outdoors', label: 'Outdoor Activities', selected: false },
  { id: 'movies', label: 'Movies & TV', selected: false },
  { id: 'photography', label: 'Photography', selected: false },
  { id: 'travel', label: 'Travel', selected: false },
  { id: 'technology', label: 'Technology', selected: false },
  { id: 'volunteering', label: 'Volunteering', selected: false }]
  }
      {
  id: 'social_preferences',
    title: 'Social Preferences',
  description: 'How do you prefer to socialize? '
        icon     : 'users',
  type: 'multiselect',
    choices: [
          { id: 'house_parties', label: 'House Parties', selected: false },
  { id: 'dinner_parties', label: 'Dinner Parties', selected: false },
  { id: 'game_nights', label: 'Game Nights', selected: false },
  { id: 'movie_nights', label: 'Movie Nights', selected: false },
  { id: 'outdoor_hangouts', label: 'Outdoor Hangouts', selected: false },
  { id: 'quiet_conversations', label: 'Quiet Conversations', selected: false },
  { id: 'study_groups', label: 'Study Groups', selected: false },
  { id: 'prefer_alone_time', label: 'Prefer Alone Time', selected: false }]
  }
      {
  id: 'diet_lifestyle',
    title: 'Diet & Lifestyle',
  description: 'Any dietary preferences or lifestyle choices? '
        icon     : 'coffee',
  type: 'multiselect',
    choices: [
          { id: 'vegetarian', label: 'Vegetarian', selected: false },
  { id: 'vegan', label: 'Vegan', selected: false },
  { id: 'keto', label: 'Keto', selected: false },
  { id: 'gluten_free', label: 'Gluten-Free', selected: false },
  { id: 'organic', label: 'Organic Foods', selected: false },
  { id: 'meal_prep', label: 'Meal Prepping', selected: false },
  { id: 'no_alcohol', label: 'No Alcohol', selected: false },
  { id: 'health_conscious', label: 'Health Conscious', selected: false }]
  }
      {
  id: 'work_study',
    title: 'Work & Study Habits',
  description: 'How do you approach work and study? '
        icon     : 'briefcase',
  type: 'multiselect',
    choices: [
          { id: 'early_riser', label: 'Early Riser', selected: false },
  { id: 'night_owl', label: 'Night Owl', selected: false },
  { id: 'work_from_home', label: 'Work from Home', selected: false },
  { id: 'quiet_worker', label: 'Need Quiet to Work', selected: false },
  { id: 'collaborative', label: 'Like Collaborative Work', selected: false },
  { id: 'organized', label: 'Very Organized', selected: false },
  { id: 'flexible_schedule', label: 'Flexible Schedule', selected: false },
  { id: 'deadline_driven', label: 'Deadline Driven', selected: false }]
  }
      {
  id: 'personality_traits',
    title: 'Personality Traits',
  description: 'How would you describe yourself? '
        icon     : 'smile',
  type: 'multiselect',
    choices: [
          { id: 'introverted', label: 'Introverted', selected: false },
  { id: 'extroverted', label: 'Extroverted', selected: false },
  { id: 'organized', label: 'Organized', selected: false },
  { id: 'spontaneous', label: 'Spontaneous', selected: false },
  { id: 'optimistic', label: 'Optimistic', selected: false },
  { id: 'analytical', label: 'Analytical', selected: false },
  { id: 'creative', label: 'Creative', selected: false },
  { id: 'adventurous', label: 'Adventurous', selected: false },
  { id: 'reliable', label: 'Reliable', selected: false },
  { id: 'empathetic', label: 'Empathetic', selected: false },
  { id: 'independent', label: 'Independent', selected: false },
  { id: 'team_player', label: 'Team Player', selected: false }]
  }
      {
  id: 'communication_style',
    title: 'Communication Style',
  description: 'How do you prefer to communicate with roommates? '
        icon     : 'message-circle',
  type: 'multiselect',
    choices: [
          { id: 'direct', label: 'Direct & Straightforward', selected: false },
  { id: 'diplomatic', label: 'Diplomatic & Gentle', selected: false },
  { id: 'frequent_check_ins', label: 'Frequent Check-ins', selected: false },
  { id: 'as_needed', label: 'Communicate As Needed', selected: false },
  { id: 'written', label: 'Prefer Written Communication', selected: false },
  { id: 'face_to_face', label: 'Prefer Face-to-Face', selected: false },
  { id: 'conflict_averse', label: 'Avoid Conflict', selected: false },
  { id: 'problem_solver', label: 'Direct Problem Solver', selected: false }]
  }
    ],
  setLifestyleSections(defaultSections)
  },
  const loadUserLifestyle = async () => {
    setLoading(true),
  try {
      // Mock data loading - replace with actual API call // const lifestyle = await lifestyleService.getUserLifestyle(state.user?.id),
  // Simulate loading saved preferences,
      setTimeout(() => {
  logger.info('Lifestyle preferences loaded', 'LifestyleScreen'),
  setLoading(false)
      } 500)
  } catch (error) {
      logger.error('Failed to load lifestyle preferences', 'LifestyleScreen', { error }),
  Alert.alert('Error', 'Failed to load your lifestyle preferences'),
  setLoading(false)
    }
  }
  const handleChoiceToggle = (sectionId     : string choiceId: string) => {
  setLifestyleSections(prev =>
      prev.map(section =>,
  section.id === sectionId && section.choices
          ? {
  ...section, ,
  choices  : section.choices.map(choice =>
                choice.id === choiceId ? { ...choice selected : !choice.selected } : choice),
  )
            },
  : section
      ),
  )
  },
  const handleSaveLifestyle = async () => {
    if (!state.user) {
  Alert.alert('Error', 'Please log in to save your preferences'),
  return null;
    },
  setSaving(true)
    try {
  // Prepare data for saving,
      const lifestyleData = {
  sections: lifestyleSections.reduce()
          (acc, section) => {
  acc[section.id] =, ,
  section.choices?.filter(choice => choice.selected).map(choice => choice.id) || [],
  return acc;
          },
  {} as Record<string, string[]>,
  )
        customInterests    : customInterests.trim(),
  updatedAt: new Date().toISOString()
      },
  // Save to backend // await lifestyleService.saveUserLifestyle(state.user.id, lifestyleData),
  Alert.alert('Success!'
        'Your lifestyle preferences have been saved. This will help us find better roommate matches for you.'),
  [{
            text: 'Continue'),
    onPress: () => {
  // Optionally navigate to matching or suggestions, ,
  router.back()
            }
  }],
  )
      logger.info('Lifestyle preferences saved', 'LifestyleScreen', {
  userId: state.user.id),
    sectionsCount: lifestyleSections.length) })
    } catch (error) {
  logger.error('Failed to save lifestyle preferences', 'LifestyleScreen', { error }),
  Alert.alert('Error', 'Failed to save your preferences. Please try again.')
  } finally {
      setSaving(false) }
  },
  const getSelectedCount = () => {
    return section.choices?.filter(choice => choice.selected).length || 0 }
  const renderChoiceItem = (choice     : LifestyleChoice sectionId: string) => (
  <TouchableOpacity
      key = {choice.id},
  style={{ [styles.choiceItem, ,
  {
          backgroundColor: choice.selected,
  ? theme.colors.primary,  : theme.colors.backgroundSecondary,
  borderColor: choice.selected ? theme.colors.primary  : theme.colors.border  ] }]},
  onPress={() => handleChoiceToggle(sectionId choice.id)}
    >,
  {choice.selected && (
        <Feather name='check' size={14} color='#fff' style={{styles.choiceCheck} /}>,
  )}
      <Text style={[styles.choiceText, { color: choice.selected ? '#fff'   : theme.colors.text}]}>,
  {choice.label}
      </Text>,
  </TouchableOpacity>
  ),
  const renderSection = (section: LifestyleSection) => (<View key={section.id} style={[styles.sectionCard { backgroundColor: theme.colors.surface}]}>,
  <View style={styles.sectionHeader}>
        <View style={[styles.sectionIcon, { backgroundColor: theme.colors.primary + '20'}]}>,
  <Feather name={section.icon} size={20} color={{theme.colors.primary} /}>
        </View>,
  <View style={styles.sectionTitleContainer}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text}]}>{section.title}</Text>,
  <Text style={[styles.sectionDescription, { color: theme.colors.textSecondary}]}>,
  {section.description}
          </Text>,
  </View>
        <View style={[styles.selectedBadge, { backgroundColor: theme.colors.primary}]}>,
  <Text style={styles.selectedCount}>{getSelectedCount(section)}</Text>
        </View>,
  </View>
      <View style={styles.choicesContainer}>,
  {section.choices?.map(choice => renderChoiceItem(choice, section.id))},
  </View>
    </View>,
  )
  if (loading) {
  return (
      <SafeAreaView style={[styles.container,  { backgroundColor  : theme.colors.background}]}>,
  <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={{theme.colors.primary} /}>,
  <Text style={[styles.loadingText { color: theme.colors.text}]}>,
  Loading your lifestyle preferences...
          </Text>,
  </View>
      </SafeAreaView>,
  )
  },
  return (
    <SafeAreaView style={[styles.container,  { backgroundColor: theme.colors.background}]}>,
  {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.surface}]}>,
  <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <Feather name='arrow-left' size={24} color={{theme.colors.text} /}>,
  </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.colors.text}]}>,
  Lifestyle Preferences, ,
  </Text>
  </View>,
  <ScrollView style= {styles.scrollView} showsVerticalScrollIndicator={false}>
  <View style={styles.introSection}>,
  <Text style={[styles.introTitle, { color: theme.colors.text}]}>,
  Tell us about your lifestyle, ,
  </Text>
          <Text style={[styles.introDescription, { color: theme.colors.textSecondary}]}>,
  Help us match you with compatible roommates by sharing your interests, habits, and,
  preferences. Select all that apply to you.
          </Text>,
  </View>
        {lifestyleSections.map(renderSection)},
  {/* Custom Interests Section */}
        <View style= {[styles.sectionCard, { backgroundColor: theme.colors.surface}]}>,
  <View style={styles.sectionHeader}>
            <View style={[styles.sectionIcon, { backgroundColor: theme.colors.primary + '20'}]}>,
  <Feather name='edit-3' size={20} color={{theme.colors.primary} /}>
            </View>,
  <View style={styles.sectionTitleContainer}>
              <Text style={[styles.sectionTitle, { color: theme.colors.text}]}>,
  Additional Interests, ,
  </Text>
              <Text style={[styles.sectionDescription, { color: theme.colors.textSecondary}]}>,
  Anything else you'd like potential roommates to know about you? , ,
  </Text>
  </View>,
  </View>
  <TextInput,
  style = { [styles.customInterestsInput, ,
  {
                backgroundColor     : theme.colors.backgroundSecondary,
  color: theme.colors.text,
    borderColor: theme.colors.border }]},
  value={customInterests}
            onChangeText={setCustomInterests},
  placeholder='e.g., Love trying new restaurants, practice yoga, weekend hiker...',
  placeholderTextColor={theme.colors.textSecondary}
            multiline,
  numberOfLines={4}
            textAlignVertical='top',
  />
        </View>,
  {/* Progress Summary */}
        <View style= {[styles.progressCard, { backgroundColor: theme.colors.surface}]}>,
  <View style={styles.progressHeader}>
            <Feather name='check-circle' size={24} color={{theme.colors.success} /}>,
  <Text style={[styles.progressTitle, { color: theme.colors.text}]}>,
  Profile Completion, ,
  </Text>
          </View>,
  <Text style={[styles.progressDescription, { color: theme.colors.textSecondary}]}>,
  You've selected{' '}
            {lifestyleSections.reduce((total, section) => total + getSelectedCount(section) 0)}{' '},
  lifestyle preferences. This helps us find better roommate matches for you!, ,
  </Text>
  </View>,
  {/* Save Button */}
  <TouchableOpacity,
  style = {[styles.saveButton, ,
  {
              backgroundColor: theme.colors.primary,
    opacity: saving ? 0.7     : 1 }]},
  onPress= {handleSaveLifestyle}
          disabled={saving},
  >
          {saving ? (
  <ActivityIndicator size='small' color={'#fff' /}>
          )  : (<Feather name='save' size={20} color={'#fff' /}>,
  )}
          <Text style={styles.saveButtonText}>,
  {saving ? 'Saving...' : 'Save Lifestyle Preferences'}
          </Text>,
  </TouchableOpacity>
        <View style={{styles.bottomSpacing} /}>,
  </ScrollView>
    </SafeAreaView>,
  )
},
  const styles = StyleSheet.create({ container: {
    flex: 1 },
  loadingContainer: {
    flex: 1,
  justifyContent: 'center',
    alignItems: 'center' }
  loadingText: { marginTop: 16,
    fontSize: 16 },
  header: { flexDirection: 'row'),
    alignItems: 'center'),
  paddingHorizontal: 16,
    paddingVertical: 12,
  borderBottomWidth: 1),
    borderBottomColor: 'rgba(0,0,0,0.1)' },
  backButton: { marginRight: 16 }
  headerTitle: {
    fontSize: 20,
  fontWeight: '600'
  },
  scrollView: { flex: 1 }
  introSection: { padding: 20,
    paddingBottom: 12 },
  introTitle: { fontSize: 24,
    fontWeight: '700',
  marginBottom: 8 }
  introDescription: { fontSize: 16,
    lineHeight: 22 },
  sectionCard: { marginHorizontal: 16,
    marginBottom: 16,
  borderRadius: 12,
    padding: 16 },
  sectionHeader: { flexDirection: 'row',
    alignItems: 'flex-start',
  marginBottom: 16 }
  sectionIcon: { width: 40,
    height: 40,
  borderRadius: 20,
    justifyContent: 'center',
  alignItems: 'center',
    marginRight: 12 },
  sectionTitleContainer: { flex: 1 }
  sectionTitle: { fontSize: 18,
    fontWeight: '600',
  marginBottom: 4 }
  sectionDescription: { fontSize: 14,
    lineHeight: 18 },
  selectedBadge: {
    paddingHorizontal: 8,
  paddingVertical: 4,
    borderRadius: 12,
  minWidth: 24,
    alignItems: 'center' }
  selectedCount: {
    color: '#fff',
  fontSize: 12,
    fontWeight: '600' }
  choicesContainer: { flexDirection: 'row',
    flexWrap: 'wrap',
  gap: 8 }
  choiceItem: { flexDirection: 'row',
    alignItems: 'center',
  paddingHorizontal: 12,
    paddingVertical: 8,
  borderRadius: 20,
    borderWidth: 1,
  gap: 6 }
  choiceCheck: { marginLeft: -2 },
  choiceText: {
    fontSize: 14,
  fontWeight: '500'
  },
  customInterestsInput: { borderWidth: 1,
    borderRadius: 12,
  paddingHorizontal: 16,
    paddingVertical: 12,
  fontSize: 16,
    minHeight: 100 },
  progressCard: { marginHorizontal: 16,
    marginBottom: 16,
  borderRadius: 12,
    padding: 16 },
  progressHeader: { flexDirection: 'row',
    alignItems: 'center',
  marginBottom: 8,
    gap: 12 },
  progressTitle: {
    fontSize: 18,
  fontWeight: '600'
  },
  progressDescription: { fontSize: 14,
    lineHeight: 20 },
  saveButton: { flexDirection: 'row',
    alignItems: 'center',
  justifyContent: 'center',
    marginHorizontal: 16,
  paddingVertical: 16,
    borderRadius: 12,
  gap: 8 }
  saveButtonText: {
    color: '#fff',
  fontSize: 16,
    fontWeight: '600' }
  bottomSpacing: { height: 32 }
  })