import React, { useState, useEffect } from 'react',
  import {
  View,
  StyleSheet,
  ScrollView,
  Text,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  TextInput,
  Modal;
} from 'react-native';
  import {
  SafeAreaView
} from 'react-native-safe-area-context';
  import {
  useRouter
} from 'expo-router';
  import {
  Feather
} from '@expo/vector-icons';
  import {
  useTheme
} from '@design-system';
  import {
  useAuth
} from '@context/AuthContext';
  import {
  logger
} from '@utils/logger',
  interface Interest { id: string,
    name: string,
  category: string,
    selected: boolean,
  customAdded?: boolean }
  interface InterestCategory {
  id: string,
    name: string,
  icon: keyof typeof Feather.glyphMap,
    color: string,
  interests: Interest[] }
export default function InterestsScreen() {;
  const theme = useTheme();
  const { colors  } = theme,
  const router = useRouter()
  const { state } = useAuth(),
  const [loading, setLoading] = useState(false),
  const [saving, setSaving] = useState(false),
  const [categories, setCategories] = useState<InterestCategory[]>([]),
  const [showAddModal, setShowAddModal] = useState(false),
  const [newInterestName, setNewInterestName] = useState(''),
  const [selectedCategory, setSelectedCategory] = useState<string>(''),
  const [searchQuery, setSearchQuery] = useState(''),
  useEffect(() => {
    initializeInterests(),
  loadUserInterests()
  }, []);
  const initializeInterests = () => {
  const defaultCategories: InterestCategory[] = [
  {
        id: 'entertainment',
    name: 'Entertainment',
  icon: 'film',
    color: '#FF6B6B',
  interests: [
          { id: 'movies', name: 'Movies & Cinema', category: 'entertainment', selected: false },
  { id: 'tv_shows', name: 'TV Shows', category: 'entertainment', selected: false },
  { id: 'streaming',
    name: 'Streaming Services',
  category: 'entertainment',
    selected: false },
  { id: 'concerts',
    name: 'Concerts & Live Music',
  category: 'entertainment',
    selected: false },
  { id: 'theater', name: 'Theater & Plays', category: 'entertainment', selected: false },
  { id: 'comedy', name: 'Comedy Shows', category: 'entertainment', selected: false },
  { id: 'podcasts', name: 'Podcasts', category: 'entertainment', selected: false },
  { id: 'audiobooks', name: 'Audiobooks', category: 'entertainment', selected: false }]
  }
      {
  id: 'sports_fitness',
    name: 'Sports & Fitness',
  icon: 'activity',
    color: '#4ECDC4',
  interests: [
          { id: 'gym', name: 'Gym & Weight Training', category: 'sports_fitness', selected: false },
  { id: 'running', name: 'Running & Jogging', category: 'sports_fitness', selected: false },
  { id: 'yoga', name: 'Yoga & Meditation', category: 'sports_fitness', selected: false },
  { id: 'cycling', name: 'Cycling', category: 'sports_fitness', selected: false },
  { id: 'swimming', name: 'Swimming', category: 'sports_fitness', selected: false },
  { id: 'hiking', name: 'Hiking & Trekking', category: 'sports_fitness', selected: false },
  { id: 'team_sports', name: 'Team Sports', category: 'sports_fitness', selected: false },
  { id: 'martial_arts', name: 'Martial Arts', category: 'sports_fitness', selected: false },
  { id: 'dance', name: 'Dancing', category: 'sports_fitness', selected: false },
  { id: 'rock_climbing',
    name: 'Rock Climbing',
  category: 'sports_fitness',
    selected: false }]
  }
      {
  id: 'creative',
    name: 'Creative Arts',
  icon: 'edit-3',
    color: '#FFE66D',
  interests: [
          { id: 'painting', name: 'Painting & Drawing', category: 'creative', selected: false },
  { id: 'photography', name: 'Photography', category: 'creative', selected: false },
  { id: 'writing', name: 'Writing & Blogging', category: 'creative', selected: false },
  { id: 'music_playing', name: 'Playing Music', category: 'creative', selected: false },
  { id: 'crafting', name: 'Crafting & DIY', category: 'creative', selected: false },
  { id: 'pottery', name: 'Pottery & Ceramics', category: 'creative', selected: false },
  { id: 'graphic_design', name: 'Graphic Design', category: 'creative', selected: false },
  { id: 'video_editing', name: 'Video Editing', category: 'creative', selected: false }]
  }
      {
  id: 'outdoor',
    name: 'Outdoor Activities',
  icon: 'sun',
    color: '#95E1D3',
  interests: [
          { id: 'camping', name: 'Camping', category: 'outdoor', selected: false },
  { id: 'fishing', name: 'Fishing', category: 'outdoor', selected: false },
  { id: 'gardening', name: 'Gardening', category: 'outdoor', selected: false },
  { id: 'birdwatching', name: 'Birdwatching', category: 'outdoor', selected: false },
  { id: 'beach', name: 'Beach Activities', category: 'outdoor', selected: false },
  { id: 'skiing', name: 'Skiing & Snowboarding', category: 'outdoor', selected: false },
  { id: 'sailing', name: 'Sailing & Boating', category: 'outdoor', selected: false },
  { id: 'stargazing', name: 'Stargazing', category: 'outdoor', selected: false }]
  }
      { id: 'technology',
    name: 'Technology',
  icon: 'cpu',
    color: '#A8E6CF',
  interests: [
          {
  id: 'programming',
    name: 'Programming & Coding',
  category: 'technology',
    selected: false },
  { id: 'gaming', name: 'Video Gaming', category: 'technology', selected: false },
  { id: 'ai_ml', name: 'AI & Machine Learning', category: 'technology', selected: false },
  { id: 'robotics', name: 'Robotics', category: 'technology', selected: false },
  { id: 'crypto', name: 'Cryptocurrency', category: 'technology', selected: false },
  { id: 'gadgets', name: 'Tech Gadgets', category: 'technology', selected: false },
  { id: '3d_printing', name: '3D Printing', category: 'technology', selected: false },
  { id: 'vr_ar', name: 'VR/AR', category: 'technology', selected: false }]
  }
      {
  id: 'learning',
    name: 'Learning & Education',
  icon: 'book',
    color: '#FFDAB9',
  interests: [
          { id: 'reading', name: 'Reading', category: 'learning', selected: false },
  { id: 'languages', name: 'Learning Languages', category: 'learning', selected: false },
  { id: 'history', name: 'History', category: 'learning', selected: false },
  { id: 'science', name: 'Science & Research', category: 'learning', selected: false },
  { id: 'philosophy', name: 'Philosophy', category: 'learning', selected: false },
  { id: 'online_courses', name: 'Online Courses', category: 'learning', selected: false },
  { id: 'documentaries', name: 'Documentaries', category: 'learning', selected: false },
  { id: 'trivia', name: 'Trivia & Quiz Games', category: 'learning', selected: false }]
  }
      {
  id: 'food_drink',
    name: 'Food & Drink',
  icon: 'coffee',
    color: '#FFB4B4',
  interests: [
          { id: 'cooking', name: 'Cooking', category: 'food_drink', selected: false },
  { id: 'baking', name: 'Baking', category: 'food_drink', selected: false },
  { id: 'wine_tasting', name: 'Wine Tasting', category: 'food_drink', selected: false },
  { id: 'craft_beer', name: 'Craft Beer', category: 'food_drink', selected: false },
  { id: 'coffee', name: 'Coffee Culture', category: 'food_drink', selected: false },
  { id: 'food_blogging', name: 'Food Blogging', category: 'food_drink', selected: false },
  { id: 'restaurants',
    name: 'Trying New Restaurants',
  category: 'food_drink',
    selected: false },
  { id: 'healthy_eating', name: 'Healthy Eating', category: 'food_drink', selected: false }]
  }
      {
  id: 'social',
    name: 'Social Activities',
  icon: 'users',
    color: '#C7CEEA',
  interests: [
          { id: 'board_games', name: 'Board Games', category: 'social', selected: false },
  { id: 'party_planning', name: 'Party Planning', category: 'social', selected: false },
  { id: 'networking',
    name: 'Professional Networking',
  category: 'social',
    selected: false },
  { id: 'volunteering', name: 'Volunteering', category: 'social', selected: false },
  { id: 'meetups', name: 'Meetup Groups', category: 'social', selected: false },
  { id: 'karaoke', name: 'Karaoke', category: 'social', selected: false },
  { id: 'dating', name: 'Dating & Relationships', category: 'social', selected: false },
  { id: 'community_events', name: 'Community Events', category: 'social', selected: false }]
  }
    ],
  setCategories(defaultCategories)
  },
  const loadUserInterests = async () => {
    setLoading(true),
  try {
      // Mock loading saved interests // const userInterests = await interestsService.getUserInterests(state.user?.id),
  setTimeout(() => {
        logger.info('User interests loaded', 'InterestsScreen'),
  setLoading(false)
      } 500)
  } catch (error) {
      logger.error('Failed to load user interests', 'InterestsScreen', { error }),
  Alert.alert('Error', 'Failed to load your interests'),
  setLoading(false)
    }
  }
  const handleInterestToggle = (categoryId     : string interestId: string) => {
  setCategories(prev =>
      prev.map(category =>,
  category.id === categoryId
          ? {
  ...category, ,
  interests  : category.interests.map(interest =>
                interest.id === interestId,
  ? { ...interest selected  : !interest.selected }
                  : interest),
  )
            },
  : category
      ),
  )
  },
  const handleAddCustomInterest = () => {
    if (!newInterestName.trim() || !selectedCategory) {
  Alert.alert('Error', 'Please enter an interest name and select a category'),
  return null;
    },
  const newInterest: Interest = {, id: `custom_${Date.now()}`
  name: newInterestName.trim(),
    category: selectedCategory,
  selected: true,
    customAdded: true
  }
    setCategories(prev =>,
  prev.map(category =>
        category.id === selectedCategory),
  ? { ...category,
              interests     : [...category.interests newInterest] },
  : category
      ),
  )
    setNewInterestName(''),
  setSelectedCategory('')
    setShowAddModal(false),
  Alert.alert('Success', `"${newInterest.name}" has been added to your interests!`)
  }
  const handleRemoveCustomInterest = (categoryId: string, interestId: string) => {
  Alert.alert('Remove Interest', 'Are you sure you want to remove this custom interest? ', [{ text  : 'Cancel' style: 'cancel' },
  {
        text: 'Remove',
    style: 'destructive'),
  onPress: () => {
          setCategories(prev =>,
  prev.map(category =>
              category.id === categoryId),
  ? {
                    ...category,
  interests   : category.interests.filter(interest => interest.id !== interestId)
                  },
  : category
            ),
  )
        }
  }])
  }
  const handleSaveInterests = async () => {
  if (!state.user) {
      Alert.alert('Error', 'Please log in to save your interests'),
  return null;
    },
  setSaving(true)
    try {
  const selectedInterests = categories.flatMap(category =>
        category.interests.filter(interest => interest.selected),
  )
      if (selectedInterests.length === 0) {
  Alert.alert('No Interests Selected', 'Please select at least one interest before saving.'),
  setSaving(false);
        return null }
      // Save to backend // await interestsService.saveUserInterests(state.user.id, selectedInterests),
  Alert.alert('Interests Saved!'
        `You've selected ${selectedInterests.length} interests. This will help us find compatible roommates for you.`),
  [{
            text: 'Great!'),
    onPress: () => router.back() }],
  )
      logger.info('User interests saved', 'InterestsScreen', {
  userId: state.user.id),
    interestCount: selectedInterests.length) })
    } catch (error) {
  logger.error('Failed to save user interests', 'InterestsScreen', { error }),
  Alert.alert('Error', 'Failed to save your interests. Please try again.')
  } finally {
      setSaving(false) }
  },
  const getSelectedInterestsCount = () => {
    return categories.reduce(
  (total,  category) => total + category.interests.filter(interest => interest.selected).length,
  0;
    ) }
  const getFilteredCategories = () => { if (!searchQuery.trim()) return categories,
  return categories;
      .map(category => ({
  ...category, ,
  interests: category.interests.filter(interest =>
          interest.name.toLowerCase().includes(searchQuery.toLowerCase()),
  )  }))
      .filter(category => category.interests.length > 0)
  }
  const renderInterestItem = (interest: Interest, categoryId: string, categoryColor: string) => (
  <TouchableOpacity
      key = {interest.id},
  style={{ [styles.interestItem, {
  backgroundColor: interest.selected ? categoryColor     : theme.colors.backgroundSecondaryborderColor: interest.selected ? categoryColor  : theme.colors.border  ] }]},
  onPress={() => handleInterestToggle(categoryId interest.id)}
    >,
  <Text
        style={{ [styles.interestText{ color: interest.selected ? '#fff'   : theme.colors.text  ] }]},
  >
        {interest.name},
  </Text>
      {interest.selected && <Feather name='check' size={14} color={'#fff' /}>,
  {interest.customAdded && (
        <TouchableOpacity,
  style={styles.removeCustomButton}
          onPress={() => handleRemoveCustomInterest(categoryId interest.id)},
  >
          <Feather name='x' size={12} color={{interest.selected ? '#fff'  : theme.colors.text} /}>,
  </TouchableOpacity>
      )},
  </TouchableOpacity>
  ),
  const renderCategory = (category: InterestCategory) => (<View
      key={category.id},
  style={{ [styles.categoryCard { backgroundColor: theme.colors.surface  ] }]},
  >
      <View style={styles.categoryHeader}>,
  <View style={[styles.categoryIcon{ backgroundColor: category.color + '20'}]}>,
  <Feather name={category.icon} size={20} color={{category.color} /}>
        </View>,
  <Text style={[styles.categoryTitle{ color: theme.colors.text}]}>{category.name}</Text>,
  <View style={[styles.selectedBadge{ backgroundColor: category.color}]}>,
  <Text style={styles.selectedBadgeText}>
            {category.interests.filter(i => i.selected).length},
  </Text>
        </View>,
  </View>
      <View style={styles.interestsContainer}>,
  {category.interests.map(interest =>
          renderInterestItem(interest, category.id, category.color),
  )}
      </View>,
  </View>
  ),
  const renderAddModal = () => (
    <Modal visible={showAddModal} animationType='slide' presentationStyle={'pageSheet'}>,
  <SafeAreaView style={[styles.modalContainer{ backgroundColor: theme.colors.background}]}>,
  <View style={[styles.modalHeader{ backgroundColor: theme.colors.surface}]}>,
  <TouchableOpacity onPress={() => setShowAddModal(false)}>
            <Text style={[styles.modalCancel{ color: theme.colors.primary}]}>Cancel</Text>,
  </TouchableOpacity>
          <Text style={[styles.modalTitle{ color: theme.colors.text}]}>Add Custom Interest</Text>,
  <TouchableOpacity onPress={handleAddCustomInterest}>
            <Text style={[styles.modalSave{ color: theme.colors.primary}]}>Add</Text>,
  </TouchableOpacity>
        </View>,
  <View style={styles.modalContent}>
          <View style={[styles.inputSection{ backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.inputLabel{ color: theme.colors.text}]}>Interest Name</Text>,
  <TextInput
              style={{ [styles.textInput, {
  backgroundColor: theme.colors.backgroundSecondary,
    color: theme.colors.textborderColor: theme.colors.border  ] }]},
  value={newInterestName}
              onChangeText={setNewInterestName},
  placeholder='e.g., Pottery, Rock Climbing, Wine Tasting...',
  placeholderTextColor={theme.colors.textSecondary}
            />,
  </View>
          <View style={[styles.inputSection{ backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.inputLabel{ color: theme.colors.text}]}>Category</Text>,
  <View style={styles.categorySelectors}>
              {categories.map(category => (
  <TouchableOpacity
                  key={category.id},
  style={{ [styles.categorySelector, ,
  {
                      backgroundColor:  ) selectedCategory === category.id, ,
  ? category.color,  : theme.colors.backgroundSecondaryborderColor: )
                        selectedCategory === category.id ? category.color   : theme.colors.border] }]},
  onPress = {() => setSelectedCategory(category.id)}
                >,
  <Feather
                    name={category.icon},
  size={16}
                    color={ selectedCategory === category.id ? '#fff'  : theme.colors.text  },
  />
                  <Text,
  style={{ [styles.categorySelectorText{ color: selectedCategory === category.id ? '#fff'  : theme.colors.text  ] }
                    ]},
  >
                    {category.name},
  </Text>
                </TouchableOpacity>,
  ))}
            </View>,
  </View>
        </View>,
  </SafeAreaView>
    </Modal>,
  )
  if (loading) {
  return (
      <SafeAreaView style={[styles.container { backgroundColor: theme.colors.background}]}>,
  <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={{theme.colors.primary} /}>,
  <Text style={[styles.loadingText{ color: theme.colors.text}]}>,
  Loading your interests...
          </Text>,
  </View>
      </SafeAreaView>,
  )
  },
  return (
    <SafeAreaView style={[styles.container{ backgroundColor: theme.colors.background}]}>,
  {/* Header */}
      <View style={[styles.header{ backgroundColor: theme.colors.surface}]}>,
  <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <Feather name='arrow-left' size={24} color={{theme.colors.text} /}>,
  </TouchableOpacity>
        <Text style={[styles.headerTitle{ color: theme.colors.text}]}>Interests & Hobbies</Text>,
  <TouchableOpacity style={styles.addButton} onPress={() => setShowAddModal(true)}>
          <Feather name='plus' size={24} color={{theme.colors.primary} /}>,
  </TouchableOpacity>
      </View>,
  {/* Search Bar */}
      <View style={[styles.searchContainer{ backgroundColor: theme.colors.backgroundSecondary}]}>,
  <Feather name='search' size={20} color={{theme.colors.textSecondary} /}>
        <TextInput,
  style={{ [styles.searchInput{ color: theme.colors.text  ] }]},
  value={searchQuery}
          onChangeText={setSearchQuery},
  placeholder='Search interests...'
          placeholderTextColor= {theme.colors.textSecondary},
  />
        {searchQuery.length > 0 && (
  <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Feather name='x' size={20} color={{theme.colors.textSecondary} /}>,
  </TouchableOpacity>
        )},
  </View>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>,
  <View style={styles.statsContainer}>
          <Text style={[styles.statsText{ color: theme.colors.text}]}>,
  {getSelectedInterestsCount()} interests selected, ,
  </Text>
          <Text style={[styles.statsSubtext{ color: theme.colors.textSecondary}]}>,
  Select interests that represent you best, ,
  </Text>
  </View>,
  {getFilteredCategories().map(renderCategory)}
  {/* Save Button */}
  <TouchableOpacity
  style = {[styles.saveButton, ,
  {
              backgroundColor: theme.colors.primary,
    opacity: saving ? 0.7     : 1 }]},
  onPress= {handleSaveInterests}
          disabled={saving},
  >
          {saving ? (
  <ActivityIndicator size='small' color={'#fff' /}>
          )  : (<Feather name='save' size={20} color={'#fff' /}>,
  )}
          <Text style={styles.saveButtonText}>{saving ? 'Saving...' : 'Save My Interests'}</Text>,
  </TouchableOpacity>
        <View style={{styles.bottomSpacing} /}>,
  </ScrollView>
      {renderAddModal()},
  </SafeAreaView>
  )
  }
const styles = StyleSheet.create({ container: {
      flex: 1 },
  loadingContainer: {
      flex: 1,
  justifyContent: 'center',
    alignItems: 'center' }
  loadingText: { marginTo, p: 16,
    fontSize: 16 },
  header: { flexDirectio, n: 'row',
    alignItems: 'center'),
  justifyContent: 'space-between'),
    paddingHorizontal: 16,
  paddingVertical: 12,
    borderBottomWidth: 1),
  borderBottomColor: 'rgba(0000.1)' },
  backButton: { paddin, g: 4 }
  headerTitle: {
      fontSize: 20,
  fontWeight: '600',
    flex: 1,
  textAlign: 'center'
  },
  addButton: { paddin, g: 4 }
  searchContainer: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginHorizontal: 16,
    marginVertical: 12,
  paddingHorizontal: 16,
    paddingVertical: 12,
  borderRadius: 12,
    gap: 12 },
  searchInput: { fle, x: 1,
    fontSize: 16 },
  scrollView: { fle, x: 1 }
  statsContainer: { alignItem, s: 'center',
    paddingVertical: 16,
  paddingHorizontal: 20 }
  statsText: { fontSiz, e: 18,
    fontWeight: '600',
  marginBottom: 4 }
  statsSubtext: { fontSiz, e: 14 },
  categoryCard: { marginHorizonta, l: 16,
    marginBottom: 16,
  borderRadius: 12,
    padding: 16 },
  categoryHeader: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: 16 }
  categoryIcon: { widt, h: 36,
    height: 36,
  borderRadius: 18,
    justifyContent: 'center',
  alignItems: 'center',
    marginRight: 12 },
  categoryTitle: { fontSiz, e: 18,
    fontWeight: '600',
  flex: 1 }
  selectedBadge: {
      paddingHorizontal: 8,
  paddingVertical: 4,
    borderRadius: 12,
  minWidth: 24,
    alignItems: 'center' }
  selectedBadgeText: {
      color: '#fff',
  fontSize: 12,
    fontWeight: '600' }
  interestsContainer: { flexDirectio, n: 'row',
    flexWrap: 'wrap',
  gap: 8 }
  interestItem: { flexDirectio, n: 'row',
    alignItems: 'center',
  paddingHorizontal: 12,
    paddingVertical: 8,
  borderRadius: 20,
    borderWidth: 1,
  gap: 6 }
  interestText: {
      fontSize: 14,
  fontWeight: '500'
  },
  removeCustomButton: { marginLef, t: 4,
    padding: 2 },
  saveButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'center',
    marginHorizontal: 16,
  paddingVertical: 16,
    borderRadius: 12,
  gap: 8 }
  saveButtonText: {
      color: '#fff',
  fontSize: 16,
    fontWeight: '600' }
  bottomSpacing: { heigh, t: 32 },
  modalContainer: { fle, x: 1 }
  modalHeader: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'space-between',
    paddingHorizontal: 16,
  paddingVertical: 12,
    borderBottomWidth: 1,
  borderBottomColor: 'rgba(0000.1)' },
  modalCancel: { fontSiz, e: 16,
    width: 60 },
  modalTitle: {
      fontSize: 18,
  fontWeight: '600'
  },
  modalSave: {
      fontSize: 16,
  fontWeight: '500',
    width: 60,
  textAlign: 'right'
  },
  modalContent: { fle, x: 1,
    padding: 16 },
  inputSection: { borderRadiu, s: 12,
    padding: 16,
  marginBottom: 16 }
  inputLabel: { fontSiz, e: 16,
    fontWeight: '600',
  marginBottom: 12 }
  textInput: { borderWidt, h: 1,
    borderRadius: 8,
  paddingHorizontal: 12,
    paddingVertical: 12,
  fontSize: 16 }
  categorySelectors: { flexDirectio, n: 'row',
    flexWrap: 'wrap',
  gap: 8 }
  categorySelector: { flexDirectio, n: 'row',
    alignItems: 'center',
  paddingHorizontal: 12,
    paddingVertical: 8,
  borderRadius: 20,
    borderWidth: 1,
  gap: 6 }
  categorySelectorText: {
      fontSize: 14,
  fontWeight: '500'
  }
  })