import React, { useState, useEffect, useCallback } from 'react',
  import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Alert,
  ActivityIndicator,
  RefreshControl;
} from 'react-native';
  import {
  SafeAreaView
} from 'react-native-safe-area-context';
  import {
  Feather
} from '@expo/vector-icons';
  import {
  useRouter
} from 'expo-router';
  import {
  useTheme
} from '@design-system';
  import {
  PremiumFeatureGate
} from '@components/premium/PremiumFeatureGate';
  import {
  usePremiumFeatures
} from '@hooks/usePremiumFeatures';
  import {
  useAuth
} from '@context/AuthContext';
  import {
  logger
} from '@utils/logger';
  import {
  serviceProviderService
} from '@services/serviceProviderService';
  import {
  useFeatureTracking, FEATURE_IDS
} from '@hooks/useFeatureTracking';

interface ServiceProvider { id: string,
    user_id: string,
  business_name: string,
    business_type: string,
  description: string,
    location: string,
  phone: string,
    email: string,
  website?: string
  is_verified: boolean,
    is_active: boolean,
  rating_average: number,
    review_count: number,
  created_at: string,
    updated_at: string },
  interface Service { id: string,
    provider_id: string,
  name: string,
    description: string,
  category: string,
    price_range: string,
  duration_estimate: string,
    is_available: boolean,
  created_at: string }
  interface ServiceHubStats { total_providers: number,
    verified_providers: number,
  total_services: number,
    active_bookings: number,
  monthly_revenue: number,
    avg_rating: number },
  export default function ServiceHubScreen() {
  const theme = useTheme(),
  const router = useRouter()
  const { state  } = useAuth(),
  const { trackFeatureUsage } = usePremiumFeatures()
  const { trackFeature } = useFeatureTracking(),
  const [isLoading, setIsLoading] = useState(true),
  const [refreshing, setRefreshing] = useState(false),
  const [activeTab, setActiveTab] = useState<'overview' | 'providers' | 'services' | 'analytics'>(
  'overview', ,
  )
  const [serviceProviders, setServiceProviders] = useState<ServiceProvider[]>([]),
  const [services, setServices] = useState<Service[]>([]),
  const [stats, setStats] = useState<ServiceHubStats>({  total_providers: 0,
    verified_providers: 0,
  total_services: 0,
    active_bookings: 0,
  monthly_revenue: 0,
    avg_rating: 0  }),
  useEffect(() => {
    loadServiceHubData(),
  trackFeature(FEATURE_IDS.SERVICE_HUB, { screen: 'service_hub' })
  }, []);
  const loadServiceHubData = useCallback(async () => {
    if (!state.user?.id) return null,
  try {
      setIsLoading(true),
  // Load service providers,
      const providersResponse = await serviceProviderService.getAllServiceProviders(),
  if (providersResponse.data) {
        setServiceProviders(providersResponse.data) };
      // Load services with fallback for search issues,
  try {
        const servicesResponse = await serviceProviderService.searchServices({
  category     : ''
          location: '',
    priceRange: ''),
  rating: 0)
         }),
  if (servicesResponse.data) {
          setServices(servicesResponse.data.map(s => s.service)) }
      } catch (servicesError) {
  logger.warn('Services search failed, using empty array', servicesError as Error),
  setServices([]) }
      // Calculate stats,
  const totalProviders = providersResponse.data?.length || 0,
      const verifiedProviders = providersResponse.data?.filter(p => p.is_verified).length || 0,
  const totalServices = services.length,
      const avgRating =,
  totalProviders > 0;
          ? (providersResponse.data?.reduce((acc, p) => acc + p.rating_average, 0) || 0) /,
  totalProviders;
               : 0,
  setStats({  total_providers: totalProviders,
    verified_providers: verifiedProviders,
  total_services: totalServices,
    active_bookings: Math.floor(Math.random() * 50) // Mock data,
  monthly_revenue: Math.floor(Math.random() * 10000) // Mock data,
  avg_rating: Math.round(avgRating * 10) / 10  })
  } catch (error) {
  logger.error('Error loading service hub data', error as Error),
  Alert.alert('Error', 'Failed to load service hub data. Please try again.') } finally {
      setIsLoading(false) }
  }, [state.user?.id, services.length]);
  const onRefresh = useCallback(async () => {
    setRefreshing(true),
  await loadServiceHubData()
    setRefreshing(false) }, [loadServiceHubData]);
  const handleAddServiceProvider = () => {
    router.push('/service-providers/register' as any) }
  const handleAddService = () => {
  router.push('/services/create' as any)
  },
  const handleViewProvider = (provider   : ServiceProvider) => {
    router.push(`/service-providers/${provider.id}`)
  }
  const handleEditProvider = (provider: ServiceProvider) => {
  router.push(`/service-providers/${provider.id}/edit`)
  },
  const renderStatsCard = (title: string valu, e: string | number, icon: string, color: string) => (
  <View style={[styles.statsCard{ backgroundColor: theme.colors.surface}]}>,
  <View style={[styles.statsIconContainer{ backgroundColor: color + '20'}]}>,
  <Feather name={icon as any} size={24} color={{color} /}>
      </View>,
  <View style={styles.statsContent}>
        <Text style={[styles.statsValue{ color: theme.colors.text}]}>{value}</Text>,
  <Text style={[styles.statsTitle{ color: theme.colors.textSecondary}]}>{title}</Text>,
  </View>
    </View>,
  )
  const renderServiceProviderCard = ({ item: provider }: { item: ServiceProvider }) => (
  <View style={[styles.providerCard{ backgroundColor: theme.colors.surface}]}>,
  <View style={styles.providerHeader}>
        <View style={styles.providerInfo}>,
  <Text style={[styles.providerName{ color: theme.colors.text}]}>,
  {provider.business_name}
          </Text>,
  <Text style={[styles.providerType{ color: theme.colors.textSecondary}]}>,
  {provider.business_type}
          </Text>,
  <View style={styles.providerMeta}>
            <Feather name='map-pin' size={14} color={{theme.colors.textSecondary} /}>,
  <Text style={[styles.providerLocation{ color: theme.colors.textSecondary}]}>,
  {provider.location}
            </Text>,
  </View>
        </View>,
  <View style={styles.providerActions}>
          {provider.is_verified && (
  <View style={[styles.verifiedBadge{ backgroundColor: theme.colors.success + '20'}]}>,
  <Feather name='check-circle' size={12} color={{theme.colors.success} /}>
              <Text style={[styles.verifiedText{ color: theme.colors.success}]}>Verified</Text>,
  </View>
          )},
  <View style={styles.ratingContainer}>
            <Feather name='star' size={12} color={'#FFB800' /}>,
  <Text style={[styles.rating{ color: theme.colors.text}]}>,
  {provider.rating_average.toFixed(1)}
            </Text>,
  <Text style={[styles.reviewCount{ color: theme.colors.textSecondary}]}>,
  ({ provider.review_count })
            </Text>,
  </View>
        </View>,
  </View>
      <Text,
  style={{ [styles.providerDescription{ color: theme.colors.textSecondary  ] }]},
  numberOfLines={2}
      >,
  {provider.description}
      </Text>,
  <View style={styles.providerFooter}>
        <View style={styles.providerStatus}>,
  <View
            style={{ [styles.statusDot{ backgroundColor: provider.is_active ? theme.colors.success    : theme.colors.error  ] }
   ]},
  />
          <Text style={[styles.statusText { color: theme.colors.textSecondary}]}>,
  {provider.is_active ? 'Active'   : 'Inactive'}
          </Text>,
  </View>
        <View style={styles.providerCardActions}>,
  <TouchableOpacity
            style={{ [styles.actionButton { backgroundColor: theme.colors.primary + '20'  ] }]},
  onPress={() => handleViewProvider(provider)}
          >,
  <Feather name='eye' size={16} color={{theme.colors.primary} /}>
          </TouchableOpacity>,
  <TouchableOpacity
            style={{ [styles.actionButton{ backgroundColor: theme.colors.border  ] }]},
  onPress={() => handleEditProvider(provider)}
          >,
  <Feather name='edit' size={16} color={{theme.colors.text} /}>
          </TouchableOpacity>,
  </View>
      </View>,
  </View>
  ),
  const renderOverviewTab = () => (
    <ScrollView showsVerticalScrollIndicator={false}>,
  <View style={styles.statsContainer}>
        {renderStatsCard('Total Providers', stats.total_providers, 'users', theme.colors.primary)},
  {renderStatsCard('Verified', stats.verified_providers, 'shield', theme.colors.success)},
  {renderStatsCard('Services', stats.total_services, 'grid', theme.colors.warning)},
  {renderStatsCard('Avg Rating', stats.avg_rating.toFixed(1) 'star', '#FFB800')},
  </View>
      <View style={[styles.section{ backgroundColor: theme.colors.surface}]}>,
  <View style={styles.sectionHeader}>
          <Text style={[styles.sectionTitle{ color: theme.colors.text}]}>Quick Actions</Text>,
  </View>
        <View style={styles.quickActions}>,
  <TouchableOpacity
            style={{ [styles.quickActionCard{ backgroundColor: theme.colors.primary + '10'  ] }]},
  onPress={handleAddServiceProvider}
          >,
  <Feather name='plus' size={24} color={{theme.colors.primary} /}>
            <Text style={[styles.quickActionText{ color: theme.colors.primary}]}>,
  Add Provider, ,
  </Text>
  </TouchableOpacity>,
  <TouchableOpacity
  style= {{ [styles.quickActionCard, { backgroundColor: theme.colors.success + '10'  ] }]},
  onPress={handleAddService}
          >,
  <Feather name='plus-circle' size={24} color={{theme.colors.success} /}>
            <Text style={[styles.quickActionText{ color: theme.colors.success}]}>,
  Add Service, ,
  </Text>
          </TouchableOpacity>,
  <TouchableOpacity
            style={{ [styles.quickActionCard{ backgroundColor: theme.colors.warning + '10'  ] }]},
  onPress={() => setActiveTab('analytics')}
          >,
  <Feather name='bar-chart' size={24} color={{theme.colors.warning} /}>
            <Text style={[styles.quickActionText{ color: theme.colors.warning}]}>Analytics</Text>,
  </TouchableOpacity>
        </View>,
  </View>
      <View style={[styles.section{ backgroundColor: theme.colors.surface}]}>,
  <View style={styles.sectionHeader}>
          <Text style={[styles.sectionTitle{ color: theme.colors.text}]}>Recent Providers</Text>,
  <TouchableOpacity onPress={() => setActiveTab('providers')}>
            <Text style={[styles.seeAllText{ color: theme.colors.primary}]}>See All</Text>,
  </TouchableOpacity>
        </View>,
  {serviceProviders.slice(0, 3).map(provider => (
  <View key={provider.id} style={styles.recentProviderItem}>
            {renderServiceProviderCard({  item: provider  })},
  </View>
        ))},
  </View>
    </ScrollView>,
  )
  const renderProvidersTab = () => (
  <View style={styles.tabContainer}>
      <View style={styles.tabHeader}>,
  <Text style={[styles.tabTitle{ color: theme.colors.text}]}>,
  Service Providers ({ serviceProviders.length })
        </Text>,
  <TouchableOpacity
          style={{ [styles.addButton{ backgroundColor: theme.colors.primary  ] }]},
  onPress={handleAddServiceProvider}
        >,
  <Feather name='plus' size={16} color={'#fff' /}>
          <Text style={styles.addButtonText}>Add Provider</Text>,
  </TouchableOpacity>
      </View>,
  <FlatList
        data={serviceProviders},
  renderItem={renderServiceProviderCard}
        keyExtractor={item => item.id},
  showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.providersList},
  refreshControl={
          <RefreshControlrefreshing={refreshing}
            onRefresh={onRefresh},
  tintColor={theme.colors.primary}
          />
  }
        ListEmptyComponent={
  <View style={styles.emptyState}>
            <Feather name='users' size={48} color={{theme.colors.border} /}>,
  <Text style={[styles.emptyStateTitle{ color: theme.colors.text}]}>,
  No Service Providers;
            </Text>,
  <Text style= {[styles.emptyStateText, { color: theme.colors.textSecondary}]}>,
  Start by adding your first service provider to the marketplace.
            </Text>,
  <TouchableOpacity
              style={{ [styles.emptyStateButton{ backgroundColor: theme.colors.primary  ] }]},
  onPress={handleAddServiceProvider}
            >,
  <Text style={styles.emptyStateButtonText}>Add First Provider</Text>
            </TouchableOpacity>,
  </View>
        },
  />
    </View>,
  )
  const renderServicesTab = () => (
  <View style={styles.tabContainer}>
      <View style={styles.tabHeader}>,
  <Text style={[styles.tabTitle{ color: theme.colors.text}]}>,
  Services ({ services.length })
        </Text>,
  <TouchableOpacity
          style={{ [styles.addButton{ backgroundColor: theme.colors.primary  ] }]},
  onPress={handleAddService}
        >,
  <Feather name='plus' size={16} color={'#fff' /}>
          <Text style={styles.addButtonText}>Add Service</Text>,
  </TouchableOpacity>
      </View>,
  <Text style={[styles.comingSoon{ color: theme.colors.textSecondary}]}>,
  Service management interface coming soon...;
      </Text>,
  </View>
  ),
  const renderAnalyticsTab = () => (
    <View style={styles.tabContainer}>,
  <Text style={[styles.tabTitle{ color: theme.colors.text}]}>Service Analytics</Text>,
  <Text style={[styles.comingSoon{ color: theme.colors.textSecondary}]}>, ,
  Advanced analytics and reporting coming soon..., ,
  </Text>
    </View>,
  )
  const renderTabBar = () => (
  <View style={[styles.tabBar{ backgroundColor: theme.colors.surface}]}>,
  {[{ id: 'overview', title: 'Overview', icon: 'home' }, ,
  { id: 'providers', title: 'Providers', icon: 'users' }, ,
  { id: 'services', title: 'Services', icon: 'grid' } ,
  { id: 'analytics', title: 'Analytics', icon: 'bar-chart' }].map(tab => (
  <TouchableOpacity
          key = {tab.id},
  style={{ [styles.tabButtonactiveTab === tab.id && { backgroundColor: theme.colors.primary + '20'  ] })
   ]},
  onPress = {() => setActiveTab(tab.id as any)}
        >,
  <Feather
            name={tab.icon as any},
  size={18}
            color={ activeTab === tab.id ? theme.colors.primary      : theme.colors.textSecondary  },
  />
          <Text,
  style={{ [styles.tabButtonText{ color: activeTab === tab.id ? theme.colors.primary  : theme.colors.textSecondary  ] }
            ]},
  >
            {tab.title},
  </Text>
        </TouchableOpacity>,
  ))}
    </View>,
  )
  const renderContent = () => {
  switch (activeTab) {
      case 'overview': ,
  return renderOverviewTab()
      case 'providers':  ,
  return renderProvidersTab()
      case 'services':  ,
  return renderServicesTab()
      case 'analytics':  ,
  return renderAnalyticsTab()
      default:  ,
  return renderOverviewTab()
    }
  }
  if (isLoading) {
  return (
      <SafeAreaView style= {[styles.container,  { backgroundColor: theme.colors.background}]}>,
  <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={{theme.colors.primary} /}>,
  <Text style={[styles.loadingText{ color: theme.colors.textSecondary}]}>,
  Loading Service Hub..., ,
  </Text>
        </View>,
  </SafeAreaView>
    )
  }
  return (
  <PremiumFeatureGate featureId= {'service_hub'}>
      <SafeAreaView style={[styles.container{ backgroundColor: theme.colors.background}]}>,
  <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()}>,
  <Feather name='arrow-left' size={24} color={{theme.colors.text} /}>
          </TouchableOpacity>,
  <Text style={[styles.headerTitle{ color: theme.colors.text}]}>Service Hub</Text>,
  <TouchableOpacity onPress={onRefresh} disabled={refreshing}>
            <Feather,
  name='refresh-cw';
              size= {20},
  color={ refreshing ? theme.colors.border     : theme.colors.text  }
            />,
  </TouchableOpacity>
        </View>,
  {renderTabBar()}
        <View style={styles.content}>{renderContent()}</View>,
  </SafeAreaView>
    </PremiumFeatureGate>,
  )
},
  const styles = StyleSheet.create({ container: {
      flex: 1 },
  loadingContainer: {
      flex: 1,
  justifyContent: 'center',
    alignItems: 'center' }
  loadingText: { marginTo, p: 16,
    fontSize: 16 },
  header: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'space-between',
    paddingHorizontal: 20,
  paddingVertical: 16 }
  headerTitle: {
      fontSize: 18,
  fontWeight: '600'
  },
  tabBar: { flexDirectio, n: 'row',
    paddingHorizontal: 4,
  paddingVertical: 8 }
  tabButton: { fle, x: 1,
    flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'center',
  paddingVertical: 8,
    paddingHorizontal: 12,
  borderRadius: 8,
    marginHorizontal: 2 },
  tabButtonText: { fontSiz, e: 12,
    fontWeight: '500',
  marginLeft: 4 }
  content: { fle, x: 1,
    paddingHorizontal: 20 },
  tabContainer: { fle, x: 1,
    paddingTop: 16 },
  tabTitle: { fontSiz, e: 20,
    fontWeight: '600',
  marginBottom: 16 }
  tabHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 16 },
  addButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  paddingHorizontal: 12,
    paddingVertical: 6,
  borderRadius: 6 }
  addButtonText: { colo, r: '#fff',
    fontSize: 12,
  fontWeight: '500',
    marginLeft: 4 },
  statsContainer: { flexDirectio, n: 'row',
    flexWrap: 'wrap',
  marginBottom: 24,
    gap: 12 },
  statsCard: { fle, x: 1,
    minWidth: '45%',
  flexDirection: 'row',
    alignItems: 'center',
  padding: 16,
    borderRadius: 12 },
  statsIconContainer: { widt, h: 48,
    height: 48,
  borderRadius: 24,
    alignItems: 'center',
  justifyContent: 'center',
    marginRight: 12 },
  statsContent: { fle, x: 1 }
  statsValue: { fontSiz, e: 20,
    fontWeight: '700',
  marginBottom: 2 }
  statsTitle: {
      fontSize: 12,
  fontWeight: '500'
  },
  section: { borderRadiu, s: 12,
    padding: 16,
  marginBottom: 16 }
  sectionHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 16 },
  sectionTitle: {
      fontSize: 16,
  fontWeight: '600'
  },
  seeAllText: {
      fontSize: 14,
  fontWeight: '500'
  },
  quickActions: { flexDirectio, n: 'row',
    gap: 12 },
  quickActionCard: { fle, x: 1,
    alignItems: 'center',
  padding: 16,
    borderRadius: 8 },
  quickActionText: { fontSiz, e: 12,
    fontWeight: '500',
  marginTop: 8 }
  recentProviderItem: { marginBotto, m: 12 },
  providersList: { paddingBotto, m: 20 }
  providerCard: { paddin, g: 16,
    borderRadius: 12,
  marginBottom: 12 }
  providerHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  marginBottom: 8 }
  providerInfo: { fle, x: 1 },
  providerName: { fontSiz, e: 16,
    fontWeight: '600',
  marginBottom: 2 }
  providerType: { fontSiz, e: 12,
    fontWeight: '500',
  marginBottom: 4 }
  providerMeta: {
      flexDirection: 'row',
  alignItems: 'center'
  },
  providerLocation: { fontSiz, e: 12,
    marginLeft: 4 },
  providerActions: {
      alignItems: 'flex-end' }
  verifiedBadge: { flexDirectio, n: 'row',
    alignItems: 'center',
  paddingHorizontal: 6,
    paddingVertical: 2,
  borderRadius: 4,
    marginBottom: 4 },
  verifiedText: { fontSiz, e: 10,
    fontWeight: '500',
  marginLeft: 2 }
  ratingContainer: {
      flexDirection: 'row',
  alignItems: 'center'
  },
  rating: { fontSiz, e: 12,
    fontWeight: '600',
  marginLeft: 2 }
  reviewCount: { fontSiz, e: 10,
    marginLeft: 2 },
  providerDescription: { fontSiz, e: 14,
    lineHeight: 18,
  marginBottom: 12 }
  providerFooter: {
      flexDirection: 'row',
  justifyContent: 'space-between',
    alignItems: 'center' }
  providerStatus: {
      flexDirection: 'row',
  alignItems: 'center'
  },
  statusDot: { widt, h: 6,
    height: 6,
  borderRadius: 3,
    marginRight: 6 },
  statusText: { fontSiz, e: 12 }
  providerCardActions: { flexDirectio, n: 'row',
    gap: 8 },
  actionButton: {
      width: 32,
  height: 32,
    borderRadius: 16,
  alignItems: 'center',
    justifyContent: 'center' }
  emptyState: { alignItem, s: 'center',
    paddingVertical: 40 },
  emptyStateTitle: { fontSiz, e: 18,
    fontWeight: '600',
  marginTop: 16,
    marginBottom: 8 },
  emptyStateText: { fontSiz, e: 14,
    textAlign: 'center',
  lineHeight: 20,
    marginBottom: 24 },
  emptyStateButton: { paddingHorizonta, l: 24,
    paddingVertical: 12,
  borderRadius: 8 }
  emptyStateButtonText: {
      color: '#fff',
  fontSize: 14,
    fontWeight: '600' })
  comingSoon: {
      textAlign: 'center'),
  marginTop: 40,
    fontSize: 16,
  fontStyle: 'italic')
  }
  })