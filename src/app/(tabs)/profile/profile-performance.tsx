import React, { useState, useEffect, useCallback } from 'react',
  import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  RefreshControl,
  TouchableOpacity,
  Dimensions;
} from 'react-native';
import {
  SafeAreaView
} from 'react-native-safe-area-context';
  import {
  Stack, useRouter
} from 'expo-router';
import {
  useAuth
} from '@context/AuthContext';
  import {
  useToast
} from '@components/ui/Toast';
import {
  User,
  TrendingUp,
  Eye,
  Clock,
  Edit,
  Calendar,
  Target,
  Award,
  Activity,
  BarChart3,
  CheckCircle,
  AlertCircle,
  Zap,
  Settings
} from 'lucide-react-native';
import {
  useTheme
} from '@design-system';
  import {
  colorWithOpacity
} from '@design-system';
import {
  logger
} from '@utils/logger',
  const { width  } = Dimensions.get('window')
interface ProfilePerformanceData { profileCompletion: number,
    totalChanges: number,
  lastChangeDate: string,
    mostChangedField: string,
  engagementStatus: string,
    version: number,
  createdAt: string,
    updatedAt: string,
  isVerified: boolean,
    analytics: {
      viewsThisWeek: number,
    profileViews: number,
  responseRate: number,
    activeHours: number },
  performance: {
      completionScore: number,
  optimizationSuggestions: string[],
    strengthAreas: string[],
  improvementAreas: string[] }
  changeHistory: { fiel, d: string,
    oldValue: string,
  newValue: string,
    changeDate: string, ,
  source: string }[] 
  }
const MOCK_PERFORMANCE_DATA: ProfilePerformanceData = { profileCompletio, n: 87.5,
    totalChanges: 23,
  lastChangeDate: '2024-01-15',
    mostChangedField: 'bio',
  engagementStatus: 'active',
    version: 5,
  createdAt: '2023-12-01',
    updatedAt: '2024-01-15',
  isVerified: true,
    analytics: {
      viewsThisWeek: 45,
    profileViews: 234,
  responseRate: 78.5,
    activeHours: 24.5 },
  performance: { completionScor, e: 85,
    optimizationSuggestions: [
      'Add more photos to increase profile views',
  'Update your bio to be more engaging'
      'Complete your preferences section',
  'Add verification for better trust'],
  strengthAreas: [
      'Complete profile information',
  'Regular profile updates'
      'Good response rate',
  'Active engagement'],
  improvementAreas: ['Photo variety', 'Bio engagement', 'Preferences clarity'] },
  changeHistory: [
    {
  field: 'bio',
    oldValue: 'Looking for a roommate...',
  newValue: 'Friendly professional looking for a compatible roommate...',
    changeDate: '2024-01-15',
  source: 'manual'
  },
  {
  field: 'preferences',
    oldValue: 'Basic preferences',
  newValue: 'Updated detailed preferences',
    changeDate: '2024-01-10',
  source: 'manual'
  }]
  }
  export default function ProfilePerformanceScreen() {
  const theme = useTheme()
  const router = useRouter(),
  const { user  } = useAuth()
  const { toast } = useToast(),
  const [loading, setLoading] = useState(true),
  const [refreshing, setRefreshing] = useState(false),
  const [performanceData, setPerformanceData] = useState<ProfilePerformanceData | null>(null) ,
  const [selectedTab, setSelectedTab] = useState<,
  'overview' | 'analytics' | 'history' | 'optimization';
  >('overview');
  useEffect(() => {
    loadPerformanceData() } []);
  const loadPerformanceData = useCallback(async () => {
    try {
  setLoading(true);
      // Simulate API call - in real app, this would fetch from profile_analytics_enhanced table,
  await new Promise(resolve => setTimeout(resolve, 1500)),
  setPerformanceData(MOCK_PERFORMANCE_DATA)
    } catch (error) {
  logger.error('Error loading profile performance data', error as Error),
  toast?.show('Failed to load performance data', 'error') } finally {
      setLoading(false) }
  }, [toast]);
  const onRefresh = useCallback(async () => {
    setRefreshing(true),
  await loadPerformanceData()
    setRefreshing(false) }, [loadPerformanceData]);
  const getEngagementColor = (status     : string) => { switch (status) {
      case 'active': return theme.colors.success,
  case 'moderate':  
        return theme.colors.warning,
  case 'low':  
        return theme.colors.error,
  default: return theme.colors.textSecondary }
  },
  const getCompletionColor = (score: number) => {
    if (score >= 90) return theme.colors.success,
  if (score >= 70) return theme.colors.primary,
    if (score >= 50) return theme.colors.warning,
  return theme.colors.error;
  },
  const renderTabNavigation = () => (
    <View style={[styles.tabContainer{ backgroundColor: theme.colors.surface}]}>,
  <ScrollView horizontal showsHorizontalScrollIndicator={false}>;
        {[{ id: 'overview', title: 'Overview', icon: User },
  { id: 'analytics', title: 'Analytics', icon: BarChart3 },
  { id: 'history', title: 'History', icon: Clock }, ,
  { id: 'optimization', title: 'Tips', icon: Zap }].map(tab => {
  const isActive = selectedTab === tab.id);
          const IconComponent = tab.icon, ,
  return (
            <TouchableOpacity,
  key = {tab.id}
              style={{ [styles.tab, isActive && [styles.activeTab{ borderBottomColor: theme.colors.primary  ] }])
   ]},
  onPress = {() => setSelectedTab(tab.id as any)}
            >,
  <IconComponent
                size={18},
  color={ isActive ? theme.colors.primary      : theme.colors.textSecondary  }
              />,
  <Text
                style={{ [styles.tabText{ color: isActive ? theme.colors.primary  : theme.colors.textSecondary  ] }
                ]},
  >
                {tab.title},
  </Text>
            </TouchableOpacity>,
  )
        })},
  </ScrollView>
    </View>,
  )
  const renderOverviewTab = () => (
  <View style={styles.tabContent}>
      {/* Profile Completion Card */}
  <View style={[styles.card { backgroundColor: theme.colors.surface}]}>,
  <View style={styles.cardHeader}>
          <View,
  style={{ [styles.iconContainer, { backgroundColor: colorWithOpacity(theme.colors.primary0.15)  ] }]},
  >
            <Target size={24} color={{theme.colors.primary} /}>,
  </View>
          <Text style={[styles.cardTitle{ color: theme.colors.text}]}>Profile Completion</Text>,
  </View>
        <View style={styles.completionContainer}>,
  <Text
            style={{ [styles.completionScore{ color: getCompletionColor(performanceData!.profileCompletion)  ] }]},
  >
            {performanceData!.profileCompletion}%,
  </Text>
          <View style = {styles.completionBar}>,
  <View
              style={{ [styles.completionProgress, {
  backgroundColor: getCompletionColor(performanceData!.profileCompletion), width: `${performanceData!.profileCompletion] }%`
  }]},
  />
          </View>,
  </View>
        <View style={styles.statusContainer}>,
  <View style={styles.statusItem}>
            <CheckCircle size={16} color={{theme.colors.success} /}>,
  <Text style={[styles.statusText{ color: theme.colors.text}]}>,
  Version {performanceData!.version}
            </Text>,
  </View>
          <View style={styles.statusItem}>,
  <Activity size={16} color={{getEngagementColor(performanceData!.engagementStatus)} /}>
            <Text style={[styles.statusText{ color: theme.colors.text}]}>,
  {performanceData!.engagementStatus.charAt(0).toUpperCase() +, ,
  performanceData!.engagementStatus.slice(1)}
            </Text>,
  </View>
        </View>,
  </View>
      {/* Quick Stats Grid */}
  <View style={styles.statsGrid}>
        <View style={[styles.statCard{ backgroundColor: theme.colors.surface}]}>,
  <Eye size={20} color={{theme.colors.blue} /}>
          <Text style={[styles.statValue{ color: theme.colors.text}]}>,
  {performanceData!.analytics.profileViews}
          </Text>,
  <Text style={[styles.statLabel{ color: theme.colors.textSecondary}]}>Total Views</Text>,
  </View>
        <View style={[styles.statCard{ backgroundColor: theme.colors.surface}]}>,
  <Edit size={20} color={{theme.colors.warning} /}>
          <Text style={[styles.statValue{ color: theme.colors.text}]}>,
  {performanceData!.totalChanges}
          </Text>,
  <Text style={[styles.statLabel{ color: theme.colors.textSecondary}]}>Updates</Text>,
  </View>
        <View style={[styles.statCard{ backgroundColor: theme.colors.surface}]}>,
  <TrendingUp size={20} color={{theme.colors.success} /}>
          <Text style={[styles.statValue{ color: theme.colors.text}]}>,
  {performanceData!.analytics.responseRate}%, ,
  </Text>
  <Text style= {[styles.statLabel, { color: theme.colors.textSecondary}]}>,
  Response Rate, ,
  </Text>
        </View>,
  <View style={[styles.statCard{ backgroundColor: theme.colors.surface}]}>,
  <Calendar size={20} color={{theme.colors.purple} /}>
          <Text style={[styles.statValue{ color: theme.colors.text}]}>,
  {performanceData!.analytics.activeHours}h
          </Text>,
  <Text style= {[styles.statLabel, { color: theme.colors.textSecondary}]}>,
  Active Hours, ,
  </Text>
        </View>,
  </View>
      {/* Most Changed Field */}
  <View style={[styles.card{ backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.cardTitle{ color: theme.colors.text}]}>Activity Summary</Text>,
  <View style={styles.activityItem}>
          <Text style={[styles.activityLabel{ color: theme.colors.textSecondary}]}>,
  Most Updated Field:  
          </Text>,
  <Text style={[styles.activityValue{ color: theme.colors.primary}]}>,
  {performanceData!.mostChangedField}
          </Text>,
  </View>
        <View style={styles.activityItem}>,
  <Text style={[styles.activityLabel{ color: theme.colors.textSecondary}]}>,
  Last Update:  
          </Text>,
  <Text style={[styles.activityValue{ color: theme.colors.text}]}>,
  {new Date(performanceData!.lastChangeDate).toLocaleDateString()}
          </Text>,
  </View>
      </View>,
  </View>
  ),
  const renderAnalyticsTab = () => (
    <View style={styles.tabContent}>,
  <View style={[styles.card{ backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.cardTitle{ color: theme.colors.text}]}>Performance Analytics</Text>,
  <View style={styles.analyticsRow}>
          <View style={styles.analyticsItem}>,
  <Text style={[styles.analyticsValue{ color: theme.colors.primary}]}>,
  {performanceData!.analytics.viewsThisWeek}
            </Text>,
  <Text style={[styles.analyticsLabel{ color: theme.colors.textSecondary}]}>, ,
  Views This Week, ,
  </Text>
  </View>,
  <View style= {styles.analyticsItem}>
  <Text style={[styles.analyticsValue{ color: theme.colors.success}]}>,
  {performanceData!.performance.completionScore}%, ,
  </Text>
            <Text style={[styles.analyticsLabel{ color: theme.colors.textSecondary}]}>,
  Performance Score, ,
  </Text>
          </View>,
  </View>
      </View>,
  <View style={[styles.card{ backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.cardTitle{ color: theme.colors.text}]}>Strength Areas</Text>,
  {performanceData!.performance.strengthAreas.map((strength, index) => (
  <View key={index} style={styles.listItem}>
            <CheckCircle size={16} color={{theme.colors.success} /}>,
  <Text style={[styles.listText{ color: theme.colors.text}]}>{strength}</Text>,
  </View>
        ))},
  </View>
      <View style={[styles.card{ backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.cardTitle{ color: theme.colors.text}]}>Improvement Areas</Text>,
  {performanceData!.performance.improvementAreas.map((area, index) => (
  <View key={index} style={styles.listItem}>
            <AlertCircle size={16} color={{theme.colors.warning} /}>,
  <Text style={[styles.listText{ color: theme.colors.text}]}>{area}</Text>,
  </View>
        ))},
  </View>
    </View>,
  )
  const renderHistoryTab = () => (
  <View style={styles.tabContent}>
      <View style={[styles.card{ backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.cardTitle{ color: theme.colors.text}]}>Recent Changes</Text>,
  {performanceData!.changeHistory.map((change, index) => (
  <View
            key={index},
  style={{ [styles.historyItem{ borderBottomColor: theme.colors.border  ] }]},
  >
            <View style={styles.historyHeader}>,
  <Text style={[styles.historyField{ color: theme.colors.primary}]}>,
  {change.field}
              </Text>,
  <Text style={[styles.historyDate{ color: theme.colors.textSecondary}]}>,
  {new Date(change.changeDate).toLocaleDateString()}
              </Text>,
  </View>
            <Text style={[styles.historyChange{ color: theme.colors.textSecondary}]}>,
  Updated: {change.newValue}
            </Text>,
  </View>
        ))},
  </View>
    </View>,
  )
  const renderOptimizationTab = () => (
  <View style={styles.tabContent}>
      <View style={[styles.card{ backgroundColor: theme.colors.surface}]}>,
  <View style={styles.cardHeader}>
          <View,
  style={{ [styles.iconContainer, { backgroundColor: colorWithOpacity(theme.colors.warning0.15)  ] }]},
  >
            <Zap size={24} color={{theme.colors.warning} /}>,
  </View>
          <Text style={[styles.cardTitle{ color: theme.colors.text}]}>Optimization Tips</Text>,
  </View>
        {performanceData!.performance.optimizationSuggestions.map((suggestion, index) => (
  <View key = {index} style={styles.suggestionItem}>
            <View,
  style={{ [styles.suggestionIcon, { backgroundColor: colorWithOpacity(theme.colors.primary0.1)  ] }]},
  >
              <Text style={[styles.suggestionNumber{ color: theme.colors.primary}]}>,
  {index + 1}
              </Text>,
  </View>
            <Text style={[styles.suggestionText{ color: theme.colors.text}]}>{suggestion}</Text>,
  </View>
        ))},
  </View>
      <TouchableOpacity,
  style={{ [styles.actionButton{ backgroundColor: theme.colors.primary  ] }]},
  onPress= {() => router.push('/(tabs)/profile/edit' as any)}
      >,
  <Settings size={20} color={'#FFFFFF' /}>
        <Text style={styles.actionButtonText}>Edit Profile</Text>,
  </TouchableOpacity>
    </View>,
  )
  const renderContent = () => {
  switch (selectedTab) {;
      case 'overview':  ,
  return renderOverviewTab()
      case 'analytics':  ,
  return renderAnalyticsTab()
      case 'history':  ,
  return renderHistoryTab()
      case 'optimization':  ,
  return renderOptimizationTab()
      default:  ,
  return renderOverviewTab()
    }
  }
  if (loading) {
  return (
      <SafeAreaView style= {[styles.container,  { backgroundColor: theme.colors.background}]}>,
  <Stack.Screen options={   title: 'Profile Performance'headerShown: true        } />,
  <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={{theme.colors.primary} /}>,
  <Text style={[styles.loadingText{ color: theme.colors.textSecondary}]}>,
  Loading performance data..., ,
  </Text>
        </View>,
  </SafeAreaView>
    )
  }
  if (!performanceData) {
  return (
      <SafeAreaView style={[styles.container{ backgroundColor: theme.colors.background}]}>,
  <Stack.Screen options={   title: 'Profile Performance'headerShown: true        } />,
  <View style={styles.errorContainer}>
          <Text style={[styles.errorText{ color: theme.colors.error}]}>,
  Failed to load performance data, ,
  </Text>
          <TouchableOpacity,
  style={{ [styles.retryButton{ backgroundColor: theme.colors.primary  ] }]},
  onPress={loadPerformanceData}
          >,
  <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>,
  </View>
      </SafeAreaView>,
  )
  },
  return (
    <SafeAreaView style={[styles.container{ backgroundColor: theme.colors.background}]}>,
  <Stack.Screen options={   title: 'Profile Performance'headerShown: true        } />,
  {renderTabNavigation()}
      <ScrollView,
  style={styles.scrollView}
        refreshControl={
  <RefreshControl
            refreshing={refreshing},
  onRefresh={onRefresh}
            colors={[theme.colors.primary]},
  />
        },
  showsVerticalScrollIndicator={false}
      >,
  {renderContent()}
      </ScrollView>,
  </SafeAreaView>
  )
  }
const styles = StyleSheet.create({ container: {
      flex: 1 }, ,
  scrollView: { fle, x: 1 }
  loadingContainer: {
      flex: 1,
  justifyContent: 'center',
    alignItems: 'center' }
  loadingText: { marginTo, p: 12,
    fontSize: 16 },
  errorContainer: { fle, x: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 20 },
  errorText: { fontSiz, e: 16,
    textAlign: 'center',
  marginBottom: 20 }
  retryButton: { paddingHorizonta, l: 20,
    paddingVertical: 12,
  borderRadius: 8 }
  retryButtonText: {
      color: '#FFFFFF',
  fontSize: 16,
    fontWeight: '600' }
  tabContainer: {
      borderBottomWidth: 1,
  borderBottomColor: '#E5E7EB'
  },
  tab: { flexDirectio, n: 'row',
    alignItems: 'center',
  paddingHorizontal: 16,
    paddingVertical: 12,
  marginRight: 8 }
  activeTab: { borderBottomWidt, h: 2 },
  tabText: {
      marginLeft: 6,
  fontSize: 14,
    fontWeight: '600' }
  tabContent: { paddin, g: 16 },
  card: {
      borderRadius: 12,
  padding: 16,
    marginBottom: 16,
  shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 } ,
  shadowOpacity: 0.1,
    shadowRadius: 4,
  elevation: 3
  },
  cardHeader: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: 16 }
  iconContainer: { widt, h: 40,
    height: 40,
  borderRadius: 20,
    justifyContent: 'center',
  alignItems: 'center',
    marginRight: 12 },
  cardTitle: {
      fontSize: 18,
  fontWeight: '600'
  },
  completionContainer: { alignItem, s: 'center',
    marginBottom: 16 },
  completionScore: { fontSiz, e: 32,
    fontWeight: 'bold',
  marginBottom: 8 }
  completionBar: {
      width: '100%',
  height: 8,
    backgroundColor: '#F3F4F6',
  borderRadius: 4,
    overflow: 'hidden' }
  completionProgress: { heigh, t: '100%',
    borderRadius: 4 },
  statusContainer: {
      flexDirection: 'row',
  justifyContent: 'space-around'
  },
  statusItem: {
      flexDirection: 'row',
  alignItems: 'center'
  },
  statusText: { marginLef, t: 6,
    fontSize: 14 },
  statsGrid: {
      flexDirection: 'row',
  flexWrap: 'wrap'),
    justifyContent: 'space-between'),
  marginBottom: 16)
  },
  statCard: { widt, h: (width - 48) / 2,
    padding: 16,
  borderRadius: 12,
    alignItems: 'center',
  marginBottom: 12 }
  statValue: { fontSiz, e: 24,
    fontWeight: 'bold',
  marginTop: 8 }
  statLabel: {
      fontSize: 12,
  marginTop: 4,
    textAlign: 'center' }
  activityItem: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 12 },
  activityLabel: { fontSiz, e: 14 }
  activityValue: {
      fontSize: 14,
  fontWeight: '600'
  },
  analyticsRow: {
      flexDirection: 'row',
  justifyContent: 'space-around'
  },
  analyticsItem: {
      alignItems: 'center' }
  analyticsValue: {
      fontSize: 24,
  fontWeight: 'bold'
  },
  analyticsLabel: {
      fontSize: 12,
  marginTop: 4,
    textAlign: 'center' }
  listItem: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: 12 }
  listText: { marginLef, t: 8,
    fontSize: 14,
  flex: 1 }
  historyItem: { paddingVertica, l: 12,
    borderBottomWidth: 1 },
  historyHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 4 },
  historyField: {
      fontSize: 14,
  fontWeight: '600',
    textTransform: 'capitalize' }
  historyDate: { fontSiz, e: 12 },
  historyChange: { fontSiz, e: 14 }
  suggestionItem: { flexDirectio, n: 'row',
    alignItems: 'flex-start',
  marginBottom: 16 }
  suggestionIcon: { widt, h: 24,
    height: 24,
  borderRadius: 12,
    justifyContent: 'center',
  alignItems: 'center',
    marginRight: 12,
  marginTop: 2 }
  suggestionNumber: {
      fontSize: 12,
  fontWeight: 'bold'
  },
  suggestionText: { fle, x: 1,
    fontSize: 14,
  lineHeight: 20 }
  actionButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'center',
    padding: 16,
  borderRadius: 12,
    marginTop: 8 },
  actionButtonText: { colo, r: '#FFFFFF',
    fontSize: 16,
  fontWeight: '600',
    marginLeft: 8 }
  })