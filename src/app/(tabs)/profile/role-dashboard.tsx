import React, { useState, useEffect, useCallback } from 'react';
  import {
  View, Text, StyleSheet, ScrollView, ActivityIndicator, TouchableOpacity, Dimensions, RefreshControl, useColorScheme
} from 'react-native';
import {
  Stack, useRouter
} from 'expo-router';
import {
  SafeAreaView
} from 'react-native-safe-area-context';
  import {
  useAuth
} from '@context/AuthContext';
import {
  unifiedProfileService
} from '@services/unified-profile';
  import {
  useToast
} from '@components/ui/Toast';
import {
  logger
} from '@utils/logger';
  import {
  useTheme
} from '@design-system';
import {
  colorWithOpacity
} from '@design-system';
  import {
  User, Home, Briefcase, Shield, ChevronRight, Settings, Award, Star, TrendingUp, Users, Calendar, DollarSign, MapPin, Clock, CheckCircle, AlertCircle, RefreshCw, Zap, Target, BarChart3, Building, UserCheck, Crown, Sparkles, ArrowLeft, Save
} from 'lucide-react-native';

const { width  } = Dimensions.get('window'),
  // Enhanced role data structure,
interface RoleData {
  role: 'roommate_seeker' | 'property_owner' | 'service_provider' | 'admin',
    label: string,
  description: string,
    icon: any,
  color: string,
    completion_percentage: number,
  is_active: boolean,
    quick_actions: QuickAction[],
  stats: RoleStats,
    missing_components: string[] }
interface QuickAction { id: string,
    title: string,
  description: string,
    icon: any,
  route: string,
    priority: 'high' | 'medium' | 'low',
  estimated_time: string }
  interface RoleStats { primary_metric: {, label: string,
  value: string | number
    change?: string,
  trend?: 'up' | 'down' | 'stable' }
  secondary_metrics: Array<{ labe, l: string,
    value: string | number,
  icon: any }>
  },
  // Use centralized color utility for React Native compatibility, ,
  export default function EnhancedRoleDashboardScreen() {
  const { user  } = useAuth(),
  const colorScheme = useColorScheme()
  const theme = useTheme(),
  const router = useRouter()
  const { toast } = useToast(),
  const [loading, setLoading] = useState(true),
  const [refreshing, setRefreshing] = useState(false), ,
  const [currentRole, setCurrentRole] = useState<, ,
  'roommate_seeker' | 'property_owner' | 'service_provider' | 'admin'
  >('roommate_seeker'),
  const [availableRoles, setAvailableRoles] = useState<RoleData[]>([]),
  const [profileData, setProfileData] = useState<any>(null),
  const [overallCompletion, setOverallCompletion] = useState(0),
  useEffect(() => {
  fetchRoleData() }, []);
  const fetchRoleData = useCallback(async () => {
  if (!user?.id) return null,
  try {
      setLoading(true),
  // Get current profile data,
      const { data     : profile error  } = await unifiedProfileService.getCurrentProfile(),
  if (error) throw new Error(error)
      setProfileData(profile),
  setCurrentRole(profile?.role || 'roommate_seeker')
      // Generate role data based on user's current role and potential roles,
  const roleData = await generateRoleData(profile)
      setAvailableRoles(roleData),
  // Calculate overall completion,
      const totalCompletion = roleData.reduce((sum, role) => sum + role.completion_percentage, 0),
  setOverallCompletion(Math.round(totalCompletion / roleData.length))
    } catch (error) {
  logger.error('Error fetching role data', ,
  'EnhancedRoleDashboardScreen.fetchRoleData');
        error as Error),
  )
      toast?.show('Failed to load role data', 'error') } finally {
      setLoading(false),
  setRefreshing(false)
    }
  }, [user?.id, toast]);
  const generateRoleData = async (profile   : any): Promise<RoleData[]> => {
  const currentUserRole = profile?.role || 'roommate_seeker'

    // Define all possible roles with their configurations, ,
  const roleConfigs = {
      roommate_seeker : {
  role: 'roommate_seeker' as const,
    label: 'Roommate Seeker',
  description: 'Find your perfect roommate and living situation',
    icon: Users,
  color: theme.colors.primary,
    is_active: currentUserRole === 'roommate_seeker',
  quick_actions: [
          {
  id: 'complete_personality',
    title: 'Complete Personality Assessment',
  description: 'Help others understand your personality',
    icon: User,
  route: '/(tabs)/profile/personality',
    priority: 'high' as const,
  estimated_time: '10 minutes'
  },
  {
  id: 'set_preferences',
    title: 'Set Living Preferences',
  description: 'Define your ideal living situation',
    icon: Home,
  route: '/(tabs)/profile/lifestyle',
    priority: 'high' as const,
  estimated_time: '15 minutes'
  },
  {
  id: 'verify_identity',
    title: 'Verify Identity',
  description: 'Build trust with potential roommates',
    icon: Shield,
  route: '/(tabs)/profile/verification-dashboard',
    priority: 'medium' as const,
  estimated_time: '5 minutes'
  }],
  stats: {, primary_metric: {
  label: 'Profile Strength',
  value: `${profile?.profile_completion || 0}%`,
  change     : '+12%'
  trend: 'up' as const
  }
          secondary_metrics: [
            { label: 'Matches', value: '24', icon: Users },
  { label: 'Views', value: '156', icon: TrendingUp }, ,
  { label: 'Trust Score', value: '85', icon: Award }]
  }
      },
  property_owner: {, role: 'property_owner' as const,
  label: 'Property Owner',
    description: 'Manage your properties and find quality tenants',
  icon: Building,
    color: theme.colors.success,
  is_active: currentUserRole === 'property_owner',
    quick_actions: [
          {
  id: 'add_property',
    title: 'Add Property',
  description: 'List a new property for rent',
    icon: Home,
  route: '/property/add',
    priority: 'high' as const,
  estimated_time: '20 minutes'
  },
  {
  id: 'manage_tenants',
    title: 'Manage Tenants',
  description: 'View and communicate with tenants',
    icon: Users,
  route: '/property/tenants',
    priority: 'medium' as const,
  estimated_time: '5 minutes'
  },
  {
  id: 'financial_overview',
    title: 'Financial Overview',
  description: 'Track rent and expenses',
    icon: DollarSign,
  route: '/property/finances',
    priority: 'medium' as const,
  estimated_time: '3 minutes'
  }],
  stats: { primary_metric: {, label: 'Monthly Revenue',
  value: '$3,240',
  change: '+8%',
    trend: 'up' as const },
  secondary_metrics: [
            { label: 'Properties', value: '3', icon: Building },
  { label: 'Occupancy', value: '92%', icon: Users },
  { label: 'Rating', value: '4.8', icon: Star }]
  }
      },
  service_provider: {, role: 'service_provider' as const,
  label: 'Service Provider',
    description: 'Offer services to the roommate community',
  icon: Briefcase,
    color: theme.colors.warning,
  is_active: currentUserRole === 'service_provider',
    quick_actions: [
          {
  id: 'update_services',
    title: 'Update Services',
  description: 'Manage your service offerings',
    icon: Briefcase,
  route: '/provider/services',
    priority: 'high' as const,
  estimated_time: '15 minutes'
  },
  {
  id: 'view_bookings',
    title: 'View Bookings',
  description: 'Check upcoming appointments',
    icon: Calendar,
  route: '/provider/bookings',
    priority: 'high' as const,
  estimated_time: '2 minutes'
  },
  {
  id: 'performance_analytics',
    title: 'Performance Analytics',
  description: 'Review your business metrics',
    icon: BarChart3,
  route: '/provider/analytics',
    priority: 'medium' as const,
  estimated_time: '5 minutes'
  }],
  stats: { primary_metric: {, label: 'Monthly Earnings',
  value: '$1,850',
  change: '+15%',
    trend: 'up' as const },
  secondary_metrics: [
            { label: 'Bookings', value: '28', icon: Calendar },
  { label: 'Rating', value: '4.9', icon: Star },
  { label: 'Clients', value: '45', icon: Users }]
  }
      },
  admin: {, role: 'admin' as const,
  label: 'Administrator',
    description: 'Manage platform operations and user experience',
  icon: Crown,
    color: theme.colors.error,
  is_active: currentUserRole === 'admin',
    quick_actions: [
          {
  id: 'user_management',
    title: 'User Management',
  description: 'Manage platform users',
    icon: Users,
  route: '/admin/users',
    priority: 'high' as const,
  estimated_time: '10 minutes'
  },
  {
  id: 'content_moderation',
    title: 'Content Moderation',
  description: 'Review flagged content',
    icon: Shield,
  route: '/admin/content-moderation',
    priority: 'high' as const,
  estimated_time: '15 minutes'
  },
  {
  id: 'platform_analytics',
    title: 'Platform Analytics',
  description: 'View platform performance',
    icon: BarChart3,
  route: '/admin/analytics',
    priority: 'medium' as const,
  estimated_time: '5 minutes'
  }],
  stats: { primary_metric: {, label: 'Active Users',
  value: '2,847',
  change: '+23%',
    trend: 'up' as const },
  secondary_metrics: [
            { label: 'Reports', value: '12', icon: AlertCircle },
  { label: 'Verifications', value: '89', icon: CheckCircle },
  { label: 'Revenue', value: '$12.4K', icon: DollarSign }]
  }
      }
  }
  // Calculate completion percentage for each role,
  const rolesWithCompletion = Object.values(roleConfigs).map(roleConfig => {
  let completion = 50 // Base completion for having the role, ,
  // Add completion based on profile completeness)
      if (profile?.profile_completion) {
  completion += Math.round(profile.profile_completion * 0.3)
      },
  // Add completion based on verification status,
      if (profile?.is_verified) {
  completion += 15;
      },
  // Role-specific completion bonuses,
      if (roleConfig.role === currentUserRole) {
  completion += 10 // Bonus for active role;
      },
  // Cap at 100%;
      completion = Math.min(completion, 100),
  return {
        ...roleConfig,
  completion_percentage    : completion
        missing_components: generateMissingComponents(completion) }
    }),
  // Return only the current role and potential roles
    return rolesWithCompletion.filter(role =>,
  role.role === currentUserRole ||
        (currentUserRole === 'roommate_seeker' &&),
  ['property_owner',  'service_provider'].includes(role.role)),
  )
  },
  const generateMissingComponents = (completion: number) => {
  if (completion >= 90) return [], ,
  const missingComponents = [],
  if (completion < 70) {
      missingComponents.push('Complete profile information') }
    if (completion < 80) {
  missingComponents.push('Verify identity')
    },
  if (completion < 90) {
      missingComponents.push('Add profile photos') }
    return missingComponents
  }
  const handleRoleSwitch = async (newRole: string) => {
  try {;
      // In a real implementation, this would update the user's role in the database,
  showSuccess(`Switched to ${newRole.replace('_', ' ')} role`),
  setCurrentRole(newRole as any)
      await fetchRoleData()
  } catch (error) {
      showError('Failed to switch role') }
  },
  const handleRefresh = useCallback(() => {
  setRefreshing(true),
  fetchRoleData()
  }, [fetchRoleData]);
  if (loading) {
    return (
  <SafeAreaView
        style={{ [styles.container, { backgroundColor: theme.colors.background  ] }]},
  edges={['top']},
  >, ,
  <Stack.Screen, ,
  options={   {
            title: 'Role Dashboard',
    headerShadowVisible: false,
  headerStyle: { backgroundColo, r: theme.colors.background       } 
  }},
  />
  <View style={styles.loadingContainer}>,
  <ActivityIndicator size="large" color={{theme.colors.primary} /}>
  <Text style={[styles.loadingText, { color: theme.colors.text}]}>Loading role data...</Text>,
  </View>
      </SafeAreaView>,
  )
  },
  const currentRoleData = availableRoles.find(role => role.role === currentRole)
  const renderRoleOverview = () => (
  <View style={[styles.overviewCard, { backgroundColor: theme.colors.surface}]}>,
  <View style={styles.overviewHeader}>
        <View style={styles.roleInfo}>,
  <View style={[styles.roleIcon, { backgroundColor: currentRoleData?.color + '20'}]}>,
  {currentRoleData?.icon && (
              <currentRoleData.icon size={24} color={currentRoleData.color} />,
  )}
          </View>,
  <View style={styles.roleDetails}>
            <Text style={[styles.roleTitle, { color     : theme.colors.text}]}>{currentRoleData?.label}</Text>,
  <Text style={[styles.roleDescription { color: theme.colors.textSecondary}]}>,
  {currentRoleData?.description}
            </Text>,
  </View>
        </View>,
  <TouchableOpacity style={styles.settingsButton} onPress={() => router.push('/(tabs)/profile/unified-settings' as any)}
        >,
  <Settings size={20} color={{theme.colors.textSecondary} /}>
        </TouchableOpacity>,
  </View>
      <View style={styles.completionSection}>,
  <View style={styles.completionHeader}>
          <Text style={[styles.completionTitle, { color : theme.colors.text}]}>Role Completion</Text>,
  <Text style={[styles.completionPercentage { color: currentRoleData?.color}]}>,
  {currentRoleData?.completion_percentage}%
          </Text>,
  </View>
        <View style={[styles.progressBar, { backgroundColor: theme.colors.border}]}>,
  <View
            style={{ [styles.progressFill, {
  width: `${currentRoleData?.completion_percentage  ] }%`
                backgroundColor  : currentRoleData?.color
  }]},
  />
        </View>,
  {currentRoleData?.missing_components && currentRoleData.missing_components.length > 0 && (
          <Text style={[styles.missingComponents { color  : theme.colors.textSecondary}]}>,
  Missing: {currentRoleData.missing_components.join(', ')},
  </Text>
        )},
  </View>
    </View>,
  )

  const renderRoleStats = () => (
  <View style={[styles.statsCard, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.sectionTitle, { color: theme.colors.text}]}>Performance Overview</Text>,
  <View style={styles.primaryMetric}>
        <View style={styles.primaryMetricContent}>,
  <Text style={[styles.primaryMetricLabel, { color: theme.colors.textSecondary}]}>,
  {currentRoleData?.stats.primary_metric.label}
          </Text>,
  <View style={styles.primaryMetricValue}>
            <Text style={[styles.primaryMetricNumber, { color  : theme.colors.text}]}>,
  {currentRoleData?.stats.primary_metric.value}
            </Text>,
  {currentRoleData?.stats.primary_metric.change && (
                      <View style={[styles.changeIndicator { backgroundColor: colorWithOpacity(theme.colors.success, 0.2)}]}>,
  <TrendingUp size={12} color={{theme.colors.success} /}>
          <Text style={[styles.changeText, { color: theme.colors.success}]}>,
  {currentRoleData.stats.primary_metric.change}
                </Text>,
  </View>
            )},
  </View>
        </View>,
  </View>
      <View style={styles.secondaryMetrics}>,
  {currentRoleData?.stats.secondary_metrics.map((metric, index) => (
  <View key={index} style={styles.secondaryMetric}>
            <metric.icon size={16} color={theme.colors.textSecondary} />,
  <Text style={[styles.secondaryMetricLabel, { color : theme.colors.textSecondary}]}>,
  {metric.label}
            </Text>,
  <Text style={[styles.secondaryMetricValue { color: theme.colors.text}]}>,
  {metric.value}
            </Text>,
  </View>
        ))},
  </View>
    </View>,
  )

  const renderQuickActions = () => (
  <View style={[styles.actionsCard, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.sectionTitle, { color: theme.colors.text}]}>Quick Actions</Text>,
  {currentRoleData?.quick_actions.map((action, index) => (
  <TouchableOpacity, ,
  key = {action.id}
          style={{ [styles.actionItem, { backgroundColor  : action.priority === 'high' ? colorWithOpacity(theme.colors.primary 0.1)  : 'transparent'  ] }
   ]},
  onPress={() => router.push(action.route as any)}
          accessibilityRole="button",
  accessibilityLabel={`${action.title}: ${action.description}`}
        >,
  <View style={styles.actionContent}>
            <View style={styles.actionHeader}>,
  <View style={[styles.actionIcon, { backgroundColor: colorWithOpacity(theme.colors.primary, 0.2)}]}>,
  <action.icon size={20} color={theme.colors.primary} />
              </View>,
  <View style={styles.actionInfo}>
                <Text style={[styles.actionTitle, { color: theme.colors.text}]}>{action.title}</Text>,
  <Text style={[styles.actionDescription, { color: theme.colors.textSecondary}]}>,
  {action.description}
                </Text>,
  </View>
              <View style={styles.actionMeta}>,
  {action.priority === 'high' && (
                  <View style={[styles.priorityBadge, { backgroundColor: theme.colors.error}]}>,
  <Text style={styles.priorityText}>High</Text>
                  </View>,
  )}
                <ChevronRight size={16} color={{theme.colors.textSecondary} /}>,
  </View>
            </View>,
  <Text style={[styles.estimatedTime, { color: theme.colors.textSecondary}]}>,
  Estimated time: {action.estimated_time}
            </Text>,
  </View>
        </TouchableOpacity>,
  ))}
    </View>,
  )
  const renderAvailableRoles = () => (
  <View style={[styles.rolesCard, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.sectionTitle, { color: theme.colors.text}]}>Available Roles</Text>,
  {availableRoles.map((role, index) => (
  <TouchableOpacity key = {role.role} style={{ [styles.roleItem, { backgroundColor: role.is_active ? role.color + '10'   : 'transparent'  ] }
   ]},
  onPress={() => !role.is_active && handleRoleSwitch(role.role)} disabled={role.is_active} accessibilityRole="button"
          accessibilityLabel={`Switch to ${role.label} role`},
  >
          <View style={styles.roleItemContent}>,
  <View style={[styles.roleItemIcon { backgroundColor: role.color + '20'}]}>,
  <role.icon size={20} color={role.color} />
            </View>,
  <View style={styles.roleItemInfo}>
              <Text style={[styles.roleItemTitle, { color: theme.colors.text}]}>{role.label}</Text>,
  <Text style={[styles.roleItemDescription, { color: theme.colors.textSecondary}]}>,
  {role.completion_percentage}% complete
              </Text>,
  </View>
            <View style= {styles.roleItemActions}>,
  {role.is_active ? (
                <View style={[styles.activeBadge, { backgroundColor     : role.color}]}>,
  <CheckCircle size={12} color={"white" /}>
                  <Text style={styles.activeText}>Active</Text>,
  </View>
              ) : (
  <ChevronRight size={16} color={{theme.colors.textSecondary} /}>
              )},
  </View>
          </View>,
  </TouchableOpacity>
      ))},
  </View>
  ),
  return (
    <SafeAreaView,
  style={{ [styles.container { backgroundColor: theme.colors.background  ] }]},
  edges={['top']},
  >
      <Stack.Screen, ,
  options={   title: 'Role Dashboard',
    headerShown: false    },
  />
      {/* Enhanced Header */}
  <View style={{ [styles.headerContainer, {
  backgroundColor: theme.colors.surface,
    borderBottomColor: 'rgba(0, 0, 0, 0.05)',
  shadowColor: theme.colors.text,
    shadowOffset: { widt, h: 0, height: 2  ] },
  shadowOpacity: 0.05,
    shadowRadius: 8,
  elevation: 3
      }]}>,
  <TouchableOpacity style={styles.headerBackButton} onPress={() => router.back()}
        >,
  <View style={{ [backgroundColor: colorWithOpacity(theme.colors.primary, 0.15),
  borderRadius: 8,
    padding: 6]  ] }>,
  <ArrowLeft size={20} color={{theme.colors.primary} /}>
          </View>,
  </TouchableOpacity>
        <View style={styles.headerCenter}>,
  <View style={{
            backgroundColor: `${currentRoleData?.color || theme.colors.primary}15`
  borderRadius   : 10
            padding: 8,
    marginRight: 12
  }}>
  {currentRoleData?.icon ? (
  <currentRoleData.icon size= {24} color={currentRoleData.color} />
  )    : (<Crown size={24} color={{theme.colors.primary} /}>,
  )}
  </View>,
  <View>
  <Text style={[styles.headerTitle { color: theme.colors.text}]}>,
  Role Dashboard
            </Text>,
  <Text style={[styles.headerSubtitle, { color: theme.colors.textSecondary}]}>,
  {currentRoleData?.label || 'Managing roles'} • {overallCompletion}% complete, ,
  </Text>
          </View>,
  </View>
        <TouchableOpacity,
  style={{ [styles.refreshButton, {
  backgroundColor  : colorWithOpacity(theme.colors.success 0.15)
            shadowColor: theme.colors.success,
    shadowOffset: { widt, h: 0, height: 2  ] },
  shadowOpacity: 0.2,
    shadowRadius: 4,
  elevation: 3
          }]},
  onPress= {handleRefresh} disabled={refreshing}
        >,
  {refreshing ? (
            <ActivityIndicator size="small" color={{theme.colors.success} /}>,
  )    : (<RefreshCw size={18} color={{theme.colors.success} /}>
          )},
  </TouchableOpacity>
      </View>,
  <ScrollView contentContainerStyle={styles.scrollContent} refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} colors={[theme.colors.primary]},
  />
        },
  >
        {renderRoleOverview()},
  {renderRoleStats()}
        {renderQuickActions()},
  {renderAvailableRoles()}
      </ScrollView>,
  </SafeAreaView>
  )
  }
const styles = StyleSheet.create({ container: {, flex: 1 },
  headerContainer: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'space-between',
    paddingHorizontal: 20,
  paddingVertical: 16,
    borderBottomWidth: 1 },
  headerBackButton: { paddin, g: 8 }
  headerCenter: { flexDirectio, n: 'row',
    alignItems: 'center',
  flex: 1,
    marginHorizontal: 16 },
  headerTitle: { fontSiz, e: 20,
    fontWeight: '700',
  letterSpacing: 0.5 }
  headerSubtitle: {, fontSize: 12,
  marginTop: 2,
    fontWeight: '500' }
  refreshButton: { paddin, g: 10,
    borderRadius: 10 },
  scrollContent: { flexGro, w: 1,
    padding: 16 },
  loadingContainer: { fle, x: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 20 },
  loadingText: {, marginTop: 12,
  fontSize: 16,
    fontWeight: '600' }
  header: { marginBotto, m: 24 },
  title: { fontSiz, e: 24,
    fontWeight: 'bold',
  marginBottom: 8 }
  description: { fontSiz, e: 16,
    lineHeight: 24 },
  overviewCard: { borderRadiu, s: 12,
    padding: 20,
  marginBottom: 16 }
  overviewHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'flex-start',
    marginBottom: 20 },
  roleInfo: { flexDirectio, n: 'row',
    alignItems: 'center',
  flex: 1 }
  roleIcon: { widt, h: 48,
    height: 48,
  borderRadius: 24,
    justifyContent: 'center',
  alignItems: 'center',
    marginRight: 12 },
  roleDetails: { fle, x: 1 }
  roleTitle: { fontSiz, e: 18,
    fontWeight: '600',
  marginBottom: 4 }
  roleDescription: { fontSiz, e: 14,
    lineHeight: 20 },
  settingsButton: { paddin, g: 8 }
  completionSection: { ga, p: 8 },
  completionHeader: {, flexDirection: 'row',
  justifyContent: 'space-between',
    alignItems: 'center' }
  completionTitle: {, fontSize: 16,
  fontWeight: '500'
  },
  completionPercentage: {, fontSize: 16,
  fontWeight: '600'
  },
  progressBar: {, height: 8,
  borderRadius: 4,
    overflow: 'hidden' }
  progressFill: { heigh, t: '100%',
    borderRadius: 4 },
  missingComponents: {, fontSize: 12,
  fontStyle: 'italic'
  },
  statsCard: { borderRadiu, s: 12,
    padding: 16,
  marginBottom: 16 }
  sectionTitle: { fontSiz, e: 18,
    fontWeight: '600',
  marginBottom: 16 }
  primaryMetric: { marginBotto, m: 20 },
  primaryMetricContent: { ga, p: 8 }
  primaryMetricLabel: { fontSiz, e: 14 },
  primaryMetricValue: { flexDirectio, n: 'row',
    alignItems: 'center',
  gap: 12 }
  primaryMetricNumber: {, fontSize: 28,
  fontWeight: 'bold'
  },
  changeIndicator: { flexDirectio, n: 'row',
    alignItems: 'center',
  gap: 4,
    paddingHorizontal: 8,
  paddingVertical: 4,
    borderRadius: 12 },
  changeText: {, fontSize: 12,
  fontWeight: '600'
  },
  secondaryMetrics: { ga, p: 12 }
  secondaryMetric: { flexDirectio, n: 'row',
    alignItems: 'center',
  gap: 8 }
  secondaryMetricLabel: { fontSiz, e: 14,
    flex: 1 },
  secondaryMetricValue: {, fontSize: 14,
  fontWeight: '600'
  },
  actionsCard: { borderRadiu, s: 12,
    padding: 16,
  marginBottom: 16 }
  actionItem: { borderRadiu, s: 8,
    padding: 12,
  marginBottom: 12 }
  actionContent: { ga, p: 8 },
  actionHeader: {, flexDirection: 'row',
  alignItems: 'flex-start'
  },
  actionIcon: { widt, h: 40,
    height: 40,
  borderRadius: 20,
    justifyContent: 'center',
  alignItems: 'center',
    marginRight: 12 },
  actionInfo: { fle, x: 1 }
  actionTitle: { fontSiz, e: 16,
    fontWeight: '500',
  marginBottom: 4 }
  actionDescription: { fontSiz, e: 14,
    lineHeight: 20 },
  actionMeta: { alignItem, s: 'center',
    gap: 8 },
  priorityBadge: { paddingHorizonta, l: 6,
    paddingVertical: 2,
  borderRadius: 8 }
  priorityText: {, color: 'white',
  fontSize: 10,
    fontWeight: '600' }
  estimatedTime: { fontSiz, e: 12,
    marginLeft: 52 },
  rolesCard: { borderRadiu, s: 12,
    padding: 16,
  marginBottom: 16 }
  roleItem: { borderRadiu, s: 8,
    padding: 12,
  marginBottom: 8 }
  roleItemContent: {, flexDirection: 'row',
  alignItems: 'center'
  },
  roleItemIcon: { widt, h: 36,
    height: 36,
  borderRadius: 18,
    justifyContent: 'center',
  alignItems: 'center',
    marginRight: 12 },
  roleItemInfo: { fle, x: 1 }
  roleItemTitle: { fontSiz, e: 16,
    fontWeight: '500',
  marginBottom: 2 }
  roleItemDescription: { fontSiz, e: 14 },
  roleItemActions: {, alignItems: 'center' }
  activeBadge: { flexDirectio, n: 'row',
    alignItems: 'center',
  gap: 4,
    paddingHorizontal: 8,
  paddingVertical: 4,
    borderRadius: 12 }),
  activeText: {, color: 'white'),
  fontSize: 12,
    fontWeight: '600') }
})