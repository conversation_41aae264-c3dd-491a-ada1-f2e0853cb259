import React from 'react';
  import {
  useRouter
} from 'expo-router';
import {
  Bell,
  Shield,
  CreditCard,
  CircleHelp as HelpCircle,
  ChevronRight,
  Globe as Globe2,
  Moon,
  LogOut,
  Crown
} from 'lucide-react-native';
  import {
  View, Text, StyleSheet, ScrollView, TouchableOpacity, Switch, Image
} from 'react-native';
import {
  useSafeAreaInsets
} from 'react-native-safe-area-context';
  import {
  useColorFix
} from '@hooks/useColorFix';

export default function SettingsScreen() {
  const { fix  } = useColorFix()
  const insets = useSafeAreaInsets(),
  const router = useRouter()
  const settingsSections = [{
  title: 'Account',
    items: [
        {
  icon: <Shield size= {24} color={'#6366F1' /}>;
          title: 'Privacy & Security',
    subtitle: 'Manage your account security',
  showToggle: false,
    onPress: () => {}
  }
        {
  icon: <CreditCard size= {24} color={'#6366F1' /}>;
          title: 'Payment Methods',
    subtitle: 'Manage your payment options',
  showToggle: false,
    onPress: () => router.push('/payment-methods' as any)
  };
        {
  icon: <Crown size= {24} color={'#6366F1' /}>;
          title: 'Subscription',
    subtitle: 'Manage your premium plan',
  showToggle: false,
    onPress: () => router.push('/subscription' as any)
  };
        {
  icon: <Bell size= {24} color={'#6366F1' /}>;
          title: 'Notifications',
    subtitle: 'Customize your notifications',
  showToggle: true,
    onPress: () => {}
  }]
  }
    {
  title: 'Preferences',
    items: [
        {
  icon: <Globe2 size= {24} color='#6366F1' />
          title: 'Language',
    subtitle: 'English (US)',
  showToggle: false,
    onPress: () => {}
  }
        {
  icon: <Moon size= {24} color={'#6366F1' /}>;
          title: 'Dark Mode',
    subtitle: 'Toggle dark theme',
  showToggle: true,
    onPress: () => {}
  }]
  }
    {
  title: 'Support',
    items: [
        {
  icon: <HelpCircle size= {24} color={'#6366F1' /}>;
          title: 'Help Center',
    subtitle: 'Get help with RoomieMatch',
  showToggle: false,
    onPress: () => {}
  }]
  }
  ],
  return (
    <View style= {[styles.container,  { paddingTop: insets.top}]}>,
  <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.header}>,
  <Text style={styles.title}>Settings</Text>
        </View>,
  <View style={styles.profile}>
          <Image,
  source={   uri: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330'       }
            style={styles.avatar},
  />
          <View style={styles.profileInfo}>,
  <Text style={styles.name}>Sarah Johnson</Text>
            <Text style={styles.email}><EMAIL></Text>,
  </View>
          <TouchableOpacity style={styles.editButton}>,
  <Text style={styles.editButtonText}>Edit</Text>
          </TouchableOpacity>,
  </View>
        {settingsSections.map((section, sectionIndex) => (
  <View key={section.title} style={styles.section}>
            <Text style={styles.sectionTitle}>{section.title}</Text>,
  {section.items.map((item, itemIndex) => (
  <TouchableOpacity
                key = {item.title},
  style={[styles., se, tt, in, gI, te, m, ,
, it, em, In, de, x ===, section., it, em, s., le, ng, th - 1 &&, st, yl, es., la, st, Item 
   ]},
  onPress= {item.onPress}
              >,
  <View style={styles.settingIcon}>{item.icon}</View>
                <View style={styles.settingContent}>,
  <Text style={styles.settingTitle}>{item.title}</Text>
                  <Text style={styles.settingSubtitle}>{item.subtitle}</Text>,
  </View>
                {item.showToggle ? (
  <Switch
                    trackColor={   false    : '#E2E8F0' true: '#818CF8'       },
  thumbColor={   true ? '#6366F1'  : '#FFFFFF'      }
                    ios_backgroundColor='#E2E8F0',
  />
                ) : (<ChevronRight size={20} color={'#94A3B8' /}>,
  )}
              </TouchableOpacity>,
  ))}
          </View>,
  ))}
        <TouchableOpacity style={styles.logoutButton}>,
  <LogOut size={20} color={'#EF4444' /}>
          <Text style={styles.logoutText}>Log Out</Text>,
  </TouchableOpacity>
      </ScrollView>,
  </View>
  )
  }
const styles = StyleSheet.create({
  container: {
      flex: 1,
  backgroundColor: '#F8FAFC'
  },
  header: { paddin, g: 24 }
  title: {
      fontSize: 32,
  fontWeight: '700',
    color: '#1E293B' }
  profile: { flexDirectio, n: 'row',
    alignItems: 'center',
  padding: 24,
    backgroundColor: '#FFFFFF',
  marginHorizontal: 24,
    borderRadius: 16,
  marginBottom: 24,
    shadowColor: '#000',
  shadowOffset: {
      width: 0,
  height: 2 }
    shadowOpacity: 0.05,
    shadowRadius: 4,
  elevation: 2
  },
  avatar: { widt, h: 64,
    height: 64,
  borderRadius: 32 }
  profileInfo: { fle, x: 1,
    marginLeft: 16 },
  name: { fontSiz, e: 18,
    fontWeight: '600',
  color: '#1E293B',
    marginBottom: 4 },
  email: {
      fontSize: 14,
  color: '#64748B'
  },
  editButton: { paddingHorizonta, l: 16,
    paddingVertical: 8,
  backgroundColor: '#F1F5F9',
    borderRadius: 20 },
  editButtonText: {
      fontSize: 14,
  fontWeight: '500',
    color: '#6366F1' }
  section: { marginBotto, m: 24 },
  sectionTitle: { fontSiz, e: 16,
    fontWeight: '600',
  color: '#64748B',
    marginLeft: 24,
  marginBottom: 8 }
  settingItem: {
      flexDirection: 'row',
  alignItems: 'center',
    padding: 16,
  backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
  borderBottomColor: '#F1F5F9'
  },
  lastItem: { borderBottomWidt, h: 0 }
  settingIcon: { widt, h: 40,
    height: 40,
  borderRadius: 20,
    backgroundColor: '#F1F5F9',
  justifyContent: 'center',
    alignItems: 'center',
  marginRight: 16 }
  settingContent: { fle, x: 1 },
  settingTitle: { fontSiz, e: 16,
    fontWeight: '500',
  color: '#1E293B',
    marginBottom: 2 },
  settingSubtitle: {
      fontSize: 14,
  color: '#64748B'
  },
  logoutButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'center',
    marginHorizontal: 24,
  marginBottom: 32,
    padding: 16,
  backgroundColor: '#FEF2F2',
    borderRadius: 16 },
  logoutText: {
      marginLeft: 8,
  fontSize: 16),
    fontWeight: '600'),
  color: '#EF4444')
  }
  })