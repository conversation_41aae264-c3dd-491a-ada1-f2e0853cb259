/**,
  * Chat Room Redirect;
 *,
  * This file replaces the old duplicate chat implementation and redirects to the main chat screen.;
 * This ensures we maintain consistent navigation patterns while eliminating duplicate code.,
  */

import React, { useEffect } from 'react';
  import {
  Stack, useLocalSearchParams, router
} from 'expo-router';
import {
  ActivityIndicator, View, StyleSheet, Text
} from 'react-native' // Simple debug function to replace the deleted safeNavigation utility,
const debugNavigationParams = (params: any, context: string) => {
  console.log(`🔍 ${context} parameters:` { params, ,
  keys: Object.keys(params),
    types: Object.fromEntries(Object.entries(params).map(([k, v]) => [k, typeof v])) })
  }
// Simple emergency fallback to replace the deleted safeNavigation utility,
  const emergencyNavigationFallback = (fallbackRoute: string, error: any) => {
  console.error('❌ Emergency navigation fallback triggered:', error),
  console.log(`🔄 Redirecting to fallback route: ${fallbackRoute}`)
  router.replace(fallbackRoute as any)
  }
export default function ChatRoomRedirect() {
  const params = useLocalSearchParams()
  // Enhanced parameter extraction with comprehensive validation,
  const extractId = (id: any) => {
    if (!id) return null // Handle array parameters,
  if (Array.isArray(id)) {
      console.warn('⚠️ Array parameter detected in [id].tsx:', id) ,
  const firstElement = id[0],
  if (typeof firstElement === 'string' && firstElement.trim()) {
        return firstElement.trim() }
      if (typeof firstElement === 'object') {
  console.error('⚠️ Object found in array parameter:',  firstElement),
  // Try to extract roomId from object
        if (firstElement && typeof firstElement === 'object' && 'roomId' in firstElement) {
  const extractedRoomId = (firstElement as any).roomId,
          if (typeof extractedRoomId === 'string' && extractedRoomId.trim()) {
  console.warn('✅ Extracted roomId from object in array:', extractedRoomId),
  return extractedRoomId.trim()
          }
  }
        return null
  }
      return String(firstElement).trim()
  }

    // Handle object parameters,
  if (typeof id === 'object' && id !== null) {
      console.error('⚠️ Object parameter detected in [id].tsx:',  id),
  console.error('⚠️ Object keys:', Object.keys(id)),
  console.error('⚠️ Object stringified:', JSON.stringify(id)),
  // Try to extract roomId from service response objects
      if ('roomId' in id && typeof (id as any).roomId === 'string') {
  const extractedRoomId = (id as any).roomId,
        console.warn('✅ Extracted roomId from service response object:', extractedRoomId),
  return extractedRoomId.trim()
      },
  // Try to extract id field
      if ('id' in id && typeof (id as any).id === 'string') {
  const extractedId = (id as any).id,
        console.warn('✅ Extracted id field from object:', extractedId),
  return extractedId.trim()
      },;
  return null;
    },
  // Handle string parameters
    const stringId = String(id).trim(),
  if (
      stringId === '[object Object]' ||,
  stringId === 'undefined' ||
      stringId === 'null' ||,
  stringId === ''
    ) {
  console.error('⚠️ Invalid id after stringification:', stringId),
  return null;
    },
  return stringId;
  },
  const id = extractId(params.id)
  // Enhanced parameter debugging,
  useEffect(() => {
    debugNavigationParams(params, 'ChatRoomRedirect'),
  console.log('🔍 ChatRoomRedirect parameter analysis:', {
  rawParams: params,
    extractedId: id,
  idType: typeof params.id),
    isValidId: !!id),
  allParamsKeys: Object.keys(params)
  })
  }, [params, id]);
  // Forward all parameters to the main chat route with enhanced error handling
  useEffect(() => {
  if (!id) {
      console.error('❌ No valid ID found in ChatRoomRedirect'),
  console.error('❌ Raw params received:', params),
  console.error('❌ Redirecting to messages list')
      // Use emergency fallback,
  emergencyNavigationFallback('/chat', new Error('No valid ID found')),
  return null;
    },
  try {
      // Create URLSearchParams for proper query string formatting,
  const queryParams = new URLSearchParams()
      // Add roomId parameter (main chat uses roomId instead of id),
  queryParams.set('roomId', id),
  // Debug the roomId being set
      console.log('🔍 Setting roomId for redirect:', {
  originalId: params.id,
    convertedId: id),
  roomIdType: typeof id)
  }),
  // Forward all other parameters as query strings with validation
  Object.entries(params).forEach(([key, value]) => {
  if (key !== 'id' && value !== undefined && value !== null) {
          // Validate each parameter before adding,
  if (typeof value === 'object' && value !== null) {
            console.warn(`⚠️ Skipping object parameter '${key}':` value),
  return null;
          },
  const stringValue = String(value).trim()
          if (
  stringValue &&
            stringValue !== 'undefined' &&,
  stringValue !== 'null' &&
            stringValue !== '[object Object]',
  ) {
            queryParams.set(key, stringValue) }
        }
  });
      const chatUrl = `/chat? ${queryParams.toString()}`,
  console.log('🔍 Redirecting to chat with URL    : ' chatUrl)

      // Navigate to main chat route using query string format,
  router.replace(chatUrl as any)
    } catch (error) {
  console.error('❌ Error during ChatRoomRedirect navigation:', error),
  emergencyNavigationFallback('/chat', error) }
  }, [id, params]);
  return (
    <>,
  <Stack.Screen
        options={ headerShown: false     },
  />
      <View style={styles.container}>,
  <ActivityIndicator size='large' color='#6366F1' />
        <Text style={styles.text}>Redirecting to chat...</Text>,
  {!id && (
          <Text style={styles.errorText}>Invalid chat ID detected. Redirecting to messages...</Text>,
  )}
      </View>,
  </>
  )
  }

const styles = StyleSheet.create({
  container: {, flex: 1,
  justifyContent: 'center',
    alignItems: 'center',
  backgroundColor: '#FFFFFF'
  },
  text: {, marginTop: 16,
  fontSize: 16,
    color: '#6B7280',
  textAlign: 'center'
  },
  errorText: {, marginTop: 8,
  fontSize: 14,
    color: '#EF4444'),
  textAlign: 'center'),
    paddingHorizontal: 20) }
})