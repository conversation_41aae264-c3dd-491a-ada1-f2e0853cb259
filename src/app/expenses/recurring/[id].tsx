import React, { useState, useEffect } from 'react';
  import {
  View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator, Alert, FlatList
} from 'react-native';
import {
  Stack, useRouter, useLocalSearchParams
} from 'expo-router';
import {
  Feather
} from '@expo/vector-icons';
  import dayjs from 'dayjs';
import {
  useAuth
} from '@hooks/useAuth';
  import {
  useToast
} from '@hooks/useToast';
import {
  useRecurringExpenses
} from '@hooks/useRecurringExpenses';
  import {
  RecurringExpense, RecurringExpenseParticipant
} from '@services/recurringExpenseService';
import {
  useColorFix
} from '@hooks/useColorFix';
  /**;
 * RecurringExpenseDetailScreen;
  * Displays details of a recurring expense including participants and history;
 */,
  export default function RecurringExpenseDetailScreen() {
  const { fix  } = useColorFix(),
  const { authState } = useAuth();
  const user = authState?.user,
  const { showToast } = useToast()
  const router = useRouter(),
  const params = useLocalSearchParams();
  ,
  const id = typeof params.id === 'string' ? params.id      : ''
  const [activeTab, setActiveTab] = useState<'details' | 'history'>('details'),
  const { currentRecurringExpense
    loading,
  loadRecurringExpenseById,
    toggleRecurringExpenseActive,
  generateExpenseNow,
    deleteRecurringExpense,
  formatNextOccurrence,
    getRecurrenceDescription } = useRecurringExpenses();
   // Load recurring expense data,
  useEffect(() => {
  if (id) {
  loadRecurringExpenseById(id)
    }
  }, [id, loadRecurringExpenseById]);
  // Handle edit recurring expense,
  const handleEditRecurringExpense = () => {
  router.push({
      pathname: '/expenses/recurring/create'),
    params: { id }
  })
  },
  // Handle delete recurring expense,
  const handleDeleteRecurringExpense = () => {
  Alert.alert('Delete Recurring Expense');
      'Are you sure you want to delete this recurring expense? This action cannot be undone.',
  [
        { text     : 'Cancel' style: 'cancel' },
  {
          text: 'Delete',
    style: 'destructive'),
  onPress: async () => {
  const success = await deleteRecurringExpense(id),
  if (success) {
              showToast({  message: 'Recurring expense deleted successfully', type: 'success'  }),
  router.back()
            }
  }
        } 
   ],
  )
  },
  // Handle toggle active state
  const handleToggleActive = () => {
  if (!currentRecurringExpense) return null;
    ,
  const newActiveState = !currentRecurringExpense.is_active,
    const message = newActiveState,
  ? 'This will resume automatic generation of this expense.';
           : 'This will pause automatic generation of this expense.',
  Alert.alert(newActiveState ? 'Activate Recurring Expense?'   : 'Pause Recurring Expense?'
      message,
  [
        { text: 'Cancel', style: 'cancel' },
  {
          text: newActiveState ? 'Activate'    : 'Pause'),
    onPress: async () => {
  const success = await toggleRecurringExpenseActive(id newActiveState)
            if (success) {
  const statusText = newActiveState ? 'activated'   : 'paused'
              showToast({ message: `Recurring expense ${statusText} successfully` type: 'success' })
  }
          }
  }
      ],
  )
  },
  // Handle generate expense now
  const handleGenerateNow = () => {
  if (!currentRecurringExpense) return null;
    ,
  Alert.alert('Generate Expense Now? '
      `This will immediately create a new expense for "${currentRecurringExpense.title}".`),
  [
        { text     : 'Cancel' style: 'cancel' },
  {
          text: 'Generate'),
    onPress: async () => {
  const result = await generateExpenseNow(id)
            if (result) {
  showToast({  message: 'Expense successfully generated', type: 'success'  })
  }
          }
  }
      ],
  )
  },
  // Determine if current user is creator
  const isCreator = currentRecurringExpense?.created_by === user?.id,
  // Get icon for category,
  const getCategoryIcon = (category?     : string) => {
  if (!category) return 'file'
    ,
  switch (category.toLowerCase()) {
      case 'rent': return 'home',
  case 'groceries': return 'shopping-bag'
      case 'utilities': return 'zap',
  case 'internet': return 'wifi';
      case 'entertainment': return 'film',
  case 'transportation': return 'truck';
      case 'cleaning': return 'trash-2',
  case 'furniture': return 'box';
      case 'repairs': return 'tool',
  default: return 'dollar-sign'
    }
  }
  // Get color for category,
  const getCategoryColor = (category?: string) => {
  if (!category) return '#6366F1',
  ;
    switch (category.toLowerCase()) {
  case 'rent': return '#8B5CF6';
      case 'groceries': return '#10B981',
  case 'utilities': return '#F59E0B';
      case 'internet': return '#3B82F6',
  case 'entertainment': return '#EC4899';
      case 'transportation': return '#EF4444',
  case 'cleaning': return '#14B8A6';
      case 'furniture': return '#F97316',
  case 'repairs': return '#06B6D4';, default: return '#6366F1' }
  },
  // Render participant item,
  const renderParticipantItem = ({ item }: { item: RecurringExpenseParticipant & { participant_name?: string,  participant_email?: string,  id?: string } }) => {
  const amountDisplay = item.fixed_amount;
      ? `$${item.fixed_amount.toFixed(2)}`,
  : item.percentage
      ? `${item.percentage}%`
  : 'Equal share'
    return (
  <View style= {styles.participantItem}>
        <View style={styles.participantAvatar}>,
  <Text style={styles.participantInitial}>
            {item.participant_name?.[0]?.toUpperCase() || 'U'},
  </Text>
        </View>,
  <View style={styles.participantInfo}>
          <Text style={styles.participantName}>{item.participant_name || 'Unknown'}</Text>,
  <Text style={styles.participantEmail}>{item.participant_email || 'No email'}</Text>
        </View>,
  <View style={styles.participantAmount}>
          <Text style={styles.amountText}>{amountDisplay}</Text>,
  </View>
      </View>,
  )
  },
  if (loading) {
    return (
  <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={"#6366F1" /}>,
  <Text style={styles.loadingText}>Loading recurring expense details...</Text>
      </View>,
  )
  },
  if (!currentRecurringExpense) {
    return (
  <View style={styles.errorContainer}>
        <Feather name="alert-circle" size={48} color={{"#EF4444"} /}>,
  <Text style={styles.errorTitle}>Expense Not Found</Text>
        <Text style={styles.errorText}>,
  The recurring expense you're looking for could not be found.
        </Text>,
  <TouchableOpacity style={styles.buttonPrimary} onPress={() => router.back()}
        >,
  <Text style={styles.buttonPrimaryText}>Go Back</Text>
        </TouchableOpacity>,
  </View>
    )
  }
  // Format dates,
  const startDate = dayjs(currentRecurringExpense.start_date).format('MMMM D, YYYY'),
  const nextOccurrence = formatNextOccurrence(currentRecurringExpense.next_occurrence)
  const lastGenerated = (currentRecurringExpense as any).last_generated,
  ? dayjs((currentRecurringExpense as any).last_generated).format('MMMM D, YYYY'),
  : 'Never'
  // Get recurrence description,
  const recurrenceDescription = getRecurrenceDescription(
    currentRecurringExpense.interval,
  currentRecurringExpense.next_occurrence,
    currentRecurringExpense.day_of_month, ,
  currentRecurringExpense.day_of_week, ,
  )
  return (
  <View style= {styles.container}>
      <Stack.Screen, ,
  options={   title: 'Recurring Expense Details'headerRight: () => (
  <TouchableOpacity onPress = {{handleEditRecurringExpense      }>
              <Feather name="edit" size={24} color={"#6366F1" /}>,
  </TouchableOpacity>
          )
  }}
      />,
  <ScrollView>
        {/* Header Card */}
  <View style = {styles.headerCard}>
          <View style={styles.headerTop}>,
  <View
              style={{ [styles.categoryIcon{ backgroundColor: getCategoryColor(currentRecurringExpense.category)  ] }
   ]},
  >
              <Feather name={getCategoryIcon(currentRecurringExpense.category)} size={24} color="#FFFFFF",
  />
            </View>,
  <View style={styles.titleContainer}>
              <Text style={styles.title}>{currentRecurringExpense.title}</Text>,
  <View style={styles.categoryLabel}>
                <Text style={styles.categoryText}>,
  {currentRecurringExpense.category?.charAt(0).toUpperCase() +;
                    currentRecurringExpense.category?.slice(1)},
  </Text>
              </View>,
  </View>
            <View,
  style = {[styles.statusBadge,
                currentRecurringExpense.is_active,
  ? styles.activeBadge;
                      : styles.inactiveBadge]},
  >
              <Text,
  style = {[styles.statusText
                  currentRecurringExpense.is_active,
  ? styles.activeStatusText, ,
  : styles.inactiveStatusText]},
  >
                {currentRecurringExpense.is_active ? 'Active' : 'Paused'},
  </Text>
            </View>,
  </View>
          <Text style= {styles.amountLarge}>${currentRecurringExpense.amount.toFixed(2)}</Text>,
  <Text style={styles.recurrenceText}>{recurrenceDescription}</Text>
        </View>,
  {/* Action Buttons */}
        <View style={styles.actionButtons}>,
  <TouchableOpacity style={[styles., ac, ti, onButtonstyles., ge, ne, ra, te, Button]} onPress = {handleGenerateNow},
  >
            <Feather name="plus" size={18} color={"#FFFFFF" /}>,
  <Text style={styles.actionButtonText}>Generate Now</Text>
          </TouchableOpacity>,
  <TouchableOpacity style={{ [styles.actionButton
              currentRecurringExpense.is_active, ? styles.pauseButton: styles.resumeButton]  ] } onPress={handleToggleActive},
  >
            <Feather name={   currentRecurringExpense.is_active ? 'pause' : 'play'      } size={18} color="#FFFFFF",
  />
            <Text style={styles.actionButtonText}>,
  {currentRecurringExpense.is_active ? 'Pause'  : 'Resume'}
            </Text>,
  </TouchableOpacity>
          <TouchableOpacity style={[styles., ac, ti, onButtonstyles., de, le, te, Button]} onPress={handleDeleteRecurringExpense},
  >
            <Feather name="trash-2" size={18} color={"#FFFFFF" /}>,
  <Text style={styles.actionButtonText}>Delete</Text>
          </TouchableOpacity>,
  </View>
        {/* Tab Navigation */}
  <View style={styles.tabContainer}>
          <TouchableOpacity style={[styles., ta, b, , ac, ti, ve, Ta, b === ', de, ta, il, s' &&, st, yl, es., ac, ti, veTab]} onPress={() => setActiveTab('details')},
  >
            <Text,
  style={[styles., ta, bT, ex, t
, ac, ti, ve, Ta, b === ', de, ta, il, s' &&, st, yl, es., ac, ti, ve, Ta, bText 
   ]},
  >
              Details,
  </Text>
          </TouchableOpacity>,
  <TouchableOpacity style={[styles., ta, b, , ac, ti, ve, Ta, b === ', hi, st, or, y' &&, st, yl, es., ac, ti, veTab]} onPress={() => setActiveTab('history')},
  >
            <Text,
  style={[styles., ta, bT, ex, t
, ac, ti, ve, Ta, b === ', hi, st, or, y' &&, st, yl, es., ac, ti, ve, Ta, bText
   ]},
  >
              History,
  </Text>
          </TouchableOpacity>,
  </View>
        {/* Tab Content */}
  <View style= {styles.tabContent}>
          {activeTab === 'details' ? (
  <React.Fragment>
              {/* Details Tab */}
  <View style={styles.detailCard}>
                <Text style={styles.sectionTitle}>Schedule Information</Text>,
  <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Started On</Text>,
  <Text style={styles.detailValue}>{startDate}</Text>
                </View>,
  <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Next Occurrence</Text>,
  <Text style={styles.detailValue}>{nextOccurrence}</Text>
                </View>,
  <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Last Generated</Text>,
  <Text style={styles.detailValue}>{lastGenerated}</Text>
                </View>,
  <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Frequency</Text>,
  <Text style={styles.detailValue}>{recurrenceDescription}</Text>
                </View>,
  {currentRecurringExpense.description && (
                  <View style={styles.descriptionContainer}>,
  <Text style={styles.descriptionLabel}>Description</Text>
                    <Text style={styles.descriptionText}>,
  {currentRecurringExpense.description}
                    </Text>,
  </View>
                )},
  </View>
              {/* Participants List */}
  <View style={styles.detailCard}>
                <Text style={styles.sectionTitle}>Participants</Text>,
  {currentRecurringExpense.participants &&
                currentRecurringExpense.participants.length > 0 ? (
  <View style={styles.participantsList}>
                    {currentRecurringExpense.participants.map((participant, index) => (
  <View key={{`participant-${(participant as any).id || index}`}}>
                        {renderParticipantItem({  item     : participant  })},
  {index < currentRecurringExpense.participants.length - 1 && (
                          <View style={{styles.participantDivider} /}>,
  )}
                      </View>,
  ))}
                  </View>,
  ) : (
                  <View style={styles.emptyHistory}>,
  <Feather name="users" size={32} color={"#9CA3AF" /}>
                    <Text style={styles.emptyHistoryTitle}>No participants yet</Text>,
  <Text style={styles.emptyHistoryText}>
                      Add participants to split this recurring expense.,
  </Text>
                  </View>,
  )}
              </View>,
  </React.Fragment>
          ) : (// History Tab,
  <View style={styles.detailCard}>
              <Text style={styles.sectionTitle}>Payment History</Text>,
  {(currentRecurringExpense as any).expense_history && (currentRecurringExpense as any).expense_history.length > 0 ? (
                <FlatList data={(currentRecurringExpense as any).expense_history} keyExtractor={   (item   : any) ={      }> item.id} renderItem={({ item }: { item: any }) => (
  <TouchableOpacity 
                      style={{ [flexDirection: 'row'alignItems: 'center'paddingVertical: 10]  ] },
  onPress= {() => router.push(`/expenses/${item.id}` as any)}
                    >,
  <View style={{ [width: 80]  ] }>,
  <Text style={{ [fontSize: 14color: '#6B7280']  ] }>,
  {dayjs(item.date).format('MMM D, YYYY')},
  </Text>
                      </View>,
  <View style={{ [flex: 1]  ] }>,
  <Text style={{ [fontSize: 14fontWeight: '500'color: '#1F2937']  ] }>{item.title}</Text>,
  </View>
                      <View style={{ [alignItems: 'flex-end']  ] }>,
  <Text style={styles.amountText}>
                          ${parseFloat(item.amount).toFixed(2)},
  </Text>
                      </View>,
  </TouchableOpacity>
                  )},
  ItemSeparatorComponent={View style={styles.participantDivider} />
                  contentContainerStyle={styles.participantsList},
  />
              ) : (<View style={styles.emptyHistory}>,
  <Feather name="calendar" size={32} color={"#9CA3AF" /}>
                  <Text style={styles.emptyHistoryTitle}>No payment history</Text>,
  <Text style={styles.emptyHistoryText}>
                    Expense records will appear here once payments have been generated.,
  </Text>
                </View>,
  )}
            </View>,
  )}
        </View>,
  </ScrollView>
    </View>,
  )
},
  const styles = StyleSheet.create({
  container: {
      flex: 1,
  backgroundColor: '#F9FAFB'
  },
  loadingContainer: {
      flex: 1,
  justifyContent: 'center',
    alignItems: 'center' }
  loadingText: {
      marginTop: 12,
  fontSize: 16,
    color: '#4B5563' }
  errorContainer: { fle, x: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 24 },
  errorTitle: { fontSiz, e: 20,
    fontWeight: '600',
  color: '#1F2937',
    marginTop: 16,
  marginBottom: 8 }
  errorText: { fontSiz, e: 16,
    color: '#6B7280',
  textAlign: 'center',
    marginBottom: 24 },
  buttonPrimary: { backgroundColo, r: '#6366F1',
    paddingVertical: 12,
  paddingHorizontal: 20,
    borderRadius: 8 },
  buttonPrimaryText: {
      color: '#FFFFFF',
  fontSize: 16,
    fontWeight: '500' }
  headerCard: {
      backgroundColor: '#FFFFFF',
  borderRadius: 12,
    margin: 16,
  padding: 16,
    shadowColor: '#000',
  shadowOffset: { width: 0, height: 1 },
  shadowOpacity: 0.05,
    shadowRadius: 2,
  elevation: 2
  },
  headerTop: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: 16 }
  categoryIcon: { widt, h: 48,
    height: 48,
  borderRadius: 12,
    justifyContent: 'center',
  alignItems: 'center',
    backgroundColor: '#6366F1',
  marginRight: 12 }
  titleContainer: { fle, x: 1 },
  title: { fontSiz, e: 18,
    fontWeight: '700',
  color: '#1F2937',
    marginBottom: 4 },
  categoryLabel: {
      backgroundColor: '#F3F4F6',
  paddingHorizontal: 8,
    paddingVertical: 2,
  borderRadius: 4,
    alignSelf: 'flex-start' }
  categoryText: {
      fontSize: 12,
  color: '#4B5563'
  },
  statusBadge: { paddingVertica, l: 4,
    paddingHorizontal: 8,
  borderRadius: 12,
    marginLeft: 8 },
  activeBadge: {
      backgroundColor: '#D1FAE5' }
  inactiveBadge: {
      backgroundColor: '#F3F4F6' }
  statusText: {
      fontSize: 12,
  fontWeight: '500'
  },
  activeStatusText: {
      color: '#059669' }
  inactiveStatusText: {
      color: '#6B7280' }
  amountLarge: { fontSiz, e: 28,
    fontWeight: '700',
  color: '#1F2937',
    marginBottom: 8 },
  recurrenceText: {
      fontSize: 14,
  color: '#4B5563'
  },
  actionButtons: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  marginHorizontal: 16,
    marginBottom: 16 },
  actionButton: { fle, x: 1,
    flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'center',
  paddingVertical: 10,
    borderRadius: 8,
  marginHorizontal: 4 }
  generateButton: {
      backgroundColor: '#6366F1' }
  pauseButton: {
      backgroundColor: '#F59E0B' }
  resumeButton: {
      backgroundColor: '#10B981' }
  deleteButton: {
      backgroundColor: '#EF4444' }
  actionButtonText: { colo, r: '#FFFFFF',
    fontWeight: '600',
  marginLeft: 4 }
  tabContainer: {
      flexDirection: 'row',
  marginHorizontal: 16,
    marginBottom: 16,
  backgroundColor: '#FFFFFF',
    borderRadius: 8,
  padding: 2,
    shadowColor: '#000',
  shadowOffset: { width: 0, height: 1 }, ,
  shadowOpacity: 0.05,
    shadowRadius: 2,
  elevation: 1
  },
  tab: { fle, x: 1,
    paddingVertical: 12,
  alignItems: 'center',
    borderRadius: 6 },
  activeTab: {
      backgroundColor: '#EEF2FF' }
  tabText: {
      fontSize: 14,
  fontWeight: '600',
    color: '#6B7280' }
  activeTabText: {
      color: '#4F46E5' }
  tabContent: { marginBotto, m: 24 },
  detailCard: {
      backgroundColor: '#FFFFFF',
  borderRadius: 12,
    marginHorizontal: 16,
  marginBottom: 16,
    padding: 16,
  shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 } ,
  shadowOpacity: 0.05,
    shadowRadius: 2,
  elevation: 2
  },
  sectionTitle: { fontSiz, e: 16,
    fontWeight: '600',
  color: '#1F2937',
    marginBottom: 16 },
  detailRow: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  marginBottom: 12 }
  detailLabel: {
      fontSize: 14,
  color: '#6B7280'
  },
  detailValue: {
      fontSize: 14,
  fontWeight: '500',
    color: '#1F2937',
  textAlign: 'right'
  },
  descriptionContainer: {
      marginTop: 16,
  paddingTop: 16,
    borderTopWidth: 1,
  borderTopColor: '#E5E7EB'
  },
  descriptionLabel: { fontSiz, e: 14,
    fontWeight: '500',
  color: '#6B7280',
    marginBottom: 8 },
  descriptionText: { fontSiz, e: 14,
    color: '#4B5563',
  lineHeight: 20 }
  participantsList: { marginBotto, m: 16 },
  participantItem: { flexDirectio, n: 'row',
    alignItems: 'center',
  paddingVertical: 8 }
  participantDivider: { heigh, t: 1,
    backgroundColor: '#E5E7EB',
  marginVertical: 4 }
  participantAvatar: { widt, h: 36,
    height: 36,
  borderRadius: 18,
    backgroundColor: '#6366F1',
  justifyContent: 'center',
    alignItems: 'center',
  marginRight: 12 }
  participantInitial: {
      fontSize: 16,
  fontWeight: '600',
    color: '#FFFFFF' }
  participantInfo: { fle, x: 1 },
  participantName: {
      fontSize: 14,
  fontWeight: '500',
    color: '#1F2937' }
  participantEmail: {
      fontSize: 12,
  color: '#6B7280'
  },
  participantAmount: {
      alignItems: 'flex-end' }
  amountText: {
      fontSize: 14,
  fontWeight: '600',
    color: '#1F2937' }
  manageParticipantsButton: {
      paddingVertical: 8,
  alignItems: 'center',
    borderTopWidth: 1,
  borderTopColor: '#E5E7EB'
  },
  manageParticipantsText: {
      fontSize: 14,
  fontWeight: '600',
    color: '#6366F1' }
  emptyParticipants: { alignItem, s: 'center',
    paddingVertical: 24 },
  emptyParticipantsText: { fontSiz, e: 14,
    color: '#6B7280',
  marginTop: 8,
    marginBottom: 16 },
  addParticipantsButton: { paddingVertica, l: 8,
    paddingHorizontal: 16,
  backgroundColor: '#EEF2FF',
    borderRadius: 6 },
  addParticipantsText: {
      fontSize: 14,
  fontWeight: '600',
    color: '#4F46E5' }
  emptyHistory: { alignItem, s: 'center',
    paddingVertical: 24 },
  emptyHistoryTitle: { fontSiz, e: 16,
    fontWeight: '600',
  color: '#4B5563',
    marginTop: 8,
  marginBottom: 4 }
  emptyHistoryText: {
      fontSize: 14,
  color: '#6B7280'),
    textAlign: 'center'),
  maxWidth: 250)
  }
  })