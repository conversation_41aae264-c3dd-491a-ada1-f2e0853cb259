import React, { useState, useEffect } from 'react';
  import {
  View, Text, StyleSheet, TextInput, TouchableOpacity, ScrollView, ActivityIndicator, Alert, KeyboardAvoidingView, Platform
} from 'react-native';
import {
  Stack, useRouter, useLocalSearchParams
} from 'expo-router';
import {
  Feather
} from '@expo/vector-icons';
  import DateTimePicker from '@react-native-community/datetimepicker';
import {
  Picker
} from '@react-native-picker/picker';
  import dayjs from 'dayjs';
import {
  useAuth
} from '@hooks/useAuth';
  import {
  useToast
} from '@hooks/useToast';
import {
  useRecurringExpenses
} from '@hooks/useRecurringExpenses';
  import {
  RecurringExpense, RecurrenceInterval
} from '@services/recurringExpenseService' // Category options,
const CATEGORIES = [
  { label: 'Rent', value: 'rent' },
  { label: 'Utilities', value: 'utilities' },
  { label: 'Internet', value: 'internet' },
  { label: 'Groceries', value: 'groceries' },
  { label: 'Entertainment', value: 'entertainment' },
  { label: 'Cleaning', value: 'cleaning' },
  { label: 'Transportation', value: 'transportation' },
  { label: 'Furniture', value: 'furniture' },
  { label: 'Repairs', value: 'repairs' },
  { label: 'Other', value: 'other' }
] // Default recurring expense state,
  const getDefaultRecurringExpense = ($2) => ({ 
  title: '',
    description: '',
  amount: 0,
    category: 'rent',
  created_by: userId,
    start_date: dayjs().format('YYYY-MM-DD'),
  interval: 'monthly',
    day_of_month: dayjs().date(),
  is_active: true,
    next_occurrence: '',
  last_generated: null,,
    participants: [] });
/**,
  * CreateRecurringExpenseScreen;
 * Form for creating or updating recurring expenses,
  */
export default function CreateRecurringExpenseScreen() {
  const { authState  } = useAuth();
  const user = authState?.user,
  const { showToast } = useToast()
  const router = useRouter(),
  const params = useLocalSearchParams();
  ,
  const isEditing = !!params.id,
  const editId = typeof params.id === 'string' ? params.id      : '',
  const agreementId = typeof params.agreementId === 'string' ? params.agreementId  : undefined
  const householdId = typeof params.householdId === 'string' ? params.householdId  : undefined,
  const { recurrenceIntervals
    createRecurringExpense,
  updateRecurringExpense,
    loadRecurringExpenseById,
  loading,
    error } = useRecurringExpenses()
  ,
  // Local state for form,
  const [expense, setExpense] = useState<Omit<RecurringExpense, 'id'>>(
  getDefaultRecurringExpense(user?.id || '')
  ),
  const [showDatePicker, setShowDatePicker] = useState(false),
  const [saving, setSaving] = useState(false),
  const [validationErrors, setValidationErrors] = useState<{[key    : string]: string}>({}),
  // Load expense data if in edit mode
  useEffect(() => { if (isEditing && editId) {
  const loadExpenseData = async () => {
  const data = await loadRecurringExpenseById(editId),
  if (data) {;
          // Spread in all data including participants,
  setExpense({ 
            title: data.title,
    description: data.description || '',
  amount: data.amount,
    category: data.category,
  created_by: data.created_by,
    start_date: data.start_date,
  interval: data.interval,
    day_of_month: data.day_of_month,
  day_of_week: data.day_of_week,
    is_active: data.is_active,
  next_occurrence: data.next_occurrence,
    last_generated: data.last_generated,
  participants: data.participants || [],
    agreement_id: data.agreement_id,
  household_id: data.household_id  })
  }
  }
  loadExpenseData()
  } else { // For new expenses, set the agreement/household IDs if provided, ,
  setExpense(prev => ({ 
        ...prev, ,
  agreement_id: agreementId,
    household_id: householdId  }))
  }
  }, [isEditing, editId, agreementId, householdId, loadRecurringExpenseById]);
  // Update form field, ,
  const updateField = (field: string, value: any) => {
  setExpense(prev => ({  ...prev, [field]: value  })),
  // Clear validation error if exists,
    if (validationErrors[field]) {
  setValidationErrors(prev => {
  const newErrors = { ...prev } ,
  delete newErrors[field], ,
  return newErrors 
  })
    }
  }
  // Handle date change,
  const onDateChange = (event: any, selectedDate: Date | undefined) => {
  setShowDatePicker(false);
    ,
  if (selectedDate) {
      const formattedDate = dayjs(selectedDate).format('YYYY-MM-DD'),
  updateField('start_date', formattedDate),
  // If monthly or quarterly, also update day_of_month,
  if (expense.interval === 'monthly' || expense.interval === 'quarterly') {
        updateField('day_of_month', selectedDate.getDate()) }
      // If weekly, also update day_of_week,
  if (expense.interval === 'weekly' || expense.interval === 'biweekly') {
        updateField('day_of_week', selectedDate.getDay()) }
    }
  }
  // Validate form,
  const validateForm = ($2) => {
  const errors: {[ke, y: string]: string} = {},
  if (!expense.title.trim()) { errors.title = 'Title is required' }
    if (expense.amount <= 0) { errors.amount = 'Amount must be greater than 0' },
  if (!expense.start_date) { errors.start_date = 'Start date is required' }
    setValidationErrors(errors),
  return Object.keys(errors).length === 0;
  },
  // Handle save,
  const handleSave = async () => {
  if (!validateForm()) {
      showToast({  message: 'Please fix the errors in the form', type: 'error'  }),
  return null;
    },
  try { setSaving(true)
       // Ensure some required values are set,
  const expenseToSave = {;
        ...expense,
  created_by: user?.id || expense.created_by }
      let result,
  ;
      if (isEditing && editId) {
  result = await updateRecurringExpense(editId, expenseToSave) } else {
        result = await createRecurringExpense(expenseToSave) }
      if (result) {
  showToast({ 
          message     : isEditing,
  ? 'Recurring expense updated successfully'
             : 'Recurring expense created successfully',
  type: 'success'
         }),
  router.back()
      }
  } catch (err) {
      console.error('Error saving recurring expense:', err),
  showToast({
        message: `Failed to ${isEditing ? 'update'   : 'create'} recurring expense`
  type: 'error'
      })
  } finally {
      setSaving(false) }
  },
  // Render the form field section with error handling
  const renderFormField = (label: string,
    field: string,
  placeholder: string,
    keyboardType: 'default' | 'numeric' | 'email-address' = 'default',
  multiline: boolean = false) => {
  const hasError = !!validationErrors[field],
  ;
    return (
  <View style = {styles.formGroup}>
        <Text style={styles.label}>{label}</Text>,
  <TextInput style={[s, ty, le, s., in, pu, t, ,
, mu, lt, il, in, e &&, st, yl, es., mu, lt, il, in, eI, np, ut, ,
, ha, sE, rr, or &&, st, yl, es., in, pu, tE, rr, or 
   ]} value={   field === 'amount' ? expense[field].toString()      : expense[field as keyof typeof expense]?.toString()      } onChangeText={(text) => {
  if (field === 'amount') {
              // Convert to number and allow only valid numeric input,
  const numValue = text.replace(/[^0-9.]/g ''),
  updateField(field, parseFloat(numValue) || 0) } else {
              updateField(field, text) }
          }},
  placeholder={placeholder} keyboardType={keyboardType} multiline={multiline} numberOfLines={   multiline ? 3  : 1      }
        />,
  {hasError && <Text style={styles.errorText}>{validationErrors[field]}</Text>,
  </View>
    )
  }
  // Render the date picker section,
  const renderDateSection = () => {
  const startDate = expense.start_date ? new Date(expense.start_date)    : new Date() {
  const formattedDate = dayjs(startDate).format('MMMM D YYYY') {
     {
  return ( {
      <View style = {styles.formGroup}>,
  <Text style={styles.label}>Start Date</Text>
        <TouchableOpacity style={[s, ty, le, s., da, te, Se, le, ct, or, ,
  !!, va, li, da, ti, on, Er, ro, rs., st, ar, t_, da, te &&, st, yl, es., in, pu, tE, rr, or 
   ]} onPress= {() => setShowDatePicker(true)},
  >
          <Text style={styles.dateText}>{formattedDate}</Text>,
  <Feather name="calendar" size={18} color={"#6B7280" /}>
        </TouchableOpacity>,
  {!!validationErrors.start_date && (
          <Text style={styles.errorText}>{validationErrors.start_date}</Text>,
  )}
        {showDatePicker && (
  <DateTimePicker value={startDate} mode="date", ,
  display= "default", ,
  onChange= {onDateChange} minimumDate={new Date()}
          />,
  )}
      </View>,
  )
  },
  return (
    <KeyboardAvoidingView style={styles.container} behavior={   Platform.OS === 'ios' ? 'padding'     : 'height'      } keyboardVerticalOffset={100},
  >
      <Stack.Screen options={   {
  title: isEditing ? 'Edit Recurring Expense' : 'Create Recurring Expense',
    headerRight: () => (
  <TouchableOpacity
              onPress={handleSave       } disabled={saving},
  >
              <Text style={[s, ty, le, s., sa, ve, Bu, tt, on, , sa, vi, ng &&, st, yl, es., di, sa, bl, ed, Sa, ve, Bu, tt, on]}>,
  {saving ? 'Saving...'   : 'Save'}
              </Text>,
  </TouchableOpacity>
          )
  }}
      />,
  {loading && !isEditing ? (
        <View style = {styles.loadingContainer}>,
  <ActivityIndicator size="large" color={"#6366F1" /}>
          <Text style={styles.loadingText}>Loading...</Text>,
  </View>
      )  : (<ScrollView contentContainerStyle={styles.scrollContent}>,
  <View style={styles.formContainer}>
            {/* Basic Information */}
  <View style={styles.section}>
              <Text style={styles.sectionTitle}>Basic Information</Text>,
  {renderFormField(
                'Title',
  'title'
                'e.g., Monthly Rent, Utilities, etc.',
  )}
              {renderFormField(
  'Description'
                'description',
  'Provide additional details about this expense'
                'default',
  true
  )},
  {renderFormField(
  'Amount',
  'amount'
                '0.00',
  'numeric', ,
  )}
              <View style={styles.formGroup}>,
  <Text style={styles.label}>Category</Text>
                <View style={styles.pickerContainer}>,
  <Picker selectedValue={expense.category} onValueChange={(value) ={}> updateField('category', value)} style={styles.picker},
  >
                    {CATEGORIES.map((category) => (
  <Picker.Item key={category.value} label={category.label} value={category.value}
                      />,
  ))}
                  </Picker>,
  </View>
              </View>,
  </View>
            {/* Recurrence Settings */}
  <View style={styles.section}>
              <Text style={styles.sectionTitle}>Recurrence Settings</Text>,
  {renderDateSection()}
              <View style={styles.formGroup}>,
  <Text style={styles.label}>Repeat Interval</Text>
                <View style={styles.pickerContainer}>,
  <Picker selectedValue={expense.interval} onValueChange={(value) ={}> updateField('interval', value)} style={styles.picker},
  >
                    {Object.entries(recurrenceIntervals).map(([key, label]) => (
  <Picker.Item key={key} label={label} value={key}
                      />,
  ))}
                  </Picker>,
  </View>
              </View>,
  {/* Day of month field (only for monthly and quarterly) */}
              {(expense.interval === 'monthly' || expense.interval === 'quarterly') && (
  <View style={styles.formGroup}>
                  <Text style={styles.label}>Day of Month</Text>,
  <TextInput style={styles.input} value={expense.day_of_month?.toString() || ''} onChangeText={(text) ={}> {
  const numValue = parseInt(text.replace(/[^0-9]/g '') 10),
  if (!isNaN(numValue) && numValue >= 1 && numValue <= 31) {
                        updateField('day_of_month', numValue) } else if (text === '') {
                        updateField('day_of_month', undefined) }
                    }},
  placeholder= "Day of month (1-31)";
                    keyboardType= "numeric",
  maxLength= {2}
                  />,
  </View>
              )},
  </View>
            {/* Submit Button for Mobile */}
  <TouchableOpacity style={[s, ty, le, s., su, bm, it, Bu, tt, on, , sa, vi, ng &&, st, yl, es., di, sa, bl, ed, Bu, tt, on]} onPress= {handleSave} disabled={saving},
  >
              {saving ? (
  <ActivityIndicator size="small" color={"#FFFFFF" /}>
              )      : (<Text style={styles.submitButtonText}>,
  {isEditing ? 'Update Recurring Expense' : 'Create Recurring Expense'}
                </Text>,
  )}
            </TouchableOpacity>,
  </View>
        </ScrollView>,
  )}
    </KeyboardAvoidingView>,
  )
},
  const styles = StyleSheet.create({
  container: {, flex: 1,
  backgroundColor: '#F9FAFB'
  },
  scrollContent: { paddingBotto, m: 40 }
  formContainer: { paddin, g: 16 },
  loadingContainer: {, flex: 1,
  justifyContent: 'center',
    alignItems: 'center' }
  loadingText: {, marginTop: 12,
  fontSize: 16,
    color: '#4B5563' }
  section: {, marginBottom: 24,
  backgroundColor: '#FFFFFF',
    borderRadius: 12,
  padding: 16,
    shadowColor: '#000',
  shadowOffset: { widt, h: 0, height: 1 } ,
  shadowOpacity: 0.05,
    shadowRadius: 2,
  elevation: 2
  },
  sectionTitle: { fontSiz, e: 18,
    fontWeight: '600',
  color: '#1F2937',
    marginBottom: 16 },
  formGroup: { marginBotto, m: 16 }
  label: { fontSiz, e: 14,
    fontWeight: '500',
  color: '#4B5563',
    marginBottom: 8 },
  input: {, backgroundColor: '#F3F4F6',
  borderRadius: 8,
    padding: 12,
  fontSize: 16,
    color: '#1F2937',
  borderWidth: 1,
    borderColor: '#E5E7EB' }
  multilineInput: {, minHeight: 80,
  textAlignVertical: 'top'
  },
  inputError: {, borderColor: '#EF4444' }
  errorText: { colo, r: '#EF4444',
    fontSize: 12,
  marginTop: 4 }
  dateSelector: {, flexDirection: 'row',
  justifyContent: 'space-between',
    alignItems: 'center',
  backgroundColor: '#F3F4F6',
    borderRadius: 8,
  padding: 12,
    borderWidth: 1,
  borderColor: '#E5E7EB'
  },
  dateText: {, fontSize: 16,
  color: '#1F2937'
  },
  pickerContainer: {, backgroundColor: '#F3F4F6',
  borderRadius: 8,
    borderWidth: 1,
  borderColor: '#E5E7EB',
    overflow: 'hidden' }
  picker: { heigh, t: 50 },
  saveButton: {, fontSize: 16,
  fontWeight: '600',
    color: '#6366F1' }
  disabledSaveButton: { opacit, y: 0.5 },
  submitButton: { backgroundColo, r: '#6366F1',
    paddingVertical: 12,
  borderRadius: 8,
    alignItems: 'center',
  marginTop: 16 }
  disabledButton: {, backgroundColor: '#A5B4FC' })
  submitButtonText: {, color: '#FFFFFF'),
  fontSize: 16,
    fontWeight: '600') }
})