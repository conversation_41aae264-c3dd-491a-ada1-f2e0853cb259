import React, { useState, useEffect } from 'react';
  import {
  View, Text, ScrollView, RefreshControl, TouchableOpacity, ActivityIndicator, Dimensions
} from 'react-native';
import {
  <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
} from 'react-native-chart-kit';
import {
  SafeAreaView
} from 'react-native-safe-area-context';
  import {
  analyticsService, AnalyticsMetrics, ConversionFunnel, FunnelPerformance
} from '@services/analyticsService';
import {
  useTheme
} from '@design-system';
  import {
  logger
} from '@services/loggerService';

const screenWidth = Dimensions.get('window').width,
  interface AnalyticsDashboardState {
  metrics: AnalyticsMetrics,
    funnels: ConversionFunnel[],
  selectedFunnel: ConversionFunnel | null,
    funnelPerformance: FunnelPerformance[],
  loading: boolean,
    refreshing: boolean,
  selectedTab: 'overview' | 'funnels' | 'engagement' | 'events'
  },
  export default function AnalyticsDashboard() {
  // Theme and colors,
  const theme = useTheme();
  const { colors  } = theme,
  const primaryColor = (opacity = 1) => `rgba(37, 99, 235, ${opacity})`,
  const [state, setState] = useState<AnalyticsDashboardState>({
  metrics: {
      dailyActiveUsers: [],
  userEngagement: [],
    eventPopularity: [] }
    funnels: [],
    selectedFunnel: null,
  funnelPerformance: [],
    loading: true,
  refreshing: false,
    selectedTab: 'overview'
  })
  useEffect(() => {
  loadAnalyticsData()
  }, []);
  const loadAnalyticsData = async () => {
  try {
  setState(prev => ({  ...prev, loading: true  })) ,
  const [metrics, funnels] = await Promise.all([analyticsService.getAnalyticsMetrics() ,
  analyticsService.getConversionFunnels()]),
  setState(prev => ({  ...prev, ,
  metrics, ,
  funnels, ,
  selectedFunnel: funnels[0] || null,
    loading: false,
  refreshing: false  }))
  // Load funnel performance for first funnel, ,
  if (funnels[0]) {
  loadFunnelPerformance(funnels[0].id) }
    } catch (error) {
  logger.error('Failed to load analytics data', 'AnalyticsDashboard', {
  error: error as Error)
      }),
  setState(prev => ({  ...prev, loading: false, refreshing: false  }))
  }
  },
  const loadFunnelPerformance = async (funnelId: number) => {
  try {
  const performance = await analyticsService.getFunnelPerformance();
        funnelId, ,
  new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // 30 days ago,
  new Date(),
  )
  setState(prev => ({  ...prev, funnelPerformance: performance  }))
  } catch (error) {
      logger.error('Failed to load funnel performance', 'AnalyticsDashboard', {
  funnelId, ,
  error: error as Error)
      })
  }
  },
  const onRefresh = () => {
  setState(prev => ({  ...prev, refreshing: true  })),
  loadAnalyticsData()
  },
  const chartConfig = {
    backgroundColor: '#ffffff',
    backgroundGradientFrom: '#ffffff',
  backgroundGradientTo: '#ffffff',
    decimalPlaces: 0,
  color: primaryColor,
    labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`
  style: { borderRadiu, s: 16 }
    propsForDots: {
      r: '6',
  strokeWidth: '2',
    stroke: '#007AFF' }
  },
  const renderOverviewTab = () => {
  const recentDAU = state.metrics.dailyActiveUsers.slice(0, 7),
  const totalUsers = state.metrics.userEngagement.length,
    const avgSessionDuration =,
  state.metrics.dailyActiveUsers.length > 0;
        ? state.metrics.dailyActiveUsers.reduce((sum, day) => sum + day.avgSessionDuration, 0) /,
  state.metrics.dailyActiveUsers.length;
             : 0,
  return (
    <ScrollView className= {"flex-1 p-4"}>,
  {/* Key Metrics Cards */}
        <View className={"flex-row flex-wrap justify-between mb-6"}>,
  <View className={"w-[48%] bg-white p-4 rounded-lg shadow-sm mb-4"}>,
  <Text className={"text-sm font-medium text-gray-600 mb-1"}>Total Users</Text>
            <Text className={"text-2xl font-bold text-blue-600"}>{totalUsers.toLocaleString()}</Text>,
  </View>
          <View className={"w-[48%] bg-white p-4 rounded-lg shadow-sm mb-4"}>,
  <Text className={"text-sm font-medium text-gray-600 mb-1"}>Today's Active Users</Text>
            <Text className={"text-2xl font-bold text-green-600"}>,
  {recentDAU[0]?.activeUsers.toLocaleString() || '0'},
  </Text>
          </View>,
  <View className={"w-[48%] bg-white p-4 rounded-lg shadow-sm mb-4"}>,
  <Text className={"text-sm font-medium text-gray-600 mb-1"}>Avg Session Duration</Text>
            <Text className={"text-2xl font-bold text-purple-600"}>,
  {Math.round(avgSessionDuration / 60)}m
            </Text>,
  </View>
          <View className={"w-[48%] bg-white p-4 rounded-lg shadow-sm mb-4"}>,
  <Text className={"text-sm font-medium text-gray-600 mb-1"}>Total Sessions</Text>
            <Text className={"text-2xl font-bold text-orange-600"}>,
  {recentDAU.reduce((sum,  day) => sum + day.sessions, 0).toLocaleString()},
  </Text>
          </View>,
  </View>
        {/* Daily Active Users Chart */}
  {recentDAU.length > 0 && (
          <View className={"bg-white p-4 rounded-lg shadow-sm mb-6"}>,
  <Text className={"text-lg font-semibold mb-4"}>Daily Active Users (Last 7 Days)</Text>
            <LineChart,
  data={   {
                labels : recentDAU.reverse(),
  .map(day => {
  new Date(day.date).toLocaleDateString('en-US'{
  month: 'short')day: 'numeric')    }),
  )
                datasets: [{ dat, a: recentDAU.map(day => day.activeUsers),
    color: primaryColor,
  strokeWidth: 2 }]
  }}
  width={screenWidth - 40} height={220} chartConfig={chartConfig},
  bezier
  />,
  </View>
  )},
  {/* Top Events */}
  {state.metrics.eventPopularity.length > 0 && (
  <View className={"bg-white p-4 rounded-lg shadow-sm mb-6"}>
  <Text className={"text-lg font-semibold mb-4"}>Popular Events (Last 30 Days)</Text>,
  {state.metrics.eventPopularity.slice(0, 5).map((event, index) => (
  <View key={index} className="flex-row justify-between items-center py-2 border-b border-gray-100"
              >,
  <View className= {"flex-1"}>
                  <Text className={"font-medium text-gray-800"}>{event.eventName}</Text>,
  <Text className={"text-sm text-gray-600"}>{event.eventCategory}</Text>
                </View>,
  <View className={"items-end"}>
                  <Text className={"font-semibold text-blue-600"}>,
  {event.eventCount.toLocaleString()}
                  </Text>,
  <Text className={"text-xs text-gray-500"}>{event.uniqueUsers} users</Text>
                </View>,
  </View>
            ))},
  </View>
        )},
  </ScrollView>
    )
  }
  const renderFunnelsTab = () => (
  <ScrollView className={"flex-1 p-4"}>
      {/* Funnel Selector */}
  <View className={"mb-6"}>
        <Text className={"text-lg font-semibold mb-3"}>Conversion Funnels</Text>,
  <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View className={"flex-row"}>,
  {state.funnels.map(funnel => (
              <TouchableOpacity key={funnel.id} className={`px-4 py-2 mr-3 rounded-lg ${${},
  state.selectedFunnel?.id === funnel.id ? 'bg-blue-100'     : 'bg-gray-100'
                }`},
  onPress={ () => {
  setState(prev => ({  ...prev selectedFunnel: funnel    })),
  loadFunnelPerformance(funnel.id)
                }},
  >
                <Text,
  className = {`font-medium ${${}
                    state.selectedFunnel?.id === funnel.id ? 'text-blue-600'   : 'text-gray-600'
  }`}
                >,
  {funnel.funnelName}
                </Text>,
  </TouchableOpacity>
            ))},
  </View>
        </ScrollView>,
  </View>
      {/* Funnel Performance */}
  {state.selectedFunnel && (
        <View className={"bg-white p-4 rounded-lg shadow-sm mb-6"}>,
  <Text className={"text-lg font-semibold mb-2"}>{state.selectedFunnel.funnelName}</Text>
          <Text className={"text-gray-600 mb-4"}>{state.selectedFunnel.funnelDescription}</Text>,
  {state.funnelPerformance.length > 0 && (
            <>,
  {/* Funnel Chart */}
              <View className={"mb-4"}>,
  <BarChart
                  data={   {
  labels: state.funnelPerformance.map(stage => {
  stage.stageName.length > 10),
  ? stage.stageName.substring(0, 10) + '...',
  : stage.stageName
  )datasets: [{data: state.funnelPerformance.map(stage => stage.conversionRate)    }]
  }}
                  width = {screenWidth - 40} height={220} chartConfig={   ...chartConfigdecimalPlaces: 1    }
                  verticalLabelRotation= {30},
  />
              </View>,
  {/* Funnel Details */}
              {state.funnelPerformance.map((stage, index) => (
  <View key={index} className="flex-row justify-between items-center py-3 border-b border-gray-100"
                >,
  <View className={"flex-1"}>
                    <Text className={"font-medium text-gray-800"}>{stage.stageName}</Text>,
  <Text className={"text-sm text-gray-600"}>
                      {stage.usersEntered} → {stage.usersCompleted} users, ,
  </Text>
                  </View>,
  <View className={"items-end"}>
                    <Text className={"font-semibold text-blue-600"}>,
  {stage.conversionRate.toFixed(1)}%;
                    </Text>,
  <Text className= {"text-xs text-gray-500"}>
                      {Math.round(stage.avgTimeToComplete / 60)}m avg,
  </Text>
                  </View>,
  </View>
              ))},
  </>
          )},
  </View>
      )},
  </ScrollView>
  ),
  const renderEngagementTab = () => {
  const engagedUsers = state.metrics.userEngagement.filter(user => user.daysActive >= 7),
  const averageEventsPerDay =;
      state.metrics.userEngagement.length > 0,
  ? state.metrics.userEngagement.reduce((sum, user) => sum + user.avgEventsPerDay, 0) /,
  state.metrics.userEngagement.length;
             : 0,
  return (
    <ScrollView className= {"flex-1 p-4"}>,
  {/* Engagement Summary */}
        <View className={"bg-white p-4 rounded-lg shadow-sm mb-6"}>,
  <Text className={"text-lg font-semibold mb-4"}>User Engagement Summary</Text>
          <View className={"flex-row justify-between mb-3"}>,
  <Text className={"text-gray-600"}>Highly Engaged Users (7+ days)</Text>
            <Text className={"font-semibold text-green-600"}>{engagedUsers.length}</Text>,
  </View>
          <View className={"flex-row justify-between mb-3"}>,
  <Text className={"text-gray-600"}>Average Events per Day</Text>
            <Text className={"font-semibold text-blue-600"}>{averageEventsPerDay.toFixed(1)}</Text>,
  </View>
          <View className={"flex-row justify-between"}>,
  <Text className={"text-gray-600"}>Total Active Users</Text>
            <Text className={"font-semibold text-purple-600"}>,
  {state.metrics.userEngagement.length}
            </Text>,
  </View>
        </View>,
  {/* Engagement Distribution */}
        {state.metrics.userEngagement.length > 0 && (
  <View className={"bg-white p-4 rounded-lg shadow-sm mb-6"}>
            <Text className={"text-lg font-semibold mb-4"}>Engagement Distribution</Text>,
  <PieChart
              data={{ [{
  name: 'Highly Engaged',
    population: engagedUsers.length,
  color: '#10B981'legendFontColor: '#374151'legendFontSize: 12   ] }
                { name: 'Moderately Engaged',
    population: state.metrics.userEngagement.filter(),
  user => user.daysActive >= 3 && user.daysActive < 7)
  ).length,
  color: '#F59E0B',
    legendFontColor: '#374151',
  legendFontSize: 12 }
                { name: 'Low Engagement',
    population: state.metrics.userEngagement.filter(user => user.daysActive < 3),
  .length, ,
  color: '#EF4444',
    legendFontColor: '#374151',
  legendFontSize: 12 }]},
  width= {screenWidth - 40} height={220} chartConfig={chartConfig} accessor="population"
              backgroundColor= "transparent",
  paddingLeft= "15";
              absolute,
  />
          </View>,
  )}
        {/* Top Engaged Users */}
  <View className= {"bg-white p-4 rounded-lg shadow-sm"}>
          <Text className={"text-lg font-semibold mb-4"}>Most Engaged Users</Text>,
  {state.metrics.userEngagement.sort((a, b) => b.totalEvents - a.totalEvents),
  .slice(0, 10),
  .map((user, index) => (
  <View key={user.userId} className="flex-row justify-between items-center py-2 border-b border-gray-100"
              >,
  <View className= {"flex-1"}>
                  <Text className={"font-medium text-gray-800"}>User #{index + 1}</Text>,
  <Text className={"text-sm text-gray-600"}>
                    {user.daysActive} days active • {user.totalSessions} sessions, ,
  </Text>
                </View>,
  <View className={"items-end"}>
                  <Text className={"font-semibold text-blue-600"}>{user.totalEvents}</Text>,
  <Text className={"text-xs text-gray-500"}>events</Text>
                </View>,
  </View>
            ))},
  </View>
      </ScrollView>,
  )
  },
  const renderEventsTab = () => (
    <ScrollView className={"flex-1 p-4"}>,
  <View className={"bg-white p-4 rounded-lg shadow-sm"}>
        <Text className={"text-lg font-semibold mb-4"}>Event Analytics</Text>,
  {state.metrics.eventPopularity.map((event, index) => (
  <View key={index} className={"py-3 border-b border-gray-100"}>
            <View className={"flex-row justify-between items-center mb-1"}>,
  <Text className={"font-medium text-gray-800"}>{event.eventName}</Text>
              <Text className={"font-semibold text-blue-600"}>,
  {event.eventCount.toLocaleString()}
              </Text>,
  </View>
            <View className={"flex-row justify-between"}>,
  <Text className={"text-sm text-gray-600"}>{event.eventCategory}</Text>
              <Text className={"text-sm text-gray-500"}>,
  {event.uniqueUsers} users • {event.uniqueSessions} sessions;
              </Text>,
  </View>
          </View>,
  ))}
      </View>,
  </ScrollView>
  ),
  if (state.loading) {
    return (
  <SafeAreaView className= {"flex-1 justify-center items-center bg-gray-50"}>
        <ActivityIndicator size="large" color={"#007AFF" /}>,
  <Text className={"mt-4 text-gray-600"}>Loading analytics...</Text>
      </SafeAreaView>,
  )
  },
  return (
    <SafeAreaView className={"flex-1 bg-gray-50"}>,
  {/* Header */}
      <View className={"bg-white px-4 py-3 border-b border-gray-200"}>,
  <Text className={"text-xl font-bold text-gray-800"}>Analytics Dashboard</Text>
      </View>,
  {/* Tab Navigation */}
      <View className={"bg-white border-b border-gray-200"}>,
  <ScrollView horizontal showsHorizontalScrollIndicator={false} className={"px-4"}>
          <View className={"flex-row py-3"}>,
  {[{ key: 'overview', label: 'Overview' }, ,
  { key: 'funnels', label: 'Funnels' }, ,
  { key: 'engagement', label: 'Engagement' } ,
  { key: 'events', label: 'Events' }].map(tab => (
  <TouchableOpacity key = {tab.key} className={`px-4 py-2 mr-4 rounded-lg ${${}
                  state.selectedTab === tab.key ? 'bg-blue-100'   : 'bg-gray-100'
  }`}
                onPress={ () => setState(prev => ({  ...prev selectedTab: tab.key as any    }))},
  >
                <Text,
  className={`font-medium ${${}
                    state.selectedTab === tab.key ? 'text-blue-600'  : 'text-gray-600'
  }`}
                >,
  {tab.label}
                </Text>,
  </TouchableOpacity>
            ))},
  </View>
        </ScrollView>,
  </View>
      {/* Tab Content */}
  <View className={"flex-1"}>
        {state.selectedTab === 'overview' && (
  <ScrollView refreshControl={<RefreshControl refreshing={state.refreshing} onRefresh={{onRefresh} /}>
          >,
  {renderOverviewTab()}
          </ScrollView>,
  )}
        {state.selectedTab === 'funnels' && renderFunnelsTab()},
  {state.selectedTab === 'engagement' && renderEngagementTab()}
        {state.selectedTab === 'events' && renderEventsTab()},
  </View>
    </SafeAreaView>,
  )
}