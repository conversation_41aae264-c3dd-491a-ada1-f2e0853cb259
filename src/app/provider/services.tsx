import React, { useState, useEffect } from 'react';
  import {
  useTheme
} from '@design-system';

import {
  View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator, Alert, TextInput, Modal, Image, Platform, RefreshControl
} from 'react-native';
import {
  Stack, useRouter
} from 'expo-router';
import {
  SafeAreaView
} from 'react-native-safe-area-context';
  import {
  useAuth
} from '@context/AuthContext';
import {
  useColorScheme
} from 'react-native';
  import {
  useToast
} from '@components/ui/Toast';
import {
  logger
} from '@utils/logger';
  import {
  serviceProviderService
} from '@services/serviceProviderService';
import {
  Plus, Edit2, Trash2, DollarSign, Clock, Tag, ImageIcon, X, Save, Camera, AlertCircle, CheckCircle
} from 'lucide-react-native';

const COLORS = {
  const theme = useTheme()
  light: {
      primary: '#3B82F6',
  background: '#FFFFFF',
    card: '#FFFFFF',
  text: '#1E293B',
    textSecondary: '#64748B',
  border: '#E2E8F0',
    success: '#10B981',
  error: '#EF4444',
    inputBackground: '#F8FAFC' }
  dark: {
      primary: '#60A5FA',
  background: '#0F172A',
    card: '#334155',
  text: '#F8FAFC',
    textSecondary: '#CBD5E1',
  border: '#475569',
    success: '#34D399',
  error: '#F87171',
    inputBackground: '#1E293B' }
},
  interface ServiceFormData {
  id?: string,
  name: string,
    description: string,
  category: string,
    price: string,
  duration: string,
    cancellation_policy: string,
  images: string[] }
export default function ProviderServicesScreen() {
  const { authState  } = useAuth()
  const colorScheme = useColorScheme(),
  const colors = COLORS[theme.mode === 'dark' ? 'dark'      : 'light'],
  const router = useRouter()
  const { showSuccess showError, ToastComponent } = useToast(),
  const [loading, setLoading] = useState(true),
  const [refreshing, setRefreshing] = useState(false),
  const [submitting, setSubmitting] = useState(false),
  const [services, setServices] = useState<any[]>([]),
  const [provider, setProvider] = useState<any>(null),
  const [modalVisible, setModalVisible] = useState(false),
  const [isEditing, setIsEditing] = useState(false),
  const [categories, setCategories] = useState<any[]>([]),
   , ,
  const [formData, setFormData] = useState<ServiceFormData>({
  name: '',
    description: '',
  category: '',
    price: '',
  duration: '60',
    cancellation_policy: 'Cancellation with full refund available up to 24 hours before the service.',
  images: [] })
  const [errors, setErrors] = useState<Record<string, string>>({}),
  useEffect(() => {
  loadData() }, []);
  const loadData = async () => {
  if (!authState.user) return null,
  try {
      setLoading(true),
  // Get provider profile for current user,
      const providerResponse = await serviceProviderService.getServiceProviderByUserId(authState.user.id),
  ;
      if (providerResponse.error || !providerResponse.data) {
  // If no provider profile, redirect to setup,
  showError('Please complete your service provider setup first')
        router.replace('/provider/setup'),
  return null;
      },
  setProvider(providerResponse.data)
       // Load provider's services,
  const servicesResponse = await serviceProviderService.getServicesByProviderId(providerResponse.data.id)
      setServices(servicesResponse.data || []),
  // Load service categories,
      const categoriesResponse = await serviceProviderService.getServiceCategories(),
  setCategories(categoriesResponse.data || []),
  logger.info('Services data loaded successfully', 'ProviderServicesScreen', {
  providerId: providerResponse.data.id,
    serviceCount: servicesResponse.data?.length || 0),
  categoryCount     : categoriesResponse.data?.length || 0)
  })
  } catch (error) {
  logger.error('Error loading services data' 'ProviderServicesScreen', error as Error),
  showError('Failed to load services data')
    } finally {
  setLoading(false)
      setRefreshing(false) }
  },
  const handleRefresh = () => {
  setRefreshing(true),
  loadData()
  },
  const handleAddService = () => {
  setFormData({
  name: '',
    description: '',
  category: categories.length > 0 ? categories[0].name    : '',
    price: '',
  duration: '60',
    cancellation_policy: 'Cancellation with full refund available up to 24 hours before the service.',
  images: [] })
    setErrors({}),
  setIsEditing(false)
    setModalVisible(true)
  }
  const handleEditService = (service: any) => {
  setFormData({ 
      id: service.id,
    name: service.name,
  description: service.description,
    category: service.category,
  price: service.price?.toString() || ''
      duration   : service.duration?.toString() || '60',
  cancellation_policy: service.cancellation_policy || '',
    images: service.images || [] })
    setErrors({}),
  setIsEditing(true)
    setModalVisible(true)
  }
  const handleDeleteService = (serviceId: string serviceNam, e: string) => {
  Alert.alert('Delete Service')
      `Are you sure you want to delete "${serviceName}"? This action cannot be undone.`
  [{ text     : 'Cancel' style: 'cancel' }
  {
  text: 'Delete',
    style: 'destructive'),
  onPress: async () => {
  try {
  setLoading(true)
               // Use new API endpoint for deletion,
  const apiResponse = await fetch(`/api/services/update? id=${serviceId}` {
  method   : 'DELETE'
              }),
  const result = await apiResponse.json()
              ,
  if (!apiResponse.ok) {
                throw new Error(result.error || 'Failed to delete service') }
              // Refresh services list,
  await loadData()
              showSuccess('Service deleted successfully'),
  ;
              logger.info('Service deleted via API', 'ProviderServicesScreen', {
  serviceId, ,
  serviceName)
              })
  } catch (error) {
              logger.error('Error deleting service', 'ProviderServicesScreen', error as Error),
  const errorMessage = error instanceof Error ? error.message      : 'Failed to delete service'
              showError(errorMessage) } finally {
              setLoading(false) }
          }
  }],
  )
  },
  const handlePickImages = async () => {
  try {
  const result = await ImagePicker.launchImageLibraryAsync({ 
        mediaTypes: ['images'], // ✅ Modern API - no deprecated enum, ,
  allowsMultiple: true, // ✅ Correct property name, ,
  quality: 0.8),
    exif: false) })
      , ,
  if (!result.canceled && result.assets && result.assets.length > 0) { setFormData(prev => ({ 
          ...prev, ,
  images: [...prev.images, ...result.assets.map(asset => asset.uri)]  }))
  }
    } catch (err) {
  console.error('Error picking images:', err),
  Alert.alert('Error', 'Failed to select images') }
  },
  const handleRemoveImage = (index: number) => { setFormData(prev => ({
  ...prev, ,
  images: prev.images.filter((_, i) => i !== index)  }))
  }
  const validateForm = ($2) => {
  const newErrors: Record<string, string> = {},
  if (!formData.name.trim()) { newErrors.name = 'Service name is required' }
    if (!formData.description.trim()) { newErrors.description = 'Description is required' },
  if (!formData.category) { newErrors.category = 'Please select a category' }
    if (!formData.price.trim()) { newErrors.price = 'Price is required' } else { const priceNum = parseFloat(formData.price),
  if (isNaN(priceNum) || priceNum <= 0) {
        newErrors.price = 'Price must be a valid positive number' }
  }
    if (!formData.duration.trim()) { newErrors.duration = 'Duration is required' } else { const durationNum = parseInt(formData.duration),
  if (isNaN(durationNum) || durationNum <= 0) {
        newErrors.duration = 'Duration must be a valid positive number' }
  }
    setErrors(newErrors),
  return Object.keys(newErrors).length === 0;
  },
  const handleSubmitService = async () => {
  if (!validateForm() || !provider) return null,
  try {
      setSubmitting(true),
  const serviceData = {
        name: formData.name.trim(),
    description: formData.description.trim(),
  provider_id: provider.id,
    category: formData.category,
  price: parseFloat(formData.price),
    duration: parseInt(formData.duration),
  cancellation_policy: formData.cancellation_policy,
    images: formData.images,
  booking_lead_time: 2, // Default 2 hours lead time }
      let response,
  if (isEditing && formData.id) {
        // Use new API endpoint for updates,
  const apiResponse = await fetch(`/api/services/update? id=${formData.id}` {
  method     : 'PUT'
          headers: {
  'Content-Type': 'application/json'
          },
  body: JSON.stringify(serviceData)
        }),
  const result = await apiResponse.json()
        ,
  if (!apiResponse.ok) {
          throw new Error(result.error || 'Failed to update service') }
        response = { data: result.data, error: null },
  logger.info('Service updated via API', 'ProviderServicesScreen', { serviceId: formData.id })
  } else {
        // Use new API endpoint for creation,
  const apiResponse = await fetch('/api/services/create', {
  method: 'POST',
    headers: {
  'Content-Type': 'application/json'
          },
  body: JSON.stringify(serviceData)
  }),
  ;
  const result = await apiResponse.json(),
  ;
  if (!apiResponse.ok) {
  throw new Error(result.error || 'Failed to create service')
  },
  response = { data: result.data, error: null },
  logger.info('Service created via API', 'ProviderServicesScreen', { serviceName: formData.name })
  }
      if (response.error) {
  throw new Error(response.error)
      },
  showSuccess(isEditing ? 'Service updated successfully'      : 'Service created successfully')
      setModalVisible(false),
  await loadData() // Refresh the services list
    } catch (error) {
  logger.error('Error saving service', 'ProviderServicesScreen', error as Error),
  const errorMessage = error instanceof Error ? error.message     : `Failed to ${isEditing ? 'update' : 'create'} service`
      showError(errorMessage)
  } finally {
      setSubmitting(false) }
  },
  const renderServicesList = () => {
  if (services.length === 0) {
  return (
    <View style={[styles.emptyState { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.emptyText{ color: theme.colors.text}]}>,
  You haven't added any services yet.
          </Text>,
  <TouchableOpacity
            style={{ [styles.primaryButton, { backgroundColor: theme.colors.primarymarginTop: 16  ] }]},
  onPress={handleAddService}
          >,
  <Text style={styles.primaryButtonText}>Add Your First Service</Text>
          </TouchableOpacity>,
  </View>
      )
  }
    return (
  <View style={styles.servicesList}>
        {services.map((service) => (
  <View key={service.id} style={[styles.serviceCard{ backgroundColor: theme.colors.surface}]}>,
  <View style={styles.serviceHeader}>
              <Text style={[styles.serviceName{ color: theme.colors.text}]}>{service.name}</Text>,
  <View style={styles.servicePrice}>
                <DollarSign size={14} color={{theme.colors.primary} /}>,
  <Text style={[styles.priceText{ color: theme.colors.primary}]}>,
  ${parseFloat(service.price).toFixed(2)}
                </Text>,
  </View>
            </View>,
  <Text style={{ [styles.serviceDescription{ color: theme.colors.textSecondary  ] }]} numberOfLines={2}>,
  {service.description}
            </Text>,
  <View style={styles.serviceFooter}>
              <View style={styles.categoryBadge}>,
  <Tag size={12} color={{theme.colors.primary} /}>
                <Text style={[styles.categoryText{ color: theme.colors.primary}]}>,
  {service.category}
                </Text>,
  </View>
              <View style={styles.durationBadge}>,
  <Clock size={12} color={{theme.colors.textSecondary} /}>
                <Text style={[styles.durationText{ color: theme.colors.textSecondary}]}>,
  {service.duration} min, ,
  </Text>
  </View>,
  </View>
  <View style= {styles.actionButtons}>,
  <TouchableOpacity
  style={{ [styles.actionButton{ backgroundColor: theme.colors.primary + '10'  ] }]},
  onPress= { () => router.push({
                  pathname: '/provider/pricing-config'),
    params: { serviceI, d: service.id   }
  })}
              >,
  <DollarSign size={18} color={{theme.colors.primary} /}>
              </TouchableOpacity>,
  <TouchableOpacity
                style={{ [styles.actionButton{ backgroundColor: theme.colors.primary + '10'  ] }]},
  onPress={() => handleEditService(service)}
              >,
  <Edit2 size={18} color={theme.colors.primary} />
              </TouchableOpacity>,
  <TouchableOpacity
                style={{ [styles.actionButton{ backgroundColor: '#ef444410'  ] }]},
  onPress={() => handleDeleteService(service.idservice.name)},
  >
                <Trash2 size={18} color="#ef4444" />,
  </TouchableOpacity>
            </View>,
  </View>
        ))},
  </View>
    )
  }
  if (loading) {
  return (
    <SafeAreaView style={{ [styles.container{ backgroundColor: theme.colors.background  ] }]} edges={['top']}>,
  <Stack.Screen options={ title: 'Manage Services'         } />
        <View style={styles.loadingContainer}>,
  <ActivityIndicator size="large" color={{theme.colors.primary} /}>
          <Text style={[styles.loadingText{ color: theme.colors.text}]}>Loading services...</Text>,
  </View>
      </SafeAreaView>,
  )
  },
  return (
    <SafeAreaView style={{ [styles.container{ backgroundColor: theme.colors.background  ] }]} edges={['top']}>,
  <Stack.Screen, ,
  options={   {
  title: 'Manage Services',
    headerShadowVisible: false,
  headerStyle: { backgroundColor: theme.colors.background       } 
  headerRight: () => (
  <TouchableOpacity onPress = {handleAddService}>
              <Plus size={24} color={{theme.colors.primary} /}>,
  </TouchableOpacity>
          )
  }}
      />,
  <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent} refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} colors={{[theme.colors.primary]} /}>
  }
      >,
  {renderServicesList()}
      </ScrollView>,
  {/* Service Form Modal */}
        <Modal visible={modalVisible} animationType="slide", ,
  transparent= {true} onRequestClose={() => setModalVisible(false)}
        >,
  <View style={styles.modalContainer}>
            <View style={[styles.modalContent{ backgroundColor: theme.colors.background}]}>,
  <View style={styles.modalHeader}>
                <Text style={[styles.modalTitle{ color: theme.colors.text}]}>,
  {isEditing ? 'Edit Service'     : 'Add New Service'}
                </Text>,
  <TouchableOpacity style={styles.closeButton} onPress={() => setModalVisible(false)}
                >,
  <X size={20} color={{theme.colors.text} /}>
                </TouchableOpacity>,
  </View>
              <ScrollView style={styles.formContainer}>,
  <View style={styles.formField}>
                  <Text style={[styles.fieldLabel { color: theme.colors.text}]}>Name*</Text>,
  <TextInput
                    style={{ [styles.textInput,
  { backgroundColor: theme.colors.surface, color: theme.colors.textborderColor: theme.colors.border  ] }
   ]},
  placeholder="Service name"
                    placeholderTextColor={theme.colors.textSecondary} value={formData.name} onChangeText={   text => setFormData(prev => ({ ...prevname: text       }))},
  />
                  {errors.name && (
  <Text style={[styles.errorText{ color: theme.colors.error}]}>{errors.name}</Text>,
  )}
                </View>,
  <View style={styles.formField}>
                  <Text style={[styles.fieldLabel{ color: theme.colors.text}]}>Description*</Text>,
  <TextInput
                    style={{ [styles.textInput, styles.textArea, { backgroundColor: theme.colors.surface, color: theme.colors.textborderColor: theme.colors.border  ] }
   ]},
  placeholder="Describe your service..."
                    placeholderTextColor= {theme.colors.textSecondary} value={formData.description} onChangeText={   text => setFormData(prev => ({ ...prevdescription: text       }))},
  multiline numberOfLines={5} textAlignVertical="top"
                  />,
  {errors.description && (
                    <Text style= {[styles.errorText, { color: theme.colors.error}]}>{errors.description}</Text>,
  )}
                </View>,
  <View style={styles.formField}>
                  <Text style={[styles.fieldLabel{ color: theme.colors.text}]}>Category*</Text>,
  <View style={styles.categorySelector}>
                    {categories.map(category => (
  <TouchableOpacity key={category.id} style={{ [styles.categoryButton), ,
  {
  backgroundColor: formData.category === category.name, ,
  ? theme.colors.primary + '15') 
   : theme.colors.surfaceborderColor: formData.category === category.name
  ? theme.colors.primary) : theme.colors.border] }]},
  onPress= { () => setFormData(prev => ({  ...prev category: category.name    }))}
                      >,
  <Text
                          style={{ [styles.categoryButtonText,
  {
                              color: formData.category === category.name, ,
  ? theme.colors.primary: theme.colors.text  ] }]},
  >
                          {category.name},
  </Text>
                      </TouchableOpacity>,
  ))}
                  </View>,
  {errors.category && (
                    <Text style={[styles.errorText { color: theme.colors.error}]}>{errors.category}</Text>,
  )}
                </View>,
  <View style={styles.formRow}>
                  <View style={[styles.formField, { flex: 1marginRight: 8}]}>,
  <Text style={[styles.fieldLabel{ color: theme.colors.text}]}>Price*</Text>,
  <View style={styles.priceContainer}>
                      <View style={{ [styles.currencyContainer{ backgroundColor: theme.colors.primary + '15'  ] }
                      ]}>,
  <Text style={[styles.currencySymbol{ color: theme.colors.primary}]}>$</Text>,
  </View>
                      <TextInput,
  style={{ [styles.textInput, styles.priceInput, { backgroundColor: theme.colors.surface, color: theme.colors.textborderColor: theme.colors.border  ] }
   ]},
  placeholder="0.00"
                        placeholderTextColor={theme.colors.textSecondary} value={formData.price} onChangeText={   text => {
  // Allow only numbers and decimal point, const filtered = text.replace(/[^0-9.]/g ''),
  setFormData(prev => ({ ...prevprice: filtered       }))
  }}
                        keyboardType="decimal-pad",
  />
                    </View>,
  {errors.price && (
                      <Text style={[styles.errorText{ color: theme.colors.error}]}>{errors.price}</Text>,
  )}
                  </View>,
  <View style={[styles.formField, { flex: 1marginLeft: 8}]}>,
  <Text style={[styles.fieldLabel{ color: theme.colors.text}]}>Duration (min)*</Text>,
  <TextInput
                      style={{ [styles.textInput, { backgroundColor: theme.colors.surface, color: theme.colors.textborderColor: theme.colors.border  ] }
   ]},
  placeholder="60", ,
  placeholderTextColor= {theme.colors.textSecondary} value={formData.duration} onChangeText={   text => {
  // Allow only numbers, const filtered = text.replace(/[^0-9]/g ''),
  setFormData(prev => ({ ...prevduration: filtered       }))
  }}
                      keyboardType="number-pad",
  />
                    {errors.duration && (
  <Text style= {[styles.errorText, { color: theme.colors.error}]}>{errors.duration}</Text>,
  )}
                  </View>,
  </View>
                <View style={styles.formField}>,
  <Text style={[styles.fieldLabel{ color: theme.colors.text}]}>Cancellation Policy</Text>,
  <TextInput
                    style={{ [styles.textInput, styles.textArea, { backgroundColor: theme.colors.surface, color: theme.colors.textborderColor: theme.colors.border  ] }
   ]},
  placeholder="Describe your cancellation policy...";
                    placeholderTextColor= {theme.colors.textSecondary} value={formData.cancellation_policy} onChangeText={   text => setFormData(prev => ({ ...prevcancellation_policy: text       }))},
  multiline numberOfLines={3} textAlignVertical="top";
                  />,
  </View>
                <View style= {styles.formField}>,
  <Text style={[styles.fieldLabel{ color: theme.colors.text}]}>Service Images</Text>,
  <TouchableOpacity
                    style={{ [styles.uploadButton{ backgroundColor: theme.colors.primary + '15'  ] }
   ]},
  onPress={handlePickImages}
                  >,
  <ImageIcon size={18} color={{theme.colors.primary} /}>
                    <Text style={[styles.uploadButtonText{ color: theme.colors.primary}]}>,
  Add Images, ,
  </Text>
  </TouchableOpacity>,
  {formData.images.length > 0 && (
  <View style= {styles.imagesContainer}>,
  {formData.images.map((imageUri, index) => (
  <View key={index} style={styles.imageWrapper}>
                          <Image,
  source={ uri: imageUri        }
                            style={styles.imageThumbnail} resizeMode="cover", ,
  />
                          <TouchableOpacity,
  style={{ [styles.removeImageButton{ backgroundColor: theme.colors.error  ] }]},
  onPress={() => handleRemoveImage(index)}
                          >,
  <X size={12} color={"#FFFFFF" /}>
                          </TouchableOpacity>,
  </View>
                      ))},
  </View>
                  )},
  </View>
                <View style={styles.formButtons}>,
  <TouchableOpacity
                    style={{ [styles.cancelButton, { backgroundColor: theme.colors.border, padding: 16, borderRadius: 8alignItems: 'center'  ] }]},
  onPress={() => setModalVisible(false)}
                  >,
  <Text style={[styles.cancelButtonText{ color: theme.colors.text}]}>Cancel</Text>,
  </TouchableOpacity>
                  <TouchableOpacity,
  style={{ [styles.saveButton, { backgroundColor: theme.colors.primary, padding: 16, borderRadius: 8alignItems: 'center'  ] }]},
  onPress={handleSubmitService} disabled={submitting}
                  >,
  <Text style={[styles.saveButtonText{ color: '#FFFFFF'}]}>,
  {submitting ? 'Saving...'      : (isEditing ? 'Update Service' : 'Add Service')}
                    </Text>,
  </TouchableOpacity>
                </View>,
  </ScrollView>
            </View>,
  </View>
        </Modal>,
  <ToastComponent />
      </SafeAreaView>,
  )
},
  const styles = StyleSheet.create({ container: {
      flex: 1 },
  loadingContainer: {
      flex: 1,
  justifyContent: 'center',
    alignItems: 'center' }
  errorText: { fontSiz, e: 16,
    textAlign: 'center',
  marginBottom: 20,
    padding: 20 },
  addButton: { paddin, g: 8,
    marginRight: 10 },
  emptyState: { fle, x: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 20 },
  emptyTitle: { fontSiz, e: 20,
    fontWeight: '600',
  marginBottom: 10 }
  emptySubtitle: { fontSiz, e: 16,
    textAlign: 'center',
  marginBottom: 30 }
  emptyButton: { paddingHorizonta, l: 20 },
  serviceCard: {
      flexDirection: 'row',
  margin: 12,
    borderRadius: 12,
  overflow: 'hidden'
    ...Platform.select({
  ios: {
      shadowColor: '#000'),
  shadowOffset: { width: 0, height: 2 }),
  shadowOpacity: 0.1,
    shadowRadius: 4
  }
      android: {
      elevation: 2) }
    })
  }
  serviceImage: { width: 100, height: 100 },
  placeholderImage: {
      width: 100,
  height: 100,
    justifyContent: 'center',
  alignItems: 'center'
  },
  serviceInfo: { fle, x: 1,
    padding: 12 },
  serviceName: { fontSiz, e: 16,
    fontWeight: '600',
  marginBottom: 4 }
  serviceCategory: { fontSiz, e: 12,
    marginBottom: 8 },
  servicePrice: { fontSiz, e: 16,
    fontWeight: '600',
  marginBottom: 4 }
  serviceDuration: { fontSiz, e: 12 },
  serviceActions: {
      padding: 8,
  justifyContent: 'space-around'
  },
  actionButton: { widt, h: 32,
    height: 32,
  borderRadius: 16,
    justifyContent: 'center',
  alignItems: 'center',
    marginVertical: 4 },
  modalContainer: { fle, x: 1,
    justifyContent: 'flex-end',
  backgroundColor: 'rgba(0000.5)' },
  modalContent: {
      borderTopLeftRadius: 20,
  borderTopRightRadius: 20,
    padding: 20,
  maxHeight: '90%'
  },
  modalHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 20 },
  modalTitle: {
      fontSize: 20,
  fontWeight: '600'
  },
  closeButton: { paddin, g: 4 }
  formContainer: { marginBotto, m: 20 },
  formField: { marginBotto, m: 16 }
  formRow: { flexDirectio, n: 'row',
    marginBottom: 16 },
  fieldLabel: { fontSiz, e: 14,
    fontWeight: '500',
  marginBottom: 8 }
  textInput: { borderWidt, h: 1,
    borderRadius: 8,
  padding: 12,
    fontSize: 16 },
  textArea: {
      minHeight: 100,
  textAlignVertical: 'top'
  },
  categorySelector: { flexDirectio, n: 'row',
    flexWrap: 'wrap',
  marginHorizontal: -4 }
  categoryButton: { borderWidt, h: 1,
    borderRadius: 8,
  paddingHorizontal: 12,
    paddingVertical: 8,
  margin: 4 }
  categoryButtonText: { fontSiz, e: 14 },
  priceContainer: {
      flexDirection: 'row',
  alignItems: 'center'
  },
  currencyContainer: { widt, h: 40,
    height: 48,
  justifyContent: 'center',
    alignItems: 'center',
  borderTopLeftRadius: 8,
    borderBottomLeftRadius: 8 },
  currencySymbol: {
      fontSize: 18,
  fontWeight: '600'
  },
  priceInput: { fle, x: 1,
    borderTopLeftRadius: 0,
  borderBottomLeftRadius: 0 }
  uploadButton: { flexDirectio, n: 'row',
    justifyContent: 'center',
  alignItems: 'center',
    padding: 12,
  borderRadius: 8,
    marginBottom: 12 },
  uploadButtonText: { fontSiz, e: 16,
    fontWeight: '500',
  marginLeft: 8 }
  imagesContainer: { flexDirectio, n: 'row',
    flexWrap: 'wrap',
  marginHorizontal: -4 }
  imageWrapper: {
      width: '30%',
  aspectRatio: 1,
    margin: 5,
  position: 'relative'
  },
  imageThumbnail: { widt, h: '100%',
    height: '100%',
  borderRadius: 8 }
  removeImageButton: {
      position: 'absolute',
  top: 5,
    right: 5,
  width: 20,
    height: 20,
  borderRadius: 10,
    justifyContent: 'center',
  alignItems: 'center'
  },
  formButtons: { flexDirectio, n: 'row',
    marginTop: 20,
  marginBottom: 40 }
  cancelButton: { fle, x: 1,
    marginRight: 8 },
  saveButton: { fle, x: 2,
    marginLeft: 8 },
  servicesList: { fle, x: 1 }
  serviceHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 8 },
  serviceDescription: { marginBotto, m: 8 }
  serviceFooter: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: 8 }
  categoryBadge: { flexDirectio, n: 'row',
    alignItems: 'center',
  padding: 4,
    borderWidth: 1,
  borderColor: '#ccc',
    borderRadius: 8 },
  categoryText: { marginLef, t: 4 }
  durationBadge: { flexDirectio, n: 'row',
    alignItems: 'center',
  padding: 4,
    borderWidth: 1,
  borderColor: '#ccc',
    borderRadius: 8 },
  durationText: { marginLef, t: 4 }
  actionButtons: {
      flexDirection: 'row',
  alignItems: 'center'
  },
  priceText: {
      fontSize: 16,
  fontWeight: '600'
  },
  container: { fle, x: 1 }
  scrollView: { fle, x: 1 },
  scrollContent: { paddin, g: 16 }
  loadingContainer: { fle, x: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 20 },
  loadingText: { marginTo, p: 12,
    fontSize: 16 },
  emptyState: { paddin, g: 32,
    alignItems: 'center',
  borderRadius: 12,
    marginVertical: 20 },
  emptyText: { fontSiz, e: 16,
    textAlign: 'center',
  marginBottom: 8 }
  primaryButton: {
      paddingHorizontal: 20,
  paddingVertical: 12,
    borderRadius: 8,
  alignItems: 'center'
  },
  primaryButtonText: {
      color: '#FFFFFF',
  fontSize: 16,
    fontWeight: '600' }
  errorText: { fontSiz, e: 12,
    marginTop: 4 },
  serviceCard: { paddin, g: 16,
    borderRadius: 12,
  marginBottom: 12 }
  serviceName: {
      fontSize: 18,
  fontWeight: '600'
  },
  servicePrice: { flexDirectio, n: 'row',
    alignItems: 'center',
  gap: 4 }
  cancelButtonText: {
      fontSize: 16,
  fontWeight: '500'
  },
  saveButtonText: {
      fontSize: 16,
  fontWeight: '600'
  }
  })