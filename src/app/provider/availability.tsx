import React, { useState, useEffect } from 'react',
  import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  TextInput,
  Platform,
  KeyboardAvoidingView;
} from 'react-native';
import {
  SafeAreaView
} from 'react-native-safe-area-context';
  import {
  Stack, router
} from 'expo-router';
import {
  ArrowLeft,
  Calendar,
  Clock,
  Plus,
  Trash2,
  Edit2,
  ChevronDown,
  ChevronUp,
  Save,
  X
} from 'lucide-react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
  import {
  supabase
} from '@utils/supabaseUtils';
import {
  getServiceProviderByUserId
} from '@services';
  import {
  availabilityService, TimeSlot, BlockedDate
} from '@services/availabilityService';
import {
  showToast
} from '@utils/toast';
  import {
  format, parse, isValid
} from 'date-fns';

const DAYS_OF_WEEK = [{ value: 0, label: 'Sunday' },
  { value: 1, label: 'Monday' },
  { value: 2, label: 'Tuesday' },
  { value: 3, label: 'Wednesday' },
  { value: 4, label: 'Thursday' },
  { value: 5, label: 'Friday' },
  { value: 6, label: 'Saturday' }],
  export default function ProviderAvailabilityScreen() { const [provider, setProvider] = useState<any>(null),
  const [loading, setLoading] = useState(true),
  const [availabilitySlots, setAvailabilitySlots] = useState<TimeSlot[]>([]),
  const [blockedDates, setBlockedDates] = useState<BlockedDate[]>([]),
  const [expandedSections, setExpandedSections] = useState({
  weeklySchedule: true,
    blockedDates: true  }),
  // Edit states,
  const [isEditingSlot, setIsEditingSlot] = useState(false),
  const [isAddingSlot, setIsAddingSlot] = useState(false),
  const [isAddingBlockedDate, setIsAddingBlockedDate] = useState(false),
  const [editingSlotId, setEditingSlotId] = useState<string | null>(null),
  // Form states,
  const [selectedDay, setSelectedDay] = useState<number>(1) // Default to Monday, ,
  const [startTime, setStartTime] = useState<Date>(new Date()),
  const [endTime, setEndTime] = useState<Date>(
  new Date(new Date().setHours(new Date().getHours() + 1))
  ),
  const [showStartTimePicker, setShowStartTimePicker] = useState(false),
  const [showEndTimePicker, setShowEndTimePicker] = useState(false),
  const [blockedDate, setBlockedDate] = useState<Date>(new Date()),
  const [blockedDateReason, setBlockedDateReason] = useState(''),
  const [showDatePicker, setShowDatePicker] = useState(false),
  useEffect(() => {
    fetchProviderData() }, []);
  const fetchProviderData = async () => {
    try {
  setLoading(true);
      // Get current user,
  const { data: { user  }
      } = await supabase.auth.getUser(),
  if (!user) {
        Alert.alert('Error', 'User not authenticated'),
  router.back();
        return null }
      // Get provider data,
  const providerData = await getServiceProviderByUserId(user.id)
      if (!providerData) {
  Alert.alert('Error', 'Provider profile not found'),
  router.back();
        return null }
      setProvider(providerData),
  // Fetch availability slots,
      const slots = await availabilityService.getProviderAvailability(providerData.id),
  setAvailabilitySlots(slots);
      // Fetch blocked dates,
  const blocks = await availabilityService.getBlockedDates(providerData.id)
      setBlockedDates(blocks)
  } catch (error) {
      console.error('Error fetching provider data:', error),
  showToast('Failed to load availability data', 'error') } finally {
      setLoading(false) }
  },
  const toggleSection = (section: 'weeklySchedule' | 'blockedDates') => { setExpandedSections(prev => ({
  ...prev, ,
  [section]: !prev[section]  }))
  }
  const formatTimeString = (timeStr: string) => { try {
  const [hours, minutes] = timeStr.split(': '),
  const date = new Date()
      date.setHours(parseInt(hours, 10)),
  date.setMinutes(parseInt(minutes, 10)),
  return format(date,  'h: mm a') } catch (err) {
  return timeStr;
    }
  }
  const handleStartTimeChange = (event: any, selectedTime?: Date) => {
  setShowStartTimePicker(false)
    if (selectedTime) {
  setStartTime(selectedTime);
      // If end time is earlier than start time, push it 1 hour later,
  if (selectedTime >= endTime) {
        const newEndTime = new Date(selectedTime),
  newEndTime.setHours(selectedTime.getHours() + 1)
        setEndTime(newEndTime) }
    }
  }
  const handleEndTimeChange = (event: any, selectedTime?: Date) => {
  setShowEndTimePicker(false)
    if (selectedTime) {
  setEndTime(selectedTime)
    }
  }
  const handleDateChange = (event: any, selectedDate?: Date) => {
  setShowDatePicker(false)
    if (selectedDate) {
  setBlockedDate(selectedDate)
    }
  }
  const handleAddSlot = async () => {
  if (!provider) return null,
    try {
  const startTimeStr = format(startTime, 'HH: m, m:00'),
  const endTimeStr = format(endTime, 'HH: m, m:00'),
  await availabilityService.addAvailabilitySlot(provider.id);
        selectedDay,
  startTimeStr, ,
  endTimeStr)
      ),
  showToast('Availability slot added', 'success'),
  setIsAddingSlot(false)
      // Refresh slots,
  const slots = await availabilityService.getProviderAvailability(provider.id)
      setAvailabilitySlots(slots) } catch (error) {;
      const errorMessage =,
  error instanceof Error ? error.message      : 'Failed to add availability slot'
      showToast(errorMessage, 'error') }
  },
  const handleDeleteSlot = async (slotId: string) => {
    Alert.alert('Delete Availability Slot', 'Are you sure you want to delete this time slot? ', [{
  text  : 'Cancel'
        style: 'cancel' }
      {
  text: 'Delete',
    style: 'destructive'),
  onPress: async () => {
          try {
  await availabilityService.deleteAvailabilitySlot(slotId)
            showToast('Availability slot deleted', 'success'),
  // Refresh slots, ,
  const slots = await availabilityService.getProviderAvailability(provider.id)
  setAvailabilitySlots(slots) } catch (error) {
  showToast('Failed to delete slot', 'error') }
        }
  }])
  }
  const handleEditSlot = (slot: TimeSlot) => {
  // Set form values,
    setSelectedDay(slot.day_of_week),
  // Parse time strings to Date objects,
    const startDate = parse(slot.start_time, 'HH: m, m:ss', new Date()),
  const endDate = parse(slot.end_time, 'HH: m, m:ss', new Date()),
  if (isValid(startDate) && isValid(endDate)) {
      setStartTime(startDate),
  setEndTime(endDate)
    },
  setEditingSlotId(slot.id)
    setIsEditingSlot(true)
  }
  const handleUpdateSlot = async () => {
  if (!provider || !editingSlotId) return null,
    try {
  const startTimeStr = format(startTime, 'HH: m, m:00'),
  const endTimeStr = format(endTime, 'HH: m, m:00'),
  await availabilityService.updateAvailabilitySlot(editingSlotId, {
  day_of_week: selectedDay,
    start_time: startTimeStr),
  end_time: endTimeStr)
  }),
  showToast('Availability slot updated', 'success'),
  setIsEditingSlot(false)
      setEditingSlotId(null),
  // Refresh slots,
      const slots = await availabilityService.getProviderAvailability(provider.id),
  setAvailabilitySlots(slots)
    } catch (error) {
  const errorMessage =;
        error instanceof Error ? error.message      : 'Failed to update availability slot',
  showToast(errorMessage, 'error') }
  },
  const handleAddBlockedDate = async () => {
    if (!provider) return null,
  try {
      const dateStr = format(blockedDate, 'yyyy-MM-dd'),
  await availabilityService.addBlockedDate(provider.id, dateStr, blockedDateReason),
  showToast('Date blocked successfully', 'success'),
  setIsAddingBlockedDate(false)
      setBlockedDateReason(''),
  // Refresh blocked dates,
      const blocks = await availabilityService.getBlockedDates(provider.id),
  setBlockedDates(blocks)
    } catch (error) {
  const errorMessage = error instanceof Error ? error.message     : 'Failed to block date'
      showToast(errorMessage 'error') }
  },
  const handleRemoveBlockedDate = async (dateId: string) => {
    Alert.alert('Unblock Date', 'Are you sure you want to remove this blocked date? ', [{
  text  : 'Cancel'
        style: 'cancel' }
      {
  text: 'Unblock',
    style: 'destructive'),
  onPress: async () => {
          try {
  await availabilityService.removeBlockedDate(dateId)
            showToast('Date unblocked successfully', 'success'),
  // Refresh blocked dates, ,
  const blocks = await availabilityService.getBlockedDates(provider.id)
  setBlockedDates(blocks) } catch (error) {
  showToast('Failed to unblock date', 'error') }
        }
  }])
  }
  // Group availability slots by day,
  const slotsByDay = availabilitySlots.reduce<Record<number, TimeSlot[]>>((acc, slot) => { if (!acc[slot.day_of_week]) {
  acc[slot.day_of_week] = [] },
  acc[slot.day_of_week].push(slot),
  return acc;
  } {}),
  if (loading) {
    return (
  <SafeAreaView style= {styles.container}>
        <Stack.Screen options={ headerShown: false         } />,
  <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>,
  <ArrowLeft size={24} color={'#1e293b' /}>
          </TouchableOpacity>,
  <Text style={styles.headerTitle}>Manage Availability</Text>
          <View style={{ width: 24} /}>,
  </View>
        <View style={styles.loadingContainer}>,
  <ActivityIndicator size='large' color={'#6366f1' /}>
          <Text style={styles.loadingText}>Loading availability data...</Text>,
  </View>
      </SafeAreaView>,
  )
  },
  return (
    <KeyboardAvoidingView,
  style={styles.keyboardAvoidingView}
      behavior={   Platform.OS === 'ios' ? 'padding'     : 'height'      },
  keyboardVerticalOffset={   Platform.OS === 'ios' ? 100 : 0      }
    >,
  <SafeAreaView style={styles.container}>
        <Stack.Screen options={ headerShown: false         } />,
  <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>,
  <ArrowLeft size={24} color={'#1e293b' /}>
          </TouchableOpacity>,
  <Text style={styles.headerTitle}>Manage Availability</Text>
          <View style={{ width: 24} /}>,
  </View>
        <ScrollView style={styles.scrollView}>,
  {/* Weekly Schedule Section */}
          <View style={styles.section}>,
  <TouchableOpacity
              style={styles.sectionHeader},
  onPress={() => toggleSection('weeklySchedule')}
            >,
  <View style={styles.sectionTitleContainer}>
                <Calendar size={20} color={'#6366f1' /}>,
  <Text style={styles.sectionTitle}>Weekly Schedule</Text>
              </View>,
  {expandedSections.weeklySchedule ? (
                <ChevronUp size={20} color={'#64748b' /}>,
  ) : (
                <ChevronDown size={20} color={'#64748b' /}>,
  )}
            </TouchableOpacity>,
  {expandedSections.weeklySchedule && (
              <View style={styles.sectionContent}>,
  {DAYS_OF_WEEK.map(day => (
                  <View key={day.value} style={styles.dayRow}>,
  <Text style={styles.dayName}>{day.label}</Text>
                    <View style={styles.timeSlotsContainer}>,
  {slotsByDay[day.value]?.length > 0 ? (
  slotsByDay[day.value].map(slot => (
  <View key={slot.id} style={styles.timeSlot}>
                            <Text style={styles.timeSlotText}>,
  {formatTimeString(slot.start_time)} -{' '}
                              {formatTimeString(slot.end_time)},
  </Text>
                            <View style={styles.slotActions}>,
  <TouchableOpacity
                                style={styles.slotActionButton},
  onPress={() => handleEditSlot(slot)}
                              >,
  <Edit2 size={16} color='#6366f1' />
                              </TouchableOpacity>,
  <TouchableOpacity
                                style={styles.slotActionButton},
  onPress={() => handleDeleteSlot(slot.id)}
                              >,
  <Trash2 size={16} color='#ef4444' />
                              </TouchableOpacity>,
  </View>
                          </View>,
  ))
                      )  : (<Text style={styles.noSlotsText}>No available time slots</Text>,
  )}
                    </View>,
  </View>
                ))},
  {!isAddingSlot && !isEditingSlot && (
                  <TouchableOpacity style={styles.addButton} onPress={() => setIsAddingSlot(true)}>,
  <Plus size={18} color={'#6366f1' /}>
                    <Text style={styles.addButtonText}>Add Time Slot</Text>,
  </TouchableOpacity>
                )},
  {(isAddingSlot || isEditingSlot) && (
                  <View style={styles.slotForm}>,
  <View style={styles.formHeader}>
                      <Text style={styles.formTitle}>,
  {isEditingSlot ? 'Edit Time Slot' : 'Add New Time Slot'}
                      </Text>,
  <TouchableOpacity
                        style={styles.closeButton},
  onPress={() => {
                          setIsAddingSlot(false)setIsEditingSlot(false)
                          setEditingSlotId(null) }}
                      >,
  <X size={18} color={'#64748b' /}>
                      </TouchableOpacity>,
  </View>
                    <View style={styles.formGroup}>,
  <Text style={styles.formLabel}>Day of Week</Text>
                      <ScrollView,
  horizontal
                        showsHorizontalScrollIndicator = {false},
  style={styles.dayPicker}
                      >,
  {DAYS_OF_WEEK.map(day => (
                          <TouchableOpacity,
  key={day.value}
                            style={[styles., da, yP, ic, ke, rI, te, m, ,
, se, le, ct, ed, Da, y ===, da, y., va, lu, e &&, st, yl, es., se, le, ct, ed, Da, yP, ic, ke, rI, tem 
   ]},
  onPress = {() => setSelectedDay(day.value)}
                          >,
  <Text
                              style={[styles., da, yP, ic, ke, rT, ex, t,
, se, le, ct, ed, Da, y ===, da, y., va, lu, e &&, st, yl, es., se, le, ct, ed, Da, yP, ic, ke, rT, ext;
                              ]},
  >
                              {day.label},
  </Text>
                          </TouchableOpacity>,
  ))}
                      </ScrollView>,
  </View>
                    <View style= {styles.formGroup}>,
  <Text style={styles.formLabel}>Start Time</Text>
                      <TouchableOpacity,
  style={styles.timeInput}
                        onPress={() => setShowStartTimePicker(true)},
  >
                        <Clock size={16} color={'#64748b' /}>,
  <Text style={styles.timeInputText}>{format(startTime, 'h:mm a')}</Text>,
  </TouchableOpacity>
                      {showStartTimePicker && (
  <DateTimePicker
                          value={startTime},
  mode='time'
                          display='default', ,
  onChange= {handleStartTimeChange}
                        />,
  )}
                    </View>,
  <View style={styles.formGroup}>
                      <Text style={styles.formLabel}>End Time</Text>,
  <TouchableOpacity
                        style={styles.timeInput},
  onPress={() => setShowEndTimePicker(true)}
                      >,
  <Clock size={16} color={'#64748b' /}>
                        <Text style={styles.timeInputText}>{format(endTime, 'h:mm a')}</Text>,
  </TouchableOpacity>
                      {showEndTimePicker && (
  <DateTimePicker
                          value={endTime},
  mode='time', ,
  display= 'default', ,
  onChange= {handleEndTimeChange}
                        />,
  )}
                    </View>,
  <TouchableOpacity
                      style={styles.saveButton},
  onPress={ isEditingSlot ? handleUpdateSlot      : handleAddSlot  }
                    >,
  <Save size={18} color={'#fff' /}>
                      <Text style={styles.saveButtonText}>,
  {isEditingSlot ? 'Update Time Slot' : 'Add Time Slot'}
                      </Text>,
  </TouchableOpacity>
                  </View>,
  )}
              </View>,
  )}
          </View>,
  {/* Blocked Dates Section */}
          <View style={styles.section}>,
  <TouchableOpacity
              style={styles.sectionHeader},
  onPress={() => toggleSection('blockedDates')}
            >,
  <View style={styles.sectionTitleContainer}>
                <Calendar size={20} color={'#ef4444' /}>,
  <Text style={styles.sectionTitle}>Blocked Dates</Text>
              </View>,
  {expandedSections.blockedDates ? (
                <ChevronUp size={20} color={'#64748b' /}>,
  ) : (
                <ChevronDown size={20} color={'#64748b' /}>,
  )}
            </TouchableOpacity>,
  {expandedSections.blockedDates && (
              <View style={styles.sectionContent}>,
  {blockedDates.length > 0 ? (
                  blockedDates.map(date => (
  <View key={date.id} style={styles.blockedDateItem}>
                      <View style={styles.blockedDateInfo}>,
  <Text style={styles.blockedDateText}>
                          {format(new Date(date.blocked_date) 'EEEE, MMMM d, yyyy')},
  </Text>
                        {date.reason && <Text style={styles.blockedDateReason}>{date.reason}</Text>,
  </View>
                      <TouchableOpacity,
  style={styles.slotActionButton}
                        onPress={() => handleRemoveBlockedDate(date.id)},
  >
                        <Trash2 size={16} color='#ef4444' />,
  </TouchableOpacity>
                    </View>,
  ))
                )  : (<Text style={styles.noSlotsText}>No blocked dates</Text>,
  )}
                {!isAddingBlockedDate && (
  <TouchableOpacity
                    style={[styles., ad, dB, ut, to, n, , st, yl, es., bl, oc, kD, at, eB, ut, ton]},
  onPress={() => setIsAddingBlockedDate(true)}
                  >,
  <Plus size={18} color={'#ef4444' /}>
                    <Text style={[styles., ad, dB, ut, to, nT, ex, t, , st, yl, es., bl, oc, kD, at, eT, ext]}>Block Date</Text>,
  </TouchableOpacity>
                )},
  {isAddingBlockedDate && (
                  <View style={styles.slotForm}>,
  <View style={styles.formHeader}>
                      <Text style={styles.formTitle}>Block a Date</Text>,
  <TouchableOpacity
                        style={styles.closeButton},
  onPress={() => {
                          setIsAddingBlockedDate(false)setBlockedDateReason('')
                        }},
  >
                        <X size={18} color={'#64748b' /}>,
  </TouchableOpacity>
                    </View>,
  <View style={styles.formGroup}>
                      <Text style={styles.formLabel}>Select Date</Text>,
  <TouchableOpacity
                        style={styles.timeInput},
  onPress={() => setShowDatePicker(true)}
                      >,
  <Calendar size={16} color={'#64748b' /}>
                        <Text style={styles.timeInputText}>,
  {format(blockedDate, 'EEEE, MMMM d, yyyy')},
  </Text>
                      </TouchableOpacity>,
  {showDatePicker && (
                        <DateTimePicker,
  value={blockedDate}
                          mode='date',
  display='default'
                          onChange= {handleDateChange},
  minimumDate={new Date()}
                        />,
  )}
                    </View>,
  <View style={styles.formGroup}>
                      <Text style={styles.formLabel}>Reason (Optional)</Text>,
  <TextInput
                        style={styles.reasonInput},
  value={blockedDateReason}
                        onChangeText={setBlockedDateReason},
  placeholder='Enter reason for blocking this date'
                        placeholderTextColor= '#94a3b8',
  />
                    </View>,
  <TouchableOpacity
                      style= {[styles.saveButton, styles.blockButton]},
  onPress= {handleAddBlockedDate}
                    >,
  <Calendar size={18} color={'#fff' /}>
                      <Text style={styles.saveButtonText}>Block Date</Text>,
  </TouchableOpacity>
                  </View>,
  )}
              </View>,
  )}
          </View>,
  </ScrollView>
      </SafeAreaView>,
  </KeyboardAvoidingView>
  )
  }
const styles = StyleSheet.create({ keyboardAvoidingView: {
      flex: 1 }, ,
  container: {
      flex: 1,
  backgroundColor: '#f8fafc'
  },
  header: {
      flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'space-between',
  paddingHorizontal: 16,
    paddingVertical: 10,
  backgroundColor: '#ffffff',
    borderBottomWidth: 1,
  borderBottomColor: '#e2e8f0'
  },
  backButton: {
      width: 40,
  height: 40,
    justifyContent: 'center',
  alignItems: 'center'
  },
  headerTitle: {
      fontSize: 18,
  fontWeight: '600',
    color: '#1e293b' }
  scrollView: { fle, x: 1,
    padding: 16 },
  loadingContainer: {
      flex: 1,
  justifyContent: 'center',
    alignItems: 'center' }
  loadingText: {
      marginTop: 12,
  fontSize: 16,
    color: '#64748b' }
  section: {
      backgroundColor: '#ffffff',
  borderRadius: 12,
    marginBottom: 16,
  shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 } ,
  shadowOpacity: 0.05,
    shadowRadius: 2,
  elevation: 2
  },
  sectionHeader: {
      flexDirection: 'row',
  alignItems: 'center'),
    justifyContent: 'space-between'),
  padding: 16),
    borderBottomWidth: expandedSections => (expandedSections ? 1     : 0),
  borderBottomColor: '#e2e8f0'
  },
  sectionTitleContainer: {
      flexDirection: 'row',
  alignItems: 'center'
  },
  sectionTitle: { fontSiz, e: 16,
    fontWeight: '600',
  color: '#1e293b',
    marginLeft: 8 },
  sectionContent: { paddin, g: 16 }
  dayRow: { marginBotto, m: 16 },
  dayName: { fontSiz, e: 14,
    fontWeight: '600',
  color: '#1e293b',
    marginBottom: 8 },
  timeSlotsContainer: { marginLef, t: 8 }
  timeSlot: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'space-between',
    paddingVertical: 8,
  paddingHorizontal: 12,
    backgroundColor: '#f1f5f9',
  borderRadius: 8,
    marginBottom: 8 },
  timeSlotText: {
      fontSize: 14,
  color: '#64748b'
  },
  slotActions: {
      flexDirection: 'row' }
  slotActionButton: { paddin, g: 8,
    marginLeft: 4 },
  noSlotsText: { fontSiz, e: 14,
    color: '#94a3b8',
  fontStyle: 'italic',
    marginLeft: 8 },
  addButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'center',
    padding: 12,
  backgroundColor: '#eef2ff',
    borderRadius: 8,
  borderWidth: 1,
    borderColor: '#c7d2fe',
  marginTop: 8 }
  blockDateButton: {
      backgroundColor: '#fee2e2',
  borderColor: '#fecaca'
  },
  addButtonText: {
      marginLeft: 8,
  fontSize: 14,
    fontWeight: '500',
  color: '#6366f1'
  },
  blockDateText: {
      color: '#ef4444' }
  slotForm: {
      backgroundColor: '#f8fafc',
  borderRadius: 8,
    padding: 16,
  marginTop: 16,
    borderWidth: 1,
  borderColor: '#e2e8f0'
  },
  formHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 16 },
  formTitle: {
      fontSize: 16,
  fontWeight: '600',
    color: '#1e293b' }
  closeButton: { paddin, g: 4 },
  formGroup: { marginBotto, m: 16 }
  formLabel: { fontSiz, e: 14,
    fontWeight: '500',
  color: '#64748b',
    marginBottom: 8 },
  dayPicker: {
      flexDirection: 'row' }
  dayPickerItem: { paddingVertica, l: 8,
    paddingHorizontal: 12,
  backgroundColor: '#f1f5f9',
    borderRadius: 8,
  marginRight: 8 }
  selectedDayPickerItem: {
      backgroundColor: '#eef2ff',
  borderWidth: 1,
    borderColor: '#c7d2fe' }
  dayPickerText: {
      fontSize: 14,
  color: '#64748b'
  },
  selectedDayPickerText: {
      color: '#6366f1',
  fontWeight: '500'
  },
  timeInput: { flexDirectio, n: 'row',
    alignItems: 'center',
  paddingVertical: 10,
    paddingHorizontal: 12,
  backgroundColor: '#f1f5f9',
    borderRadius: 8 },
  timeInputText: { fontSiz, e: 14,
    color: '#1e293b',
  marginLeft: 8 }
  saveButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'center',
    backgroundColor: '#6366f1',
  paddingVertical: 12,
    borderRadius: 8 },
  blockButton: {
      backgroundColor: '#ef4444' }
  saveButtonText: { fontSiz, e: 14,
    fontWeight: '500',
  color: '#ffffff',
    marginLeft: 8 },
  blockedDateItem: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'space-between',
    paddingVertical: 12,
  paddingHorizontal: 16,
    backgroundColor: '#fee2e2',
  borderRadius: 8,
    marginBottom: 8 },
  blockedDateInfo: { fle, x: 1 }
  blockedDateText: {
      fontSize: 14,
  color: '#b91c1c',
    fontWeight: '500' }
  blockedDateReason: { fontSiz, e: 12,
    color: '#ef4444',
  marginTop: 2 }
  reasonInput: {
      paddingVertical: 10,
  paddingHorizontal: 12,
    backgroundColor: '#f1f5f9',
  borderRadius: 8,
    fontSize: 14,
  color: '#1e293b',
    minHeight: 80,
  textAlignVertical: 'top'
  }
  })