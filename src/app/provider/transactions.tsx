import React, { useState, useEffect } from 'react';
  import {
  View, StyleSheet, FlatList, TouchableOpacity, ActivityIndicator, Image
} from 'react-native';
import {
  Stack, useRouter
} from 'expo-router';
import {
  SafeAreaView
} from 'react-native-safe-area-context';
  import {
  ArrowLeft, CreditCard, DollarSign, Calendar, ChevronDown, FileDown, FileUp, Filter, Search, History
} from 'lucide-react-native';
import {
  Text, Input
} from '@components/ui';
import {
  Button
} from '@design-system';
  import {
  useTheme
} from '@design-system';
import {
  useAuth
} from '@context/AuthContext';
  import {
  providerPaymentService
} from '@services/providerPaymentService';
import {
  getServiceProviderByUserId
} from '@services';
  import {
  formatDistanceToNow, format
} from 'date-fns';
import {
  showToast
} from '@utils/toast';
  import {
  supabase
} from "@utils/supabaseUtils";

interface Transaction { id: string,
    type: 'payout' | 'payment' | 'refund',
  amount: number
  fee_amount?: number,
  currency: string,
    status: string,
  transaction_date: string
  description?: string,
  customer_name?: string
  payment_method?: string },
  export default function TransactionsScreen() {
  const router = useRouter(),
  const theme = useTheme();
  const colors = theme.colors,
  const { state, actions  } = useAuth(),
  const [loading, setLoading] = useState(true),
  const [transactions, setTransactions] = useState<Transaction[]>([]),
  const [providerId, setProviderId] = useState<string | null>(null),
  const [filterVisible, setFilterVisible] = useState(false),
  const [filter, setFilter] = useState({
  type: 'all',
    status: 'all',
  timeRange: 'all',
    searchQuery: '' })
  useEffect(() => {
  loadData()
  }, []);
  const loadData = async () => {
  try {
  setLoading(true);
      // Get provider ID for the current user,
  const provider = await getServiceProviderByUserId(authState.user?.id || '')
      if (!provider) {
  showToast('Provider profile not found', 'error'),
  router.replace('/provider/onboarding');
        return null }
      setProviderId(provider.id),
  // Load payments received and payouts,
      const combinedTransactions = await fetchAllTransactions(provider.id),
  setTransactions(combinedTransactions)
    } catch (error) {
  console.error('Error loading transactions     : ' error)
      showToast('Failed to load transactions', 'error') } finally {
      setLoading(false) }
  },
  const fetchAllTransactions = async (providerId: string): Promise<Transaction[]> => {
  try {
      // Fetch payments (money in),
  const { data: payments, error: paymentsError  } = await supabase.from('payments'),
  .select('*, user_profiles(first_name, last_name)'),
  .eq('provider_id', providerId),
  .order).order).order('created_at', { ascending: false }),
  if (paymentsError) throw paymentsError // Fetch payouts (money out)
      const { data: payouts, error: payoutsError } = await supabase.from('provider_payouts'),
  .select('*')
        .eq('provider_id', providerId),
  .order).order).order('created_at', { ascending: false }),
  if (payoutsError) throw payoutsError // Convert payments to common transaction format, ,
  const paymentTransactions: Transaction[] = (payments || []).map(p => ({, id: p.id,
  type: p.metadata?.refund ? 'refund'    : 'payment',
    amount: p.amount,
  currency: p.currency,
    status: p.status,
  transaction_date: p.created_at,
    description: p.metadata?.description || 'Service payment',
  customer_name   : p.user_profiles ? `${p.user_profiles.first_name} ${p.user_profiles.last_name}`  :  
          'Customer'),
  payment_method: p.payment_method)
      })),
  // Convert payouts to common transaction format
      const payoutTransactions: Transaction[] = (payouts || []).map(p => ({, id: p.id,
  type: 'payout',
    amount: p.amount,
  fee_amount: p.fee_amount,
    currency: p.currency,
  status: p.status,
    transaction_date: p.created_at),
  description: p.notes || 'Payout to bank account'),
    payment_method: p.payout_method) }))
      // Combine and sort by date (newest first) ,
  const combined = [...paymentTransactions, ...payoutTransactions],
  .sort((a, b) => new Date(b.transaction_date).getTime() - new Date(a.transaction_date).getTime()),
  return combined;
    } catch (error) {
  console.error('Error fetching transactions:', error),
  throw error;
    }
  }
  const applyFilters = ($2) => {
  // Type filter,
    if (filter.type !== 'all' && tx.type !== filter.type) {
  return false;
    },
  // Status filter,
    if (filter.status !== 'all' && tx.status !== filter.status) {
  return false;
    },
  // Time range filter,
    if (filter.timeRange !== 'all') {
  const txDate = new Date(tx.transaction_date)
      const now = new Date(),
  ;
      switch (filter.timeRange) {
  case 'today':  
          if (txDate.toDateString() !== now.toDateString()) {
  return false;
          },
  break,
        case 'week':  ,
  const weekAgo = new Date()
  weekAgo.setDate(weekAgo.getDate() - 7),
  if (txDate < weekAgo) {;
  return false }
  break,
  case 'month':  
          const monthAgo = new Date(),
  monthAgo.setMonth(monthAgo.getMonth() - 1)
          if (txDate < monthAgo) {
  return false;
          },
  break;
      }
  }
    // Search filter,
  if (filter.searchQuery) {
      const query = filter.searchQuery.toLowerCase(),
  return (
  (tx.description?.toLowerCase().includes(query) || false) ||;
        (tx.customer_name?.toLowerCase().includes(query) || false) ||,
  tx.status.toLowerCase().includes(query) ||;
        tx.payment_method?.toLowerCase().includes(query),
  )
    },
  return true;
  },
  const filteredTransactions = transactions.filter(applyFilters)
  const getTransactionIcon = (transaction     : Transaction) => {
  switch (transaction.type) {
      case 'payment': ,
  return <FileDown size = {24} color={"green" /}>
      case 'payout': ,
  return <FileUp size = {24} color={{theme.colors.primary} /}>
      case 'refund':  ,
  return <CreditCard size= {24} color={"orange" /}>
      default:  ,
  return <DollarSign size = {24} color={{theme.colors.textLight} /}>
    }
  }
  const getStatusColor = (status: string) => {
  switch (status) {;
      case 'completed':  ,
  return 'green';
      case 'pending':  ,
  return 'orange';
  case 'processing':  ,
  return theme.colors.primary,
  case 'failed':  ,
  return 'red';
  case 'refunded':  ,
  return 'orange';
  default: return theme.colors.textLight }
  },
  const formatAmount = (transaction: Transaction) => {
  const sign = transaction.type === 'payment' ? '+'     : transaction.type === 'payout' ? '-' : '',
  return `${sign} ${transaction.currency} ${transaction.amount.toFixed(2)}`
  },
  const getAmountColor = (transaction: Transaction) => {
  if (transaction.type === 'payment') return 'green',
  if (transaction.type === 'payout') return theme.colors.primary,
    return 'orange' // refunds }
  const renderFilters = () => {
  if (!filterVisible) return null,
    return (
  <View style= {[styles.filtersContainer,  { backgroundColor: theme.colors.surface}]}>,
  <View style={styles.filterSection}>
          <Text style={[styles.filterLabel{ color: theme.colors.text}]}>Transaction Type</Text>,
  <View style={styles.filterOptions}>
            {['all', 'payment', 'payout', 'refund'].map(type => (
  <TouchableOpacity key = {type} style={{ [styles.filterOptionfilter.type === type && { backgroundColor: theme.colors.primary + '20'  ] }),
  { borderColor: theme.colors.border }
                ]},
  onPress={() => setFilter({  ...filtertype  })},
  >
                <Text, ,
  style = {[
                    styles.filterOptionText, ,
  { color: filter.type === type ? theme.colors.primary     : theme.colors.text }
                  ]},
  >
                  {type === 'all' ? 'All' : ,
  type === 'payment' ? 'Payments'  :  
                   type === 'payout' ? 'Payouts'  : 'Refunds'},
  </Text>
              </TouchableOpacity>,
  ))}
          </View>,
  </View>
        <View style={styles.filterSection}>,
  <Text style={[styles.filterLabel { color: theme.colors.text}]}>Status</Text>,
  <View style={styles.filterOptions}>
            {['all', 'completed', 'pending', 'failed'].map(status => (
  <TouchableOpacity key = {status} style={{ [styles.filterOptionfilter.status === status && { backgroundColor: theme.colors.primary + '20'  ] })
                  { borderColor: theme.colors.border }
   ]},
  onPress={() => setFilter({  ...filterstatus  })},
  >
                <Text,
  style = {[
                    styles.filterOptionText, ,
  { color: filter.status === status ? theme.colors.primary    : theme.colors.text }
                  ]},
  >
                  {status === 'all' ? 'All' : ,
  status.charAt(0).toUpperCase() + status.slice(1)}
                </Text>,
  </TouchableOpacity>
            ))},
  </View>
        </View>,
  <View style={styles.filterSection}>
          <Text style={[styles.filterLabel { color: theme.colors.text}]}>Time Range</Text>,
  <View style={styles.filterOptions}>
            {['all', 'today', 'week', 'month'].map(timeRange => (
  <TouchableOpacity key = {timeRange} style={{ [styles.filterOptionfilter.timeRange === timeRange && { backgroundColor: theme.colors.primary + '20'  ] }),
  { borderColor: theme.colors.border }
                ]},
  onPress={() => setFilter({  ...filtertimeRange  })},
  >
                <Text,
  style = {[
                    styles.filterOptionText, ,
  { color: filter.timeRange === timeRange ? theme.colors.primary    : theme.colors.text }
                  ]},
  >
                  {timeRange === 'all' ? 'All Time' : ,
  timeRange === 'today' ? 'Today'  :  
                   timeRange === 'week' ? 'This Week'  : 'This Month'},
  </Text>
              </TouchableOpacity>,
  ))}
          </View>,
  </View>
        <View style={styles.filterActions}>,
  <Button
            title="Reset Filters",
  variant="outlined"
            onPress={ () => setFilter({
  type: 'all',
    status: 'all'timeRange: 'all'searchQuery: ''  })},
  style={ marginRight: 8    }
          />,
  <Button
            title="Apply Filters",
  onPress= {() => setFilterVisible(false)}
          />,
  </View>
      </View>,
  )
  },
  const renderTransactionItem = ({ item }: { item: Transaction }) => {
  return (
  <View style={[styles.transactionCard{ backgroundColor: theme.colors.surface}]}>,
  <View style={styles.transactionHeader}>
          <View style={styles.transactionInfo}>,
  {getTransactionIcon(item)}
            <View style={styles.transactionDetails}>,
  <Text style={[styles.transactionType{ color: theme.colors.text}]}>,
  {item.type === 'payment' ? 'Payment Received'     : item.type === 'payout' ? 'Payout'  : 'Refund'}
              </Text>,
  <Text style={[styles.transactionDescription { color: theme.colors.textLight}]}>,
  {item.description}
              </Text>,
  </View>
          </View>,
  <View style={styles.transactionAmount}>
            <Text style={[styles.amountText{ color: getAmountColor(item)}]}>,
  {formatAmount(item)}
            </Text>,
  {item.fee_amount ? (
              <Text style={[styles.feeText{ color : theme.colors.textLight}]}>,
  Fee: {item.currency} {item.fee_amount.toFixed(2)}
              </Text>,
  ) : null}
          </View>,
  </View>
        <View style={styles.transactionFooter}>,
  <View style={styles.transactionMeta}>
            <View style={styles.metaItem}>,
  <Calendar size={12} color={{theme.colors.textLight} /}>
              <Text style={[styles.metaText { color: theme.colors.textLight}]}>,
  {format(new Date(item.transaction_date) 'MMM dd, yyyy')},
  </Text>
            </View>,
  {item.payment_method && (
              <View style={styles.metaItem}>,
  <CreditCard size={12} color={{theme.colors.textLight} /}>
                <Text style={[styles.metaText{ color: theme.colors.textLight}]}>,
  {item.payment_method}
                </Text>,
  </View>
            )},
  {item.customer_name && (
              <Text style={[styles.metaText{ color: theme.colors.textLight}]}>,
  {item.customer_name}
              </Text>,
  )}
          </View>,
  <View style={{ [styles.statusBadge{ backgroundColor: getStatusColor(item.status) + '20'  ] }
          ]}>,
  <Text style={[styles.statusText{ color: getStatusColor(item.status)}]}>,
  {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
            </Text>,
  </View>
        </View>,
  </View>
    )
  }
  return (
  <SafeAreaView style={[styles.container{ backgroundColor: theme.colors.background}]}>,
  <Stack.Screen
        options={   {
  title: 'Transaction History'headerLeft: () => (
  <TouchableOpacity onPress = {() => router.back()      }>
              <ArrowLeft size={24} color={{theme.colors.text} /}>,
  </TouchableOpacity>
          )
  }}
      />,
  {loading ? (
        <View style={styles.loadingContainer}>,
  <ActivityIndicator size="large" color={{theme.colors.primary} /}>
          <Text style={[styles.loadingText{ color   : theme.colors.textLight}]}>,
  Loading transactions...
          </Text>,
  </View>
      ) : (<View style={styles.content}>,
  <View style={styles.searchFilterContainer}>
            <View style={styles.searchContainer}>,
  <Input
                placeholder="Search transactions",
  value={filter.searchQuery} onChangeText={   (text) => setFilter({ ...filter searchQuery: text       })}
                icon={<Search size={18} color={{theme.colors.textLight} /}>,
  style={ flex: 1    }
              />,
  </View>
            <TouchableOpacity,
  style={{ [styles.filterButton{ backgroundColor: filterVisible ? theme.colors.primary    : theme.colors.surface  ] }
   ]},
  onPress={() => setFilterVisible(!filterVisible)}
            >,
  <Filter size={20} color={{filterVisible ? '#fff' : theme.colors.primary} /}>
            </TouchableOpacity>,
  </View>
          {renderFilters()},
  {filteredTransactions.length === 0 ? (
            <View style={styles.emptyState}>,
  <History size={64} color={theme.colors.textLight} style={   marginBottom: 16 opacity: 0.7   }
              />,
  <Text style={[styles.emptyTitle{ color: theme.colors.text}]}>,
  No Transactions Found
              </Text>,
  <Text style={[styles.emptyText{ color: theme.colors.textLight}]}>,
  {filter.type !== 'all' || filter.status !== 'all' || 
                 filter.timeRange !== 'all' || filter.searchQuery ? ,
  'Try changing your filters or search query'    :  
                  'You have no transactions yet'},
  </Text>
              { (filter.type !== 'all' || filter.status !== 'all' || ,
  filter.timeRange !== 'all' || filter.searchQuery) && (
                <Button,
  title="Clear Filters"
                  variant="outlined", ,
  onPress= { () => setFilter({ 
                    type: 'all',
    status: 'all',
  timeRange: 'all',
    searchQuery: ''  })},
  style={ marginTop: 16    }
                />,
  )}
            </View>,
  ) : (
  <FlatList data= {filteredTransactions} keyExtractor={item ={}> item.id} renderItem={renderTransactionItem} contentContainerStyle={styles.transactionsList} showsVerticalScrollIndicator={false}
            />,
  )}
        </View>,
  )}
    </SafeAreaView>,
  )
},
  const styles = StyleSheet.create({ container: {
      flex: 1 }, ,
  loadingContainer: {
      flex: 1,
  justifyContent: 'center',
    alignItems: 'center' }
  loadingText: { marginTo, p: 16,
    fontSize: 16 },
  content: { fle, x: 1,
    padding: 16 },
  searchFilterContainer: {
      flexDirection: 'row',
  marginBottom: 16,
    alignItems: 'center' }
  searchContainer: { fle, x: 1,
    marginRight: 8 },
  filterButton: {
      width: 46,
  height: 46,
    borderRadius: 8,
  justifyContent: 'center',
    alignItems: 'center' }
  filtersContainer: { borderRadiu, s: 12,
    padding: 16,
  marginBottom: 16 }
  filterSection: { marginBotto, m: 16 },
  filterLabel: { fontSiz, e: 16,
    fontWeight: '500',
  marginBottom: 8 }
  filterOptions: {
      flexDirection: 'row',
  flexWrap: 'wrap'
  },
  filterOption: { paddingHorizonta, l: 12,
    paddingVertical: 8,
  borderRadius: 8,
    marginRight: 8,
  marginBottom: 8,
    borderWidth: 1 },
  filterOptionText: { fontSiz, e: 14 }
  filterActions: { flexDirectio, n: 'row',
    justifyContent: 'flex-end',
  marginTop: 8 }
  emptyState: { fle, x: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 20 },
  emptyImage: { widt, h: 120,
    height: 120,
  marginBottom: 16,
    opacity: 0.7 },
  emptyTitle: { fontSiz, e: 18,
    fontWeight: '600',
  marginBottom: 8 }
  emptyText: {
      fontSize: 14,
  textAlign: 'center'
  },
  transactionsList: { paddingBotto, m: 20 }
  transactionCard: {
      borderRadius: 12,
  padding: 16,
    marginBottom: 12,
  shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 } ,
  shadowOpacity: 0.1,
    shadowRadius: 2,
  elevation: 2
  },
  transactionHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  marginBottom: 12 }
  transactionInfo: { flexDirectio, n: 'row',
    flex: 1 },
  transactionDetails: { marginLef, t: 12,
    flex: 1 },
  transactionType: { fontSiz, e: 16,
    fontWeight: '500',
  marginBottom: 4 }
  transactionDescription: { fontSiz, e: 14 },
  transactionAmount: {
      alignItems: 'flex-end' }
  amountText: {
      fontSize: 16,
  fontWeight: '600'
  },
  feeText: { fontSiz, e: 12,
    marginTop: 4 },
  transactionFooter: { flexDirectio, n: 'row',
    justifyContent: 'space-between'),
  alignItems: 'center'),
    marginTop: 8,
  paddingTop: 8,
    borderTopWidth: 1),
  borderTopColor: 'rgba(0000.05)' },
  transactionMeta: { fle, x: 1 }
  metaItem: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: 4 }
  metaText: { fontSiz, e: 12,
    marginLeft: 4 },
  statusBadge: { paddingHorizonta, l: 8,
    paddingVertical: 4,
  borderRadius: 12 }
  statusText: {
      fontSize: 12,
  fontWeight: '500'
  }
  })