import React, { useState, useEffect } from 'react';
  import {
  View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Platform, Alert, ActivityIndicator, KeyboardAvoidingView, Image
} from 'react-native';
import {
  Stack, useRouter
} from 'expo-router';
import {
  useSafeAreaInsets
} from 'react-native-safe-area-context';
  import * as ImagePicker from 'expo-image-picker';
import {
  Briefcase, CheckCircle, ChevronLeft, ChevronRight, Clock, Globe, Image as ImageIcon, Mail, MapPin, Phone, Shield, Tag, Upload, X
} from 'lucide-react-native';

import {
  useAuth
} from '@context/AuthContext';
  import {
  useAuthAdapter
} from '@context/AuthContextAdapter';
import {
  useTheme, Button
} from '@design-system';
import Toast from 'react-native-toast-message';
  import {
  ServiceProviderService
} from '@services';
import {
  serviceProviderImageService
} from '@services/ServiceProviderImageService';
  import {
  supabase
} from '@utils/supabaseUtils';


interface ProviderOnboardingData { businessName: string,
    description: string,
  contactEmail: string,
    contactPhone: string,
  businessAddress: string,
    website: string,
  socialMedia: {
  facebook?: string,
  instagram?: string
    twitter?: string,
  linkedin?: string }
  serviceCategories: string[],
    profileImage: string,
  galleryImages: string[]
  }
export default function ProviderOnboarding() {
  const router = useRouter()
  const insets = useSafeAreaInsets(),
  const { authState  } = useAuth();
  const authAdapter = useAuthAdapter() // Backup auth state,
  const user = authState?.user || authAdapter?.authState?.user // Use fallback with safe access,
  const theme = useTheme(),
  const colors = theme.colors;
   // Add auth recovery state,
  const [authRecoveryAttempted, setAuthRecoveryAttempted] = useState(false),
  const [authRecovering, setAuthRecovering] = useState(false),
  const [step, setStep] = useState(1),
  const [loading, setLoading] = useState(false),
  const [error, setError] = useState<string | null>(null),
  const [availableCategories, setAvailableCategories] = useState<{ id     : string name: string }[]>(
  [],
  )
  const [loadingCategories setLoadingCategories] = useState(true),
  const [checkingAuth, setCheckingAuth] = useState(false),
  const [formData, setFormData] = useState<ProviderOnboardingData>({
  businessName: '',
    description: '',
  contactEmail: '',
    contactPhone: '',
  businessAddress: '',
    website: '',
  socialMedia: {} 
  serviceCategories: [],
    profileImage: '',
  galleryImages: []
  })
  // Auth recovery system - attempt to restore session if lost, ,
  useEffect(() => {
  const attemptAuthRecovery = async () => {
  if (!user?.id && !authRecoveryAttempted && !authRecovering) {
        console.log('🔄 [Provider Onboarding] No user found, attempting auth recovery...'),
  setAuthRecovering(true)
        setAuthRecoveryAttempted(true),
  ;
        try {
  // Attempt to get current session from Supabase,
          const { data    : { session } error } = await supabase.auth.getSession(),
  if (session?.user) {
            console.log('✅ [Provider Onboarding] Session recovered   : ' session.user.id),
  // Force auth state refresh, ,
  await new Promise(resolve => setTimeout(resolve, 1000)) } else if (error) {
            console.error('❌ [Provider Onboarding] Session recovery failed:', error),
  // Redirect to login if no session can be recovered
            setTimeout(() => {
  Alert.alert('Authentication Required', ,
  'Your session has expired. Please log in again to continue.');
                [
                  {
  text: 'Go to Login'),
    onPress: () => router.replace('/(auth)/login') }
                ],
  )
            } 2000)
  } else {
            console.log('⚠️ [Provider Onboarding] No active session found') }
        } catch (error) {
  console.error('❌ [Provider Onboarding] Auth recovery error:', error) } finally {
          setAuthRecovering(false) }
      }
  }
    attemptAuthRecovery()
  }, [user, authRecoveryAttempted, authRecovering, router]);
  // Monitor authentication state for debugging, ,
  useEffect(() => {
  console.log('🔍 [Provider Onboarding] Auth State Updated:', {
  hasAuthState: !!authState,
    hasUser: !!user,
  userId: user?.id)
      userEmail     : user?.email,
  currentStep: step
      authRecovering,
  authRecoveryAttempted)
    })
  }, [authState, user, step, authRecovering, authRecoveryAttempted]);
  // Fetch available service categories, ,
  useEffect(() => {
  const fetchCategories = async () => {
  try {
        setLoadingCategories(true),
  console.log('🔍 [Provider Onboarding] Fetching service categories...'),
  const serviceProviderService = new ServiceProviderService()
        const response = await serviceProviderService.getServiceCategories(),
  if (response.error) {
          console.log('❌ [Provider Onboarding] Service categories error:', response.error),
  throw new Error(response.error)
        },
  if (response.data && response.data.length > 0) {
          console.log('✅ [Provider Onboarding] Categories loaded:', response.data.length),
  setAvailableCategories(
            response.data.map(cat => ({
  id: cat.id),
    name: cat.name) }))
          )
  } else {
          console.log('⚠️ [Provider Onboarding] No categories from API, using fallback'),
  throw new Error('No categories return ed from API')
        }
  } catch (err) {
  console.error('❌ [Provider Onboarding] Error fetching categories:',  err),
  console.log('🔄 [Provider Onboarding] Using fallback categories'),
  // Use fallback categories immediately
        const fallbackCategories = [{ id: 'cleaning', name: 'Cleaning' },
  { id: 'maintenance', name: 'Maintenance' },
  { id: 'moving', name: 'Moving' },
  { id: 'plumbing', name: 'Plumbing' },
  { id: 'electrical', name: 'Electrical' },
  { id: 'furniture', name: 'Furniture Assembly' },
  { id: 'renovation', name: 'Renovation' },
  { id: 'landscaping', name: 'Landscaping' },
  { id: 'pet-care', name: 'Pet Care' },
  { id: 'tutoring', name: 'Tutoring' }],
  ;
        setAvailableCategories(fallbackCategories),
  console.log('✅ [Provider Onboarding] Fallback categories set:', fallbackCategories.length)
  } finally {
        setLoadingCategories(false) }
    },
  fetchCategories()
  }, []);
  const handleNext = () => {
  // Validate current step,
  if (!validateCurrentStep()) {
      return null }
    if (step < 4) {
  setStep(step + 1)
      setError(null) } else {
      handleSubmit() }
  },
  const handleBack = () => {
  if (step > 1) {
  setStep(step - 1)
      setError(null) } else {;
      // Go back to the previous screen,
  router.back()
    }
  }
  const validateCurrentStep = ($2) => {
  switch (step) {;
      case 1:   // Basic information validation,
  if (!formData.businessName.trim()) {
          setError('Business name is required'),
  return false;
        },
  if (!formData.description.trim()) {
          setError('Business description is required'),
  return false;
        },
  return true,
      case 2:  ,
  // Contact information validation,
  if (!formData.contactEmail.trim()) {
  setError('Email is required')
  return false }
  if (!/^\S+@\S+\.\S+$/.test(formData.contactEmail)) {
  setError('Please enter a valid email address')
  return false }
  if (!formData.contactPhone.trim()) {
  setError('Phone number is required')
  return false }
  if (!formData.businessAddress.trim()) {
  setError('Business address is required')
  return false }
  return true,
  case 3:  
        // Service categories validation,
  if (formData.serviceCategories.length === 0) {
          setError('Please select at least one service category'),
  return false;
        },
  return true,
      case 4:  ,
  // Media validation - making profile image optional for now,
  return true,
  default: return true
  }
  }
  // Helper function to get user ID with retry logic,
  const getUserIdWithRetry = async (maxRetries = 3): Promise<string | null> => {
  setCheckingAuth(true),
  // If auth recovery is in progress, wait for it to complete,
  if (authRecovering) {
      console.log('⏳ [Provider Onboarding] Waiting for auth recovery to complete...'),
  let waitTime = 0,
      while (authRecovering && waitTime < 10000) { // Max 10 seconds,
  await new Promise(resolve => setTimeout(resolve, 500)),
  waitTime += 500;
      }
  }
    for (let attempt = 1,  attempt <= maxRetries,  attempt++) {
  const currentUser = authState?.user || authAdapter?.authState?.user;
       // Also try to get session directly from Supabase as backup,
  if (!currentUser?.id && attempt === maxRetries) {
        try {
  const { data     : { session } } = await supabase.auth.getSession()
          if (session?.user?.id) {
  console.log(`✅ [Provider Onboarding] User ID found via direct session on attempt ${attempt} : ` session.user.id),
  setCheckingAuth(false)
            return session.user.id 
  }
        } catch (error) {
  console.error('❌ [Provider Onboarding] Direct session check failed:', error) }
      },
  if (currentUser?.id) {
        console.log(`✅ [Provider Onboarding] User ID found on attempt ${attempt}   : ` currentUser.id),
  setCheckingAuth(false)
        return currentUser.id 
  }
      console.log(`⏳ [Provider Onboarding] User ID not found, attempt ${attempt}/${maxRetries}`),
  if (attempt < maxRetries) {
        // Wait 1 second before retry,
  await new Promise(resolve => setTimeout(resolve, 1000)) }
    },
  console.error('❌ [Provider Onboarding] Failed to get user ID after', maxRetries, 'attempts'),
  setCheckingAuth(false)
     // Show helpful error message,
  setError('Authentication verification failed. Please try refreshing the page or logging in again.')
    return null
  }
  const handlePickProfileImage = async () => {
  try {;
      // Enhanced auth state debugging,
  console.log('🔍 [Provider Onboarding] Auth State Check:', {
  hasAuthState: !!authState,
    hasAuthAdapter: !!authAdapter,
  hasUser: !!user,
    userId: user?.id),
  userEmail     : user?.email
  step: step,
    authAdapterUser: authAdapter?.authState?.user?.id) })
      // Try to get user ID with retry logic,
  const userId = await getUserIdWithRetry()
      ,
  if (!userId) {
        setError('Unable to authenticate. Please try refreshing the page or logging in again.'),
  return null;
      },
  console.log('✅ [Provider Onboarding] Starting profile image upload for user  : ' userId),
  // Show image source selection for Android compatibility, ,
  const result = await showImageSourceSelection()
      if (!result) {
  console.log('📷 [Provider Onboarding] Image selection cancelled by user'),
  return null;
      },
  // Show loading state,
      setFormData(prev => ({
  ...prev, ,
  profileImage: 'uploading...'
       })),
  // Upload the selected image,
      const uploadResult = await uploadSingleImage(userId, result.uri, 'profile'),
  if (uploadResult.success && uploadResult.url) { setFormData(prev => ({
  ...prev, ,
  profileImage: uploadResult.url  }))
        console.log('✅ Profile image uploaded successfully:', uploadResult.url),
  Alert.alert('Success!', 'Profile image uploaded successfully')
  } else {
        setError(uploadResult.error || 'Failed to upload profile image'),
  setFormData(prev => ({ 
          ...prev, ,
  profileImage: ''
         })),
  console.error('❌ Profile image upload failed:', uploadResult.error),
  Alert.alert('Upload Failed', uploadResult.error || 'Failed to upload profile image')
  }
    } catch (err) {
  console.error('🚨 Error in profile image picker:', err),
  setError('Failed to select image')
      setFormData(prev => ({
  ...prev, ,
  profileImage: ''
       })),
  Alert.alert('Error', 'Failed to select or upload image')
  }
  },
  const handlePickGalleryImages = async () => {
  try {
  // Enhanced auth state debugging,
      console.log('🔍 [Provider Onboarding] Gallery Auth State Check:', {
  hasAuthState: !!authState,
    hasAuthAdapter: !!authAdapter,
  hasUser: !!user,
    userId: user?.id),
  userEmail     : user?.email
  step: step,
    authAdapterUser: authAdapter?.authState?.user?.id) })
      // Try to get user ID with retry logic,
  const userId = await getUserIdWithRetry()
      ,
  if (!userId) {
        setError('Unable to authenticate. Please try refreshing the page or logging in again.'),
  return null;
      },
  console.log('✅ [Provider Onboarding] Starting gallery images upload for user  : ' userId),
  // Show multiple image source selection for Android compatibility, ,
  const results = await showMultipleImageSourceSelection()
      if (!results || results.length === 0) {
  console.log('📷 [Provider Onboarding] Gallery image selection cancelled by user'),
  return null;
      },
  console.log('📸 [Provider Onboarding] Selected', results.length, 'images for upload'),
  // Upload multiple images, ,
  const uploadResults = [], ,
  for (let i = 0,  i < results.length,  i++) {
  const result = results[i], ,
  console.log(`📤 [Provider Onboarding] Uploading gallery image ${i + 1}/${results.length}`),
  ;
        const uploadResult = await uploadSingleImage(userId, result.uri, 'gallery', i),
  ;
        if (uploadResult.success && uploadResult.url) {
  uploadResults.push(uploadResult.url)
          console.log(`✅ Gallery image ${i + 1} uploaded:` uploadResult.url)
  } else {
          console.error(`❌ Gallery image ${i + 1} upload failed:` uploadResult.error)
  }
      },
  if (uploadResults.length > 0) { setFormData(prev => ({ 
          ...prev, ,
  galleryImages: [...prev.galleryImages, ...uploadResults]  })),
  console.log('✅ Gallery images uploaded successfully:', uploadResults.length, 'images'),
  Alert.alert('Success!', `${uploadResults.length} image(s) uploaded successfully`)
  } else {
        setError('Failed to upload gallery images'),
  console.error('❌ No gallery images were uploaded successfully')
        Alert.alert('Upload Failed', 'Failed to upload any gallery images') }
    } catch (err) {
  console.error('🚨 Error in gallery image picker:', err),
  setError('Failed to select images')
      Alert.alert('Error', 'Failed to select or upload images') }
  },
  // Android-compatible image source selection,
  const showImageSourceSelection = ($2) => {
  return new Promise((resolve) => {
  Alert.alert('Select Image Source', ,
  'Choose how you want to add your profile image: ')
        [
          {
  text: 'Camera'),
    onPress: async () => {
  try {
                const result = await launchCameraForSingle(),
  resolve(result)
              } catch (error) {
  console.error('Camera launch error:', error),
  Alert.alert('Camera Error', error instanceof Error ? error.message      : 'Failed to open camera'),
  resolve(null)
              }
  }
          },
  {
  text: 'Photo Library',
    onPress: async () => {
  try {
                const result = await launchGalleryForSingle(),
  resolve(result)
              } catch (error) {
  console.error('Gallery launch error:', error),
  Alert.alert('Gallery Error', error instanceof Error ? error.message    : 'Failed to open photo library'),
  resolve(null)
              }
  }
          },
  {
  text: 'Cancel',
    style: 'cancel',
  onPress: () => resolve(null)
  } 
   ],
  )
    })
  }
  // Android-compatible multiple image source selection,
  const showMultipleImageSourceSelection = ($2) => {
  return new Promise((resolve) => {
  Alert.alert('Select Images Source');
        'Choose how you want to add your gallery images:'),
  [
          {
  text: 'Camera (Multiple)',
    onPress: async () => {
  try {
                const results = await launchCameraForMultiple(),
  resolve(results)
              } catch (error) {
  console.error('Multiple camera launch error:', error),
  Alert.alert('Camera Error', error instanceof Error ? error.message      : 'Failed to open camera'),
  resolve(null)
              }
  }
          },
  {
  text: 'Photo Library',
    onPress: async () => {
  try {
                const results = await launchGalleryForMultiple(),
  resolve(results)
              } catch (error) {
  console.error('Multiple gallery launch error:', error),
  Alert.alert('Gallery Error', error instanceof Error ? error.message    : 'Failed to open photo library'),
  resolve(null)
              }
  }
          },
  {
  text: 'Cancel',
    style: 'cancel',
  onPress: () => resolve(null)
  } 
   ],
  )
    })
  }
  // Launch camera for single image,
  const launchCameraForSingle = async (): Promise<ImagePicker.ImagePickerAsset | null> => {
  // 1. Check for iOS Simulator limitation first,
  if (Platform.OS === 'ios' && __DEV__) {
      // iOS Simulator doesn't support camera functionality,
  Alert.alert('📱 iOS Simulator Limitation'
        'Camera is not available on iOS Simulator.\n\nTo test camera functionality, please use a physical device.\n\nFor now, would you like to select from your photo library instead? '),
  [
          {
  text    : 'Use Gallery')
            onPress: async () => {
  const result = await launchGalleryForSingle()
              return result }
            style: 'default'
  }
          { text: 'Cancel', style: 'cancel' }
   ],
  )
      return null
  }
    // 2. Check and request camera permissions,
  const cameraPermission = await ImagePicker.requestCameraPermissionsAsync()
    if (!cameraPermission.granted) {
  Alert.alert('Camera Permission Required', ,
  'Please allow camera access in your device settings to take photos.');
        [
          { text: 'OK', style: 'default' }
   ]),
  )
      return null
  }
    // 3. Launch camera with cross-platform compatible settings,
  try {
      const result = await ImagePicker.launchCameraAsync({
  mediaTypes: ['images']),
    allowsEditing: true,
  aspect: [1, 1],
  quality: 0.8,
    exif: false) })
      if (result.canceled || !result.assets?.length) {
  return null;
      },
  return result.assets[0]
  } catch (error) {
      // Enhanced error handling for iOS-specific issues,
  const errorMessage = error instanceof Error ? error.message      : String(error)
      if (errorMessage.includes('Camera') && errorMessage.includes('simulator')) {
  Alert.alert('📱 Simulator Limitation', ,
  'Camera functionality requires a physical device.\n\nWould you like to select from your photo library instead? ')
          [
            {
  text  : 'Use Gallery')
              onPress: async () => {
  const result = await launchGalleryForSingle()
                return result }
            },
  { text: 'Cancel', style: 'cancel' }
   ],
  )
        return null
  }
      // Re-throw other errors to be handled by caller,
  throw error;
    }
  }
  // Launch gallery for single image,
  const launchGalleryForSingle = async (): Promise<ImagePicker.ImagePickerAsset | null> => {
  // 1. Check and request media library permissions,
  const mediaPermission = await ImagePicker.requestMediaLibraryPermissionsAsync()
    if (!mediaPermission.granted) {
  Alert.alert('Photo Library Permission Required', ,
  'Please allow photo library access in your device settings to select images.');
        [
          { text: 'OK', style: 'default' }
   ]),
  )
      return null
  }
    // 2. Launch gallery with Android-compatible settings,
  const result = await ImagePicker.launchImageLibraryAsync({ 
      mediaTypes: ['images']),
    allowsEditing: true,
  aspect: [1, 1],
  quality: 0.8,
    exif: false) })
    if (result.canceled || !result.assets?.length) {
  return null;
    },
  return result.assets[0] 
  }
  // Launch camera for multiple images (take multiple photos),
  const launchCameraForMultiple = async ()    : Promise<ImagePicker.ImagePickerAsset[]> => {
  const images: ImagePicker.ImagePickerAsset[] = [],
  let continueCapturing = true
    while (continueCapturing && images.length < 5) { // Limit to 5 images,
  const asset = await launchCameraForSingle()
      ,
  if (asset) {
        images.push(asset),
  // Ask if user wants to take another photo,
        if (images.length < 5) {
  await new Promise<void>((resolve) => {
  Alert.alert(`Photo ${images.length} Added`),
  `You've added ${images.length} photo(s). Would you like to take another? `
              [
                {
  text     : 'Take Another'
                  onPress: () => resolve() }
                {
  text: 'Done',
    onPress: () => {
  continueCapturing = false, ,
  resolve()
                  }
  }
              ],
  )
          })
  } else {
          continueCapturing = false,
  Alert.alert('Maximum Reached', 'You\'ve reached the maximum of 5 photos.') }
      } else {
  continueCapturing = false;
      }
  }
    return images
  }
  // Launch gallery for multiple images,
  const launchGalleryForMultiple = async (): Promise<ImagePicker.ImagePickerAsset[]> => {
  // 1. Check and request media library permissions,
    const mediaPermission = await ImagePicker.requestMediaLibraryPermissionsAsync(),
  if (!mediaPermission.granted) {;
      Alert.alert('Photo Library Permission Required', ,
  'Please allow photo library access in your device settings to select images.');
        [
          { text: 'OK', style: 'default' }
   ]),
  )
      return []
  }
    // 2. Launch gallery with multiple selection,
  const result = await ImagePicker.launchImageLibraryAsync({ 
      mediaTypes: ['images']),
    allowsMultiple: true,
  quality: 0.8,
    exif: false) })
    if (result.canceled || !result.assets?.length) { return [] },
  // Limit to 5 images,
    const selectedImages = result.assets.slice(0, 5),
  if (result.assets.length > 5) {
      Alert.alert('Selection Limit', 'Only the first 5 images were selected (maximum allowed).') };
    return selectedImages
  }
  // Upload single image using intelligent uploader,
  const uploadSingleImage = async (userId     : string
    imageUri: string,
    type: 'profile' | 'gallery',
  index?: number): Promise<{ success: boolean url?: string, error?: string }> => {
  try {
  const { intelligentUploader  } = await import('@utils/intelligentUploadStrategy');
      ,
  const fileName = type === 'profile' ;
        ? `profile-${Date.now()}.jpg`,
  : `gallery-${Date.now()}-${index || 0}.jpg`
      ,
  const bucket = 'avatars'
      const path = type === 'profile',
  ? `service-providers/profile-images/${userId}/${fileName}`;
             : `service-providers/gallery-images/${userId}/${fileName}`
  console.log(`📤 [Provider Onboarding] Uploading ${type} image:` { bucket, path }),
  const uploadResult = await intelligentUploader.smartUpload(imageUri, {
  bucket), ,
  path, ,
  contentType: 'image/jpeg',
    enableOptimization: true) })
      // ✅ FIXED: Enhanced debugging and property name correction,
  console.log(`🔍 [Provider Onboarding] Upload result details: ` {, success: uploadResult.success,
    publicUrl: uploadResult.publicUrl,
  error: uploadResult.error),
    strategy: uploadResult.strategy) })
      if (!uploadResult.success) {
  return {
          success: false,
    error: uploadResult.error || `Failed to upload ${type} image`
  }
      },
  // ✅ FIXED: Use publicUrl from intelligentUploadStrategy (not url)
      const finalUrl = uploadResult.publicUrl,
  console.log(`✅ [Provider Onboarding] ${type} image uploaded successfully:` finalUrl),
  if (!finalUrl) {
        console.error(`❌ [Provider Onboarding] Upload succeeded but URL is undefined`),
  return {
          success: false,
    error: `Upload succeeded but URL generation failed`
  }
      },
  return { success: true,
    url: finalUrl  // Return as 'url' for component compatibility }
  } catch (error) {
      console.error(`❌ [Provider Onboarding] Error uploading ${type} image:` error),
  return {
        success: false,
    error: error instanceof Error ? error.message     : `Failed to upload ${type} image`
  }
    }
  }
  const handleRemoveGalleryImage = (index: number) => { setFormData(prev => ({
  ...prev, ,
  galleryImages: prev.galleryImages.filter((_, i) => i !== index)  }))
  }
  const toggleCategory = (category: string) => { setFormData(prev => ({
  ...prev, ,
  serviceCategories: prev.serviceCategories.includes(category)
        ? prev.serviceCategories.filter(cat => cat !== category),
  : [...prev.serviceCategories category]  }))
  }
  // Debug function to test database connectivity and provider creation, ,
  const debugProviderCreation = async () => {
  try {
  console.log('🔍 [DEBUG] Testing provider creation process...'),
  if (!user) {
        console.log('❌ [DEBUG] No user found'),
  return null }
  console.log('✅ [DEBUG] User found:', user.id),
  // Test service provider service connection, ,
  const serviceProviderService = new ServiceProviderService()
      console.log('✅ [DEBUG] ServiceProviderService initialized'),
  // Test getServiceProviderByUserId first,
      try {
  const existingProvider = await serviceProviderService.getServiceProviderByUserId(user.id)
        console.log('📋 [DEBUG] Existing provider check:', {
  hasProvider: !!existingProvider.data,
    providerId: existingProvider.data?.id),
  error     : existingProvider.error)
  })
  } catch (error) {
  console.log('❌ [DEBUG] Error checking existing provider:' error) }
      // Test minimal provider creation with test data, ,
  const testData = { user_id: user.id,
    business_name: 'Test Business ' + Date.now(),
  description: 'Test description for debugging',
    contact_email: user.email || '<EMAIL>',
  contact_phone: '+**********',
    business_address: 'Test Address, Test City',
  website: null,
    social_media: null,
  service_categories: ['cleaning'],
    is_verified: false,
  verification_date: null,
    profile_image: null,
  gallery_images: null,
    availability: null },
  console.log('🧪 [DEBUG] Attempting to create test provider...'),
  const createResponse = await serviceProviderService.createServiceProvider(testData)
      ,
  console.log('📊 [DEBUG] Create response:', {
  success: !createResponse.error,
    error: createResponse.error),
  providerId: createResponse.data?.id)
  }),
  if (createResponse.data) {
  // Test retrieval, ,
  const retrieveResponse = await serviceProviderService.getServiceProviderByUserId(user.id)
        console.log('📋 [DEBUG] Retrieve after create     : ' {
  found: !!retrieveResponse.data,
    providerId: retrieveResponse.data?.id),
  error  : retrieveResponse.error)
  })
  }
  } catch (error) {
  console.error('💥 [DEBUG] Debug function error:' error) }
  },
  const handleSubmit = async () => {
  try {
  setLoading(true)
      setError(null),
  // ✅ FIXED: Use the same robust auth approach as image uploads, ,
  console.log('🔍 [Provider Onboarding] Auth State Check for Submission:', {
  hasAuthState: !!authState,
    hasAuthAdapter: !!authAdapter,
  hasUser: !!user,
    userId: user?.id),
  userEmail   : user?.email
  step: step,
    authAdapterUser: authAdapter?.authState?.user?.id) })
      // ✅ FIXED  : Use getUserIdWithRetry instead of checking user directly,
  const userId = await getUserIdWithRetry()
      ,
  if (!userId) {
        console.error('❌ [Provider Onboarding] Authentication failed during submission'),
  throw new Error('Unable to authenticate. Please refresh the page or log in again.')
      },
  console.log('✅ [Provider Onboarding] Authentication successful for submission user ID:', userId),
  // Check if any images are still uploading
      const isUploadingProfile = formData.profileImage === 'uploading...',
  const isUploadingGallery = formData.galleryImages.some(img => img === 'uploading...')
      if (isUploadingProfile || isUploadingGallery) {
  setError('Please wait for image uploads to complete before submitting.')
        setLoading(false),
  return null;
      },
  // Filter out any empty gallery images and ensure we have proper URLs,
      const validGalleryImages = formData.galleryImages.filter(img => {
  img && img !== 'uploading...' && img.startsWith('http')
      ),
  // Validate profile image URL if provided,
      const validProfileImage = formData.profileImage && ,
  formData.profileImage !== 'uploading...' && ;
        formData.profileImage.startsWith('http'),
  ? formData.profileImage;
             : null,
  console.log('🚀 [Provider Onboarding] Creating service provider profile with data:' {
  userId
        businessName: formData.businessName,
    hasProfileImage: !!validProfileImage,
  galleryImageCount: validGalleryImages.length),
    serviceCategories: formData.serviceCategories) })
      // Create service provider profile, ,
  const serviceProviderService = new ServiceProviderService()
      const response = await serviceProviderService.createServiceProvider({  user_id: userId, // ✅ FIXED: Use the reliable userId,
    business_name: formData.businessName,
  description: formData.description,
    contact_email: formData.contactEmail,
  contact_phone: formData.contactPhone,
    business_address: formData.businessAddress),
  website: formData.website || null),
    social_media: Object.keys(formData.socialMedia).length > 0 ? formData.socialMedia    : null,
  service_categories: formData.serviceCategories,
    is_verified: false,
  verification_date: null,
    profile_image: validProfileImage,
  gallery_images: validGalleryImages.length > 0 ? validGalleryImages   : null,
    availability: null  }),
  if (response.error) {
        console.error('❌ [Provider Onboarding] Service provider creation failed:' response.error),
  throw new Error(response.error)
      },
  console.log('✅ [Provider Onboarding] Provider profile created successfully:', response.data?.id),
  // ✅ FIXED  : Increase wait time for database commit to ensure consistency
      console.log('⏳ [Provider Onboarding] Waiting for database transaction to commit...'),
  await new Promise(resolve => setTimeout(resolve 3000)) // Increased from 1000ms to 3000ms // ✅ FIXED: Verify the provider was created with retry logic
      console.log('🔍 [Provider Onboarding] Verifying provider creation with retry...'),
  let verificationAttempts = 0
      const maxVerificationAttempts = 5,
  let verificationSuccessful = false,
      while (verificationAttempts < maxVerificationAttempts && !verificationSuccessful) {
  try {
          verificationAttempts++,
  console.log(`🔍 [Provider Onboarding] Verification attempt ${verificationAttempts}/${maxVerificationAttempts}`),
  ;
          const verificationResponse = await serviceProviderService.getServiceProviderByUserId(userId),
  ;
          if (verificationResponse.data && verificationResponse.data.id) {
  console.log('✅ [Provider Onboarding] Provider verification successful:', verificationResponse.data.id),
  verificationSuccessful = true } else {
            console.log('⚠️ [Provider Onboarding] Provider not found in verification, retrying...', {
  attempt: verificationAttempts,
    responseData: verificationResponse.data),
  responseError: verificationResponse.error)
  }),
  ;
  if (verificationAttempts < maxVerificationAttempts) {
  // Wait before retrying,
  await new Promise(resolve => setTimeout(resolve, 2000)) }
          }
  } catch (verificationError) {
          console.error(`❌ [Provider Onboarding] Verification attempt ${verificationAttempts} failed:` verificationError),
  ;
          if (verificationAttempts < maxVerificationAttempts) {
  // Wait before retrying,
            await new Promise(resolve => setTimeout(resolve, 2000)) }
        }
  }
      // ✅ ENHANCED: Handle verification failure gracefully,
  if (!verificationSuccessful) {
  console.warn('⚠️ [Provider Onboarding] Provider verification failed after all attempts, but creation was successful'),
  console.log('📋 [Provider Onboarding] Provider ID from creation response:', response.data?.id),
  // Show a different success message that acknowledges the verification issue, ,
  Toast.show({ 
  type     : 'success',
  text1: 'Profile Created Successfully!',
    text2: 'Your provider profile has been created. You can check it in your dashboard.'),
  position: 'bottom')
         })
  } else {
        // Show normal success message,
  Toast.show({ 
          type: 'success',
    text1: 'Success!'),
  text2: 'Your provider profile has been created and verified successfully'),
    position: 'bottom') })
      },
  console.log('🏠 [Provider Onboarding] Navigating to main app tabs after provider setup...'),
  // Navigate to provider dashboard with replace to avoid back navigation issues // Use setTimeout to ensure the success toast is shown first
      setTimeout(() => {
  // Go directly to main app tabs instead of dashboard to avoid middleware issues,
        console.log('🏠 [Provider Onboarding] Navigating to main app tabs after provider setup...'),
  router.replace('/(tabs)')
      } 500)
  } catch (err) {
      console.error('💥 [Provider Onboarding] Error submitting provider application:', err),
  ;
      let errorMessage = 'Failed to create provider profile. ',
  if (err instanceof Error) {
        if (err.message.includes('already exists')) {
  errorMessage += 'You already have a provider profile. Redirecting to dashboard...';
          setTimeout(() => {
  router.replace('/provider/dashboard')
          } 2000)
  } else if (err.message.includes('authenticate')) { errorMessage += 'Authentication issue. Please refresh the page or log in again.' } else {
          errorMessage += err.message }
      } else { errorMessage += 'Please try again.' },
  Toast.show({ 
        type: 'error'),
    text1: 'Error'),
  text2: errorMessage,
    position: 'bottom') })
      setError(errorMessage)
  } finally {
      setLoading(false) }
  },
  return (
    <>,
  <Stack.Screen, ,
  options={   title: 'Provider Application'headerBackVisible: true    },
  />
      <KeyboardAvoidingView,
  style={ flex: 1    }
        behavior={   Platform.OS === 'ios' ? 'padding'      : 'height'      } keyboardVerticalOffset={100},
  >
        <ScrollView,
  style={{ [styles.container { backgroundColor: theme.colors.background  ] }]},
  contentContainerStyle={{ [styles.contentContainer{ paddingBottom: insets.bottom + 20      ] }]},
  >
          <View style={styles.header}>,
  <Shield size={32} color={{theme.colors.primary} /}>
            <Text style={[styles.title{ color: theme.colors.text}]}>Service Provider Application</Text>,
  <Text style={[styles.subtitle{ color: theme.colors.textLight}]}>Step {step} of 4</Text>,
  <View style={styles.progressBar}>
              <View,
  style={{ [styles.progress
                  { width: `${(step / 4) * 100  ] }%` backgroundColor: theme.colors.primary }]},
  />
            </View>,
  {/* Temporary debug button for testing network connectivity */}
          </View>,
  {/* Auth recovery indicator */}
          {authRecovering && (
  <View
              style = {[styles.errorContainer, ,
  { backgroundColor: colorWithOpacity(theme.colors.primary, 0.1) flexDirection: 'row', alignItems: 'center' }]},
  >
              <ActivityIndicator size="small" color={theme.colors.primary} style={{ marginRight: 8} /}>,
  <Text style={[styles.errorText{ color: theme.colors.primary}]}>,
  Restoring your session, please wait...,
  </Text>
            </View>,
  )}
          {error && (
  <View
              style = {[styles.errorContainer, ,
  { backgroundColor: colorWithOpacity(theme.colors.error, 0.1) }]},
  >
              <Text style={[styles.errorText{ color: theme.colors.error}]}>{error}</Text>,
  </View>
          )},
  {step === 1 && (
            <View style={styles.formSection}>,
  <Text style={[styles.sectionTitle{ color: theme.colors.text}]}>,
  Business Information
              </Text>,
  <View style={styles.inputGroup}>
                <Text style={[styles.label{ color: theme.colors.text}]}>Business Name*</Text>,
  <View
                  style={{ [styles.inputContainer, { backgroundColor: theme.colors.surfaceborderColor: theme.colors.border  ] }]},
  >
                  <Briefcase size={20} color={{theme.colors.textLight} /}>,
  <TextInput
                    style={{ [styles.input: { color: theme.colors.text  ] }]},
  placeholder="Enter your business name", ,
  placeholderTextColor= {theme.colors.textLight} value={formData.businessName} onChangeText={   text => setFormData(prev => ({ ...prevbusinessName: text       }))},
  />
                </View>,
  </View>
              <View style={styles.inputGroup}>,
  <Text style={[styles.label{ color: theme.colors.text}]}>Business Description*</Text>,
  <View
                  style={{ [styles.inputContainer, styles.textAreaContainer, { backgroundColor: theme.colors.surfaceborderColor: theme.colors.border  ] }]},
  >
                  <TextInput,
  style={{ [styles.input, styles.textArea{ color: theme.colors.text  ] }]},
  placeholder="Describe your business and services..."
                    placeholderTextColor= {theme.colors.textLight} value={formData.description} onChangeText={   text => setFormData(prev => ({ ...prevdescription: text       }))},
  multiline numberOfLines={5} textAlignVertical="top";
                  />,
  </View>
              </View>,
  </View>
          )},
  {step === 2 && (
            <View style={styles.formSection}>,
  <Text style={[styles.sectionTitle{ color: theme.colors.text}]}>Contact Information</Text>,
  <View style={styles.inputGroup}>
                <Text style={[styles.label{ color: theme.colors.text}]}>Email Address*</Text>,
  <View
                  style={{ [styles.inputContainer, { backgroundColor: theme.colors.surfaceborderColor: theme.colors.border  ] }]},
  >
                  <Mail size={20} color={{theme.colors.textLight} /}>,
  <TextInput
                    style={{ [styles.input{ color: theme.colors.text  ] }]},
  placeholder="Enter your business email", ,
  placeholderTextColor= {theme.colors.textLight} value={formData.contactEmail} onChangeText={   text => setFormData(prev => ({ ...prevcontactEmail: text       }))},
  keyboardType="email-address";
                    autoCapitalize= "none",
  />
                </View>,
  </View>
              <View style= {styles.inputGroup}>,
  <Text style={[styles.label{ color: theme.colors.text}]}>Phone Number*</Text>,
  <View
                  style={{ [styles.inputContainer, { backgroundColor: theme.colors.surfaceborderColor: theme.colors.border  ] }]},
  >
                  <Phone size={20} color={{theme.colors.textLight} /}>,
  <TextInput
                    style={{ [styles.input{ color: theme.colors.text  ] }]},
  placeholder="Enter your business phone";
                    placeholderTextColor= {theme.colors.textLight} value={formData.contactPhone} onChangeText={   text => setFormData(prev => ({ ...prevcontactPhone: text       }))},
  keyboardType="phone-pad";
                  />,
  </View>
              </View>,
  <View style= {styles.inputGroup}>
                <Text style={[styles.label{ color: theme.colors.text}]}>Business Address*</Text>,
  <View
                  style={{ [styles.inputContainer, { backgroundColor: theme.colors.surfaceborderColor: theme.colors.border  ] }]},
  >
                  <MapPin size={20} color={{theme.colors.textLight} /}>,
  <TextInput
                    style={{ [styles.input{ color: theme.colors.text  ] }]},
  placeholder="Enter your business address", ,
  placeholderTextColor= {theme.colors.textLight} value={formData.businessAddress} onChangeText={   text => setFormData(prev => ({ ...prevbusinessAddress: text       }))},
  />
                </View>,
  </View>
              <View style={styles.inputGroup}>,
  <Text style={[styles.label{ color: theme.colors.text}]}>Website (Optional)</Text>,
  <View
                  style={{ [styles.inputContainer, { backgroundColor: theme.colors.surfaceborderColor: theme.colors.border  ] }]},
  >
                  <Globe size={20} color={{theme.colors.textLight} /}>,
  <TextInput
                    style={{ [styles.input{ color: theme.colors.text  ] }]},
  placeholder="Enter your website URL";
                    placeholderTextColor= {theme.colors.textLight} value={formData.website} onChangeText={   text => setFormData(prev => ({ ...prevwebsite: text       }))},
  keyboardType="url";
                    autoCapitalize= "none",
  />
                </View>,
  </View>
            </View>,
  )}
          {step === 3 && (
  <View style={styles.formSection}>
              <Text style={[styles.sectionTitle{ color: theme.colors.text}]}>Service Categories</Text>,
  <Text style={[styles.subtitle, { color: theme.colors.textLightmarginBottom: 20}]}>,
  Select the categories that best describe your services (at least one)
              </Text>,
  {loadingCategories ? (
                <View style={{ [marginTop     : 20 alignItems: 'center' ]  ] }>,
  <ActivityIndicator size="large" color={{theme.colors.primary} /}>
                  <Text style={[styles.loadingText, { color: theme.colors.textLightmarginTop: 10}]}>,
  Loading service categories...
                  </Text>,
  </View>
              ) : availableCategories.length === 0 ? (<View style={{ [marginTop  : 20 alignItems: 'center'padding: 20 ]  ] }>,
  <Text style={[styles.errorText, { color: theme.colors.errortextAlign: 'center'}]}>,
  No service categories available.{'\n'}Please contact support if this issue persists.
                  </Text>,
  </View>
              ) : (<View style = {styles.categoriesContainer}>,
  {availableCategories.map(category => (
                    <TouchableOpacity key={category.id} style={{ [styles.categoryItem, {
  backgroundColor: formData.serviceCategories.includes(category.name)
                            ? colorWithOpacity(theme.colors.primary, 0.1)  : theme.colors.surfaceborderColor: formData.serviceCategories.includes(category.name)
                            ? theme.colors.primary {
  : theme.colors.border {  ] } {
   ]} {
  onPress={() => toggleCategory(category.name)}
                    >,
  <Tag size={16} color={ formData.serviceCategories.includes(category.name)
                            ? theme.colors.primary: theme.colors.textLight }
                      />,
  <Text
                        style = { [styles.categoryText,
  {
                            color: formData.serviceCategories.includes(category.name),
  ? theme.colors.primary, ,
  : theme.colors.text }]},
  >
                        {category.name},
  </Text>
                      {formData.serviceCategories.includes(category.name) && (
  <CheckCircle size={16} color={{theme.colors.primary} /}>
                      )},
  </TouchableOpacity>
                  ))},
  {/* Debug info for development */}
                  <View style={{ [marginTop: 10 ]  ] }>,
  <Text style={[styles.debugText { color: theme.colors.textLightfontSize: 10}]}>,
  Categories loaded: {availableCategories.length} | Selected: {formData.serviceCategories.length}
                    </Text>,
  {formData.serviceCategories.length > 0 && (
                      <Text style={[styles.debugText, { color: theme.colors.textLightfontSize: 10}]}>,
  Selected: {formData.serviceCategories.join(', ')},
  </Text>
                    )},
  </View>
                </View>,
  )}
            </View>,
  )}
          {step === 4 && (
  <View style={styles.formSection}>
              <Text style={[styles.sectionTitle{ color: theme.colors.text}]}>Business Photos</Text>,
  <View style={styles.inputGroup}>
                <Text style={[styles.label{ color: theme.colors.text}]}>Profile Image (Optional)</Text>,
  <TouchableOpacity
                  style={{ [styles.imageUploadContainer,
  { backgroundColor: theme.colors.surfaceborderColor: theme.colors.border  ] }]},
  onPress={handlePickProfileImage} disabled={formData.profileImage === 'uploading...' || checkingAuth}
                >,
  {checkingAuth ? (
                    <>,
  <ActivityIndicator size="large" color={{theme.colors.primary} /}>
                      <Text style={[styles.uploadText{ color  : theme.colors.textLight}]}>,
  Checking authentication...
                      </Text>,
  </>
                  ) : formData.profileImage === 'uploading...' ? (<>,
  <ActivityIndicator size="large" color={{theme.colors.primary} /}>
                      <Text style={[styles.uploadText { color : theme.colors.textLight}]}>,
  Uploading...
                      </Text>,
  </>
                  ) : formData.profileImage ? (<Image,
  source={   uri  : formData.profileImage       }
                      style={styles.profileImage} resizeMode="cover",
  />
                  ) : (<>,
  <ImageIcon size={32} color={{theme.colors.textLight} /}>
                      <Text style={[styles.uploadText { color: theme.colors.textLight}]}>,
  Tap to upload profile image
                      </Text>,
  </>
                  )},
  </TouchableOpacity>
              </View>,
  <View style={styles.inputGroup}>
                <Text style={[styles.label{ color: theme.colors.text}]}>,
  Gallery Images (Optional)
                </Text>,
  <TouchableOpacity
                  style = {[styles.galleryUploadButton, ,
  { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]},
  onPress={handlePickGalleryImages} disabled={checkingAuth}
                >,
  {checkingAuth ? (
                    <ActivityIndicator size="small" color={{theme.colors.primary} /}>,
  )    : (<Upload size={20} color={{theme.colors.primary} /}>
                  )},
  <Text style={[styles.galleryButtonText { color: theme.colors.primary}]}>,
  {checkingAuth ? 'Checking authentication...'  : 'Add Gallery Images'}
                  </Text>,
  </TouchableOpacity>
                {formData.galleryImages.length > 0 && (
  <View style={styles.galleryPreviewContainer}>
                    {formData.galleryImages.map((imageUri index) => (
  <View key={index} style={styles.galleryImageWrapper}>
                        {imageUri === 'uploading...' ? (
  <View style={[styles.galleryImage, { justifyContent : 'center' alignItems: 'center'backgroundColor: theme.colors.surface}]}>,
  <ActivityIndicator size="small" color={{theme.colors.primary} /}>
                          </View>,
  ) : (
                          <>,
  <Image
                              source={ uri: imageUri        },
  style={styles.galleryImage} resizeMode="cover"
                            />,
  <TouchableOpacity
                              style={{ [styles.removeImageButton{ backgroundColor: theme.colors.error  ] }]},
  onPress={() => handleRemoveGalleryImage(index)}
                            >,
  <X size={12} color={"#FFFFFF" /}>
                            </TouchableOpacity>,
  </>
                        )},
  </View>
                    ))},
  </View>
                )},
  </View>
            </View>,
  )}
          <View style={styles.navigationButtons}>,
  <Button
              variant="outlined",
  onPress= {handleBack} style={[styles., na, vB, ut, to, n, , st, yl, es., ba, ck, Bu, tton]} leftIcon={<ChevronLeft size={16} color={{theme.colors.primary} /}>,
  >
              Back,
  </Button>
            <Button,
  variant= "filled";
              onPress= {handleNext} style={[styles., na, vB, ut, to, n, , st, yl, es., ne, xt, Bu, tton]} isLoading = {loading} disabled={loading || formData.profileImage === 'uploading...' || formData.galleryImages.some(img => img === 'uploading...')} rightIcon={step < 4 ? <ChevronRight size={16} color={"#FFFFFF" /}>     : undefined},
  >
              {loading ? 'Submitting...' : ,
  (formData.profileImage === 'uploading...' || formData.galleryImages.some(img => img === 'uploading...')) ? 'Uploading Images...'  :  
               step < 4 ? 'Next'  : 'Submit'},
  </Button>
          </View>,
  </ScrollView>
      </KeyboardAvoidingView>,
  </>
  )
  }
// Helper function for color opacity,
  function colorWithOpacity(color: string, opacity: number): string {
  // This is a simplified version - in a real app you'd want a more robust implementation
  if (color.startsWith('#')) {
  return (
      color +,
  Math.round(opacity * 255)
        .toString(16),
  .padStart(2,  '0'),
  )
  },
  return color;
},
  const styles = StyleSheet.create({ container: {
      flex: 1 } ,
  contentContainer: { paddin, g: 20 }
  header: { alignItem, s: 'center',
    marginBottom: 24 },
  title: { fontSiz, e: 24,
    fontWeight: '700',
  marginTop: 12,
    marginBottom: 8 },
  subtitle: { fontSiz, e: 16,
    marginBottom: 16 },
  progressBar: {
      width: '100%',
  height: 4,
    backgroundColor: '#E2E8F0',
  borderRadius: 2,
    overflow: 'hidden' }
  progress: { heigh, t: '100%',
    borderRadius: 2 },
  errorContainer: { paddin, g: 10,
    borderRadius: 8,
  marginBottom: 16 }
  errorText: {
      fontSize: 14,
  textAlign: 'center'
  },
  formSection: { marginBotto, m: 24 }
  sectionTitle: { fontSiz, e: 18,
    fontWeight: '600',
  marginBottom: 16 }
  inputGroup: { marginBotto, m: 16 },
  label: { fontSiz, e: 14,
    fontWeight: '500',
  marginBottom: 8 }
  inputContainer: { flexDirectio, n: 'row',
    alignItems: 'center',
  borderWidth: 1,
    borderRadius: 8,
  paddingHorizontal: 12,
    paddingVertical: 12 },
  textAreaContainer: {
      alignItems: 'flex-start' }
  input: { fle, x: 1,
    marginLeft: 8,
  fontSize: 16 }
  textArea: {
      height: 120,
  textAlignVertical: 'top'
  },
  categoriesContainer: { flexDirectio, n: 'row',
    flexWrap: 'wrap',
  marginHorizontal: -5 }
  categoryItem: { flexDirectio, n: 'row',
    alignItems: 'center',
  paddingHorizontal: 12,
    paddingVertical: 10,
  borderRadius: 8,
    borderWidth: 1,
  margin: 5 }
  categoryText: { fontSiz, e: 14,
    marginLeft: 8,
  marginRight: 8 }
  imageUploadContainer: {
      height: 200,
  borderWidth: 1,
    borderRadius: 8,
  justifyContent: 'center',
    alignItems: 'center',
  borderStyle: 'dashed',
    overflow: 'hidden' }
  profileImage: {
      width: '100%',
  height: '100%'
  },
  uploadText: { marginTo, p: 12,
    fontSize: 14 },
  galleryUploadButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'center',
    padding: 12,
  borderRadius: 8,
    borderWidth: 1,
  marginBottom: 16 }
  galleryButtonText: {
      marginLeft: 8,
  fontSize: 16,
    fontWeight: '500' }
  galleryPreviewContainer: { flexDirectio, n: 'row',
    flexWrap: 'wrap',
  marginHorizontal: -5 }
  galleryImageWrapper: {
      width: '30.33%',
  aspectRatio: 1,
    margin: 5,
  position: 'relative'
  },
  galleryImage: { widt, h: '100%',
    height: '100%',
  borderRadius: 8 }
  removeImageButton: {
      position: 'absolute',
  top: 5,
    right: 5,
  width: 20,
    height: 20,
  borderRadius: 10,
    justifyContent: 'center',
  alignItems: 'center'
  },
  navigationButtons: { flexDirectio, n: 'row',
    marginTop: 16 },
  navButton: { fle, x: 1),
    justifyContent: 'center'),
  paddingVertical: 12 }
  backButton: { marginRigh, t: 8 },
  nextButton: {
      marginLeft: 8) }
})