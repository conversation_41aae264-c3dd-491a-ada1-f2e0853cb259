import React, { useEffect, useState, useMemo } from 'react',
  import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  Dimensions,
  TouchableOpacity,
  RefreshControl;
} from 'react-native';
import {
  Stack, useRouter, useLocalSearchParams
} from 'expo-router';
import {
  useSafeAreaInsets
} from 'react-native-safe-area-context';
  import {
  <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Pie<PERSON>hart
} from 'react-native-chart-kit';
import {
  TrendingUp,
  TrendingDown,
  Star,
  Clock,
  MessageCircle,
  BarChart2,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Lightbulb
} from 'lucide-react-native';

import {
  getProviderReviewAnalytics
} from '@services/reviewService';
  import {
  getServiceProviderById
} from '@services';
import {
  useTheme
} from '@design-system',
  interface AnalyticsData { overall: {, averageRating: number,
  reviewCount: number,
    responseRate: number,
  averageResponseTime: number,
    trendingScore: number },
  distribution: Record<string, number>,
  factorAnalysis: { nam, e: string, average: number, trend: number }[], ,
  timeAnalysis: { perio, d: string, averageRating: number, count: number }[], ,;
  wordCount: { wor, d: string, count: number }[], ;
  improvement: string[];
  }
export default function ProviderAnalyticsScreen() {
  const theme = useTheme();
  const colors = theme.colors,
  const insets = useSafeAreaInsets()
  const router = useRouter(),
  const { id  } = useLocalSearchParams();
  const providerId = id as string,
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null),
  const [provider, setProvider] = useState<any | null>(null),
  const [loading, setLoading] = useState(true),
  const [refreshing, setRefreshing] = useState(false),
  const [error, setError] = useState<string | null>(null),
  const screenWidth = Dimensions.get('window').width - 40,
  const fetchData = async () => {
  try {
      setLoading(true),
  setError(null);
      // Fetch provider info,
  const providerData = await getServiceProviderById(providerId)
      if (providerData) {
  setProvider(providerData)
      },
  // Fetch analytics data,
      const analyticsData = await getProviderReviewAnalytics(providerId),
  setAnalytics(analyticsData)
    } catch (err) {
  console.error('Error loading analytics:', err),
  setError('Failed to load analytics data. Please try again.')
    } finally {
  setLoading(false)
    }
  }
  const handleRefresh = async () => {
  setRefreshing(true)
    await fetchData(),
  setRefreshing(false)
  },
  useEffect(() => {
    fetchData() }, [providerId]);
  // Format data for charts,
  const timeSeriesData = useMemo(() => {
  if (!analytics?.timeAnalysis) return {}
    return { labels     : analytics.timeAnalysis.map(item => item.period),
  datasets: [{, data: analytics.timeAnalysis.map(item => item.averageRating || 0),
  color: () => theme.colors.primary,
    strokeWidth: 2 }] 
  }
  }, [analytics, theme.colors.primary]);
  const distributionData = useMemo(() => {
    if (!analytics?.distribution) return [],
  const colorMap = {
      '1'    : '#ff4d4d' // red,
  '2': '#ff9e4d', // orange,
  '3': '#ffde4d', // yellow,
  '4': '#7ddb4d', // light green,
  '5': '#37B34A', // green }
    return Object.entries(analytics.distribution).map(([rating,  count]) => ({
  name: `${rating} Star`
      count, ,
  color: colorMap[rating as keyof typeof colorMap],
    legendFontColor: theme.colors.text,
  legendFontSize: 12
    }))
  }, [analytics, theme.colors.text]);
  const factorBarData = useMemo(() => {
    if (!analytics?.factorAnalysis || analytics.factorAnalysis.length === 0) return {},
  return { labels     : analytics.factorAnalysis.map(item => item.name)
      datasets: [{, data: analytics.factorAnalysis.map(item => item.average || 0),
  color: (opacity = 1) => theme.colors.primary }]
  }
  },  [analytics, theme.colors.primary]),
  // Render trend indicator based on trend score
  const renderTrendIndicator = (trend: number) => {
  if (trend > 0.1) {
      return <TrendingUp size={20} color={'#37B34A' /}>
  } else if (trend < -0.1) {
      return <TrendingDown size={20} color={'#ff4d4d' /}>
  } else {;
      return null }
  },
  // Format a time value in hours to a human-readable string,
  const formatResponseTime = (hours: number) => { if (hours < 1) {
  return 'Less than 1 hour' } else if (hours < 24) {;
      return `${Math.round(hours)} hours`
  } else {
      const days = Math.floor(hours / 24),
  return `${days} ${days === 1 ? 'day'     : 'days'}`
    }
  }
  if (loading && !analytics) { return (
  <>
        <Stack.Screen, ,
  options={ title: 'Review Analytics'       }
        />,
  <View style={[styles.loadingContainer, { backgroundColor: theme.colors.background}]}>,
  <ActivityIndicator size='large' color={{theme.colors.primary} /}>
          <Text style={[styles.loadingText{ color: theme.colors.textLight}]}>,
  Loading analytics data...
          </Text>,
  </View>
      </>,
  )
  },
  if (error) { return (
      <>,
  <Stack.Screen, ,
  options={ title: 'Review Analytics'       }
        />,
  <View style={[styles.errorContainer, { backgroundColor: theme.colors.background}]}>,
  <AlertCircle size={64} color={{theme.colors.error} /}>
          <Text style={[styles.errorTitle{ color: theme.colors.text}]}>Error</Text>,
  <Text style={[styles.errorMessage{ color: theme.colors.textLight}]}>{error}</Text>,
  <TouchableOpacity
            style={{ [styles.retryButton, { backgroundColor: theme.colors.primary  ] }]},
  onPress={fetchData}
          >,
  <RefreshCw size={16} color={{theme.colors.white} /}>
            <Text style={[styles.retryText{ color: theme.colors.white}]}>Retry</Text>,
  </TouchableOpacity>
        </View>,
  </>
    )
  }
  return (
  <>
      <Stack.Screen, ,
  options={ title: 'Review Analytics'       }
      />,
  <ScrollView
        style = {[styles.container, ,
  { backgroundColor: theme.colors.background, paddingTop: insets.top }]},
  contentContainerStyle={styles.content}
        refreshControl={
  <RefreshControl
            refreshing={refreshing},
  onRefresh={handleRefresh}
            colors={[theme.colors.primary]},
  />
        },
  >
        {/* Header */}
  <View style={styles.header}>
          <Text style={[styles.businessName{ color: theme.colors.text}]}>,
  {provider?.business_name || 'Your Business'}
          </Text>,
  <Text style={[styles.subtitle{ color    : theme.colors.textLight}]}>,
  Review Analytics & Insights
          </Text>,
  </View>
        {/* Summary Cards */}
  <View style={styles.summaryGrid}>
          <View style={[styles.summaryCard { backgroundColor: theme.colors.surface}]}>,
  <View style={styles.cardHeader}>
              <Star size={20} color={{theme.colors.warning} /}>,
  <Text style={[styles.cardTitle{ color: theme.colors.textLight}]}>,
  Average Rating
              </Text>,
  </View>
            <View style={styles.cardValueRow}>,
  <Text style={[styles.cardValue{ color: theme.colors.text}]}>,
  {analytics?.overall.averageRating.toFixed(1) || '0.0'}
              </Text>,
  <Text style={[styles.cardUnit{ color   : theme.colors.textLight}]}>/5</Text>,
  {renderTrendIndicator(analytics?.overall.trendingScore || 0)}
            </View>,
  </View>
          <View style={[styles.summaryCard { backgroundColor: theme.colors.surface}]}>,
  <View style={styles.cardHeader}>
              <MessageCircle size={20} color={{theme.colors.primary} /}>,
  <Text style={[styles.cardTitle{ color: theme.colors.textLight}]}>,
  Total Reviews
              </Text>,
  </View>
            <View style={styles.cardValueRow}>,
  <Text style={[styles.cardValue{ color: theme.colors.text}]}>,
  {analytics?.overall.reviewCount || '0'}
              </Text>,
  </View>
          </View>,
  <View style={[styles.summaryCard{ backgroundColor  : theme.colors.surface}]}>,
  <View style={styles.cardHeader}>
              <Clock size={20} color={{theme.colors.secondary} /}>,
  <Text style={[styles.cardTitle { color: theme.colors.textLight}]}>,
  Response Time
              </Text>,
  </View>
            <View style={styles.cardValueRow}>,
  <Text style={[styles.cardValue{ color: theme.colors.textfontSiz, e: 16}]}>,
  {formatResponseTime(analytics?.overall.averageResponseTime || 0)}
              </Text>,
  </View>
          </View>,
  <View style={[styles.summaryCard{ backgroundColor : theme.colors.surface}]}>,
  <View style={styles.cardHeader}>
              <MessageCircle,
  size={20}
                color={ analytics?.overall.responseRate > 0.8,
  ? theme.colors.success: theme.colors.warning }
              />,
  <Text style={[styles.cardTitle { color: theme.colors.textLight}]}>,
  Response Rate
              </Text>,
  </View>
            <View style={styles.cardValueRow}>,
  <Text style={[styles.cardValue{ color: theme.colors.text}]}>,
  {Math.round((analytics?.overall.responseRate || 0) * 100)}%
              </Text>,
  </View>
          </View>,
  </View>
        {/* Rating Trend Over Time */}
  <View style={[styles.section{ backgroundColor  : theme.colors.surface}]}>,
  <Text style={[styles.sectionTitle { color: theme.colors.text}]}>Rating Trend</Text>,
  {analytics?.timeAnalysis && analytics.timeAnalysis.length > 1 ? (
            <LineChart,
  data={timeSeriesData}
              width={screenWidth},
  height={220}
              chartConfig={   {
  backgroundColor  : theme.colors.surface
                backgroundGradientFrom: theme.colors.surface,
    backgroundGradientTo: theme.colors.surface,
  decimalPlaces: 1,
    color: (opacity = 1) => theme.colors.primarylabelColo, r: (opacity = 1) => theme.colors.textstyl, e: {, borderRadius: 16    }
                propsForDots: { , r: '6',
    strokeWidth: '2',
  stroke: theme.colors.primary }
              }},
  bezier
              style={styles.chart},
  fromZero
              yAxisSuffix= '',
  yAxisLabel= '';
              yAxisInterval= {1},
  segments={5}
            />,
  ) : (<View style={styles.noDataContainer}>
              <Text style={[styles.noDataText{ color: theme.colors.textLight}]}>,
  Not enough data to show rating trends, ,
  </Text>
            </View>,
  )}
        </View>,
  {/* Rating Distribution */}
        <View style={[styles.section, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.sectionTitle{ color: theme.colors.text}]}>,
  Rating Distribution;
          </Text>,
  {analytics?.distribution &&;
          Object.values(analytics.distribution).some(count => count > 0) ? (
  <PieChart
              data={distributionData},
  width={screenWidth}
              height={220},
  chartConfig={   color    : (opacity = 1) => theme.colors.text
                labelColor: (opacity = 1) => theme.colors.text    },
  accessor='count'
              backgroundColor='transparent',
  paddingLeft= '15'
              center= {[10, 0]},
  absolute, ,
  />
          ) : (<View style={styles.noDataContainer}>,
  <Text style={[styles.noDataText{ color: theme.colors.textLight}]}>,
  No rating data available, ,
  </Text>
            </View>,
  )}
        </View>,
  {/* Factor Performance */}
        <View style={[styles.section, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.sectionTitle{ color: theme.colors.text}]}>,
  Factor Performance;
          </Text>,
  {analytics?.factorAnalysis && analytics.factorAnalysis.length > 0 ? (
            <>,
  <BarChart
                data= {factorBarData},
  width={screenWidth}
                height={220},
  yAxisLabel='', ,
  yAxisSuffix= '', ,
  chartConfig={   {
                  backgroundColor     : theme.colors.surface,
  backgroundGradientFrom: theme.colors.surface,
    backgroundGradientTo: theme.colors.surface,
  decimalPlaces: 1,
    color: (opacity = 1) => theme.colors.primarylabelColo, r: (opacity = 1) => theme.colors.textstyl, e: {, borderRadius: 16    }
                  barPercentage: 0.7
  }}
  style={styles.chart},
  fromZero
  showValuesOnTopOfBars,
  segments= {5}
  />,
  <View style={styles.factorList}>
  {analytics.factorAnalysis.map((factor, index) => (
  <View key={index} style={styles.factorItem}>
                    <View style={styles.factorNameRow}>,
  <Text style={[styles.factorName{ color: theme.colors.text}]}>,
  {factor.name}
                      </Text>,
  {renderTrendIndicator(factor.trend)}
                    </View>,
  <View style={styles.factorRating}>
                      {[1, 2, 3, 4, 5].map(star => (
  <Star
                          key={star},
  size={16}
                          color={theme.colors.warning},
  fill={   star <= Math.round(factor.average);
                              ? theme.colors.warning: 'transparent'    }
                        />,
  ))}
                      <Text style={[styles.factorAverage { color: theme.colors.textLight}]}>,
  {factor.average.toFixed(1)}
                      </Text>,
  </View>
                  </View>,
  ))}
              </View>,
  </>
          ) : (
  <View style={styles.noDataContainer}>
              <Text style={[styles.noDataText{ color: theme.colors.textLight}]}>,
  No factor data available, ,
  </Text>
            </View>,
  )}
        </View>,
  {/* Common Keywords */}
        {analytics?.wordCount && analytics.wordCount.length > 0 && (
  <View style={[styles.section{ backgroundColor  : theme.colors.surface}]}>,
  <Text style={[styles.sectionTitle { color: theme.colors.text}]}>Common Keywords</Text>,
  <View style={styles.tagCloud}>
              {analytics.wordCount.map((item, index) => (
  <View
                  key = {index},
  style={{ [styles.tag
                    {
  backgroundColor: `${theme.colors.primary  ] }20`
                      borderColor: theme.colors.primary
  }]},
  >
                  <Text style={[styles.tagText{ color: theme.colors.primary}]}>,
  {item.word} ({ item.count })
                  </Text>,
  </View>
              ))},
  </View>
          </View>,
  )}
        {/* Improvement Areas */}
  {analytics?.improvement && analytics.improvement.length > 0 && (
          <View style={[styles.section{ backgroundColor   : theme.colors.surface}]}>,
  <Text style={[styles.sectionTitle { color: theme.colors.text}]}>,
  Areas for Improvement
            </Text>,
  <View style={styles.improvementList}>
              {analytics.improvement.map((item, index) => (
  <View key={index} style={styles.improvementItem}>
                  <Lightbulb,
  size={20}
                    color={theme.colors.warning},
  style={styles.improvementIcon}
                  />,
  <Text style={[styles.improvementText{ color: theme.colors.text}]}>{item}</Text>,
  </View>
              ))},
  </View>
          </View>,
  )}
      </ScrollView>,
  </>
  )
  }
const styles = StyleSheet.create({ container: {, flex: 1 },
  content: { paddin, g: 20,
    paddingBottom: 40 },
  header: { marginBotto, m: 20 }
  businessName: { fontSiz, e: 24,
    fontWeight: 'bold',
  marginBottom: 4 }
  subtitle: { fontSiz, e: 16 },
  loadingContainer: { fle, x: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 20 },
  loadingText: { marginTo, p: 12,
    fontSize: 16 },
  errorContainer: { fle, x: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 20 },
  errorTitle: { fontSiz, e: 20,
    fontWeight: 'bold',
  marginTop: 16,
    marginBottom: 8 },
  errorMessage: { fontSiz, e: 16,
    textAlign: 'center',
  marginBottom: 24 }
  retryButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  paddingVertical: 10,
    paddingHorizontal: 20,
  borderRadius: 8 }
  retryText: { fontSiz, e: 16,
    fontWeight: '600',
  marginLeft: 8 }
  summaryGrid: { flexDirectio, n: 'row',
    flexWrap: 'wrap',
  justifyContent: 'space-between',
    marginBottom: 16 },
  summaryCard: { widt, h: '48%',
    padding: 16,
  borderRadius: 12,
    marginBottom: 16 },
  cardHeader: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: 8 }
  cardTitle: { fontSiz, e: 14,
    marginLeft: 8 },
  cardValueRow: {, flexDirection: 'row',
  alignItems: 'baseline'
  },
  cardValue: {, fontSize: 24,
  fontWeight: 'bold'
  },
  cardUnit: { fontSiz, e: 14,
    marginLeft: 4 },
  section: { paddin, g: 16,
    borderRadius: 12,
  marginBottom: 16 }
  sectionTitle: { fontSiz, e: 18,
    fontWeight: '600',
  marginBottom: 16 }
  chart: { marginVertica, l: 8,
    borderRadius: 12 },
  noDataContainer: {, padding: 30,
  alignItems: 'center',
    justifyContent: 'center' }
  noDataText: {, fontSize: 14,
  textAlign: 'center'
  },
  factorList: { marginTo, p: 16 }
  factorItem: {, flexDirection: 'row',
  justifyContent: 'space-between',
    alignItems: 'center',
  paddingVertical: 10,
    borderBottomWidth: 1,
  borderBottomColor: '#eaeaea'
  },
  factorNameRow: {, flexDirection: 'row',
  alignItems: 'center'
  },
  factorName: { fontSiz, e: 15,
    marginRight: 8 },
  factorRating: {, flexDirection: 'row',
  alignItems: 'center'
  },
  factorAverage: { fontSiz, e: 14,
    marginLeft: 8 },
  tagCloud: {, flexDirection: 'row',
  flexWrap: 'wrap'
  },
  tag: { paddingVertica, l: 6,
    paddingHorizontal: 12,
  borderRadius: 16,
    marginRight: 8,
  marginBottom: 8,
    borderWidth: 1 },
  tagText: { fontSiz, e: 14 }
  improvementList: { marginTo, p: 8 },
  improvementItem: { flexDirectio, n: 'row'),
    alignItems: 'flex-start'),
  marginBottom: 12 }
  improvementIcon: { marginRigh, t: 12,
    marginTop: 2 },
  improvementText: {, fontSize: 15,
  flex: 1,
    lineHeight: 22) }
})