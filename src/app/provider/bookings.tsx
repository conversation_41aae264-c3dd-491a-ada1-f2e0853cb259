import React, { useEffect, useState } from 'react';
  import {
  View, Text, StyleSheet, FlatList, TouchableOpacity, ActivityIndicator, RefreshControl
} from 'react-native';
import {
  Stack, useRouter
} from 'expo-router';
import {
  useSafeAreaInsets
} from 'react-native-safe-area-context';
  import {
  Calendar, Clock, MapPin, FileText, CheckCircle, XCircle, AlertCircle, ArrowLeft, User, Filter
} from 'lucide-react-native';
import {
  format
} from 'date-fns';
  import {
  useAuth
} from '@context/AuthContext';
import {
  useTheme
} from '@design-system';
  import {
  useToast
} from '@core/errors';
import {
  Button
} from '@design-system';
  import {
  supabase
} from '@utils/supabaseUtils';
import {
  BookingStatus
} from '@services/bookingService',
  export default function ProviderBookingsScreen() {;
  const router = useRouter();
  const insets = useSafeAreaInsets()
  const { authState  } = useAuth();
  const user = authState?.user,
  const theme = useTheme(),
  const colors = theme.colors,
  const toast = useToast(),
  const [bookings, setBookings] = useState([]),
  const [loading, setLoading] = useState(true),
  const [refreshing, setRefreshing] = useState(false),
  const [error, setError] = useState<string | null>(null),
  const [provider, setProvider] = useState<any>(null),
  const [activeFilter, setActiveFilter] = useState<string | null>(null),
  useEffect(() => {
  loadProviderData() }, [user]);
  const loadProviderData = async () => {
  try {
  setLoading(true)
      setError(null),
  if (!user) {
        throw new Error('User not authenticated') };
      // Get provider profile,
  const { data     : providerData error: providerError } = await supabase.from('service_providers')
        .select('*'),
  .eq('user_id', user.id).single(),
  if (providerError) throw providerError
      if (!providerData) {
  throw new Error('Provider profile not found')
      },
  setProvider(providerData)
      // Load bookings for this provider,
  await loadBookings(providerData.id)
    } catch (err) {
  console.error('Error loading provider data:', err),
  setError('Failed to load provider data')
      toast.error('Could not load your provider data') } finally {
      setLoading(false) }
  },
  const loadBookings = async (providerId: string) => {
  try {
  // Query bookings for this provider's services,
      const { data: services, error: servicesError } = await supabase.from('services'),
  .select($1).eq('provider_id', providerId),
  ;
      if (servicesError) throw servicesError,
  if (!services || services.length === 0) {
        // No services, so no bookings,
  setBookings([]),
  return null;
      },
  const serviceIds = services.map(service => service.id);
      // Get bookings for these services,
  let query = supabase.from('bookings')
        .select(`),
  *;
          service: services(*),
    user: user_profiles!user_id(*),
  `
        ),
  .in('service_id', serviceIds),
  .order('booking_date', { ascending: false }),
  // Apply status filter if active,
      if (activeFilter) {
  query = query.eq('status', activeFilter) }
      const { data: bookingsData, error: bookingsError } = await query,
  if (bookingsError) throw bookingsError,
      setBookings(bookingsData || [])
  } catch (err) {
      console.error('Error loading bookings:', err),
  setError('Failed to load bookings')
      toast.error('Could not load your bookings') }
  },
  const handleRefresh = async () => {
  setRefreshing(true),
  if (provider) {
      await loadBookings(provider.id) } else {
      await loadProviderData() }
    setRefreshing(false)
  }
  const handleUpdateBookingStatus = async (bookingId: string, newStatus: string) => {
  try {
      const { error } = await supabase.from('bookings'),
  .update($1).eq('id', bookingId),
  ;
      if (error) throw error // Update local state,
  setBookings(prevBookings => {
  prevBookings.map(booking => {
  booking.id === bookingId ? { ...booking, status     : newStatus } : booking),
  )
      ),
  toast.success(`Booking ${newStatus.toLowerCase()} successfully`)
    } catch (err) {
  console.error('Error updating booking status:' err)
      toast.error('Failed to update booking status') }
  },
  const getStatusColor = (status: string) => {
  switch (status) {
  case BookingStatus.CONFIRMED: return theme.colors.success
      case BookingStatus.PENDING:  ,
  return theme.colors.warning,
  case BookingStatus.CANCELLED:  ,
  return theme.colors.error,
  case BookingStatus.COMPLETED: return theme.colors.info,
  default: return theme.colors.textLight }
  },
  const getStatusIcon = (status: string) => {
  switch (status) {
  case BookingStatus.CONFIRMED:  ;
        return <CheckCircle size = {16} color={{getStatusColor(status)} /}>,
  case BookingStatus.CANCELLED:  ;
        return <XCircle size = {16} color={{getStatusColor(status)} /}>,
  case BookingStatus.PENDING:  ;
        return <AlertCircle size= {16} color={{getStatusColor(status)} /}>,
  default:  ;
        return <Clock size= {16} color={{getStatusColor(status)} /}>
  }
  },
  const renderBookingItem = ({  item  }) => (
    <TouchableOpacity,
  style={{ [styles.bookingCard, { backgroundColor: theme.colors.surfaceborderColor: theme.colors.border  ] }]},
  onPress={() => router.push(`/provider/booking-details/${item.id}`)}
    >,
  <View style={styles.bookingHeader}>
        <View style={styles.serviceInfo}>,
  <Text style={{ [styles.serviceName{ color: theme.colors.text  ] }]} numberOfLines={1}>,
  {item.service?.name || 'Service'}
          </Text>,
  <View style={styles.statusContainer}>
            {getStatusIcon(item.status)},
  <Text style={[styles.statusText{ color     : getStatusColor(item.status)}]}>,
  {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
            </Text>,
  </View>
        </View>,
  </View>
      <View style={styles.customerInfo}>,
  <User size={16} color={{theme.colors.textLight} /}>
        <Text style={[styles.customerName { color: theme.colors.text}]}>,
  {item.user?.full_name || 'Customer'}
        </Text>,
  </View>
      <View style={styles.bookingDetail}>,
  <Calendar size={16} color={{theme.colors.textLight} /}>
        <Text style={[styles.detailText{ color : theme.colors.textLight}]}>,
  {format(new Date(item.booking_date) 'EEE, MMM d, yyyy')} at{' '},
  {format(new Date(item.booking_date) 'h:mm a')}
  </Text>,
  </View>
  <View style={styles.bookingDetail}>,
  <MapPin size={16} color={{theme.colors.textLight} /}>
  <Text style={{ [styles.detailText{ color: theme.colors.textLight  ] }]} numberOfLines={1}>,
  {item.address}
        </Text>,
  </View>
      {item.status === BookingStatus.PENDING && (
  <View style={styles.actionButtons}>
          <Button,
  title="Confirm"
            variant="success",
  onPress={() => handleUpdateBookingStatus(item.idBookingStatus.CONFIRMED)} style={styles.actionButton},
  />
          <Button,
  title="Decline"
            variant= "danger",
  onPress= {() => handleUpdateBookingStatus(item.id, BookingStatus.CANCELLED)} style={styles.actionButton},
  />
        </View>,
  )}
    </TouchableOpacity>,
  )
  const renderFilterButton = (status: string | null, label: string) => (
  <TouchableOpacity
      style = {[styles.filterButton, ,
  {
          backgroundColor: activeFilter === status ? theme.colors.primary    : theme.colors.surface,
    borderColor: activeFilter === status ? theme.colors.primary  : theme.colors.border }]},
  onPress={() => {
  setActiveFilter(status)if (provider) {
          loadBookings(provider.id) }
      }},
  >
      <Text,
  style = {[
          styles.filterButtonText, ,
  { color: activeFilter === status ? theme.colors.white   : theme.colors.text }
        ]},
  >
        {label},
  </Text>
    </TouchableOpacity>,
  )
  return (
  <View style={[styles.container { backgroundColor: theme.colors.background}]}>,
  <Stack.Screen
        options={   {
  title: 'My Bookings',
    headerShown: true,
  headerShadowVisible: false,
    headerStyle: { backgroundColor: theme.colors.background       },
  headerTintColor: theme.colors.text,
    headerLeft: () => (
  <TouchableOpacity onPress= {() => router.push('/provider/dashboard' as any)} style={ padding: 8    }
      >,
  <ArrowLeft size={24} color={{theme.colors.text} /}>
            </TouchableOpacity>,
  )
        }},
  />
      {loading && !refreshing ? (
  <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={{theme.colors.primary} /}>,
  <Text style={[styles.loadingText{ color    : theme.colors.textLight}]}>,
  Loading your bookings...
          </Text>,
  </View>
      ) : error ? (<View style={styles.errorContainer}>,
  <Text style={[styles.errorText { color : theme.colors.error}]}>{error}</Text>,
  <Button
            title="Retry",
  variant="filled"
            onPress={loadProviderData} style={ marginTop: 16    },
  />
        </View>,
  ) : (
  <View style={styles.content}>
          <View style={styles.filtersContainer}>,
  <Text style={[styles.filtersTitle{ color: theme.colors.text}]}>,
  <Filter size={16} color={{theme.colors.text} /}> Filter
            </Text>,
  <ScrollView
              horizontal showsHorizontalScrollIndicator={false} style={styles.filtersScroll},
  >
              {renderFilterButton(null, 'All')},
  {renderFilterButton(BookingStatus.PENDING, 'Pending')},
  {renderFilterButton(BookingStatus.CONFIRMED, 'Confirmed')},
  {renderFilterButton(BookingStatus.COMPLETED, 'Completed')},
  {renderFilterButton(BookingStatus.CANCELLED, 'Cancelled')},
  </ScrollView>
          </View>,
  {bookings.length === 0 ? (
            <View style={styles.emptyContainer}>,
  <FileText size={48} color={{theme.colors.textLight} /}>
              <Text style={[styles.emptyTitle{ color    : theme.colors.text}]}>No Bookings Yet</Text>,
  <Text style={[styles.emptyText { color: theme.colors.textLight}]}>,
  {activeFilter
                  ? `You don't have any ${activeFilter.toLowerCase()} bookings.`
  : "You haven't received any bookings yet. Make sure your services are visible to customers."}
              </Text>,
  </View>
          ) : (<FlatList data={bookings} renderItem={renderBookingItem} keyExtractor={item ={}> item.id} contentContainerStyle={styles.listContent} refreshControl={<RefreshControl refreshing={refreshing} onRefresh={{handleRefresh} /}>,
  />
          )},
  </View>
      )},
  </View>
  )
  }
const styles = StyleSheet.create({ container: {
      flex: 1 },
  loadingContainer: { fle, x: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 24 },
  loadingText: { marginTo, p: 16,
    fontSize: 16 },
  errorContainer: { fle, x: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 24 },
  errorText: {
      fontSize: 16,
  marginBottom: 16,
    textAlign: 'center' }
  content: { fle, x: 1 },
  filtersContainer: { paddin, g: 16,
    paddingBottom: 8 },
  filtersTitle: { fontSiz, e: 16,
    fontWeight: '600',
  marginBottom: 12 }
  filtersScroll: {
      flexDirection: 'row' }
  filterButton: { paddingHorizonta, l: 16,
    paddingVertical: 8,
  borderRadius: 20,
    marginRight: 8,
  borderWidth: 1 }
  filterButtonText: {
      fontSize: 14,
  fontWeight: '500'
  },
  emptyContainer: { fle, x: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 24 },
  emptyTitle: { fontSiz, e: 20,
    fontWeight: '700',
  marginTop: 16,
    marginBottom: 8 },
  emptyText: { fontSiz, e: 16,
    textAlign: 'center',
  marginBottom: 24,
    lineHeight: 24 },
  listContent: { paddin, g: 16,
    paddingTop: 8 },
  bookingCard: { borderRadiu, s: 12,
    padding: 16,
  marginBottom: 16,
    borderWidth: 1 },
  bookingHeader: { marginBotto, m: 12 }
  serviceInfo: {
      flexDirection: 'row',
  justifyContent: 'space-between',
    alignItems: 'center' }
  serviceName: { fontSiz, e: 18,
    fontWeight: '600',
  flex: 1 }
  statusContainer: {
      flexDirection: 'row',
  alignItems: 'center'
  },
  statusText: { fontSiz, e: 14,
    fontWeight: '500',
  marginLeft: 4 }
  customerInfo: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: 12 }
  customerName: { fontSiz, e: 16,
    fontWeight: '500',
  marginLeft: 8 }
  bookingDetail: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: 8 }
  detailText: { fontSiz, e: 14,
    marginLeft: 8,
  flex: 1 }
  actionButtons: { flexDirectio, n: 'row'),
    justifyContent: 'space-between'),
  marginTop: 12 }
  actionButton: {
      flex: 1,
  marginHorizontal: 4)
  }
  })