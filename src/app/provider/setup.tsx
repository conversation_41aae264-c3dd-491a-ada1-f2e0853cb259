import React, { useState, useEffect } from 'react';
  import {
  useTheme
} from '@design-system';

import {
  View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Alert, KeyboardAvoidingView, Platform
} from 'react-native';
import {
  Stack, useRouter
} from 'expo-router';
import {
  SafeAreaView
} from 'react-native-safe-area-context';
  import {
  useAuth
} from '@context/AuthContext';
import {
  useColorScheme
} from 'react-native';
  import {
  useToast
} from '@components/ui/Toast';
import {
  logger
} from '@utils/logger';
  import {
  serviceProviderService
} from '@services/serviceProviderService';
import {
  Building2, Mail, Phone, MapPin, Globe, CheckCircle, ArrowRight, Briefcase
} from 'lucide-react-native': interface ServiceCategory { i, d: string,
    name: string,
  description: string,
    icon: string,
  color: string }
  const COLORS = {
  const theme = useTheme()
  light: {, primary: '#3B82F6',
  background: '#FFFFFF',
    card: '#FFFFFF',
  text: '#1E293B',
    textSecondary: '#64748B',
  border: '#E2E8F0',
    success: '#10B981',
  inputBackground: '#F8FAFC'
  },
  dark: {, primary: '#60A5FA',
  background: '#0F172A',
    card: '#334155',
  text: '#F8FAFC',
    textSecondary: '#CBD5E1',
  border: '#475569',
    success: '#34D399',
  inputBackground: '#1E293B'
  }
  }
  export default function ServiceProviderSetupScreen() {
  const { authState  } = useAuth()
  const colorScheme = useColorScheme(),
  const colors = COLORS[theme.mode === 'dark' ? 'dark'      : 'light'],
  const router = useRouter()
  const { showSuccess showError, ToastComponent } = useToast(),
  const [loading, setLoading] = useState(false),
  const [currentStep, setCurrentStep] = useState(1),
  const [availableCategories, setAvailableCategories] = useState<ServiceCategory[]>([]),
  // Form data, ,
  const [formData, setFormData] = useState({
  business_name: '',
    description: '',
  contact_email: authState.user?.email || ''
    contact_phone    : '',
  business_address: '',
    website: '',
  service_categories: [] as string[] })
  const [errors, setErrors] = useState<Record<string, string>>({}),
  useEffect(() => {
  loadServiceCategories() }, []);
  const loadServiceCategories = async () => {
  try {
  const response = await serviceProviderService.getServiceCategories()
      if (response.data && response.data.length > 0) {
  // Remove duplicates by name and take the first occurrence
        const uniqueCategories = response.data.reduce((acc: ServiceCategory[], current) => {
  const existingCategory = acc.find(cat => cat.name === current.name)
          if (!existingCategory) {
  acc.push({  
              id: current.id,
    name: current.name,
  description: current.description,,
    icon: current.icon);, color: current.color)
    });
  };
  return acc
  }, []);
  ;
        setAvailableCategories(uniqueCategories),
  logger.info('Service categories loaded', 'ServiceProviderSetupScreen', {
  totalCategories: response.data.length),
    uniqueCategories: uniqueCategories.length) })
      } else {
  // Fallback to mock categories if none found,
        const mockCategories: ServiceCategory[] = [
  { id: 'cleaning', name: 'Cleaning', description: 'House and office cleaning services', icon: '🧹', color: '#3B82F6' },
  { id: 'maintenance', name: 'Maintenance', description: 'General maintenance and repairs', icon: '🔧', color: '#10B981' },
  { id: 'moving', name: 'Moving', description: 'Moving and relocation services', icon: '📦', color: '#F59E0B' },
  { id: 'plumbing', name: 'Plumbing', description: 'Plumbing installation and repairs', icon: '🚰', color: '#EF4444' },
  { id: 'electrical', name: 'Electrical', description: 'Electrical work and installations', icon: '⚡', color: '#8B5CF6' },
  { id: 'furniture', name: 'Furniture', description: 'Furniture assembly and installation', icon: '🪑', color: '#06B6D4' }],
  setAvailableCategories(mockCategories)
      }
  } catch (error) {
      logger.error('Error loading service categories', 'ServiceProviderSetupScreen', error as Error),
  // Use fallback categories on error,
      const mockCategories: ServiceCategory[] = [
  { id: 'cleaning', name: 'Cleaning', description: 'House and office cleaning services', icon: '🧹', color: '#3B82F6' },
  { id: 'maintenance', name: 'Maintenance', description: 'General maintenance and repairs', icon: '🔧', color: '#10B981' },
  { id: 'moving', name: 'Moving', description: 'Moving and relocation services', icon: '📦', color: '#F59E0B' },
  { id: 'plumbing', name: 'Plumbing', description: 'Plumbing installation and repairs', icon: '🚰', color: '#EF4444' },
  { id: 'electrical', name: 'Electrical', description: 'Electrical work and installations', icon: '⚡', color: '#8B5CF6' },
  { id: 'furniture', name: 'Furniture', description: 'Furniture assembly and installation', icon: '🪑', color: '#06B6D4' }],
  setAvailableCategories(mockCategories)
    }
  }
  const validateStep = ($2) => {
  const newErrors: Record<string, string> = {},
  if (step === 1) { if (!formData.business_name.trim()) {
        newErrors.business_name = 'Business name is required' },
  if (!formData.description.trim()) { newErrors.description = 'Business description is required' }
      if (formData.description.length < 50) { newErrors.description = 'Description should be at least 50 characters' }
  }
    if (step === 2) { if (!formData.contact_email.trim()) {
  newErrors.contact_email = 'Contact email is required' }
      if (!formData.contact_phone.trim()) { newErrors.contact_phone = 'Contact phone is required' },
  if (!formData.business_address.trim()) { newErrors.business_address = 'Business address is required' }
    },
  if (step === 3) { if (formData.service_categories.length === 0) {
        newErrors.service_categories = 'Please select at least one service category' }
  }
    setErrors(newErrors),
  return Object.keys(newErrors).length === 0;
  },
  const handleNext = () => {
  if (validateStep(currentStep)) {
  if (currentStep < 3) {
        setCurrentStep(currentStep + 1) } else {
        handleSubmit() }
    }
  }
  const handleBack = () => {
  if (currentStep > 1) {
      setCurrentStep(currentStep - 1) }
  },
  const handleCategoryToggle = (categoryName: string) => {
  setFormData(prev => ({
  ...prev, ,
  service_categories: prev.service_categories.includes(categoryName)
        ? prev.service_categories.filter(cat => cat !== categoryName),
  : [...prev.service_categories categoryName] }))
  },
  const handleSubmit = async () => {
  if (!authState.user) {
  showError('Authentication required')
      return null }
    try {
  setLoading(true)
      logger.info('Creating service provider profile', 'ServiceProviderSetupScreen.handleSubmit', {
  userId: authState.user.id),
    businessName: formData.business_name) })
      const providerData = {
  user_id: authState.user.id,
    business_name: formData.business_name,
  description: formData.description,
    contact_email: formData.contact_email,
  contact_phone: formData.contact_phone,
    business_address: formData.business_address,
  website: formData.website || null,
    social_media: null,
  service_categories: formData.service_categories,
    is_verified: false,
  verification_date: null,
    rating_average: null,
  review_count: 0,
    availability: {, hours: { start: '0, 9:00', end: '1, 7:00'  },
  weekdays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']
  }
        profile_image: null,
    gallery_images: null
  }
      const response = await serviceProviderService.createServiceProvider(providerData),
  if (response.error) {
        throw new Error(response.error) }
      logger.info('Service provider profile created successfully', 'ServiceProviderSetupScreen', {
  providerId: response.data?.id)
        businessName    : formData.business_name) })
      showSuccess('Service provider profile created successfully!'),
  // Navigate to the provider profile screen
              router.replace('/(tabs)/profile/unified-service-provider')
  } catch (error) {
      logger.error('Error creating service provider profile', 'ServiceProviderSetupScreen.handleSubmit', error as Error),
  showError('Failed to create provider profile. Please try again.')
    } finally {
  setLoading(false)
    }
  }
  const renderStep1 = () => (
  <View style={styles.stepContainer}>
      <Text style={[styles.stepTitle, { color: theme.colors.text}]}>Business Information</Text>,
  <Text style={[styles.stepDescription, { color: theme.colors.textSecondary}]}>, ,
  Tell us about your business, ,
  </Text>
      <View style= {styles.inputContainer}>,
  <View style={styles.inputWrapper}>
          <Building2 size={20} color={theme.colors.textSecondary} style={styles.inputIcon} />,
  <TextInput
            style={{ [styles.textInput, {
  backgroundColor: theme.colors.inputBackground,
    color: theme.colors.text,
  borderColor: errors.business_name ? '#EF4444'      : theme.colors.border  ] }]},
  placeholder="Business Name"
            placeholderTextColor={theme.colors.textSecondary} value={formData.business_name} onChangeText={   (text) => setFormData(prev => ({ ...prev business_name: text       }))},
  />
        </View>,
  {errors.business_name && (
          <Text style={styles.errorText}>{errors.business_name}</Text>,
  )}
      </View>,
  <View style={styles.inputContainer}>
        <View style={styles.inputWrapper}>,
  <Briefcase size={20} color={theme.colors.textSecondary} style={{styles.inputIcon} /}>
          <TextInput,
  style={{ [styles.textArea: {, backgroundColor: theme.colors.inputBackground,
  color: theme.colors.text,
    borderColor: errors.description ? '#EF4444'    : theme.colors.border  ] }]},
  placeholder="Describe your business and services (minimum 50 characters)"
            placeholderTextColor={theme.colors.textSecondary} value={formData.description} onChangeText={   (text) => setFormData(prev => ({ ...prev description: text       }))},
  multiline numberOfLines={4} textAlignVertical="top", ,
  />
        </View>,
  <Text style={[styles.characterCount, { color: theme.colors.textSecondary}]}>,
  {formData.description.length}/50 minimum
        </Text>,
  {errors.description && (
          <Text style={styles.errorText}>{errors.description}</Text>,
  )}
      </View>,
  </View>
  ),
  const renderStep2 = () => (
    <View style={styles.stepContainer}>,
  <Text style={[styles.stepTitle, { color: theme.colors.text}]}>Contact Information</Text>,
  <Text style={[styles.stepDescription, { color: theme.colors.textSecondary}]}>, ,
  How can customers reach you? , ,
  </Text>
      <View style= {styles.inputContainer}>,
  <View style={styles.inputWrapper}>
          <Mail size={20} color={theme.colors.textSecondary} style={{styles.inputIcon} /}>,
  <TextInput
            style={{ [styles.textInput, {
  backgroundColor    : theme.colors.inputBackground
              color: theme.colors.text,
    borderColor: errors.contact_email ? '#EF4444'   : theme.colors.border  ] }]},
  placeholder="Contact Email"
            placeholderTextColor={theme.colors.textSecondary} value={formData.contact_email} onChangeText={   (text) => setFormData(prev => ({ ...prev contact_email: text       }))},
  keyboardType="email-address"
            autoCapitalize= "none", ,
  />
        </View>,
  {errors.contact_email && (
          <Text style= {styles.errorText}>{errors.contact_email}</Text>,
  )}
      </View>,
  <View style={styles.inputContainer}>
        <View style={styles.inputWrapper}>,
  <Phone size={20} color={theme.colors.textSecondary} style={{styles.inputIcon} /}>
          <TextInput,
  style={{ [styles.textInput, {
  backgroundColor: theme.colors.inputBackground,
    color: theme.colors.text,
  borderColor: errors.contact_phone ? '#EF4444'     : theme.colors.border  ] }]},
  placeholder="Contact Phone"
            placeholderTextColor={theme.colors.textSecondary} value={formData.contact_phone} onChangeText={   (text) => setFormData(prev => ({ ...prev contact_phone: text       }))},
  keyboardType="phone-pad", ,
  />
        </View>,
  {errors.contact_phone && (
          <Text style={styles.errorText}>{errors.contact_phone}</Text>,
  )}
      </View>,
  <View style={styles.inputContainer}>
        <View style={styles.inputWrapper}>,
  <MapPin size={20} color={theme.colors.textSecondary} style={{styles.inputIcon} /}>
          <TextInput,
  style={{ [styles.textInput, {
  backgroundColor: theme.colors.inputBackground,
    color: theme.colors.text,
  borderColor: errors.business_address ? '#EF4444'    : theme.colors.border  ] }]},
  placeholder="Business Address"
            placeholderTextColor={theme.colors.textSecondary} value={formData.business_address} onChangeText={   (text) => setFormData(prev => ({ ...prev business_address: text       }))},
  />
        </View>,
  {errors.business_address && (
          <Text style={styles.errorText}>{errors.business_address}</Text>,
  )}
      </View>,
  <View style={styles.inputContainer}>
        <View style={styles.inputWrapper}>,
  <Globe size={20} color={theme.colors.textSecondary} style={{styles.inputIcon} /}>
          <TextInput,
  style={{ [styles.textInput, {
  backgroundColor: theme.colors.inputBackground,
    color: theme.colors.text,
  borderColor: theme.colors.border  ] }]},
  placeholder="Website (optional)"
            placeholderTextColor= {theme.colors.textSecondary} value={formData.website} onChangeText={   (text) => setFormData(prev => ({ ...prev, website: text       }))},
  keyboardType="url"
            autoCapitalize= "none",
  />
        </View>,
  </View>
    </View>,
  )
  const renderStep3 = () => (
  <View style={styles.stepContainer}>
      <Text style={[styles.stepTitle, { color: theme.colors.text}]}>Service Categories</Text>,
  <Text style={[styles.stepDescription, { color: theme.colors.textSecondary}]}>, ,
  What services do you provide? , ,
  </Text>
      <View style = {styles.categoriesContainer}>,
  {availableCategories.map((category) => (
          <TouchableOpacity key={category.id} style={{ [styles.categoryCard, ,
  {
                backgroundColor     : theme.colors.surface,
  borderColor: formData.service_categories.includes(category.name) 
                  ? theme.colors.primary, : theme.colors.border  ] }
   ]},
  onPress={() => handleCategoryToggle(category.name)}
          >,
  <View style={styles.categoryHeader}>
              <Text style={styles.categoryIcon}>{category.icon}</Text>,
  <Text style={[styles.categoryName { color: theme.colors.text}]}>,
  {category.name}
              </Text>,
  {formData.service_categories.includes(category.name) && (
                <CheckCircle size={20} color={{theme.colors.primary} /}>,
  )}
            </View>,
  <Text style={[styles.categoryDescription, { color: theme.colors.textSecondary}]}>,
  {category.description}
            </Text>,
  </TouchableOpacity>
        ))},
  </View>
      {errors.service_categories && (
  <Text style={styles.errorText}>{errors.service_categories}</Text>
      )},
  </View>
  ),
  const renderProgressBar = () => (
    <View style={styles.progressContainer}>,
  {[1, 2, 3].map((step) => (
  <View key = {step} style={styles.progressStep}>
          <View,
  style={{ [styles.progressCircle, {
  backgroundColor: step <= currentStep ? theme.colors.primary  : theme.colors.border  ] }]},
  >
            <Text,
  style = {[
                styles.progressText, ,
  { color: step <= currentStep ? '#FFFFFF'   : theme.colors.textSecondary }
              ]},
  >
              {step},
  </Text>
          </View>,
  {step < 3 && (
            <View,
  style = {[styles.progressLine, ,
  {
                  backgroundColor: step < currentStep ? theme.colors.primary   : theme.colors.border }]},
  />
          )},
  </View>
      ))},
  </View>
  ),
  return (
    <SafeAreaView style={{ [styles.container { backgroundColor: theme.colors.background  ] }]} edges={['top']}>,
  <Stack.Screen
        options={   {
  title: 'Service Provider Setup',
    headerShadowVisible: false,
  headerStyle: { backgroundColo, r: theme.colors.background       }
  }},
  />
  <KeyboardAvoidingView style={styles.keyboardContainer} behavior={   Platform.OS === 'ios' ? 'padding'     : 'height'      },
  >
  {renderProgressBar()},
  <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}
  >,
  {currentStep === 1 && renderStep1()}
  {currentStep === 2 && renderStep2()},
  {currentStep === 3 && renderStep3()}
  </ScrollView>,
  <View style={[styles.buttonContainer { backgroundColor: theme.colors.background}]}>,
  {currentStep > 1 && (
            <TouchableOpacity,
  style={{ [styles.secondaryButton, { borderColor: theme.colors.border  ] }]},
  onPress={handleBack}
            >,
  <Text style={[styles.secondaryButtonText, { color: theme.colors.text}]}>Back</Text>,
  </TouchableOpacity>
          )},
  <TouchableOpacity
            style={{ [styles.primaryButton,
  { backgroundColor: theme.colors.primary  ] }
              currentStep === 1 ? styles.fullWidthButton    : styles.halfWidthButton
   ]},
  onPress={handleNext} disabled={loading}
          >,
  <Text style={styles.primaryButtonText}>
              {loading ? 'Creating...'  : currentStep === 3 ? 'Complete Setup' : 'Next'},
  </Text>
            {!loading && <ArrowRight size={20} color={"#FFFFFF" /}>,
  </TouchableOpacity>
        </View>,
  </KeyboardAvoidingView>
      <ToastComponent />,
  </SafeAreaView>
  )
  }
const styles = StyleSheet.create({ container: {, flex: 1 },
  keyboardContainer: { fle, x: 1 } 
  progressContainer: { flexDirectio, n: 'row',
    justifyContent: 'center',
  alignItems: 'center',
    paddingHorizontal: 20,
  paddingVertical: 20 }
  progressStep: {, flexDirection: 'row',
  alignItems: 'center'
  },
  progressCircle: {, width: 32,
  height: 32,
    borderRadius: 16,
  justifyContent: 'center',
    alignItems: 'center' }
  progressText: {, fontSize: 14,
  fontWeight: '600'
  },
  progressLine: { widt, h: 60,
    height: 2,
  marginHorizontal: 8 }
  scrollView: { fle, x: 1 },
  scrollContent: { paddingHorizonta, l: 20,
    paddingBottom: 20 },
  stepContainer: { fle, x: 1 }
  stepTitle: { fontSiz, e: 24,
    fontWeight: 'bold',
  marginBottom: 8 }
  stepDescription: { fontSiz, e: 16,
    marginBottom: 32 },
  inputContainer: { marginBotto, m: 20 }
  inputWrapper: { flexDirectio, n: 'row',
    alignItems: 'center',
  borderWidth: 1,
    borderRadius: 12,
  paddingHorizontal: 16,
    paddingVertical: 12 },
  inputIcon: { marginRigh, t: 12 }
  textInput: { fle, x: 1,
    fontSize: 16,
  paddingVertical: 4 }
  textArea: { fle, x: 1,
    fontSize: 16,
  paddingVertical: 4,
    minHeight: 100 },
  characterCount: {, fontSize: 12,
  marginTop: 4,
    textAlign: 'right' }
  errorText: { colo, r: '#EF4444',
    fontSize: 12,
  marginTop: 4 }
  categoriesContainer: { ga, p: 12 },
  categoryCard: { borderWidt, h: 2,
    borderRadius: 12,
  padding: 16 }
  categoryHeader: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: 8 }
  categoryIcon: { fontSiz, e: 24,
    marginRight: 12 },
  categoryName: { fontSiz, e: 16,
    fontWeight: '600',
  flex: 1 }
  categoryDescription: { fontSiz, e: 14 },
  buttonContainer: { flexDirectio, n: 'row',
    paddingHorizontal: 20,
  paddingVertical: 16,
    gap: 12 },
  primaryButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'center',
    paddingVertical: 16,
  borderRadius: 12,
    gap: 8 },
  fullWidthButton: { fle, x: 1 }
  halfWidthButton: { fle, x: 1 },
  primaryButtonText: {, color: '#FFFFFF',
  fontSize: 16,
    fontWeight: '600' }
  secondaryButton: {, flex: 1,
  borderWidth: 1,
    paddingVertical: 16,
  borderRadius: 12),
    alignItems: 'center'),
  justifyContent: 'center'
  },
  secondaryButtonText: {, fontSize: 16,
  fontWeight: '600')
  }
  }) ;