import React, { useState, useEffect } from 'react';
  import {
  View, Text, StyleSheet, ScrollView, TouchableOpacity, Switch
} from 'react-native';
import {
  useRouter
} from 'expo-router';
  import {
  useSafeAreaInsets
} from 'react-native-safe-area-context';
import {
  ChevronLeft, Sliders, MapPin, Star, Clock, DollarSign
} from 'lucide-react-native';
import {
  useTheme
} from '@design-system';
  import {
  useServiceProviders
} from '@hooks/useServiceProviders';
import {
  Button
} from '@design-system';
  import Slider from '@components/common/Slider' // Service areas/locations,
const serviceAreas = ['Downtown', ,
  'Midtown'
  'Upper East Side',
  'Upper West Side'
  'Brooklyn',
  'Queens'
  'Bronx',
  'Staten Island'
  'Within 5 miles',
  'Within 10 miles'
  'Within 25 miles'] // Availability options, ,
  const availabilityOptions = ['Available Today', ,
  'Available This Week'
  'Available This Month',
  'Weekdays Only'
  'Weekends Only',
  'Emergency Services'] // Rating filter options,
  const ratingOptions = [{ label: '5 Stars Only', value: 5 },
  { label: '4+ Stars', value: 4 },
  { label: '3+ Stars', value: 3 },
  { label: 'Any Rating', value: 0 }],
  export default function ServiceFilterScreen() {
  const router = useRouter(),
  const theme = useTheme()
  const styles = createStyles(theme),
  const insets = useSafeAreaInsets()
  const { categories  } = useServiceProviders(),
  // Filter state,
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]),
  const [selectedAreas, setSelectedAreas] = useState<string[]>([]),
  const [selectedAvailability, setSelectedAvailability] = useState<string[]>([]),
  const [minRating, setMinRating] = useState(0),
  const [priceRange, setPriceRange] = useState({  min: 0, max: 500  }),
  const [verifiedOnly, setVerifiedOnly] = useState(false),
  const [instantBooking, setInstantBooking] = useState(false),
  // Get service categories,
  const serviceCategories = categories.length > 0 ? categories      : [{ id: '1' nam, e: 'Cleaning', description: 'Professional cleaning services' },
  { id: '2', name: 'Maintenance', description: 'General maintenance and repairs' },
  { id: '3', name: 'Moving', description: 'Moving and relocation assistance' },
  { id: '4', name: 'Plumbing', description: 'Plumbing repairs and installation' },
  { id: '5', name: 'Electrical', description: 'Electrical repairs and upgrades' },
  { id: '6', name: 'Renovation', description: 'Home renovation and remodeling' },
  { id: '7', name: 'Landscaping', description: 'Garden and outdoor space maintenance' },
  { id: '8', name: 'Pet Care', description: 'Pet sitting and grooming services' },
  { id: '9', name: 'Tutoring', description: 'Educational and academic support' },
  { id: '10', name: 'Fitness', description: 'Personal training and fitness coaching' }],
  const toggleCategory = (categoryName: string) => {
  setSelectedCategories(prev => {
  prev.includes(categoryName)
        ? prev.filter(cat => cat !== categoryName),
  : [...prev categoryName],
  )
  },
  const toggleArea = (area: string) => {
  setSelectedAreas(prev => {
  prev.includes(area)
        ? prev.filter(a => a !== area),
  : [...prev area],
  )
  },
  const toggleAvailability = (availability: string) => {
  setSelectedAvailability(prev => {
  prev.includes(availability)
        ? prev.filter(a => a !== availability),
  : [...prev availability],
  )
  },
  const handleClearFilters = () => {
  setSelectedCategories([]),
  setSelectedAreas([]),
  setSelectedAvailability([]),
  setMinRating(0)
    setPriceRange({  min: 0, max: 500  }),
  setVerifiedOnly(false)
    setInstantBooking(false)
  }
  const handleApplyFilters = () => {
  // Create filter object to pass back
    const filters = {
  categories: selectedCategories,
    areas: selectedAreas,
  availability: selectedAvailability,
  minRating,
      priceRange,
  verifiedOnly,
      instantBooking }
    // In a real implementation, you would pass these filters back to the search screen // For now, we'll just navigate back,
  console.log('Applying service filters:', filters),
  router.back()
  },
  const hasFilters =  ;
    selectedCategories.length > 0 ||,
  selectedAreas.length > 0 ||;
    selectedAvailability.length > 0 ||,
  minRating > 0 ||;
    verifiedOnly ||,
  instantBooking,
  return (
  <View style= {[styles.container,  { paddingTop: insets.top}]}>,
  <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>,
  <ChevronLeft size={24} color={{theme.colors.text} /}>
        </TouchableOpacity>,
  <Text style={[styles.title{ color: theme.colors.text}]}>Service Filters</Text>,
  {hasFilters && (
          <TouchableOpacity onPress={handleClearFilters} style={styles.clearButton}>,
  <Text style={[styles.clearButtonText{ color: theme.colors.primary}]}>Clear All</Text>,
  </TouchableOpacity>
        )},
  </View>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>,
  {/* Service Categories */}
        <View style={styles.section}>,
  <View style={styles.sectionHeader}>
            <Sliders size={20} color={{theme.colors.primary} /}>,
  <Text style={[styles.sectionTitle{ color: theme.colors.text}]}>Service Categories</Text>,
  </View>
          <View style={styles.optionsGrid}>,
  {serviceCategories.map(category => (
              <TouchableOpacity key={category.id} style={{ [
                  styles.optionChip), ,
  {
                    backgroundColor: selectedCategories.includes(category.name) ,
  ? theme.colors.primaryLight, ,
  : theme.colors.surface
                    borderColor: selectedCategories.includes(category.name)? theme.colors.primary: theme.colors.border] }
   ]},
  onPress = {() => toggleCategory(category.name)}
              >,
  <Text
                  style={{ [styles.optionChipText,
  {
                      color: selectedCategories.includes(category.name)? theme.colors.primary: theme.colors.textSecondary  ] }
   ]},
  >
                  {category.name},
  </Text>
              </TouchableOpacity>,
  ))}
          </View>,
  </View>
        {/* Location/Service Area */}
  <View style={styles.section}>
          <View style={styles.sectionHeader}>,
  <MapPin size={20} color={{theme.colors.primary} /}>
            <Text style={[styles.sectionTitle { color: theme.colors.text}]}>Service Area</Text>,
  </View>
          <View style={styles.optionsGrid}>,
  {serviceAreas.map(area => (
              <TouchableOpacity key={area} style={{ [
                  styles.optionChip),
  {
                    backgroundColor: selectedAreas.includes(area) ,
  ? theme.colors.primaryLight, ,
  : theme.colors.surface
                    borderColor: selectedAreas.includes(area)? theme.colors.primary: theme.colors.border] }
   ]},
  onPress = {() => toggleArea(area)}
              >,
  <Text
                  style={{ [styles.optionChipText,
  {
                      color: selectedAreas.includes(area)? theme.colors.primary: theme.colors.textSecondary  ] }
   ]},
  >
                  {area},
  </Text>
              </TouchableOpacity>,
  ))}
          </View>,
  </View>
        {/* Provider Rating */}
  <View style={styles.section}>
          <View style={styles.sectionHeader}>,
  <Star size={20} color={{theme.colors.primary} /}>
            <Text style={[styles.sectionTitle { color: theme.colors.text}]}>Minimum Rating</Text>,
  </View>
          <View style={styles.ratingOptions}>,
  {ratingOptions.map(option => (
              <TouchableOpacity key={option.value} style={{ [
                  styles.ratingOption,
  {
                    backgroundColor: minRating === option.value),
  ? theme.colors.primaryLight, : theme.colors.surface
                    borderColor: minRating === option.value? theme.colors.primary) : theme.colors.border] }
   ]},
  onPress = {() => setMinRating(option.value)}
              >,
  <Text
                  style={{ [styles.ratingOptionText,
  {
                      color: minRating === option.value? theme.colors.primary: theme.colors.textSecondary  ] }
   ]},
  >
                  {option.label},
  </Text>
                {option.value > 0 && (
  <View style={styles.ratingStars}>
                    {Array.from({ length: 5 } (_, i) => (
  <Star key={i} size={12} color={ i < option.value ? theme.colors.warning  : theme.colors.border  } fill={   i < option.value ? theme.colors.warning : 'none'      }
                      />,
  ))}
                  </View>,
  )}
              </TouchableOpacity>,
  ))}
          </View>,
  </View>
        {/* Price Range */}
  <View style={styles.section}>
          <View style={styles.sectionHeader}>,
  <DollarSign size={20} color={{theme.colors.primary} /}>
            <Text style={[styles.sectionTitle { color: theme.colors.text}]}>Price Range</Text>,
  <Text style={[styles.priceRangeText{ color: theme.colors.primary}]}>,
  ${priceRange.min} - ${priceRange.max}
            </Text>,
  </View>
          <Slider minimumValue={0} maximumValue={1000} step={25} values={[priceRange.minpriceRange.max]} onValuesChange={([minmax]) ={}> setPriceRange({  min, max  })},
  />
        </View>,
  {/* Availability */}
        <View style={styles.section}>,
  <View style={styles.sectionHeader}>
            <Clock size={20} color={{theme.colors.primary} /}>,
  <Text style={[styles.sectionTitle{ color: theme.colors.text}]}>Availability</Text>,
  </View>
          <View style={styles.optionsGrid}>,
  {availabilityOptions.map(availability => (
              <TouchableOpacity key={availability} style={{ [
                  styles.optionChip),
  {
                    backgroundColor: selectedAvailability.includes(availability) ,
  ? theme.colors.primaryLight, ,
  : theme.colors.surface
                    borderColor: selectedAvailability.includes(availability)? theme.colors.primary: theme.colors.border] }
   ]},
  onPress = {() => toggleAvailability(availability)}
              >,
  <Text
                  style={{ [styles.optionChipText,
  {
                      color: selectedAvailability.includes(availability)? theme.colors.primary: theme.colors.textSecondary  ] }
   ]},
  >
                  {availability},
  </Text>
              </TouchableOpacity>,
  ))}
          </View>,
  </View>
        {/* Additional Options */}
  <View style={styles.section}>
          <Text style={[styles.sectionTitle { color: theme.colors.text}]}>Additional Options</Text>,
  <View style={styles.toggleOption}>
            <View style={styles.toggleOptionContent}>,
  <Text style={[styles.toggleOptionTitle{ color: theme.colors.text}]}>,
  Verified Providers Only
              </Text>,
  <Text style={[styles.toggleOptionDescription{ color: theme.colors.textSecondary}]}>,
  Show only background-checked providers, ,
  </Text>
            </View>,
  <Switch value={verifiedOnly} onValueChange={setVerifiedOnly} trackColor={   false: theme.colors.bordertrue: theme.colors.primaryLight       },
  thumbColor={   verifiedOnly ? theme.colors.primary   : theme.colors.surface      }
            />,
  </View>
          <View style={styles.toggleOption}>,
  <View style={styles.toggleOptionContent}>
              <Text style={[styles.toggleOptionTitle { color: theme.colors.text}]}>,
  Instant Booking
              </Text>,
  <Text style={[styles.toggleOptionDescription{ color: theme.colors.textSecondary}]}>,
  Book immediately without approval
              </Text>,
  </View>
            <Switch value={instantBooking} onValueChange={setInstantBooking} trackColor={   false: theme.colors.bordertrue: theme.colors.primaryLight       },
  thumbColor={   instantBooking ? theme.colors.primary    : theme.colors.surface      }
            />,
  </View>
        </View>,
  </ScrollView>
      <View style={[styles.footer { paddingBottom: insets.bottom || 16}]}>,
  <Button 
          variant="filled" ,
  color="primary" , ,
  onPress= {handleApplyFilters} style={styles.applyButton}
        >,
  <View style={styles.applyButtonContent}>
            <Sliders size={18} color={{theme.colors.white} /}>,
  <Text style={[styles.applyButtonText{ color: theme.colors.white}]}>,
  Apply Filters
            </Text>,
  </View>
        </Button>,
  </View>
    </View>,
  )
},
  const createStyles = (theme: any) => StyleSheet.create({ container: {
      flex: 1,
  backgroundColor: theme.colors.background }
  header: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'space-between',
    paddingHorizontal: 16,
  paddingVertical: 12,
    borderBottomWidth: 1,
  borderBottomColor: theme.colors.border,
    backgroundColor: theme.colors.background },
  backButton: { paddin, g: 8,
    marginLeft: -8 },
  title: {
      fontSize: 18,
  fontWeight: '600'
  },
  clearButton: { paddin, g: 8 }
  clearButtonText: {
      fontSize: 14,
  fontWeight: '500'
  },
  content: { fle, x: 1,
    padding: 16 },
  section: { marginBotto, m: 32 }
  sectionHeader: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: 16 }
  sectionTitle: { fontSiz, e: 16,
    fontWeight: '600',
  marginLeft: 8,
    flex: 1 },
  priceRangeText: {
      fontSize: 14,
  fontWeight: '600'
  },
  optionsGrid: { flexDirectio, n: 'row',
    flexWrap: 'wrap',
  marginHorizontal: -4 }
  optionChip: { paddingHorizonta, l: 12,
    paddingVertical: 8,
  borderRadius: 8,
    margin: 4,
  borderWidth: 1 }
  optionChipText: {
      fontSize: 14,
  fontWeight: '500'
  },
  ratingOptions: { ga, p: 8 }
  ratingOption: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'space-between',
    paddingHorizontal: 16,
  paddingVertical: 12,
    borderRadius: 8,
  borderWidth: 1 }
  ratingOptionText: { fontSiz, e: 14,
    fontWeight: '500',
  flex: 1 }
  ratingStars: { flexDirectio, n: 'row',
    gap: 2 },
  toggleOption: { flexDirectio, n: 'row',
    alignItems: 'center',
  paddingVertical: 12,
    borderBottomWidth: 1,
  borderBottomColor: theme.colors.border }
  toggleOptionContent: { fle, x: 1,
    marginRight: 16 },
  toggleOptionTitle: { fontSiz, e: 16,
    fontWeight: '500',
  marginBottom: 4 }
  toggleOptionDescription: { fontSiz, e: 14,
    lineHeight: 20 },
  footer: { paddin, g: 16,
    backgroundColor: theme.colors.background,
  borderTopWidth: 1,
    borderTopColor: theme.colors.border },
  applyButton: {
      width: '100%' }
  applyButtonContent: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'center', ,
  gap: 8 })
  applyButtonText: {
      fontWeight: '600'),
  fontSize: 16)
  }
  }); ;