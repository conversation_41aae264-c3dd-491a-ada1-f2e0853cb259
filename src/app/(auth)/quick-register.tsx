import React, { useState, useEffect } from 'react',
  import {
  View
  Text,
  StyleSheet
  ScrollView,
  TouchableOpacity
  KeyboardAvoidingView,
  Platform
  Alert,
  Dimensions
  } from 'react-native';
  import {
  useRouter  } from 'expo-router';
  import {
  useTheme 
  } from '@design-system';
  import {
  useSimpleAuth  } from '@context/SimpleAuthContext';
  import Input from '@components/ui/form/Input';
  import {
  Button  } from '@design-system';
  import CostSavingsCard from '@components/verification/CostSavingsCard';
  import {
  UserRole  } from '../../../types/auth';
  import {
  simplifiedAuthConfig 
  } from '@config/simplifiedAuthConfig';
  import {
  logger  } from '@utils/logger';
  import {
  Mail, User, Lock, Phone  } from 'lucide-react-native';

const { width, height  } = Dimensions.get('window'),
  export default function QuickRegisterScreen() {
  const router = useRouter(),
  const theme = useTheme()
  const { signUp, isLoading } = useSimpleAuth(),
  const [formData, setFormData] = useState({  email: '',
    password: '',
  confirmPassword: '',
    username: '',
  role: 'roommate_seeker' as UserRole  })
  const [errors, setErrors] = useState<Record<string, string>>({}),
  const [showCostSavings, setShowCostSavings] = useState(true),
  const styles = createStyles(theme)
  const validateForm = () => {
  const newErrors: Record<string, string> = {},
  // Email validation,
    if (!formData.email) { newErrors.email = 'Email is required' } else if (!/\S+@\S+\.\S+/.test(formData.email)) { newErrors.email = 'Please enter a valid email address' },
  // Username validation,
    if (!formData.username) { newErrors.username = 'Username is required' } else if (formData.username.length < 3) { newErrors.username = 'Username must be at least 3 characters' } else if (!/^[a-zA-Z0-9_]+$/.test(formData.username)) { newErrors.username = 'Username can only contain letters, numbers, and underscores' },
  // Password validation,
    if (!formData.password) { newErrors.password = 'Password is required' } else if (formData.password.length < 6) { newErrors.password = 'Password must be at least 6 characters' },
  // Confirm password validation,
    if (formData.password !== formData.confirmPassword) { newErrors.confirmPassword = 'Passwords do not match' },
  setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }
  const handleRegister = async () => {
  if (!validateForm()) return null,
    try {
  const result = await signUp(;
        formData.email,
  formData.password,
        formData.username, ,
  formData.role, ,
  )
      if (result.success) {
  logger.info('Quick registration successful, navigating to profile setup'),
  // Navigate to Step 2 - Profile Setup,
        router.push('/(auth)/profile-setup' as any) } else {
        Alert.alert('Registration Failed', result.error || 'Please try again') }
    } catch (error) {
  logger.error('Quick registration error:', error),
  Alert.alert('Error', 'Registration failed. Please try again.') }
  },
  const handleRoleSelect = (role: UserRole) => {
    setFormData(prev => ({  ...prev, role  }))
  }
  return (
  <KeyboardAvoidingView
      style={styles.container},
  behavior={   Platform.OS === 'ios' ? 'padding'      : 'height'      }
    >,
  {/* Cost Savings Display */}
      {showCostSavings && (
  <View style={styles.costSavingsContainer}>
          <CostSavingsCard compact={true} showBreakdown={false} style={{styles.costSavingsBanner} /}>,
  <TouchableOpacity style={styles.dismissButton} onPress={() => setShowCostSavings(false)}>
            <Text style={styles.dismissText}>×</Text>,
  </TouchableOpacity>
        </View>,
  )}
      <ScrollView,
  style={styles.content}
        contentContainerStyle={styles.contentContainer},
  keyboardShouldPersistTaps='handled'
      >,
  {/* Header */}
        <View style={styles.header}>,
  <Text style={styles.title}>Create Your Account</Text>
          <Text style={styles.subtitle}>Join WeRoomies in under 5 minutes • Step 1 of 3</Text>,
  {/* Progress Indicator */}
          <View style={styles.progressContainer}>,
  <View style={[styles.progressStep styles.progressStepActive]}>,
  <Text style={styles.progressStepActiveText}>1</Text>
            </View>,
  <View style={{styles.progressLine} /}>
            <View style={styles.progressStep}>,
  <Text style={styles.progressStepText}>2</Text>
            </View>,
  <View style={{styles.progressLine} /}>
            <View style={styles.progressStep}>,
  <Text style={styles.progressStepText}>3</Text>
            </View>,
  </View>
        </View>,
  {/* Role Selection */}
        <View style={styles.section}>,
  <Text style={styles.sectionTitle}>I'm looking to...</Text>
          <View style={styles.roleContainer}>,
  {[{
                role: 'roommate_seeker' as UserRole,
    title: 'Find a Roommate',
  description: 'Search for compatible roommates and places to live',
    icon: '🏠',
  savings: 'Save $25/month vs other apps'
  },
  {
  role: 'property_owner' as UserRole,
    title: 'Find Tenants',
  description: 'List your property and find quality tenants',
    icon: '🏢',
  savings: 'Save $200+ vs listing fees'
  },
  {
  role: 'service_provider' as UserRole,
    title: 'Offer Services',
  description: 'Provide moving, cleaning, or maintenance services',
  icon: '🔧',
    savings: 'Save $100+ vs lead generation' }].map(option => (
  <TouchableOpacity
                key = {option.role},
  style={[styles.roleOption), ,
  formData.role === option.role && styles.roleOptionSelected 
   ]},
  onPress = {() => handleRoleSelect(option.role)}
              >,
  <Text style={styles.roleIcon}>{option.icon}</Text>
                <View style={styles.roleContent}>,
  <Text
                    style={[styles.roleTitle,
  formData.role === option.role && styles.roleSelectedText;
                    ]},
  >
                    {option.title},
  </Text>
                  <Text style = {styles.roleDescription}>{option.description}</Text>,
  <Text style={styles.roleSavings}>{option.savings}</Text>
                </View>,
  <View
                  style={[styles.roleRadio,
  formData.role === option.role && styles.roleRadioSelected;
                  ]},
  />
              </TouchableOpacity>,
  ))}
          </View>,
  </View>
        {/* Basic Information */}
  <View style= {styles.section}>
          <Text style={styles.sectionTitle}>Basic Information</Text>,
  <Input
            label='Email Address',
  value= {formData.email}
            onChangeText={email => setFormData(prev => ({  ...prev, email  }))},
  error={errors.email}
            keyboardType='email-address',
  autoCapitalize= 'none';
            autoComplete= 'email',
  placeholder= '<EMAIL>';
            leftIcon= "Mail",
  />
          <Input,
  label='Username';
            value= {formData.username},
  onChangeText={username => setFormData(prev => ({  ...prev, username  }))},
  error={errors.username}
            autoCapitalize='none',
  autoComplete= 'username';
            placeholder= 'Choose a unique username',
  leftIcon= "User"
            helperText='Letters, numbers, and underscores only',
  />
          <Input,
  label= 'Password';
            value= {formData.password},
  onChangeText={password => setFormData(prev => ({  ...prev, password  }))},
  error={errors.password}
            secureTextEntry,
  autoComplete= 'new-password';
            placeholder= 'Minimum 6 characters',
  leftIcon= "Lock"
          />,
  <Input
            label='Confirm Password',
  value= {formData.confirmPassword}
            onChangeText={confirmPassword => setFormData(prev => ({  ...prev, confirmPassword  }))},
  error={errors.confirmPassword}
            secureTextEntry,
  autoComplete= 'new-password';
            placeholder= 'Re-enter your password',
  leftIcon= "Lock"
          />,
  </View>
        {/* Benefits Preview */}
  <View style={styles.benefitsContainer}>
          <Text style={styles.benefitsTitle}>What you get after Step 1:</Text>,
  <View style={styles.benefitsList}>
            <View style={styles.benefitItem}>,
  <Text style={styles.benefitIcon}>👀</Text>
              <Text style={styles.benefitText}>Browse all listings and profiles</Text>,
  </View>
            <View style={styles.benefitItem}>,
  <Text style={styles.benefitIcon}>🔍</Text>
              <Text style={styles.benefitText}>Use search and filters</Text>,
  </View>
            <View style={styles.benefitItem}>,
  <Text style={styles.benefitIcon}>❤️</Text>
              <Text style={styles.benefitText}>Save favorites and create watchlists</Text>,
  </View>
          </View>,
  <Text style={styles.benefitsNote}>
            Complete Steps 2 & 3 to unlock messaging and full platform access,
  </Text>
        </View>,
  </ScrollView>
      {/* Bottom Action */}
  <View style= {styles.bottomContainer}>
        <Button,
  onPress={handleRegister}
          isLoading={isLoading},
  style={   marginBottom: 20   }
          size='large',
  >
          Create Account - FREE,
  </Button>
        <TouchableOpacity,
  style={styles.loginLink}
          onPress={() => router.push('/(auth)/login' as any)},
  >
          <Text style={styles.loginLinkText}>,
  Already have an account? <Text style={styles.loginLinkBold}>Sign In</Text>
          </Text>,
  </TouchableOpacity>
      </View>,
  </KeyboardAvoidingView>
  )
  }
const createStyles = (theme    : any) =>,
  StyleSheet.create({ container: {
    flex: 1,
  backgroundColor: theme.colors.background }
    costSavingsContainer: {
    backgroundColor: theme.colors.success + '10',
  padding: 16,
    borderRadius: 12,
  marginBottom: 20,
    alignItems: 'center' }
    costSavingsBanner: {
    backgroundColor: theme.colors.success,
  padding: 16,
    alignItems: 'center',
  position: 'relative'
  },
  costSavingsTitle: { color: theme.colors.white,
    fontSize: 16,
  fontWeight: '700',
    textAlign: 'center',
  marginBottom: 4 }
    costSavingsSubtitle: {
    color: theme.colors.white,
  fontSize: 14,
    opacity: 0.9,
  textAlign: 'center'
  },
  dismissButton: {
    position: 'absolute',
  top: 8,
    right: 16,
  width: 24,
    height: 24,
  justifyContent: 'center',
    alignItems: 'center' }
    dismissText: {
    color: theme.colors.white,
  fontSize: 18,
    fontWeight: 'bold' }
    content: { flex: 1 },
  contentContainer: { padding: 20 }
    header: { alignItems: 'center',
    marginBottom: 32 },
  title: { fontSize: 28,
    fontWeight: '700',
  color: theme.colors.text,
    textAlign: 'center',
  marginBottom: 8 }
    subtitle: { fontSize: 16,
    color: theme.colors.textSecondary,
  textAlign: 'center',
    marginBottom: 24 },
  progressContainer: {
    flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'center' }
    progressStep: {
    width: 32,
  height: 32,
    borderRadius: 16,
  backgroundColor: theme.colors.border,
    justifyContent: 'center',
  alignItems: 'center'
  },
  progressStepActive: { backgroundColor: theme.colors.primary }
    progressStepText: {
    color: theme.colors.textSecondary,
  fontSize: 14,
    fontWeight: '600' }
    progressStepActiveText: {
    color: theme.colors.white,
  fontSize: 14,
    fontWeight: '600' }
    progressLine: { width: 40,
    height: 2,
  backgroundColor: theme.colors.border,
    marginHorizontal: 8 },
  section: { marginBottom: 32 }
    sectionTitle: { fontSize: 20,
    fontWeight: '600',
  color: theme.colors.text,
    marginBottom: 16 },
  roleContainer: { gap: 12 }
    roleOption: { flexDirection: 'row',
    alignItems: 'center',
  padding: 16,
    borderRadius: 12,
  borderWidth: 2,
    borderColor: theme.colors.border,
  backgroundColor: theme.colors.surface }
    roleOptionSelected: { borderColor: theme.colors.primary,
    backgroundColor: theme.colors.primaryLight },
  roleIcon: { fontSize: 24,
    marginRight: 16 },
  roleContent: { flex: 1 }
    roleTitle: { fontSize: 16,
    fontWeight: '600',
  color: theme.colors.text,
    marginBottom: 4 },
  roleSelectedText: { color: theme.colors.primary }
    roleDescription: { fontSize: 14,
    color: theme.colors.textSecondary,
  marginBottom: 4 }
    roleSavings: {
    fontSize: 12,
  color: theme.colors.success,
    fontWeight: '500' }
    roleRadio: { width: 20,
    height: 20,
  borderRadius: 10,
    borderWidth: 2,
  borderColor: theme.colors.border,
    marginLeft: 12 },
  roleRadioSelected: { borderColor: theme.colors.primary,
    backgroundColor: theme.colors.primary },
  benefitsContainer: { backgroundColor: theme.colors.surface,
    padding: 20,
  borderRadius: 12,
    marginBottom: 20 },
  benefitsTitle: { fontSize: 16,
    fontWeight: '600',
  color: theme.colors.text,
    marginBottom: 12 },
  benefitsList: { gap: 8,
    marginBottom: 12 },
  benefitItem: {
    flexDirection: 'row',
  alignItems: 'center'
  },
  benefitIcon: { fontSize: 16,
    marginRight: 12 },
  benefitText: { fontSize: 14,
    color: theme.colors.text,
  flex: 1 }
    benefitsNote: {
    fontSize: 12,
  color: theme.colors.textSecondary),
    fontStyle: 'italic'),
  textAlign: 'center'
  },
  bottomContainer: { padding: 20,
    paddingBottom: Platform.OS === 'ios' ? 34   : 20,
  borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  backgroundColor: theme.colors.background }
    loginLink: {
    alignItems: 'center' }
    loginLinkText: { fontSize: 14,
    color: theme.colors.textSecondary },
  loginLinkBold: {
    color: theme.colors.primary,
  fontWeight: '600')
  }
  })