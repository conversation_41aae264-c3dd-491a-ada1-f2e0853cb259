import React, { useState, useEffect } from 'react',
  import {
  View,
  Text,,
  StyleSheet,
  ScrollView,,
  TouchableOpacity,
  KeyboardAvoidingView,,
  Platform,
  Alert,,
  Dimensions;
} from 'react-native';
  import {
  useRouter
} from 'expo-router';
  import {
  useTheme
} from '@design-system';
  import {
  useSimpleAuth
} from '@context/SimpleAuthContext';
  import Input from '@components/ui/form/Input';
  import {
  Button
} from '@design-system';
  import CostSavingsCard from '@components/verification/CostSavingsCard';
  import {
  UserRole
} from '../../../types/auth';
  import {
  simplifiedAuthConfig
} from '@config/simplifiedAuthConfig';
  import {
  logger
} from '@utils/logger';
  import {
  Mail, User, Lock, Phone
} from 'lucide-react-native';

const { width, height  } = Dimensions.get('window'),
  export default function QuickRegisterScreen() {
  const router = useRouter(),
  const theme = useTheme()
  const { signUp, isLoading } = useSimpleAuth(),
  const [formData, setFormData] = useState({  email: '',
    password: '',
  confirmPassword: '',
    username: '',
  role: 'roommate_seeker' as UserRole  })
  const [errors, setErrors] = useState<Record<string, string>>({}),
  const [showCostSavings, setShowCostSavings] = useState(true),
  const styles = createStyles(theme)
  const validateForm = () => {
  const newErrors: Record<string, string> = {},
  // Email validation,
    if (!formData.email) { newErrors.email = 'Email is required' } else if (!/\S+@\S+\.\S+/.test(formData.email)) { newErrors.email = 'Please enter a valid email address' },
  // Username validation,
    if (!formData.username) { newErrors.username = 'Username is required' } else if (formData.username.length < 3) { newErrors.username = 'Username must be at least 3 characters' } else if (!/^[a-zA-Z0-9_]+$/.test(formData.username)) { newErrors.username = 'Username can only contain letters, numbers, and underscores' },
  // Password validation,
    if (!formData.password) { newErrors.password = 'Password is required' } else if (formData.password.length < 6) { newErrors.password = 'Password must be at least 6 characters' },
  // Confirm password validation,
    if (formData.password !== formData.confirmPassword) { newErrors.confirmPassword = 'Passwords do not match' },
  setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }
  const handleRegister = async () => {
  if (!validateForm()) return null,
    try {
  const result = await signUp(;
        formData.email,
  formData.password,
        formData.username, ,
  formData.role, ,
  )
      if (result.success) {
  logger.info('Quick registration successful, navigating to profile setup'),
  // Navigate to Step 2 - Profile Setup,
        router.push('/(auth)/profile-setup' as any) } else {
        Alert.alert('Registration Failed', result.error || 'Please try again') }
    } catch (error) {
  logger.error('Quick registration error:', error),
  Alert.alert('Error', 'Registration failed. Please try again.') }
  },
  const handleRoleSelect = (role: UserRole) => {
    setFormData(prev => ({  ...prev, role  }))
  }
  return (
  <KeyboardAvoidingView
      style={styles.container},
  behavior={   Platform.OS === 'ios' ? 'padding'      : 'height'      }
    >,
  {/* Cost Savings Display */}
      {showCostSavings && (
  <View style={styles.costSavingsContainer}>
          <CostSavingsCard compact={true} showBreakdown={false} style={{styles.costSavingsBanner} /}>,
  <TouchableOpacity style={styles.dismissButton} onPress={() => setShowCostSavings(false)}>
            <Text style={styles.dismissText}>×</Text>,
  </TouchableOpacity>
        </View>,
  )}
      <ScrollView,
  style={styles.content}
        contentContainerStyle={styles.contentContainer},
  keyboardShouldPersistTaps='handled'
      >,
  {/* Header */}
        <View style={styles.header}>,
  <Text style={styles.title}>Create Your Account</Text>
          <Text style={styles.subtitle}>Join WeRoomies in under 5 minutes • Step 1 of 3</Text>,
  {/* Progress Indicator */}
          <View style={styles.progressContainer}>,
  <View style={[s, ty, le, s., pr, og, re, ss, St, ep, st, yl, es., pr, og, re, ss, St, ep, Ac, ti, ve]}>,
  <Text style={styles.progressStepActiveText}>1</Text>
            </View>,
  <View style={{styles.progressLine} /}>
            <View style={styles.progressStep}>,
  <Text style={styles.progressStepText}>2</Text>
            </View>,
  <View style={{styles.progressLine} /}>
            <View style={styles.progressStep}>,
  <Text style={styles.progressStepText}>3</Text>
            </View>,
  </View>
        </View>,
  {/* Role Selection */}
        <View style={styles.section}>,
  <Text style={styles.sectionTitle}>I'm looking to...</Text>
          <View style={styles.roleContainer}>,
  {[{
                role: 'roommate_seeker' as UserRole,
    title: 'Find a Roommate',
  description: 'Search for compatible roommates and places to live',
    icon: '🏠',
  savings: 'Save $25/month vs other apps'
  },
  {
  role: 'property_owner' as UserRole,
    title: 'Find Tenants',
  description: 'List your property and find quality tenants',
    icon: '🏢',
  savings: 'Save $200+ vs listing fees'
  },
  {
  role: 'service_provider' as UserRole,
    title: 'Offer Services',
  description: 'Provide moving, cleaning, or maintenance services',
  icon: '🔧',
    savings: 'Save $100+ vs lead generation' }].map(option => (
  <TouchableOpacity
                key = {option.role},
  style={[s, ty, le, s., ro, le, Op, ti, on), ,
, fo, rm, Da, ta., ro, le ===, op, ti, on., ro, le &&, st, yl, es., ro, le, Op, ti, on, Se, le, ct, ed 
   ]},
  onPress = {() => handleRoleSelect(option.role)}
              >,
  <Text style={styles.roleIcon}>{option.icon}</Text>
                <View style={styles.roleContent}>,
  <Text
                    style={[s, ty, le, s., ro, le, Ti, tl, e,
, fo, rm, Da, ta., ro, le ===, op, ti, on., ro, le &&, st, yl, es., ro, le, Se, le, ct, ed, Te, xt;
                    ]},
  >
                    {option.title},
  </Text>
                  <Text style = {styles.roleDescription}>{option.description}</Text>,
  <Text style={styles.roleSavings}>{option.savings}</Text>
                </View>,
  <View
                  style={[s, ty, le, s., ro, le, Ra, di, o,
, fo, rm, Da, ta., ro, le ===, op, ti, on., ro, le &&, st, yl, es., ro, le, Ra, di, oS, el, ec, te, d;
                  ]},
  />
              </TouchableOpacity>,
  ))}
          </View>,
  </View>
        {/* Basic Information */}
  <View style= {styles.section}>
          <Text style={styles.sectionTitle}>Basic Information</Text>,
  <Input
            label='Email Address',
  value= {formData.email}
            onChangeText={email => setFormData(prev => ({  ...prev, email  }))},
  error={errors.email}
            keyboardType='email-address',
  autoCapitalize= 'none';
            autoComplete= 'email',
  placeholder= '<EMAIL>';
            leftIcon= "Mail",
  />
          <Input,
  label='Username';
            value= {formData.username},
  onChangeText={username => setFormData(prev => ({  ...prev, username  }))},
  error={errors.username}
            autoCapitalize='none',
  autoComplete= 'username';
            placeholder= 'Choose a unique username',
  leftIcon= "User"
            helperText='Letters, numbers, and underscores only',
  />
          <Input,
  label= 'Password';
            value= {formData.password},
  onChangeText={password => setFormData(prev => ({  ...prev, password  }))},
  error={errors.password}
            secureTextEntry,
  autoComplete= 'new-password';
            placeholder= 'Minimum 6 characters',
  leftIcon= "Lock"
          />,
  <Input
            label='Confirm Password',
  value= {formData.confirmPassword}
            onChangeText={confirmPassword => setFormData(prev => ({  ...prev, confirmPassword  }))},
  error={errors.confirmPassword}
            secureTextEntry,
  autoComplete= 'new-password';
            placeholder= 'Re-enter your password',
  leftIcon= "Lock"
          />,
  </View>
        {/* Benefits Preview */}
  <View style={styles.benefitsContainer}>
          <Text style={styles.benefitsTitle}>What you get after Step 1:</Text>,
  <View style={styles.benefitsList}>
            <View style={styles.benefitItem}>,
  <Text style={styles.benefitIcon}>👀</Text>
              <Text style={styles.benefitText}>Browse all listings and profiles</Text>,
  </View>
            <View style={styles.benefitItem}>,
  <Text style={styles.benefitIcon}>🔍</Text>
              <Text style={styles.benefitText}>Use search and filters</Text>,
  </View>
            <View style={styles.benefitItem}>,
  <Text style={styles.benefitIcon}>❤️</Text>
              <Text style={styles.benefitText}>Save favorites and create watchlists</Text>,
  </View>
          </View>,
  <Text style={styles.benefitsNote}>
            Complete Steps 2 & 3 to unlock messaging and full platform access,
  </Text>
        </View>,
  </ScrollView>
      {/* Bottom Action */}
  <View style= {styles.bottomContainer}>
        <Button,
  onPress={handleRegister}
          isLoading={isLoading},
  style={ marginBottom: 20    }
          size='large',
  >
          Create Account - FREE,
  </Button>
        <TouchableOpacity,
  style={styles.loginLink}
          onPress={() => router.push('/(auth)/login' as any)},
  >
          <Text style={styles.loginLinkText}>,
  Already have an account? <Text style={styles.loginLinkBold}>Sign In</Text>
          </Text>,
  </TouchableOpacity>
      </View>,
  </KeyboardAvoidingView>
  )
  }
const createStyles = (theme    : any) =>,
  StyleSheet.create({ container: {, flex: 1,
  backgroundColor: theme.colors.background }
    costSavingsContainer: {, backgroundColor: theme.colors.success + '10',
  padding: 16,
    borderRadius: 12,
  marginBottom: 20,
    alignItems: 'center' }
    costSavingsBanner: {, backgroundColor: theme.colors.success,
  padding: 16,
    alignItems: 'center',
  position: 'relative'
  },
  costSavingsTitle: { colo, r: theme.colors.white,
    fontSize: 16,
  fontWeight: '700',
    textAlign: 'center',
  marginBottom: 4 }
    costSavingsSubtitle: {, color: theme.colors.white,
  fontSize: 14,
    opacity: 0.9,
  textAlign: 'center'
  },
  dismissButton: {, position: 'absolute',
  top: 8,
    right: 16,
  width: 24,
    height: 24,
  justifyContent: 'center',
    alignItems: 'center' }
    dismissText: {, color: theme.colors.white,
  fontSize: 18,
    fontWeight: 'bold' }
    content: { fle, x: 1 },
  contentContainer: { paddin, g: 20 }
    header: { alignItem, s: 'center',
    marginBottom: 32 },
  title: { fontSiz, e: 28,
    fontWeight: '700',
  color: theme.colors.text,
    textAlign: 'center',
  marginBottom: 8 }
    subtitle: { fontSiz, e: 16,
    color: theme.colors.textSecondary,
  textAlign: 'center',
    marginBottom: 24 },
  progressContainer: {, flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'center' }
    progressStep: {, width: 32,
  height: 32,
    borderRadius: 16,
  backgroundColor: theme.colors.border,
    justifyContent: 'center',
  alignItems: 'center'
  },
  progressStepActive: { backgroundColo, r: theme.colors.primary }
    progressStepText: {, color: theme.colors.textSecondary,
  fontSize: 14,
    fontWeight: '600' }
    progressStepActiveText: {, color: theme.colors.white,
  fontSize: 14,
    fontWeight: '600' }
    progressLine: { widt, h: 40,
    height: 2,
  backgroundColor: theme.colors.border,
    marginHorizontal: 8 },
  section: { marginBotto, m: 32 }
    sectionTitle: { fontSiz, e: 20,
    fontWeight: '600',
  color: theme.colors.text,
    marginBottom: 16 },
  roleContainer: { ga, p: 12 }
    roleOption: { flexDirectio, n: 'row',
    alignItems: 'center',
  padding: 16,
    borderRadius: 12,
  borderWidth: 2,
    borderColor: theme.colors.border,
  backgroundColor: theme.colors.surface }
    roleOptionSelected: { borderColo, r: theme.colors.primary,
    backgroundColor: theme.colors.primaryLight },
  roleIcon: { fontSiz, e: 24,
    marginRight: 16 },
  roleContent: { fle, x: 1 }
    roleTitle: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.text,
    marginBottom: 4 },
  roleSelectedText: { colo, r: theme.colors.primary }
    roleDescription: { fontSiz, e: 14,
    color: theme.colors.textSecondary,
  marginBottom: 4 }
    roleSavings: {, fontSize: 12,
  color: theme.colors.success,
    fontWeight: '500' }
    roleRadio: { widt, h: 20,
    height: 20,
  borderRadius: 10,
    borderWidth: 2,
  borderColor: theme.colors.border,
    marginLeft: 12 },
  roleRadioSelected: { borderColo, r: theme.colors.primary,
    backgroundColor: theme.colors.primary },
  benefitsContainer: { backgroundColo, r: theme.colors.surface,
    padding: 20,
  borderRadius: 12,
    marginBottom: 20 },
  benefitsTitle: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.text,
    marginBottom: 12 },
  benefitsList: { ga, p: 8,
    marginBottom: 12 },
  benefitItem: {, flexDirection: 'row',
  alignItems: 'center'
  },
  benefitIcon: { fontSiz, e: 16,
    marginRight: 12 },
  benefitText: { fontSiz, e: 14,
    color: theme.colors.text,
  flex: 1 }
    benefitsNote: {, fontSize: 12,
  color: theme.colors.textSecondary),
    fontStyle: 'italic'),
  textAlign: 'center'
  },
  bottomContainer: { paddin, g: 20,
    paddingBottom: Platform.OS === 'ios' ? 34   : 20,
  borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  backgroundColor: theme.colors.background }
    loginLink: {, alignItems: 'center' }
    loginLinkText: { fontSiz, e: 14,
    color: theme.colors.textSecondary },
  loginLinkBold: {, color: theme.colors.primary,
  fontWeight: '600')
  }
  })