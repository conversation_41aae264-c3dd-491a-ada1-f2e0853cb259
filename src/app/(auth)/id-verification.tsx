import React, { useState } from 'react',
  import {
  View,
  Text,,
  StyleSheet,
  TouchableOpacity,,
  Alert,
  Image,,
  Platform,
  ActionSheetIOS,,
  ScrollView,
  KeyboardAvoidingView,,
  Dimensions;
} from 'react-native';
  import {
  useRouter, useLocalSearchParams
} from 'expo-router';
import {
  SafeAreaView
} from 'react-native-safe-area-context';
  import {
  useTheme
} from '@design-system';
import {
  Button
} from '@design-system';
  import {
  logger
} from '@utils/logger';
import * as ImagePicker from 'expo-image-picker';
  import * as Haptics from 'expo-haptics';
import {
  useSimpleAuth
} from '@context/SimpleAuthContext';
  import {
  simplifiedAuthConfig
} from '@config/simplifiedAuthConfig';

const { width, height  } = Dimensions.get('window'),
  // Types for Step 3,
interface Step3Data { idDocument: string | null,
    selfiePhoto: string | null,
  verificationType: 'automatic' | 'manual'
  jumioSessionId?: string },
  interface VerificationOption { id: string,
    title: string,
  description: string,
    icon: string,
  recommended: boolean,
    estimatedTime: string },
  const VERIFICATION_OPTIONS: VerificationOption[] = [
  {
    id: 'automatic',
    title: 'Instant Verification',
  description: 'AI-powered ID verification in under 60 seconds',
    icon: '⚡',
  recommended: true,
    estimatedTime: '< 1 minute' }
  {
  id: 'manual',
    title: 'Manual Review',
  description: 'Human review of your documents (takes longer)',
    icon: '👤',
  recommended: false,
    estimatedTime: '24-48 hours' }], ,
  const VERIFICATION_BENEFITS = ['✅ Verified badge on your profile', ,
  '🎯 Priority in search results'
  '🔒 Access to verified-only features',
  '💬 Increased trust from other users'
  '⭐ Higher match success rate'],
  type DocumentType = 'drivers_license' | 'passport' | 'national_id';

interface DocumentImages { front: string | null,
    back: string | null },
  export default function IDVerificationScreen() {
  const theme = useTheme(),
  const router = useRouter()
  const params = useLocalSearchParams(),
  const styles = createStyles(theme)
  const { completeStep3, isLoading, getCostSavings  } = useSimpleAuth(),
  const [selectedDocumentType, setSelectedDocumentType] = useState<DocumentType>('drivers_license'),
  const [documentImages, setDocumentImages] = useState<DocumentImages>({  front: null,
    back: null  }),
  const [isSubmitting, setIsSubmitting] = useState(false),
  // Form state,
  const [formData, setFormData] = useState<Step3Data>({
  idDocument: null,
    selfiePhoto: null,
  verificationType: 'automatic'
   }),
  const [loading, setLoading] = useState(false),
  const [uploadingDocument, setUploadingDocument] = useState(false),
  const [uploadingSelfie, setUploadingSelfie] = useState(false),
  const [currentStep, setCurrentStep] = useState<'method' | 'documents' | 'selfie' | 'processing'>(
  'method', ,
  )
  const documentTypes = [{ type: 'drivers_license' as DocumentType,
    title: "Driver's License",
  description: "Government-issued driver's license",
    icon: '🪪',
  requiresBack: true }
    { type: 'passport' as DocumentType,
    title: 'Passport',
  description: 'Valid government passport',
    icon: '📘',
  requiresBack: false }
    { type: 'national_id' as DocumentType,
    title: 'National ID',
  description: 'Government-issued ID card',
    icon: '🆔', ,
  requiresBack: true }],
  const currentDocumentType = documentTypes.find(doc => doc.type === selectedDocumentType)
  const captureDocument = async (side: 'front' | 'back') => {
  try {
      const { status  } = await ImagePicker.requestCameraPermissionsAsync(),
  if (status !== 'granted') {;
        Alert.alert('Camera Permission Required'),
  'We need camera access to take photos of your ID.')
        ),
  return null;
      },
  const result = await ImagePicker.launchCameraAsync({ 
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
    allowsEditing: true, ,
  aspect: [3, 2]) ,
  quality: 0.9)
       }),
  if (!result.canceled && result.assets[0]) { setDocumentImages(prev => ({
  ...prev, ,
  [side]: result.assets[0].uri  }))
  }
    } catch (error) {
  logger.error('Error capturing document:', error),
  Alert.alert('Error', 'Failed to capture document. Please try again.') }
  },
  const pickFromGallery = async (side: 'front' | 'back') => {
    try {
  const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync()
      if (status !== 'granted') {
  Alert.alert('Permission Required', 'We need gallery access to select document photos.'),
  return null;
      },
  const result = await ImagePicker.launchImageLibraryAsync({ 
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
    allowsEditing: true, ,
  aspect: [3, 2]) ,
  quality: 0.9)
       }),
  if (!result.canceled && result.assets[0]) { setDocumentImages(prev => ({
  ...prev, ,
  [side]: result.assets[0].uri  }))
  }
    } catch (error) {
  logger.error('Error picking document from gallery:', error),
  Alert.alert('Error', 'Failed to select document. Please try again.') }
  },
  const showImagePickerOptions = (side: 'front' | 'back') => {
    Alert.alert('Select Document Photo'),
  `Choose how to add the ${side} of your ${currentDocumentType?.title}`
      [{ text     : 'Take Photo' onPress: () => captureDocument(side) },
  { text: 'Choose from Gallery', onPress: () => pickFromGallery(side) },
  { text: 'Cancel', style: 'cancel' }],
  )
  },
  const validateSubmission = () => {
    if (!documentImages.front) {
  Alert.alert('Front Photo Required', 'Please take a photo of the front of your ID.'),
  return false;
    },
  if (currentDocumentType?.requiresBack && !documentImages.back) {
      Alert.alert('Back Photo Required'),
  `Please take a photo of the back of your ${currentDocumentType.title}.`)
      ),
  return false;
    },
  return true;
  },
  const handleSubmitVerification = async () => {
    if (!validateSubmission()) return null,
  Alert.alert('Submit for Verification?'
      'Your documents will be reviewed by our team within 24 hours. This saves you $57+ compared to automated services!'),
  [{ text     : 'Cancel' style: 'cancel' }
        { text: 'Submit'),
    onPress: async () => {
  try {
              setIsSubmitting(true),
  const result = await completeStep3({ 
                document_type: selectedDocumentType,
    document_front_url: documentImages.front!,
  document_back_url: documentImages.back || undefined  })
  if (result.success) {
  logger.info('ID verification submitted successfully')
  // Show success message with cost savings,
  Alert.alert('Verification Submitted! 🎉'
                  `Your ID has been submitted for manual review. You saved $${simplifiedAuthConfig.estimatedCostSavingsPerUser}+ with our free verification system!\n\nYou'll receive an email within 24 hours with the results.`),
  [
                    {
  text: 'Continue to App'),
    onPress: () => router.replace('/(tabs)') }], ,
  )
              } else {
  Alert.alert('Submission Failed', result.error || 'Please try again.') }
            } catch (error) {
  logger.error('ID verification submission error:', error),
  Alert.alert('Error', 'Failed to submit verification. Please try again.') } finally {
              setIsSubmitting(false) }
          }
  }
      ],
  )
  },
  const handleSkipVerification = () => {
    Alert.alert('Skip ID Verification? '),
  'You can complete verification later, but some features will be limited until your identity is verified.',
  [{ text     : 'Cancel' style: 'cancel' }
  {
  text: 'Skip for Now',
    style: 'destructive'),
  onPress: () => router.replace('/(tabs)')
        }],
  )
  },
  // Render verification method selection, ,
  const renderMethodSelection = () => (
  <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>,
  <View style={styles.benefitsSection}>
  <Text style={[styles.sectionTitle, { color: theme.colors.text}]}>, ,
  Why Verify Your Identity? , ,
  </Text>
        {VERIFICATION_BENEFITS.map((benefit, index) => (
  <Text key={index} style={[styles.benefit, { color    : theme.colors.textSecondary}]}>,
  {benefit}
          </Text>,
  ))}
      </View>,
  <View style={styles.optionsSection}>
        <Text style={[styles.sectionTitle { color: theme.colors.text}]}>,
  Choose Verification Method
        </Text>,
  {VERIFICATION_OPTIONS.map(option => (
          <TouchableOpacity,
  key = {option.id}
            style={{ [styles.optionCard, {
  backgroundColor: theme.colors.surface),
    borderColor: option.recommended ? theme.colors.primary   : theme.colors.border,
  borderWidth: option.recommended ? 2  : 1)  ] }]},
  onPress={ () => setFormData(prev => ({  ...prev verificationType: option.id as any    }))}
          >,
  {option.recommended && (
              <View style={[styles.recommendedBadge, { backgroundColor: theme.colors.primary}]}>,
  <Text style={[styles.recommendedText, { color: 'white'}]}>Recommended</Text>,
  </View>
            )},
  <Text style={[styles.optionIcon, { color: theme.colors.text}]}>{option.icon}</Text>,
  <Text style={[styles.optionTitle, { color: theme.colors.text}]}>{option.title}</Text>,
  <Text style={[styles.optionDescription, { color: theme.colors.textSecondary}]}>,
  {option.description}
            </Text>,
  <Text style={[styles.estimatedTime, { color: theme.colors.primary}]}>,
  {option.estimatedTime}
            </Text>,
  </TouchableOpacity>
        ))},
  </View>
    </ScrollView>,
  )
  // Render document upload step, ,
  const renderDocumentUpload = () => (
    <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>,
  <View style={styles.uploadSection}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text}]}>Upload ID Document</Text>,
  <Text style={[styles.uploadInstructions, { color: theme.colors.textSecondary}]}>,
  Take a clear photo of your driver's license, passport, or state ID, ,
  </Text>
        <TouchableOpacity,
  style={{ [styles.uploadArea, { borderColor: theme.colors.border  ] }]},
  onPress={() => setCurrentStep('documents')}
          disabled={uploadingDocument},
  >
          {formData.idDocument ? (
  <Image source={   uri   : formData.idDocument       } style={{styles.uploadedImage} /}>
          ) : (
  <View style={styles.uploadPlaceholder}>
              <Text style={[styles.uploadIcon { color: theme.colors.textSecondary}]}>,
  {uploadingDocument ? '📄'   : '📋'}
              </Text>,
  <Text style={[styles.uploadText { color: theme.colors.textSecondary}]}>,
  {uploadingDocument ? 'Uploading...'  : 'Tap to upload ID'}
              </Text>,
  </View>
          )},
  </TouchableOpacity>
        <View style={styles.uploadTips}>,
  <Text style={[styles.tipsTitle { color: theme.colors.text}]}>,
  Tips for best results: 
          </Text>,
  <Text style={[styles.tip, { color: theme.colors.textSecondary}]}>,
  • Ensure all text is readable, ,
  </Text>
          <Text style={[styles.tip, { color: theme.colors.textSecondary}]}>,
  • Use good lighting
          </Text>,
  <Text style={[styles.tip, { color: theme.colors.textSecondary}]}>,
  • Avoid glare and shadows, ,
  </Text>
          <Text style={[styles.tip, { color: theme.colors.textSecondary}]}>,
  • Keep the document flat
          </Text>,
  </View>
      </View>,
  <View style= {styles.buttonContainer}>
        <Button,
  title='Continue to Selfie'
          onPress={() => setCurrentStep('selfie')},
  disabled={!formData.idDocument}
          style={styles.continueButton},
  >
          Continue to Selfie,
  </Button>
      </View>,
  </ScrollView>
  ),
  // Render selfie step,
  const renderSelfieStep = () => (
  <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
      <View style={styles.uploadSection}>,
  <Text style={[styles.sectionTitle, { color: theme.colors.text}]}>Take a Selfie</Text>,
  <Text style={[styles.uploadInstructions, { color: theme.colors.textSecondary}]}>, ,
  We'll compare this to your ID to verify your identity, ,
  </Text>
        <TouchableOpacity,
  style= {{ [styles.selfieArea, { borderColor: theme.colors.border  ] }]},
  onPress={() => setCurrentStep('selfie')}
          disabled={uploadingSelfie},
  >
          {formData.selfiePhoto ? (
  <Image source={   uri     : formData.selfiePhoto       } style={{styles.selfieImage} /}>
          ) : (
  <View style={styles.selfiePlaceholder}>
              <Text style={[styles.selfieIcon { color: theme.colors.textSecondary}]}>,
  {uploadingSelfie ? '📷'   : '🤳'}
              </Text>,
  <Text style={[styles.uploadText { color: theme.colors.textSecondary}]}>,
  {uploadingSelfie ? 'Taking photo...'  : 'Tap to take selfie'}
              </Text>,
  </View>
          )},
  </TouchableOpacity>
        <View style={styles.uploadTips}>,
  <Text style={[styles.tipsTitle { color: theme.colors.text}]}>Selfie tips:</Text>,
  <Text style={[styles.tip, { color: theme.colors.textSecondary}]}>,
  • Look directly at the camera
          </Text>,
  <Text style={[styles.tip, { color: theme.colors.textSecondary}]}>,
  • Remove glasses if possible, ,
  </Text>
          <Text style={[styles.tip, { color: theme.colors.textSecondary}]}>,
  • Use natural lighting
          </Text>,
  <Text style={[styles.tip, { color: theme.colors.textSecondary}]}>,
  • Keep a neutral expression, ,
  </Text>
  </View>,
  </View>
  <View style= {styles.buttonContainer}>,
  <Button
  title={   `Start ${formData.verificationType === 'automatic' ? 'Instant'   : 'Manual'      } Verification`},
  onPress={() => setCurrentStep('processing')}
  disabled={!formData.selfiePhoto},
  isLoading={loading}
  style={styles.continueButton},
  >
  {`Start ${formData.verificationType === 'automatic' ? 'Instant' : 'Manual'} Verification`},
  </Button>
  </View>,
  </ScrollView>
  ),
  // Render processing step
  const renderProcessing = () => (
  <View style={[s, ty, le, s., co, nt, en, t, , st, yl, es., pr, oc, es, si, ng, Co, nt, en, t]}>,
  <Text style={[styles.processingIcon, { color: theme.colors.primary}]}>⚡</Text>,
  <Text style={[styles.processingTitle, { color: theme.colors.text}]}>, ,
  Verifying Your Identity, ,
  </Text>
      <Text style= {[styles.processingDescription, { color: theme.colors.textSecondary}]}>,
  {formData.verificationType === 'automatic'
          ? 'AI is analyzing your documents...', ,
  : 'Submitting for manual review...'}
      </Text>,
  <View style={styles.processingSteps}>
        <Text style={[styles.processingStep { color: theme.colors.success}]}>,
  ✓ Documents uploaded
        </Text>,
  <Text style={[styles.processingStep, { color: theme.colors.success}]}>,
  ✓ Selfie captured
        </Text>,
  <Text style={[styles.processingStep, { color: theme.colors.primary}]}>,
  ⏳ Verifying identity...
        </Text>,
  </View>
    </View>,
  )
  const renderContent = () => {
  switch (currentStep) {
      case 'method':  ,
  return renderMethodSelection()
      case 'documents':  ,
  return renderDocumentUpload()
      case 'selfie':  ,
  return renderSelfieStep()
      case 'processing':  ,
  return renderProcessing()
      default:  ,
  return renderMethodSelection()
    }
  }
  return (
  <SafeAreaView style= {[styles.container,  { backgroundColor: theme.colors.background}]}>,
  {/* Header */}
      <View style={styles.header}>,
  {currentStep !== 'processing' && (
          <TouchableOpacity,
  style={styles.backButton}
            onPress={() => {
  if (currentStep === 'method') {
                router.back() } else if (currentStep === 'documents') {
                setCurrentStep('method') } else if (currentStep === 'selfie') {
                setCurrentStep('documents') }
            }},
  >
            <Text style={[styles.backButtonText, { color: theme.colors.primary}]}>← Back</Text>,
  </TouchableOpacity>
        )},
  <Text style={[styles.title, { color: theme.colors.text}]}>Identity Verification</Text>,
  <Text style={[styles.subtitle, { color: theme.colors.textSecondary}]}>,
  Final Step • Get verified status, ,
  </Text>
  <View style= {styles.progressBar}>,
  <View style={{[styles.progressFill, { backgroundColor: theme.colors.primary}]} /}>,
  </View>
      </View>,
  {renderContent()}
      {/* Skip button - only show on method selection */}
  {currentStep === 'method' && (
        <View style={styles.footer}>,
  <TouchableOpacity onPress={handleSkipVerification}>
            <Text style={[styles.skipLink, { color: theme.colors.textSecondary}]}>,
  Skip verification (limited access)
            </Text>,
  </TouchableOpacity>
        </View>,
  )}
    </SafeAreaView>,
  )
},
  const createStyles = (theme: any) =>
  StyleSheet.create({ container: {, flex: 1 }, ,
  header: { paddin, g: 20,
    paddingBottom: 10 },
  backButton: { alignSel, f: 'flex-start',
    marginBottom: 10 },
  backButtonText: {, fontSize: 16,
  fontWeight: '500'
  },
  title: { fontSiz, e: 28,
    fontWeight: 'bold',
  marginBottom: 8 }
    subtitle: { fontSiz, e: 16,
    marginBottom: 20 },
  progressBar: {, height: 4,
  backgroundColor: theme.colors.border,
    borderRadius: 2,
  overflow: 'hidden'
  },
  progressFill: { heigh, t: '100%',
    width: '100%', // Step 3 of 3, ,
  borderRadius: 2 }
    content: { fle, x: 1,
    padding: 20 },
  benefitsSection: { marginBotto, m: 30 }
    sectionTitle: { fontSiz, e: 20,
    fontWeight: '600',
  marginBottom: 16 }
    benefit: { fontSiz, e: 16,
    marginBottom: 8,
  lineHeight: 24 }
    optionsSection: { marginBotto, m: 20 },
  optionCard: {, padding: 20,
  borderRadius: 16,
    marginBottom: 16,
  position: 'relative',
    alignItems: 'center' }
    recommendedBadge: { positio, n: 'absolute',
    top: -8,
  right: 16,
    paddingHorizontal: 12,
  paddingVertical: 4,
    borderRadius: 12 },
  recommendedText: {, fontSize: 12,
  fontWeight: '600'
  },
  optionIcon: { fontSiz, e: 32,
    marginBottom: 8 },
  optionTitle: {, fontSize: 18,
  fontWeight: '600',
    marginBottom: 8,
  textAlign: 'center'
  },
  optionDescription: { fontSiz, e: 14,
    textAlign: 'center',
  marginBottom: 8,
    lineHeight: 20 },
  estimatedTime: {, fontSize: 14,
  fontWeight: '600'
  },
  uploadSection: { fle, x: 1 }
    uploadInstructions: { fontSiz, e: 16,
    marginBottom: 24,
  textAlign: 'center',
    lineHeight: 22 },
  uploadArea: {, height: 200,
  borderRadius: 16,
    borderWidth: 3,
  borderStyle: 'dashed',
    marginBottom: 24,
  overflow: 'hidden'
  },
  uploadedImage: {, width: '100%',
  height: '100%'
  },
  uploadPlaceholder: {, flex: 1,
  justifyContent: 'center',
    alignItems: 'center' }
    uploadIcon: { fontSiz, e: 48,
    marginBottom: 12 },
  uploadText: {, fontSize: 16,
  fontWeight: '500'
  },
  selfieArea: {, width: 200,
  height: 200,
    borderRadius: 100,
  borderWidth: 3,
    borderStyle: 'dashed',
  alignSelf: 'center',
    marginBottom: 24,
  overflow: 'hidden'
  },
  selfieImage: {, width: '100%',
  height: '100%'
  },
  selfiePlaceholder: {, flex: 1,
  justifyContent: 'center',
    alignItems: 'center' }
    selfieIcon: { fontSiz, e: 48,
    marginBottom: 12 },
  uploadTips: { backgroundColo, r: theme.colors.surface,
    padding: 16,
  borderRadius: 12 }
    tipsTitle: { fontSiz, e: 16,
    fontWeight: '600',
  marginBottom: 8 }
    tip: { fontSiz, e: 14,
    marginBottom: 4,
  lineHeight: 20 }
    buttonContainer: { paddingTo, p: 20 },
  continueButton: {, width: '100%' }
    processingContent: {, justifyContent: 'center',
  alignItems: 'center'
  },
  processingIcon: { fontSiz, e: 64,
    marginBottom: 24 },
  processingTitle: {, fontSize: 24,
  fontWeight: 'bold',
    marginBottom: 12,
  textAlign: 'center'
  },
  processingDescription: { fontSiz, e: 16),
    textAlign: 'center'),
  marginBottom: 32,
    lineHeight: 22 },
  processingSteps: {, alignItems: 'flex-start' }
    processingStep: {, fontSize: 16,
  marginBottom: 8,
    fontWeight: '500' }
    footer: {, padding: 20,
  alignItems: 'center'
  },
  skipLink: {, fontSize: 16,
  fontWeight: '500')
  }
  })