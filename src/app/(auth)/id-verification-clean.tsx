import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Image,
  Platform,
  ActionSheetIOS,
  ScrollView,
  KeyboardAvoidingView,
  Dimensions
} from 'react-native';
import {
  useRouter,
  useLocalSearchParams
} from 'expo-router';
import {
  SafeAreaView
} from 'react-native-safe-area-context';
import {
  useTheme
} from '@design-system';
import {
  Button
} from '@design-system';
import {
  logger
} from '@utils/logger';
import * as ImagePicker from 'expo-image-picker';
import * as Haptics from 'expo-haptics';
import {
  useSimpleAuth
} from '@context/SimpleAuthContext';
import {
  simplifiedAuthConfig
} from '@config/simplifiedAuthConfig';

const { width, height } = Dimensions.get('window');

// Types for Step 3
interface Step3Data {
  idDocument: string | null;
  selfiePhoto: string | null;
  verificationType: 'automatic' | 'manual';
  jumioSessionId?: string;
}

interface VerificationOption {
  id: string;
  title: string;
  description: string;
  icon: string;
  recommended: boolean;
  estimatedTime: string;
}

const VERIFICATION_OPTIONS: VerificationOption[] = [
  {
    id: 'automatic',
    title: 'Instant Verification',
    description: 'AI-powered ID verification in under 60 seconds',
    icon: '⚡',
    recommended: true,
    estimatedTime: '< 1 minute'
  },
  {
    id: 'manual',
    title: 'Manual Review',
    description: 'Human review of your documents (takes longer)',
    icon: '👤',
    recommended: false,
    estimatedTime: '24-48 hours'
  }
];

const VERIFICATION_BENEFITS = [
  '✅ Verified badge on your profile',
  '🎯 Priority in search results',
  '🔒 Access to verified-only features',
  '💬 Increased trust from other users',
  '⭐ Higher match success rate'
];

type DocumentType = 'drivers_license' | 'passport' | 'national_id';

interface DocumentImages {
  front: string | null;
  back: string | null;
}

export default function IDVerificationScreen() {
  const theme = useTheme();
  const router = useRouter();
  const params = useLocalSearchParams();
  const styles = createStyles(theme);
  const { completeStep3, isLoading, getCostSavings } = useSimpleAuth();
  const [selectedDocumentType, setSelectedDocumentType] = useState<DocumentType>('drivers_license');
  const [documentImages, setDocumentImages] = useState<DocumentImages>({
    front: null,
    back: null
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form state
  const [formData, setFormData] = useState<Step3Data>({
    idDocument: null,
    selfiePhoto: null,
    verificationType: 'automatic'
  });
  const [loading, setLoading] = useState(false);
  const [uploadingDocument, setUploadingDocument] = useState(false);
  const [uploadingSelfie, setUploadingSelfie] = useState(false);
  const [currentStep, setCurrentStep] = useState<'method' | 'documents' | 'selfie' | 'processing'>(
    'method'
  );

  const documentTypes = [
    {
      type: 'drivers_license' as DocumentType,
      title: "Driver's License",
      description: "Government-issued driver's license",
      icon: '🪪',
      requiresBack: true
    },
    {
      type: 'passport' as DocumentType,
      title: 'Passport',
      description: 'Valid government passport',
      icon: '📘',
      requiresBack: false
    },
    {
      type: 'national_id' as DocumentType,
      title: 'National ID',
      description: 'Government-issued ID card',
      icon: '🆔',
      requiresBack: true
    }
  ];

  const currentDocumentType = documentTypes.find(doc => doc.type === selectedDocumentType);

  const captureDocument = async (side: 'front' | 'back') => {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Camera Permission Required', 'We need camera access to take photos of your ID.');
        return null;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [3, 2],
        quality: 0.9
      });

      if (!result.canceled && result.assets[0]) {
        setDocumentImages(prev => ({
          ...prev,
          [side]: result.assets[0].uri
        }));
      }
    } catch (error) {
      logger.error('Error capturing document:', error);
      Alert.alert('Error', 'Failed to capture document. Please try again.');
    }
  };

  const pickFromGallery = async (side: 'front' | 'back') => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'We need gallery access to select document photos.');
        return null;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [3, 2],
        quality: 0.9
      });

      if (!result.canceled && result.assets[0]) {
        setDocumentImages(prev => ({
          ...prev,
          [side]: result.assets[0].uri
        }));
      }
    } catch (error) {
      logger.error('Error picking document from gallery:', error);
      Alert.alert('Error', 'Failed to select document. Please try again.');
    }
  };

  const showImagePickerOptions = (side: 'front' | 'back') => {
    Alert.alert(
      'Select Document Photo',
      `Choose how to add the ${side} of your ${currentDocumentType?.title}`,
      [
        { text: 'Take Photo', onPress: () => captureDocument(side) },
        { text: 'Choose from Gallery', onPress: () => pickFromGallery(side) },
        { text: 'Cancel', style: 'cancel' }
      ]
    );
  };

  const validateSubmission = () => {
    if (!documentImages.front) {
      Alert.alert('Front Photo Required', 'Please take a photo of the front of your ID.');
      return false;
    }

    if (currentDocumentType?.requiresBack && !documentImages.back) {
      Alert.alert(
        'Back Photo Required',
        `Please take a photo of the back of your ${currentDocumentType.title}.`
      );
      return false;
    }

    return true;
  };

  const handleSubmitVerification = async () => {
    if (!validateSubmission()) return null;

    Alert.alert(
      'Submit for Verification?',
      'Your documents will be reviewed by our team within 24 hours. This saves you $57+ compared to automated services!',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Submit',
          onPress: async () => {
            try {
              setIsSubmitting(true);
              const result = await completeStep3({
                document_type: selectedDocumentType,
                document_front_url: documentImages.front!,
                document_back_url: documentImages.back || undefined
              });

              if (result.success) {
                logger.info('ID verification submitted successfully');
                Alert.alert(
                  'Verification Submitted! 🎉',
                  `Your ID has been submitted for manual review. You saved $${simplifiedAuthConfig.estimatedCostSavingsPerUser}+ with our free verification system!`,
                  [
                    {
                      text: 'Continue to App',
                      onPress: () => router.replace('/(tabs)')
                    }
                  ]
                );
              } else {
                Alert.alert('Submission Failed', result.error || 'Please try again.');
              }
            } catch (error) {
              logger.error('ID verification submission error:', error);
              Alert.alert('Error', 'Failed to submit verification. Please try again.');
            } finally {
              setIsSubmitting(false);
            }
          }
        }
      ]
    );
  };

  const handleSkipVerification = () => {
    Alert.alert(
      'Skip ID Verification?',
      'You can complete verification later, but some features will be limited until your identity is verified.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Skip for Now',
          style: 'destructive',
          onPress: () => router.replace('/(tabs)')
        }
      ]
    );
  };

  // Render verification method selection
  const renderMethodSelection = () => (
    <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
      <View style={styles.benefitsSection}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Why Verify Your Identity?
        </Text>
        {VERIFICATION_BENEFITS.map((benefit, index) => (
          <Text key={index} style={[styles.benefit, { color: theme.colors.textSecondary }]}>
            {benefit}
          </Text>
        ))}
      </View>

      <View style={styles.optionsSection}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Choose Verification Method
        </Text>
        {VERIFICATION_OPTIONS.map(option => (
          <TouchableOpacity
            key={option.id}
            style={[
              styles.optionCard,
              {
                backgroundColor: theme.colors.surface,
                borderColor: option.recommended ? theme.colors.primary : theme.colors.border,
                borderWidth: option.recommended ? 2 : 1
              }
            ]}
            onPress={() => setFormData(prev => ({ ...prev, verificationType: option.id as any }))}
          >
            {option.recommended && (
              <View style={[styles.recommendedBadge, { backgroundColor: theme.colors.primary }]}>
                <Text style={[styles.recommendedText, { color: 'white' }]}>Recommended</Text>
              </View>
            )}
            <Text style={[styles.optionIcon, { color: theme.colors.text }]}>{option.icon}</Text>
            <Text style={[styles.optionTitle, { color: theme.colors.text }]}>{option.title}</Text>
            <Text style={[styles.optionDescription, { color: theme.colors.textSecondary }]}>
              {option.description}
            </Text>
            <Text style={[styles.estimatedTime, { color: theme.colors.primary }]}>
              {option.estimatedTime}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </ScrollView>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {renderMethodSelection()}
    </SafeAreaView>
  );
}

const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1
    },
    content: {
      flex: 1,
      padding: 20
    },
    benefitsSection: {
      marginBottom: 30
    },
    sectionTitle: {
      fontSize: 20,
      fontWeight: '600',
      marginBottom: 16
    },
    benefit: {
      fontSize: 16,
      marginBottom: 8,
      lineHeight: 24
    },
    optionsSection: {
      marginBottom: 20
    },
    optionCard: {
      padding: 20,
      borderRadius: 16,
      marginBottom: 16,
      position: 'relative',
      alignItems: 'center'
    },
    recommendedBadge: {
      position: 'absolute',
      top: -8,
      right: 16,
      paddingHorizontal: 12,
      paddingVertical: 4,
      borderRadius: 12
    },
    recommendedText: {
      fontSize: 12,
      fontWeight: '600'
    },
    optionIcon: {
      fontSize: 32,
      marginBottom: 12
    },
    optionTitle: {
      fontSize: 18,
      fontWeight: '600',
      marginBottom: 8,
      textAlign: 'center'
    },
    optionDescription: {
      fontSize: 14,
      textAlign: 'center',
      marginBottom: 8
    },
    estimatedTime: {
      fontSize: 14,
      fontWeight: '500'
    }
  });
