import React, { useState, useEffect } from 'react';
import {
  useRouter,
  useLocalSearchParams
} from 'expo-router';
import {
  Shield,
  Mail,
  Lock,
  LogIn,
  AlertCircle
} from 'lucide-react-native';
import {
  Ionicons
} from '@expo/vector-icons';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Alert,
  ImageBackground,
  StatusBar,
  SafeAreaView
} from 'react-native';
import {
  LinearGradient
} from 'expo-linear-gradient';
import SimpleInput from '@components/ui/form/SimpleInput';
import {
  useAuthCompat
} from '@hooks/useAuthCompat';
import {
  validateEmail
} from '@utils/validation';
import {
  useTheme,
  colorWithOpacity,
  type Theme
} from '@design-system';

// Theme-dependent styles function
const createStyles = (theme: Theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background
    },
    scrollContainer: {
      flexGrow: 1,
      paddingHorizontal: theme.spacing.lg,
      paddingTop: theme.spacing.xl,
      paddingBottom: theme.spacing.lg
    },
    logoContainer: {
      alignItems: 'center',
      marginBottom: theme.spacing.xl,
      marginTop: theme.spacing.lg
    },
    logo: {
      width: 120,
      height: 120,
      borderRadius: 60,
      marginBottom: theme.spacing.md
    },
    logoText: {
      fontSize: theme.typography.fontSize.xl,
      fontWeight: '800' as any,
      color: theme.colors.text,
      marginBottom: theme.spacing.xs
    },
    tagline: {
      fontSize: theme.typography.fontSize.md,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      lineHeight: 20
    },
    formContainer: {
      backgroundColor: theme.colors.surface,
      borderRadius: theme.borderRadius.lg,
      padding: theme.spacing.lg,
      borderWidth: 1,
      borderColor: theme.colors.border,
      ...theme.shadows.medium
    },
    form: {
      gap: theme.spacing.md
    },
    input: {
      backgroundColor: theme.colors.background,
      borderColor: theme.colors.border,
      color: theme.colors.text,
      borderRadius: theme.borderRadius.md,
      borderWidth: 1,
      fontSize: theme.typography.fontSize.md
    },
    inputLabel: {
      color: theme.colors.text,
      fontSize: theme.typography.fontSize.sm,
      fontWeight: '600' as any,
      marginBottom: theme.spacing.xs
    },
    forgotPassword: {
      alignSelf: 'flex-end',
      marginTop: -theme.spacing.xs,
      marginBottom: theme.spacing.md
    },
    forgotPasswordText: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.primary,
      fontWeight: '500' as any
    },
    errorContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.error + '15',
      padding: theme.spacing.sm,
      borderRadius: theme.borderRadius.md,
      marginBottom: theme.spacing.md,
      borderWidth: 1,
      borderColor: theme.colors.error + '30'
    },
    errorIcon: {
      marginRight: theme.spacing.xs
    },
    errorText: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.error,
      flex: 1
    },
    messageContainer: {
      backgroundColor: theme.colors.info + '15',
      padding: theme.spacing.sm,
      borderRadius: theme.borderRadius.md,
      marginBottom: theme.spacing.md,
      borderWidth: 1,
      borderColor: theme.colors.info + '30'
    },
    messageText: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.info
    },
    loginButton: {
      backgroundColor: theme.colors.primary,
      borderRadius: theme.borderRadius.lg,
      paddingVertical: theme.spacing.md,
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: theme.spacing.xs,
      ...theme.shadows.small
    },
    loginButtonText: {
      color: theme.colors.textInverse,
      fontSize: theme.typography.fontSize.lg,
      fontWeight: '700' as any
    },
    divider: {
      flexDirection: 'row',
      alignItems: 'center',
      marginVertical: theme.spacing.md
    },
    dividerLine: {
      flex: 1,
      height: 1,
      backgroundColor: theme.colors.border
    },
    dividerText: {
      paddingHorizontal: theme.spacing.md,
      color: theme.colors.textSecondary,
      fontWeight: '500' as any
    },
    linkButton: {
      alignItems: 'center'
    },
    linkText: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.textSecondary
    },
    link: {
      color: theme.colors.primary,
      fontWeight: '600' as any
    },
    backgroundGradient: {
      flex: 1
    },
    safeContainer: {
      flex: 1
    },
    keyboardAvoidView: {
      flex: 1
    },
    content: {
      flexGrow: 1,
      padding: 24
    },
    header: {
      alignItems: 'center',
      marginBottom: 32
    },
    title: {
      fontSize: 28,
      fontWeight: '700',
      color: '#FFFFFF',
      marginBottom: 8
    },
    subtitle: {
      fontSize: 16,
      color: '#CBD5E1',
      textAlign: 'center'
    }
  } as any);
export default function LoginScreen() {
  const theme = useTheme();
  const styles = createStyles(theme);
  const router = useRouter();
  const { from, email: prefilledEmail } = useLocalSearchParams();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [message, setMessage] = useState<string | null>(null);
  const { authState, signIn } = useAuthCompat();

  // Field validation states
  const [emailError, setEmailError] = useState<string | null>(null);
  const [passwordError, setPasswordError] = useState<string | null>(null);
  const [formTouched, setFormTouched] = useState(false);
  // Show welcome message if redirected from registration and pre-fill email
  useEffect(() => {
    if (from === 'register') {
      setMessage('This email is already registered. Please log in instead.');
      setTimeout(() => setMessage(null), 5000);
    }

    // Pre-fill email if provided
    if (prefilledEmail && typeof prefilledEmail === 'string') {
      setEmail(prefilledEmail);
    }
  }, [from, prefilledEmail]);

  // Reset field errors when input changes
  const handleEmailChange = (text: string) => {
    setEmail(text);
    setEmailError(null);
    setError(null);
    setFormTouched(true);
  };

  const handlePasswordChange = (text: string) => {
    setPassword(text);
    setPasswordError(null);
    setError(null);
    setFormTouched(true);
  };
  // Validate individual fields
  const validateFields = () => {
    let isValid = true;

    // Reset previous errors
    setEmailError(null);
    setPasswordError(null);
    setError(null);

    // Validate email
    if (!email.trim()) {
      setEmailError('Email is required');
      isValid = false;
    } else if (!validateEmail(email)) {
      setEmailError('Please enter a valid email address');
      isValid = false;
    }

    // Validate password
    if (!password) {
      setPasswordError('Password is required');
      isValid = false;
    } else if (password.length < 6) {
      setPasswordError('Password must be at least 6 characters');
      isValid = false;
    }

    return isValid;
  };
  const handleLogin = async () => {
    // Validate fields first
    if (!validateFields()) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Use the auth context to sign in
      const errorMsg = await signIn({ email, password });

      if (errorMsg) {
        setError(errorMsg);

        // For certain errors, provide additional guidance
        if (errorMsg.includes('incorrect')) {
          setTimeout(() => {
            Alert.alert('Login Failed', 'Would you like to reset your password?', [
              { text: 'Cancel', style: 'cancel' },
              {
                text: 'Reset Password',
                onPress: () => router.push('/(auth)/forgot-password' as any)
              }
            ]);
          }, 500);
        }
      } else {
        // Login succeeded - wait for auth state to propagate before navigating
        console.log('Login successful, waiting for auth state to propagate');

        // Give more time for auth state to fully propagate and Root Layout to mount
        setTimeout(() => {
          try {
            console.log('Attempting navigation with router.replace');
            router.replace('/(tabs)');
          } catch (e) {
            console.error('Error during navigation:', e);
            // If navigation fails, it's likely because the app isn't fully mounted yet
            // The RouteCheck component will handle the redirect once it's ready
            console.log('Navigation failed, RouteCheck will handle redirect when ready');
          }
        }, 1500); // Increased delay to ensure Root Layout is mounted
      }
    } catch (err) {
      console.error('Login error:', err);
      setError(err instanceof Error ? err.message : 'Failed to sign in');
    } finally {
      setLoading(false);
    }
  };
  return (
    <View style={styles.container}>
      <StatusBar barStyle='light-content' />
      <LinearGradient
        colors={['#1e293b', '#334155', '#475569']}
        style={styles.backgroundGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <SafeAreaView style={styles.safeContainer}>
          <KeyboardAvoidingView
            style={styles.keyboardAvoidView}
            behavior={Platform.select({
              ios: 'padding',
              android: 'height',
              default: undefined
            })}
            keyboardVerticalOffset={Platform.select({
              ios: 0,
              android: 20,
              default: 0
            })}
          >
            <ScrollView contentContainerStyle={styles.content}>
              <View style={styles.header}>
                <View style={styles.logoContainer}>
                  <Shield size={32} color='#FFFFFF' />
                </View>
                <Text style={styles.title}>Welcome Back</Text>
                <Text style={styles.subtitle}>Sign in to continue to WeRoomies</Text>
              </View>
              {message && (
                <View style={styles.messageContainer}>
                  <Text style={styles.messageText}>{message}</Text>
                </View>
              )}

              <View style={styles.formContainer}>
                <View style={styles.form}>
                  <View>
                    <Text style={styles.inputLabel}>Email</Text>
                    <SimpleInput
                      value={email}
                      onChangeText={handleEmailChange}
                      placeholder='Enter your email'
                      keyboardType='email-address'
                      autoCapitalize='none'
                      autoComplete='email'
                      error={emailError || undefined}
                      style={styles.input}
                    />
                  </View>

                  <View>
                    <Text style={styles.inputLabel}>Password</Text>
                    <SimpleInput
                      value={password}
                      onChangeText={handlePasswordChange}
                      placeholder='Enter your password'
                      secureTextEntry
                      error={passwordError || undefined}
                      style={styles.input}
                    />
                  </View>

                  <TouchableOpacity
                    style={styles.forgotPassword}
                    onPress={() => router.push('/(auth)/forgot-password' as any)}
                  >
                    <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
                  </TouchableOpacity>
                  {error && (
                    <View style={styles.errorContainer}>
                      <AlertCircle size={16} color='#FF6B6B' style={styles.errorIcon} />
                      <Text style={styles.errorText}>{error}</Text>
                    </View>
                  )}

                  <TouchableOpacity
                    style={styles.loginButton}
                    onPress={handleLogin}
                    disabled={
                      loading || authState.isLoading || (!formTouched && !(email && password))
                    }
                  >
                    {loading || authState.isLoading ? (
                      <ActivityIndicator color='#FFFFFF' size='small' />
                    ) : (
                      <>
                        <Text style={styles.loginButtonText}>Sign In</Text>
                        <Ionicons
                          name='log-in-outline'
                          size={20}
                          color='#FFFFFF'
                          style={{ marginLeft: 8 }}
                        />
                      </>
                    )}
                  </TouchableOpacity>
                </View>
                <View style={styles.divider}>
                  <View style={styles.dividerLine} />
                  <Text style={styles.dividerText}>OR</Text>
                  <View style={styles.dividerLine} />
                </View>

                <TouchableOpacity
                  style={styles.linkButton}
                  onPress={() => router.push('/(auth)/register' as any)}
                >
                  <Text style={styles.linkText}>
                    Don't have an account? <Text style={styles.link}>Sign Up</Text>
                  </Text>
                </TouchableOpacity>
              </View>
            </ScrollView>
          </KeyboardAvoidingView>
        </SafeAreaView>
      </LinearGradient>
    </View>
  );
}