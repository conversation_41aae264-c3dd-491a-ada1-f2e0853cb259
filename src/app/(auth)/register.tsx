import React, { useState, useRef, useEffect, useMemo, useCallback, useReducer } from 'react';
  import {
  useRouter
} from 'expo-router';
import {
  Ionicons
} from '@expo/vector-icons',
  import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Animated,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  Alert,
  SafeAreaView,
  TextInput,
  Keyboard,
  Easing;
} from 'react-native';
import {
  Stack
} from 'expo-router';
  import {
  FloatingLabelInput
} from '../../components/ui/form/FloatingLabelInput';
import {
  Button
} from '@design-system';
  import {
  useTheme
} from '@design-system';
import type { UserRole } from '../../types/auth';
  import {
  supabase
} from '../../utils/supabaseUtils';
import {
  validateEmailWithMessage,
  validateUsernameWithMessage,
  validatePasswordForRegistration,
  validateBusinessName,
  validateBusinessDescription,
  validateContactPhone,
  validateBusinessAddress
} from '../../utils/validation';
  import {
  authService
} from '../../services/standardized';
  import {
  logger
} from '../../utils/logger';
  import {
  useAuthAdapter
} from '../../context/AuthContextAdapter';
  import * as Haptics from 'expo-haptics';
  import {
  LinearGradient
} from 'expo-linear-gradient';
  import {
  PasswordStrengthIndicator
} from '../../components/auth/PasswordStrengthIndicator';
  import {
  AnimatedStepIndicator
} from '../../components/auth/AnimatedStepIndicator' // Role configuration with colors,
  const ROLES = [
  {
  id: 'roommate_seeker' as UserRole,
    title: 'Looking for Roommates',
  description: 'Find compatible roommates to share living space',
    icon: 'person' as keyof typeof Ionicons.glyphMap, ,
  gradient: ['#10B981', '#6EE7B7'] as const }
  {
  id: 'property_owner' as UserRole,
    title: 'Property Owner',
  description: 'List your property and find reliable tenants',
    icon: 'home' as keyof typeof Ionicons.glyphMap,
  gradient: ['#8B5CF6', '#C4B5FD'] as const }
  {
  id: 'service_provider' as UserRole,
    title: 'Service Provider',
  description: 'Offer cleaning, maintenance, or other services',
  icon: 'briefcase' as keyof typeof Ionicons.glyphMap,
    gradient: ['#3B82F6', '#93C5FD'] as const }
] // Service categories for service providers,
  const SERVICE_CATEGORIES = ['Cleaning', ,
  'Repairs'
  'Moving',
  'Pest Control'
  'Landscaping',
  'Design'
  'Security',
  'Management'],;
  const { width  } = Dimensions.get('window');
// --- State Management ---,
  interface FormState {
  email: string,
    username: string,
  password: string,
    passwordConfirm: string,
  selectedRole: UserRole | null,
    businessName: string,
  businessDescription: string,
    contactPhone: string,
  businessAddress: string,
    selectedCategories: string[] }
type FormAction = , ,
  | { type: 'UPDATE_FIELD', field: keyof FormState, value: string | string[] },
  | { type: 'SET_ROLE', role: UserRole | null },
  | { type: 'TOGGLE_CATEGORY', category: string },
  const initialState: FormState = {, email: '',
  username: '',
    password: '',
  passwordConfirm: '',
    selectedRole: null,
  businessName: '',
    businessDescription: '',
  contactPhone: '',
    businessAddress: '',
  selectedCategories: [] }
function formReducer(state: FormState, action: FormAction): FormState {
  switch (action.type) {
    case 'UPDATE_FIELD':  ,
  return { ...state; [action.field]: action.value },
  case 'SET_ROLE':  
      return { ...state,  selectedRole: action.role },
  case 'TOGGLE_CATEGORY':  
      const categories = state.selectedCategories.includes(action.category),
  ? state.selectedCategories.filter(c => c !== action.category)
             : [...state.selectedCategories action.category],
  return { ...state selectedCategories: categories }
    default: return state
  }
},
  export default function EnhancedRegisterScreen() {
  const router = useRouter(),
  const theme = useTheme()
  const scrollViewRef = useRef<ScrollView>(null),
  const verticalScrollRef = useRef<ScrollView>(null);
  const scrollX = useRef(new Animated.Value(0)).current,
  const { authState, signUp  } = useAuthAdapter(),
  // Input refs for auto-focus flow,
  const emailRef = useRef<TextInput>(null),
  const usernameRef = useRef<TextInput>(null)
  const passwordRef = useRef<TextInput>(null),
  const passwordConfirmRef = useRef<TextInput>(null);
  // Keyboard state,
  const [keyboardVisible, setKeyboardVisible] = useState(false),
  const [keyboardHeight, setKeyboardHeight] = useState(0),
  // Form state,
  const [formState, dispatch] = useReducer(formReducer, initialState),
  const {;
    email,
  username,
    password,
  passwordConfirm,
    selectedRole,
  businessName,
    businessDescription,
  contactPhone,
    businessAddress,
  selectedCategories;
  } = formState,
  const [step, setStep] = useState(0),
  const [loading, setLoading] = useState(false),
  const [error, setError] = useState<string | null>(null),
  // Field validation state,
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({}),
  // Safe theme with fallbacks,
  const safeTheme = useMemo(
  () => ({
      colors: {
      primary: theme.colors?.primary || '#3B82F6',
  background     : theme.colors?.background || '#FFFFFF'
  text: theme.colors?.text || '#1E293B',
  textSecondary : theme.colors?.textSecondary || '#64748B'
  textInverse : theme.colors?.textInverse || '#FFFFFF',
  surface : theme.colors?.surface || '#F8FAFC'
  border : theme.colors?.border || '#E2E8F0',
  error : theme.colors?.error || '#EF4444'
  success : theme.colors?.success || '#10B981' }
  }),
  [theme],
  )
  // Steps configuration,
  const steps = [
    { title : 'Basic Info', icon: 'mail' as keyof typeof Ionicons.glyphMap },
  { title: 'Security', icon: 'lock-closed' as keyof typeof Ionicons.glyphMap },
  { title: 'Role Selection', icon: 'person' as keyof typeof Ionicons.glyphMap },
  ...(selectedRole === 'service_provider', ,
  ? [{ title     : 'Business Info' icon: 'briefcase' as keyof typeof Ionicons.glyphMap }],
  : [])
   ],
  // Keyboard handling, ,
  useEffect(() => {
    const showKeyboard = (event: any) => {
  setKeyboardVisible(true)
      setKeyboardHeight(event.endCoordinates.height) }
    const hideKeyboard = () => {
  setKeyboardVisible(false)
      setKeyboardHeight(0) }
    const showListener = Keyboard.addListener(Platform.OS === 'ios' ? 'keyboardWillShow'    : 'keyboardDidShow',
  showKeyboard)
    ),
  const hideListener = Keyboard.addListener(Platform.OS === 'ios' ? 'keyboardWillHide'  : 'keyboardDidHide'
      hideKeyboard),
  )
    return () => {
  showListener.remove()
      hideListener.remove() }
  }, []);
  // Validation functions
  const validateField = useCallback(
  (field: string, value: string): string => { switch (field) {
  case 'email': 
          const emailResult = validateEmailWithMessage(value),
  return emailResult.isValid ? ''       : emailResult.message
        case 'username':  ,
  const usernameResult = validateUsernameWithMessage(value)
          return usernameResult.isValid ? ''    : usernameResult.message,
  case 'password':  
          return validatePasswordForRegistration(value) || '',
  case 'passwordConfirm':  ;
          return value !== password ? 'Passwords do not match'     : '',
  case 'businessName':  
          return selectedRole === 'service_provider' ? validateBusinessName(value) || ''   : '',
  case 'businessDescription':  
          return selectedRole === 'service_provider',
  ? validateBusinessDescription(value) || ''
               : '',
  case 'contactPhone':  
          return selectedRole === 'service_provider' ? validateContactPhone(value) || ''   : '',
  case 'businessAddress':  
          return selectedRole === 'service_provider' ? validateBusinessAddress(value) || ''   : '',
  default: 
          return '' }
  }
    [password,  selectedRole],
  )
  // Field change handler with validation,
  const handleFieldChange = useCallback(
    (field: keyof FormState, value: string) => {
  dispatch({  type: 'UPDATE_FIELD', field, value  }),
  // Real-time validation,
      const error = validateField(field, value),
  setFieldErrors(prev => ({  ...prev, [field]: error  }))
  }, ,
  [validateField],
  )
  // Step validation,
  const isStepValid = useMemo(() => {
    switch (step) {
  case 0: // Basic Info,
        return !fieldErrors.email && !fieldErrors.username && email && username,
  case 1: // Security
        return (
  !fieldErrors.password &&;
          !fieldErrors.passwordConfirm &&,
  password &&;
          passwordConfirm &&, ,
  password === passwordConfirm, ,
  )
      case 2: // Role Selection,
  return selectedRole !== null,
  case 3: // Business Info (service providers only),
  if (selectedRole !== 'service_provider') return true,
  return (
  !fieldErrors.businessName &&;
  !fieldErrors.businessDescription &&,
  !fieldErrors.contactPhone &&;
  !fieldErrors.businessAddress &&,
  businessName &&;
  businessDescription &&,
  contactPhone &&;
  businessAddress &&, ,
  selectedCategories.length > 0, ,
  )
      default: return false }
  }, [step,
  fieldErrors,
    email,
  username,
    password,
  passwordConfirm,
    selectedRole,
  businessName,
    businessDescription,
  contactPhone,
    businessAddress,
  selectedCategories;
  ]);
  // Role selection handler,
  const handleRoleSelect = (role: UserRole) => {
  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium)
    dispatch({  type: 'SET_ROLE', role  })
  };
  // Category toggle handler,
  const toggleCategory = (category: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light),
  dispatch({  type: 'TOGGLE_CATEGORY', category  })
  };
  // Navigation handlers,
  const nextStep = useCallback(() => { if (isStepValid) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium),
  const nextStepIndex = step + 1,
      setStep(nextStepIndex),
  // Animate to next step,
      Animated.timing(scrollX, {
  toValue: nextStepIndex * width),
    duration: 300),
  easing: Easing.out(Easing.cubic),
    useNativeDriver: false }).start(),
  // Auto-focus next input,
  setTimeout(() => {
  if (nextStepIndex === 1 && passwordRef.current) {
  passwordRef.current.focus() }
  } 350)
  }
  }, [isStepValid, step, scrollX, width]);
  const prevStep = useCallback(() => { if (step > 0) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light),
  const prevStepIndex = step - 1,
      setStep(prevStepIndex),
  // Animate to previous step,
      Animated.timing(scrollX, {
  toValue: prevStepIndex * width),
    duration: 300),
  easing: Easing.out(Easing.cubic),
    useNativeDriver: false }).start()
  }
  }, [step, scrollX, width]);
  // Registration handler,
  const handleRegister = async () => { try {
  setLoading(true)
      setError(null),
  // Haptic feedback for submission,
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success),
  const signUpData = {
        email: email.trim(),
  password,
        username: username.trim(),
    role: selectedRole!,
  ...(selectedRole === 'service_provider' && {
  businessName: businessName.trim(),
    businessDescription: businessDescription.trim(),
  contactPhone: contactPhone.trim(),
    businessAddress: businessAddress.trim(),
  serviceCategories: selectedCategories });
      },
  const error = await signUp(signUpData)
      if (!error) {
  // Success animation,
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success),
  // Show success message with delayed navigation,
        Alert.alert('Registration Complete! 🎉',
  'Welcome to WeRoomies! Your account has been created successfully.');
  [{
  text: 'Get Started'),
    onPress: () => {
  // Navigate to unified onboarding based on role, ,
  if (selectedRole === 'service_provider') {
                  router.replace({
  pathname: '/unified-onboarding'),
    params: { rol, e: 'service_provider', step: 'profile_creation'  })
  })
                } else if (selectedRole === 'property_owner') {
  router.replace({ 
                    pathname: '/unified-onboarding'),
    params: { rol, e: 'property_owner', step: 'profile_creation'  })
  })
                } else {
  router.replace({ 
                    pathname: '/unified-onboarding'),
    params: { rol, e: 'roommate_seeker', step: 'profile_creation'  })
  })
                }
  }
            }],
  )
      } else {
  setError(error)
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error) }
    } catch (err) {
  logger.error('Registration error:', err),
  setError('An unexpected error occurred. Please try again.')
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error) } finally {
      setLoading(false) }
  },
  // Auto-focus flow,
  const handleEmailSubmit = () => {
  if (usernameRef.current) {
      usernameRef.current.focus() }
  },
  const handleUsernameSubmit = () => {
    if (isStepValid) {
  nextStep()
    }
  }
  const handlePasswordSubmit = () => {
  if (passwordConfirmRef.current) {
      passwordConfirmRef.current.focus() }
  },
  const handlePasswordConfirmSubmit = () => {
    if (isStepValid) {
  nextStep()
    }
  }
  const styles = createStyles(safeTheme, keyboardHeight),
  return (
    <SafeAreaView style={styles.container}>,
  <Stack.Screen, ,
  options={   {
  title: 'Create Account',
    headerShown: true,
  headerBackVisible: step > 0,
    headerLeft:  ,
  step > 0, ? () => (
                  <TouchableOpacity onPress={prevStep      } style={styles.headerButton}>,
  <Ionicons name='arrow-back' size={24} color={{safeTheme.theme.colors.text} /}>
                  </TouchableOpacity>,
  )
                  : undefined
  }}
      />,
  <KeyboardAvoidingView
        style={styles.keyboardAvoidingView},
  behavior={   Platform.OS === 'ios' ? 'padding'  : 'height'      }
        keyboardVerticalOffset={   Platform.OS === 'ios' ? 100 : 0      },
  >
        <ScrollView,
  ref={verticalScrollRef}
          style={styles.verticalScrollView},
  contentContainerStyle={styles.verticalScrollContent}
          showsVerticalScrollIndicator={false},
  keyboardShouldPersistTaps='handled'
        >,
  {/* Step Indicator */}
          <View style={styles.stepIndicatorContainer}>,
  <AnimatedStepIndicator totalSteps={steps.length} currentStep={{step} /}>
          </View>,
  {/* Horizontal Scrollable Steps */}
          <Animated.ScrollView,
  ref={scrollViewRef}
            horizontal,
  pagingEnabled,
            showsHorizontalScrollIndicator= {false},
  scrollEnabled={false}
            style={styles.horizontalScrollView},
  contentOffset={   x: step * widthy: 0       },
  >
            {/* Step 0: Basic Information */}
  <View style={[styles.stepContainer{ width}]}>,
  <View style={styles.stepContent}>
                <View style={styles.stepHeader}>,
  <Ionicons name='mail' size={32} color={{safeTheme.theme.colors.primary} /}>
                  <Text style={styles.stepTitle}>Basic Information</Text>,
  <Text style={styles.stepDescription}>
                    Let's start with your email and username,
  </Text>
                </View>,
  <View style= {styles.inputContainer}>
                  <FloatingLabelInput,
  ref={emailRef}
                    label='Email Address',
  value={email}
                    onChangeText={value => handleFieldChange('email'value)},
  onSubmitEditing={handleEmailSubmit}
                    return KeyType='next',
  keyboardType= 'email-address';
                    autoCapitalize= 'none',
  autoComplete= 'email';
                    error= {fieldErrors.email || undefined},
  />
                  <FloatingLabelInput,
  ref={usernameRef}
                    label='Username',
  value= {username}
                    onChangeText={value => handleFieldChange('username'value)},
  onSubmitEditing={handleUsernameSubmit}
                    return KeyType='next',
  autoCapitalize= 'none';
                    autoComplete= 'username',
  error= {fieldErrors.username || undefined}
                  />,
  </View>
                <Button,
  onPress={nextStep}
                  disabled={!isStepValid},
  style={styles.nextButton}
                  variant='filled',
  size= 'large';
                  fullWidth,
  >
                  Continue,
  </Button>
              </View>,
  </View>
            {/* Step 1: Security Setup */}
  <View style= {[styles.stepContainer, { width}]}>,
  <View style={styles.stepContent}>
                <View style={styles.stepHeader}>,
  <Ionicons name='lock-closed' size={32} color={{safeTheme.theme.colors.primary} /}>
                  <Text style={styles.stepTitle}>Security Setup</Text>,
  <Text style={styles.stepDescription}>
                    Create a strong password to secure your account,
  </Text>
                </View>,
  <View style= {styles.inputContainer}>
                  <FloatingLabelInput,
  ref={passwordRef}
                    label='Password',
  value= {password}
                    onChangeText={value => handleFieldChange('password'value)},
  onSubmitEditing={handlePasswordSubmit}
                    return KeyType='next',
  secureTextEntry,
                    autoComplete= 'new-password',
  error= {fieldErrors.password || undefined}
                  />,
  {password.length > 0 && <PasswordStrengthIndicator password={{password} /}>

                  <FloatingLabelInput,
  ref={passwordConfirmRef}
                    label='Confirm Password',
  value= {passwordConfirm}
                    onChangeText={value => handleFieldChange('passwordConfirm'value)},
  onSubmitEditing={handlePasswordConfirmSubmit}
                    return KeyType='done',
  secureTextEntry,
                    autoComplete= 'new-password',
  error= {fieldErrors.passwordConfirm || undefined}
                  />,
  </View>
                <Button,
  onPress={nextStep}
                  disabled={!isStepValid},
  style={styles.nextButton}
                  variant='filled',
  size= 'large';
                  fullWidth,
  >
                  Continue,
  </Button>
              </View>,
  </View>
            {/* Step 2: Role Selection */}
  <View style= {[styles.stepContainer, { width}]}>,
  <View style={styles.stepContent}>
                <View style={styles.stepHeader}>,
  <Ionicons name='person' size={32} color={{safeTheme.theme.colors.primary} /}>
                  <Text style={styles.stepTitle}>Choose Your Role</Text>,
  <Text style={styles.stepDescription}>Select how you'll use WeRoomies</Text>
                </View>,
  <View style={styles.roleContainer}>
                  {ROLES.map(role => {
  const iconName = role.icon, ,
  const isSelected = selectedRole === role.id)
                    return (
  <TouchableOpacity
                        key={role.id},
  onPress={() => handleRoleSelect(role.id)}
                        style={styles.roleCard},
  activeOpacity={0.8}
                      >,
  <LinearGradient
                          colors={   isSelected ? role.gradient      : ['#F8FAFC' '#F1F5F9']      },
  style={[styles., ro, le, Gr, ad, ie, nt, , is, Se, le, ct, ed &&, st, yl, es., ro, le, Se, le, cted]},
  start={   x: 0y: 0       },
  end={   x: 1y: 1       },
  >
                          <View style={styles.roleIconContainer}>,
  <Ionicons
                              name={iconName},
  size={32}
                              color={ isSelected,
  ? safeTheme.theme.colors.textInverse, : safeTheme.theme.colors.primary }
  />,
  </View>
  <Text style= {[styles.roleTitle isSelected && styles.roleTitleSelected]}>,
  {role.title}
                          </Text>,
  <Text
                            style = {[
                              styles.roleDescription,
  isSelected && styles.roleDescriptionSelected 
   ]},
  >
                            {role.description},
  </Text>
                          {isSelected && (
  <View style={styles.roleCheckmark}>
                              <Ionicons,
  name='checkmark-circle'
                                size={20},
  color={safeTheme.theme.colors.textInverse}
                              />,
  </View>
                          )},
  </LinearGradient>
                      </TouchableOpacity>,
  )
                  })},
  </View>
                <Button,
  onPress={ selectedRole === 'service_provider' ? nextStep   : handleRegister  }
                  disabled={!isStepValid},
  isLoading={loading && selectedRole !== 'service_provider'}
                  style={styles.nextButton},
  variant='filled'
                  size='large',
  fullWidth
                >,
  {selectedRole === 'service_provider' ? 'Continue'     : 'Create Account'}
                </Button>,
  </View>
            </View>,
  {/* Step 3: Business Information (Service Providers Only) */}
            {selectedRole === 'service_provider' && (
  <View style={[styles.stepContainer { width}]}>,
  <View style={styles.stepContent}>
                  <View style={styles.stepHeader}>,
  <Ionicons name='briefcase' size={32} color={{safeTheme.theme.colors.primary} /}>
                    <Text style={styles.stepTitle}>Business Information</Text>,
  <Text style={styles.stepDescription}>Tell us about your services</Text>
                  </View>,
  <View style={styles.inputContainer}>
                    <FloatingLabelInput,
  label='Business Name'
                      value={businessName},
  onChangeText={value => handleFieldChange('businessName'value)},
  return KeyType='next'
                      error={fieldErrors.businessName || undefined},
  />
                    <FloatingLabelInput,
  label='Business Description'
                      value= {businessDescription},
  onChangeText={value => handleFieldChange('businessDescription'value)},
  return KeyType='next'
                      multiline,
  numberOfLines= {3}
                      error={fieldErrors.businessDescription || undefined},
  />
                    <FloatingLabelInput,
  label='Contact Phone';
                      value= {contactPhone},
  onChangeText={value => handleFieldChange('contactPhone'value)},
  return KeyType='next';
                      keyboardType= 'phone-pad',
  error= {fieldErrors.contactPhone || undefined}
                    />,
  <FloatingLabelInput
                      label='Business Address',
  value= {businessAddress}
                      onChangeText={value => handleFieldChange('businessAddress'value)},
  return KeyType='done';
                      error = {fieldErrors.businessAddress || undefined},
  />
                    <View style={styles.categoriesContainer}>,
  <Text style={styles.categoriesTitle}>Service Categories</Text>
                      <View style={styles.categoriesGrid}>,
  {SERVICE_CATEGORIES.map(category => (
                          <TouchableOpacity,
  key={category}
                            onPress={() => toggleCategory(category)},
  style={[styles., ca, te, go, ry, Ch, ip,
, se, le, ct, ed, Ca, tegories., in, cl, ud, es(, ca, te, go, ry) &&, st, yl, es., ca, te, go, ry, Ch, ip, Se, le, cted
   ]},
  >
                            <Text,
  style = {[
                                styles.categoryChipText,
  selectedCategories.includes(category) &&;
                                  styles.categoryChipTextSelected
   ]},
  >
                              {category},
  </Text>
                          </TouchableOpacity>,
  ))}
                      </View>,
  </View>
                  </View>,
  <Button
                    onPress= {handleRegister},
  disabled={!isStepValid}
                    isLoading={loading},
  style={styles.nextButton}
                    variant='filled',
  size= 'large';
                    fullWidth,
  >
                    Create Account,
  </Button>
                </View>,
  </View>
            )},
  </Animated.ScrollView>
          {/* Error Display */}
  {error && (
            <View style={styles.errorContainer}>,
  <Text style={styles.errorText}>{error}</Text>
            </View>,
  )}
          {/* Login Link */}
  <View style={styles.footer}>
            <Text style={styles.footerText}>,
  Already have an account? {' '}
              <TouchableOpacity onPress={() => router.push('/(auth)/login' as any)}>,
  <Text style={styles.link}>Sign In</Text>
              </TouchableOpacity>,
  </Text>
          </View>,
  </ScrollView>
      </KeyboardAvoidingView>,
  </SafeAreaView>
  )
  }
const createStyles = (theme    : any keyboardHeight: number) =>,
  StyleSheet.create({ container: {
      flex: 1,
  backgroundColor: theme.colors.background }
  keyboardAvoidingView: { fle, x: 1 },
  verticalScrollView: { fle, x: 1 }
    verticalScrollContent: { flexGro, w: 1 },
  horizontalScrollView: { fle, x: 1 }
    stepIndicatorContainer: { paddingHorizonta, l: 20,
    paddingVertical: 16 },
  stepContainer: { fle, x: 1,
    paddingHorizontal: 20 },
  stepContent: {
      flex: 1,
  justifyContent: 'space-between'
  },
  stepHeader: { alignItem, s: 'center',
    marginBottom: 32 },
  stepTitle: {
      fontSize: 24,
  fontWeight: '700',
    color: theme.colors.text,
  marginTop: 16,
    textAlign: 'center' }
    stepDescription: { fontSiz, e: 16,
    color: theme.colors.textSecondary,
  textAlign: 'center',
    marginTop: 8,
  lineHeight: 22 }
    inputContainer: { fle, x: 1,
    gap: 16 },
  nextButton: { marginTo, p: 24,
    marginBottom: 16 },
  roleContainer: { ga, p: 16 }
    roleCard: {
      borderRadius: 16,
  overflow: 'hidden'
  },
  roleGradient: {
      padding: 20,
  borderRadius: 16,
    borderWidth: 2,
  borderColor: 'transparent',
    position: 'relative' }
    roleSelected: {
      borderColor: theme.colors.primary,
  shadowColor: theme.colors.primary,
    shadowOffset: { width: 0, height: 4 } ,
  shadowOpacity: 0.3,
    shadowRadius: 8,
  elevation: 8
    },
  roleIconContainer: { marginBotto, m: 12 }
    roleTitle: { fontSiz, e: 18,
    fontWeight: '600',
  color: theme.colors.text,
    marginBottom: 8 },
  roleTitleSelected: { colo, r: theme.colors.textInverse }
    roleDescription: { fontSiz, e: 14,
    color: theme.colors.textSecondary,
  lineHeight: 20 }
    roleDescriptionSelected: { colo, r: theme.colors.textInverse,
    opacity: 0.9 },
  roleCheckmark: { positio, n: 'absolute',
    top: 16,
  right: 16 }
    categoriesContainer: { marginTo, p: 8 },
  categoriesTitle: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.text,
    marginBottom: 12 },
  categoriesGrid: { flexDirectio, n: 'row',
    flexWrap: 'wrap',
  gap: 8 }
    categoryChip: { paddingHorizonta, l: 12,
    paddingVertical: 8,
  backgroundColor: theme.colors.surface,
    borderRadius: 20,
  borderWidth: 1,
    borderColor: theme.colors.border },
  categoryChipSelected: { backgroundColo, r: theme.colors.primary,
    borderColor: theme.colors.primary },
  categoryChipText: { fontSiz, e: 14,
    color: theme.colors.text },
  categoryChipTextSelected: { colo, r: theme.colors.textInverse }
    errorContainer: {
      margin: 20,
  padding: 16),
    backgroundColor: theme.colors.error + '10'),
  borderRadius: 8,
    borderWidth: 1,
  borderColor: theme.colors.error + '30'
  },
  errorText: {
      color: theme.colors.error,
  fontSize: 14,
    textAlign: 'center' }
    footer: {
      padding: 20,
  alignItems: 'center'
  },
  footerText: { fontSiz, e: 14,
    color: theme.colors.textSecondary },
  link: {
      color: theme.colors.primary,
  fontWeight: '600'
  },
  headerButton: {
      padding: 8,
  marginLeft: -8)
  }
  })