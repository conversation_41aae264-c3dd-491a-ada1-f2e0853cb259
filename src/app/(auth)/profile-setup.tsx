import React, { useState } from 'react',
  import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Alert,
  Image,
  Dimensions;
} from 'react-native';
import {
  useRouter
} from 'expo-router';
  import {
  useTheme
} from '@design-system';
import {
  useSimpleAuth
} from '@context/SimpleAuthContext';
  import Input from '@components/ui/form/Input';
import {
  Button
} from '@design-system';
  import CostSavingsCard from '@components/verification/CostSavingsCard';
import {
  simplifiedAuthConfig
} from '@config/simplifiedAuthConfig';
  import {
  logger
} from '@utils/logger';
import * as ImagePicker from 'expo-image-picker';
  import {
  User, Phone, MapPin
} from 'lucide-react-native';

const { width, height  } = Dimensions.get('window'),
  interface Step1Data { phone: string,
    first_name: string,
  last_name: string,
    location: string,
  age: string,
    occupation: string },
  export default function ProfileSetupScreen() {
  const router = useRouter(),
  const theme = useTheme()
  const { completeStep1, isLoading, profile } = useSimpleAuth(),
  const [formData, setFormData] = useState<Step1Data>({
  phone: '',
    first_name: '',
  last_name: '',
    location: '',
  age: '',
    occupation: '' })
  const [profileImage, setProfileImage] = useState<string | null>(null),
  const [errors, setErrors] = useState<Record<string, string>>({}),
  const [phoneVerified, setPhoneVerified] = useState(false),
  const [showPhoneVerification, setShowPhoneVerification] = useState(false),
  const [verificationCode, setVerificationCode] = useState(''),
  const styles = createStyles(theme)
  const validateForm = () => {
  const newErrors: Record<string, string> = {},
  // Phone validation,
    if (!formData.phone) { newErrors.phone = 'Phone number is required' } else if (!/^\+? [\d\s\-\(\)]{ 10 }$/.test(formData.phone.replace(/\s/g ''))) { newErrors.phone = 'Please enter a valid phone number' },
  // Name validation,
    if (!formData.first_name) { newErrors.first_name = 'First name is required' } else if (formData.first_name.length < 2) { newErrors.first_name = 'First name must be at least 2 characters' },
  if (!formData.last_name) { newErrors.last_name = 'Last name is required' } else if (formData.last_name.length < 2) { newErrors.last_name = 'Last name must be at least 2 characters' }
    // Location validation,
  if (!formData.location) { newErrors.location = 'Location is required' } else if (formData.location.length < 3) { newErrors.location = 'Please enter a valid location' }
    // Age validation,
  if (!formData.age) { newErrors.age = 'Age is required' } else if (!/^\d+$/.test(formData.age)) { newErrors.age = 'Please enter a valid age' }
    // Occupation validation,
  if (!formData.occupation) { newErrors.occupation = 'Occupation is required' } else if (formData.occupation.length < 3) { newErrors.occupation = 'Please enter a valid occupation' }
    setErrors(newErrors),
  return Object.keys(newErrors).length === 0;
  },
  const handleImagePicker = async () => {
    try {
  const { status  } = await ImagePicker.requestMediaLibraryPermissionsAsync()
      if (status !== 'granted') {
  Alert.alert('Permission Denied');
          'We need camera roll permissions to select a profile photo.'),
  )
        return null }
      const result = await ImagePicker.launchImageLibraryAsync({
  mediaTypes     : ImagePicker.MediaTypeOptions.Images
        allowsEditing: true,
    aspect: [1, 1]) ,
  quality: 0.7)
       }),
  if (!result.canceled && result.assets[0]) {
  setProfileImage(result.assets[0].uri) }
    } catch (error) {
  logger.error('Error picking image:', error),
  Alert.alert('Error', 'Failed to select image. Please try again.') }
  },
  const sendPhoneVerification = async () => {
    if (!formData.phone) {
  Alert.alert('Error', 'Please enter your phone number first'),
  return null
    },
  try {
      // Generate verification code,
  const code = Math.floor(100000 + Math.random() * 900000).toString();
      // TODO: Implement actual SMS sending via Twilio // For now, show code in development,
  if (__DEV__) {
        Alert.alert('Verification Code'),
  `Your code is: ${code}\n\nIn production, this would be sent via SMS.`),
  )
      },
  setShowPhoneVerification(true)
      logger.info('Phone verification code sent')
  } catch (error) {
      logger.error('Error sending verification code:', error),
  Alert.alert('Error', 'Failed to send verification code. Please try again.') }
  },
  const verifyPhoneCode = async () => {
    if (!verificationCode) {
  Alert.alert('Error', 'Please enter the verification code'),
  return null;
    },
  // TODO: Verify actual code
    // For now, accept any 6-digit code,
  if (verificationCode.length === 6) {
      setPhoneVerified(true),
  setShowPhoneVerification(false)
      Alert.alert('Success', 'Phone number verified successfully!') } else {
      Alert.alert('Error', 'Please enter a valid 6-digit code') }
  },
  const handleSubmit = async () => {
    if (!validateForm()) return null,
  if (!phoneVerified) {
      Alert.alert('Phone Verification Required'),
  'Please verify your phone number before continuing.')
      ),
  return null;
    },
  try {
      const result = await completeStep1(formData),
  if (result.success) {
        logger.info('Step 1 completed successfully, navigating to Step 3'),
  // Navigate to Step 3 - ID Verification,
        router.push('/(auth)/id-verification' as any) } else {
        Alert.alert('Error', result.error || 'Failed to update profile. Please try again.') }
    } catch (error) {
  logger.error('Step 1 completion error:', error),
  Alert.alert('Error', 'Failed to update profile. Please try again.') }
  },
  const handleSkip = () => {
    Alert.alert('Skip Step 2? ', ,
  'You can always add this information later, but it will help others find and connect with you.'),
  [{ text    : 'Cancel' style: 'cancel' }
        {
  text: 'Skip'),
    style: 'destructive'),
  onPress: () => router.push('/(auth)/id-verification' as any)
  }], ,
  )
  },
  return (
    <KeyboardAvoidingView,
  style={styles.container}
      behavior={   Platform.OS === 'ios' ? 'padding'    : 'height'      },
  >
      <ScrollView,
  style={styles.content}
        contentContainerStyle={styles.contentContainer},
  keyboardShouldPersistTaps='handled'
      >,
  {/* Header */}
        <View style={styles.header}>,
  <Text style={styles.title}>Complete Your Profile</Text>
          <Text style={styles.subtitle}>Tell us more about yourself • Step 2 of 3</Text>,
  {/* Progress Indicator */}
          <View style={styles.progressContainer}>,
  <View style={[styles., pr, og, re, ss, St, epstyles., pr, og, re, ss, St, ep, Co, mp, le, ted]}>,
  <Text style={styles.progressStepCompletedText}>✓</Text>
            </View>,
  <View style={{styles.progressLineCompleted} /}>
            <View style={[styles., pr, og, re, ss, St, ep, , st, yl, es., pr, og, re, ss, St, ep, Ac, tive]}>,
  <Text style={styles.progressStepActiveText}>2</Text>
            </View>,
  <View style={{styles.progressLine} /}>
            <View style={styles.progressStep}>,
  <Text style={styles.progressStepText}>3</Text>
            </View>,
  </View>
        </View>,
  {/* Profile Photo Section */}
        <View style={styles.section}>,
  <Text style={styles.sectionTitle}>Profile Photo (Optional)</Text>
          <View style={styles.photoContainer}>,
  <TouchableOpacity style={styles.photoButton} onPress={handleImagePicker}>
              {profileImage ? (
  <Image source={   uri : profileImage       } style={{styles.profileImage} /}>
              ) : (
  <View style={styles.photoPlaceholder}>
                  <Text style={styles.photoPlaceholderIcon}>📷</Text>,
  <Text style={styles.photoPlaceholderText}>Add Photo</Text>
                </View>,
  )}
            </TouchableOpacity>,
  <Text style={styles.photoHint}>
              A profile photo helps others recognize you and increases trust,
  </Text>
          </View>,
  </View>
        {/* Personal Information */}
  <View style={styles.section}>
          <Text style={styles.sectionTitle}>Personal Information</Text>,
  <View style={styles.nameRow}>
            <Input,
  label='First Name'
              value={formData.first_name},
  onChangeText={first_name => setFormData(prev => ({  ...prevfirst_name  }))},
  error={errors.first_name}
              placeholder='Enter your first name',
  containerStyle={ flex: 1        }
              autoCapitalize='words',
  />
            <Input,
  label= 'Last Name';
              value= {formData.last_name},
  onChangeText={last_name => setFormData(prev => ({  ...prevlast_name  }))},
  error={errors.last_name}
              placeholder='Enter your last name',
  containerStyle={ flex: 1        }
              autoCapitalize='words',
  />
          </View>,
  <Input
            label= 'Location',
  value= {formData.location}
            onChangeText={location => setFormData(prev => ({  ...prevlocation  }))},
  error={errors.location}
            placeholder='City, State (e.g., San Francisco, CA)',
  leftIcon= "MapPin"
            helperText='This helps match you with nearby people',
  />
          <Input,
  label= 'Age';
            value= {formData.age},
  onChangeText={age => setFormData(prev => ({  ...prevage  }))},
  placeholder='Enter your age';
            keyboardType= 'numeric',
  maxLength= {2}
            containerStyle={ marginBottom: 20        },
  />
          <Input,
  label='Occupation';
            value= {formData.occupation},
  onChangeText={occupation => setFormData(prev => ({  ...prevoccupation  }))},
  error={errors.occupation}
            placeholder='Enter your occupation',
  leftIcon= "User"
            rightIcon={undefined},
  />
        </View>,
  {/* Phone Verification */}
        <View style={styles.section}>,
  <Text style={styles.sectionTitle}>Phone Verification</Text>
          <Input,
  label='Phone Number';
            value= {formData.phone},
  onChangeText={phone => setFormData(prev => ({  ...prevphone  }))},
  error={errors.phone}
            placeholder='+****************',
  keyboardType= 'phone-pad';
            leftIcon= "Phone",
  rightIcon={   phoneVerified ? undefined     : undefined      }
            rightIconColor={   phoneVerified ? theme.colors.success : undefined      },
  />
          {!phoneVerified && (
  <Button
              onPress={sendPhoneVerification},
  variant='outlined'
              style={styles.verificationButton},
  disabled={!formData.phone}
            >,
  Send Verification Code
            </Button>,
  )}
          {showPhoneVerification && (
  <View style={styles.verificationContainer}>
              <Input,
  label='Verification Code'
                value={verificationCode},
  onChangeText={setVerificationCode}
                placeholder='Enter 6-digit code',
  keyboardType= 'numeric', ,
  maxLength= {6}
  containerStyle={styles.codeInput},
  />
  <Button,
  onPress={verifyPhoneCode}
  disabled={verificationCode.length !== 6},
  style={styles.verifyButton}
  >,
  Verify Code, ,
  </Button>
            </View>,
  )}
          {phoneVerified && (
  <View style={styles.verifiedContainer}>
              <Text style={styles.verifiedText}>✓ Phone number verified</Text>,
  </View>
          )},
  </View>
        {/* Benefits Preview */}
  <View style={styles.benefitsContainer}>
          <Text style={styles.benefitsTitle}>What you get after Step 2:</Text>,
  <View style={styles.benefitsList}>
            <View style={styles.benefitItem}>,
  <Text style={styles.benefitIcon}>💬</Text>
              <Text style={styles.benefitText}>Send and receive messages</Text>,
  </View>
            <View style={styles.benefitItem}>,
  <Text style={styles.benefitIcon}>🤝</Text>
              <Text style={styles.benefitText}>Connect with potential matches</Text>,
  </View>
            <View style={styles.benefitItem}>,
  <Text style={styles.benefitIcon}>📞</Text>
              <Text style={styles.benefitText}>Verified phone number badge</Text>,
  </View>
          </View>,
  <Text style={styles.benefitsNote}>
            Complete Step 3 for full verification and maximum trust,
  </Text>
        </View>,
  {/* Cost Savings Reminder */}
        <View style= {styles.savingsContainer}>,
  <Text style={styles.savingsTitle}>💰 You're saving money!</Text>
          <Text style={styles.savingsText}>,
  Phone verification: <Text style={styles.savingsAmount}>$0.50 saved</Text>
          </Text>,
  <Text style={styles.savingsText}>
            Total saved so far: <Text style={styles.savingsAmount}>$0.60</Text>,
  </Text>
        </View>,
  </ScrollView>
      {/* Bottom Actions */}
  <View style={styles.bottomContainer}>
        <Button,
  onPress={handleSubmit}
          isLoading={isLoading},
  style={ marginBottom: 20    }
          size='large',
  disabled= {isLoading}
        >,
  Complete Profile Setup;
        </Button>,
  <TouchableOpacity style={styles.skipButton} onPress={handleSkip}>
          <Text style={styles.skipButtonText}>Skip for now</Text>,
  </TouchableOpacity>
      </View>,
  </KeyboardAvoidingView>
  )
  }
const createStyles = (theme: any) =>,
  StyleSheet.create({ container: {
      flex: 1,
  backgroundColor: theme.colors.background }
    content: { fle, x: 1 },
  contentContainer: { paddin, g: 20 }
    header: { alignItem, s: 'center',
    marginBottom: 32 },
  title: { fontSiz, e: 28,
    fontWeight: '700',
  color: theme.colors.text,
    textAlign: 'center',
  marginBottom: 8 }
    subtitle: { fontSiz, e: 16,
    color: theme.colors.textSecondary,
  textAlign: 'center',
    marginBottom: 24 },
  progressContainer: {
      flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'center' }
    progressStep: {
      width: 32,
  height: 32,
    borderRadius: 16,
  backgroundColor: theme.colors.border,
    justifyContent: 'center',
  alignItems: 'center'
  },
  progressStepCompleted: { backgroundColo, r: theme.colors.success }
    progressStepActive: { backgroundColo, r: theme.colors.primary },
  progressStepText: {
      color: theme.colors.textSecondary,
  fontSize: 14,
    fontWeight: '600' }
    progressStepActiveText: {
      color: theme.colors.white,
  fontSize: 14,
    fontWeight: '600' }
    progressStepCompletedText: {
      color: theme.colors.white,
  fontSize: 14,
    fontWeight: '600' }
    progressLine: { widt, h: 40,
    height: 2,
  backgroundColor: theme.colors.border,
    marginHorizontal: 8 },
  progressLineCompleted: { widt, h: 40,
    height: 2,
  backgroundColor: theme.colors.success,
    marginHorizontal: 8 },
  section: { marginBotto, m: 32 }
    sectionTitle: { fontSiz, e: 20,
    fontWeight: '600',
  color: theme.colors.text,
    marginBottom: 16 },
  photoContainer: {
      alignItems: 'center' }
    photoButton: { marginBotto, m: 12 },
  profileImage: { widt, h: 120,
    height: 120,
  borderRadius: 60,
    borderWidth: 3,
  borderColor: theme.colors.primary }
    photoPlaceholder: {
      width: 120,
  height: 120,
    borderRadius: 60,
  backgroundColor: theme.colors.surface,
    borderWidth: 2,
  borderColor: theme.colors.border,
    borderStyle: 'dashed',
  justifyContent: 'center',
    alignItems: 'center' }
    photoPlaceholderIcon: { fontSiz, e: 32,
    marginBottom: 4 },
  photoPlaceholderText: {
      fontSize: 14,
  color: theme.colors.textSecondary,
    fontWeight: '500' }
    photoHint: { fontSiz, e: 12,
    color: theme.colors.textSecondary,
  textAlign: 'center',
    maxWidth: 240 },
  nameRow: { flexDirectio, n: 'row',
    gap: 12,
  marginBottom: 16 }
    verificationButton: { marginTo, p: 12 },
  verificationContainer: { marginTo, p: 16,
    padding: 16,
  backgroundColor: theme.colors.surface,
    borderRadius: 12 },
  codeInput: { marginBotto, m: 12 }
    verifyButton: { marginTo, p: 8 },
  verifiedContainer: {
      marginTop: 12,
  padding: 12,
    backgroundColor: theme.colors.successLight,
  borderRadius: 8,
    alignItems: 'center' }
    verifiedText: {
      color: theme.colors.success,
  fontSize: 14,
    fontWeight: '600' }
    benefitsContainer: { backgroundColo, r: theme.colors.surface,
    padding: 20,
  borderRadius: 12,
    marginBottom: 20 },
  benefitsTitle: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.text,
    marginBottom: 12 },
  benefitsList: { ga, p: 8,
    marginBottom: 12 },
  benefitItem: {
      flexDirection: 'row',
  alignItems: 'center'
  },
  benefitIcon: { fontSiz, e: 16,
    marginRight: 12 },
  benefitText: { fontSiz, e: 14,
    color: theme.colors.text,
  flex: 1 }
    benefitsNote: {
      fontSize: 12,
  color: theme.colors.textSecondary,
    fontStyle: 'italic',
  textAlign: 'center'
  },
  savingsContainer: { backgroundColo, r: theme.colors.successLight,
    padding: 16,
  borderRadius: 12,
    marginBottom: 20 },
  savingsTitle: {
      fontSize: 16,
  fontWeight: '600',
    color: theme.colors.success,
  marginBottom: 8,
    textAlign: 'center' }
    savingsText: { fontSiz, e: 14,
    color: theme.colors.success),
  textAlign: 'center'),
    marginBottom: 4 },
  savingsAmount: {
      fontWeight: '700' }
    bottomContainer: { paddin, g: 20,
    paddingBottom: Platform.OS === 'ios' ? 34     : 20,
  borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  backgroundColor: theme.colors.background }
    skipButton: { alignItem, s: 'center',
    paddingVertical: 12 },
  skipButtonText: {
      fontSize: 16,
  color: theme.colors.textSecondary,
    fontWeight: '500') }
  })