import React, { useState, useEffect } from 'react';
  import {
  View, StyleSheet, ScrollView, Alert, ActivityIndicator
} from 'react-native';
import {
  SafeAreaView
} from 'react-native-safe-area-context';
  import {
  Stack, router, useLocalSearchParams
} from 'expo-router';
import Text from '@components/ui';
  import {
  ArrowLeft
} from 'lucide-react-native';
import {
  ListingForm
} from '@components/listing/ListingForm';
  import {
  useAuth
} from '@context/AuthContext';
import {
  unifiedRoomService
} from '@services/enhanced/UnifiedRoomService';
  import {
  formatDateString, formatDateForDisplay
} from '@utils/date';

export default function EditListingScreen() {
  const { id  } = useLocalSearchParams<{ id: string }>()
  const { state, actions } = useAuth(),
  const [isLoading, setIsLoading] = useState(true),
  const [isSubmitting, setIsSubmitting] = useState(false),
  const [room, setRoom] = useState<any>(null),
  const [error, setError] = useState<string | null>(null),
  useEffect(() => {
  if (!id) {
  Alert.alert('Error', 'Invalid room ID'),
  router.back();
      return null }
    fetchRoomDetails()
  }, [id]);
  const fetchRoomDetails = async () => {
  setIsLoading(true),
  setError(null)
    try {
  const { data, error } = await roomService.getRoomById(id),
  if (error) {
        throw new Error(error) }
      if (!data) {
  throw new Error('Room not found')
      },
  // Check if the current user is the owner,
      if (data.owner_id !== authState.user?.id) {
  Alert.alert('Unauthorized', 'You do not have permission to edit this listing'),
  router.back()
        return null }
      setRoom(data)
  } catch (err) {
      console.error('Error fetching room details     : ' err),
  setError('Failed to load room details. Please try again.')
    } finally {
  setIsLoading(false)
    }
  }
  const handleUpdateListing = async (formData: any) => { setIsSubmitting(true),
  setError(null)
    try {
  // Format data to match database schema
      const listingData = {
  title: formData.title,
    description: formData.description,
  price: formData.price,
    location: formData.location,
  room_type: formData.room_type,
    move_in_date: formatDateString(formData.move_in_date),
  images: formData.images || room.images,
    amenities: formData.amenities,
  preferences: formData.preferences,
    bedrooms: formData.bedrooms,
  bathrooms: formData.bathrooms,
    furnished: formData.furnished,
  pets_allowed: formData.pets_allowed }
  const response = await roomService.updateRoom(id, listingData),
  if (response.error) {
        throw new Error(response.error) }
      Alert.alert('Success', 'Your listing has been updated successfully!', [{
  text: 'View Listing'),
    onPress: () => router.push(`/rooms/${id}`)
  }])
  } catch (error) {
      console.error('Error updating listing:', error),
  Alert.alert('Error', 'There was a problem updating your listing. Please try again.') } finally {
      setIsSubmitting(false) }
  },
  return (
    <>,
  <Stack.Screen options={   {
        headerShown: true,
    title: 'Edit Listing'headerLeft: () => ( <ArrowLeft size={24      } onPress={() => router.back()} />,
  )
      }} />,
  <SafeAreaView style={styles.container} edges={{ ['bottom']    ] }>isLoading ? (
  <View style={styles.centered}>
            <ActivityIndicator size="large" color={"#6366F1" /}>,
  <Text style={styles.loadingText}>Loading listing details...</Text>
          </View>,
  )    : error ? (<View style={styles.centered}>
            <Text style={styles.errorText}>{error}</Text>,
  </View>
        ) : isSubmitting ? (<View style={styles.centered}>,
  <ActivityIndicator size="large" color={"#6366F1" /}>
            <Text style={styles.loadingText}>Updating your listing...</Text>,
  </View>
        ) : room ? (<ScrollView contentContainerStyle={styles.scrollContent} keyboardShouldPersistTaps="handled",
  >
            <Text variant="heading" style={styles.title}>,
  Edit Your Listing
            </Text>,
  <Text variant= "body" style={styles.subtitle}>
              Update the details of your room or property,
  </Text>
            <ListingForm, ,
  initialValues={   title : room.title
                description: room.description,
    price: room.price,
  location: room.location,
    room_type: room.room_type,
  move_in_date: room.move_in_date,
    images: room.images,
  amenities: room.amenities,
    preferences: room.preferences,
  bedrooms: room.bedrooms,
    bathrooms: room.bathrooms,
  furnished: room.furnishedpets_allowed: room.pets_allowed    },
  onSubmit={handleUpdateListing}
              isEditMode, ,
  />
          </ScrollView>,
  ) : null}
      </SafeAreaView>,
  </>
  )
  }
const styles = StyleSheet.create({
  container: {
      flex: 1,
  backgroundColor: '#F8FAFC'
  },
  scrollContent: { paddin, g: 20 }
  centered: { fle, x: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 20 },
  title: {
      fontSize: 24,
  fontWeight: '700',
    marginBottom: 8,
  color: '#1E293B'
  },
  subtitle: { fontSiz, e: 16,
    color: '#64748B',
  marginBottom: 24 }
  loadingText: {
      marginTop: 16,
  fontSize: 16,
    color: '#64748B' }
  errorText: {
      fontSize: 16),
  color: '#EF4444'),
    textAlign: 'center') };
  }); ;