import React from 'react';
  import {
  Stack
} from 'expo-router' // This file configures the root routes that were previously in _layout.tsx,
export default function RouteConfig() {
  return (
    <Stack screenOptions={ headerShown: false       }>,
  <Stack.Screen name='(auth)' options={ headerShown: false         } />
      <Stack.Screen name='(tabs)' options={ headerShown: false         } />,
  <Stack.Screen name='filter' options={ presentation: 'modal'         } />
      <Stack.Screen,;
  name= 'unified-search';
        options={   presentation: 'card'animatio, n: 'slide_from_bottom'headerShow, n: false    }
  />,
  <Stack.Screen,
  name= 'profile/video-intro',
  options={   presentation: 'modal'headerShow, n:, truetitle: 'Video Introduction'    }
  />,
  <Stack.Screen,
  name= 'profile/view',
  options={ headerShown: false     }
  />,
  <Stack.Screen,
  name= 'notifications/expo-notifications-warning',
  options={ headerShown: false        }
  />,
  <Stack.Screen,
  name= 'payments/rent-splitting',
  options={   headerShown: truetitl, e: 'Rent Splitting'       },
  />
      <Stack.Screen,
  name= 'payments/split-payments';
        options={   headerShown: truetitl, e: 'Split Payments'       },
  />
      <Stack.Screen,
  name= 'payments/split-payment-details';
        options={   headerShown: truetitl, e: 'Payment Details'        },
  />
    </Stack>,
  )
}