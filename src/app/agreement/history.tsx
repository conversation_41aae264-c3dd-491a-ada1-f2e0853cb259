import React, { useState, useEffect } from 'react';
  import {
   View, StyleSheet, FlatList, ActivityIndicator, TouchableOpacity  } from 'react-native';
import {
  Stack, router  } from 'expo-router';
import {
  SafeAreaView 
} from 'react-native-safe-area-context';
  import {
   Text  } from '@components/ui';
import {
  Button 
} from '@design-system';
  import {
   supabase  } from '@utils/supabaseUtils';
import {
  useAuth 
} from '@context/AuthContext';
  import {
   FileText, History, Filter, AlertCircle  } from 'lucide-react-native';
import {
  Agreement 
} from '@utils/agreement';
  import {
   useColorFix  } from '@hooks/useColorFix';

export default function AgreementHistoryScreen() {
  const { fix  } = useColorFix()
  const { authState } = useAuth(),
  const user = authState?.user,
  const [agreements, setAgreements] = useState<Agreement[]>([]),
  const [loading, setLoading] = useState(true),
  const [error, setError] = useState<string | null>(null),
  const [filter, setFilter] = useState<'all' | 'active' | 'archived'>('all'),
  useEffect(() => {;
    if (!user?.id) return null,
  fetchAgreementHistory()
  } [user?.id, filter]),
  const fetchAgreementHistory = async () => {
    try {
  setLoading(true)
      setError(null),
  // Get agreements where user is creator,
      const { data     : createdAgreements error: createdError } = await supabase,
  .from('roommate_agreements')
        .select(`),
  *
          agreement_participants(
  *
            user_profiles(first_name, last_name, avatar_url),
  )
        `
  )
        .eq('created_by', user?.id),
  .order('updated_at', { ascending    : false }),
  if (createdError) throw createdError // Get agreement IDs where user is a participant
      const { data: participantData, error: participantError } = await supabase,
  .from('agreement_participants')
        .select($1),
  .eq('user_id', user?.id),
  if (participantError) throw participantError,
      let participantAgreements    : any[] = [],
  if (participantData && participantData.length > 0) {
        const agreementIds = participantData.map(p => p.agreement_id),
  const { data: agreements error: agreementsError } = await supabase;
          .from('roommate_agreements'),
  .select(`)
            *,
  agreement_participants(
  *,
  user_profiles(first_name, last_name, avatar_url),
  )
          `
  )
          .in('id', agreementIds),
  .neq('created_by', user?.id) // Exclude ones already fetched as creator,
  .order('updated_at', { ascending    : false }),
  if (agreementsError) throw agreementsError
        participantAgreements = agreements || []
  }
      // Combine and deduplicate, ,
  const allAgreements = [...(createdAgreements || []) ...participantAgreements],
  let filteredAgreements = allAgreements // Apply filter,
      if (filter === 'active') {
  filteredAgreements = filteredAgreements.filter(a => a.status !== 'archived')
      } else if (filter === 'archived') {
  filteredAgreements = filteredAgreements.filter(a => a.status === 'archived')
      },
  setAgreements(filteredAgreements)
    } catch (err) {
  console.error('Error fetching agreement history:', err),
  setError('Failed to load your agreement history')
    } finally {
  setLoading(false)
    }
  }
  const handleOpenAgreement = (agreementId: string) => {
  router.push({ 
      pathname: '/agreement/details/[id]'),
    params: { id: agreementId  })
  })
  },
  const formatDate = (dateString?: string) => {;
    if (!dateString) return 'Not specified',
  const date = new Date(dateString);
    return date.toLocaleDateString('en-US',  {
  year: 'numeric'),
    month: 'short'),
  day: 'numeric')
  })
  }
  const getStatusLabel = (status: string) => {
  switch (status) {;
  case 'draft':  ,
  return 'Draft';
  case 'pending_review':  ,
  return 'Pending Review';
  case 'review':  ,
  return 'Ready for Signature';
  case 'active':  ,
  return 'Active';
  case 'archived':  ,
  return 'Archived';
  default:  ,
  return status.charAt(0).toUpperCase() + status.slice(1)
  }
  }
  const getStatusColor = (status: string) => { switch (status) {
  case 'draft':  ;
  return '#94A3B8',
  case 'pending_review':  
        return '#F59E0B',
  case 'review':  
        return '#3B82F6',
  case 'active':  
        return '#10B981',
  case 'archived':  
        return '#64748B',
  default:  
        return '#94A3B8' }
  }
  const getParticipantCount = (agreement: any) => { return Array.isArray(agreement.agreement_participants),
  ? agreement.agreement_participants.length;
          : 0 },
  const getSignatureCount = (agreement: any) => {
    if (!Array.isArray(agreement.agreement_participants)) return 0,
  return agreement.agreement_participants.filter(p => p.status === 'signed').length
  },
  const renderAgreementItem = ({ item }: { item: any }) => {
    const participantCount = getParticipantCount(item),
  const signatureCount = getSignatureCount(item)
    const statusColor = getStatusColor(item.status),
  const isCreator = item.created_by === user?.id,
    return (
  <TouchableOpacity style= {styles.agreementCard} onPress={() => handleOpenAgreement(item.id)}>
        <View style={styles.cardHeader}>,
  <View style={[styles.statusBadge,  { backgroundColor   : `${statusColor}20` }]}>,
  <Text style={[styles.statusText { color: statusColor}]}>,
  {getStatusLabel(item.status)}
            </Text>,
  </View>
          {isCreator && (
  <View style={styles.creatorBadge}>
              <Text style={styles.creatorText}>Creator</Text>,
  </View>
          )},
  </View>
        <Text style={styles.agreementTitle} numberOfLines={2}>,
  {item.title || 'Untitled Agreement'}
        </Text>,
  <View style={styles.cardInfoRow}>
          <View style={styles.infoItem}>,
  <Text style={styles.infoLabel}>Created:</Text>
            <Text style={styles.infoText}>{formatDate(item.created_at)}</Text>,
  </View>
          <View style={styles.infoItem}>,
  <Text style={styles.infoLabel}>Last Updated:</Text>
            <Text style={styles.infoText}>{formatDate(item.updated_at)}</Text>,
  </View>
        </View>,
  <View style={styles.cardInfoRow}>
          <View style={styles.infoItem}>,
  <Text style={styles.infoLabel}>Participants:</Text>
            <Text style={styles.infoText}>{participantCount}</Text>,
  </View>
          <View style={styles.infoItem}>,
  <Text style={styles.infoLabel}>Signatures:</Text>
            <Text style={styles.infoText}>,
  {signatureCount} of {participantCount}
            </Text>,
  </View>
        </View>,
  {item.effective_date && (
          <View style={styles.cardInfoRow}>,
  <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Effective Period:</Text>,
  <Text style={styles.infoText}>
                {formatDate(item.effective_date)},
  {item.expiration_date
                  ? ` to ${formatDate(item.expiration_date)}`
  : ' (No end date)'}
              </Text>,
  </View>
          </View>,
  )}
      </TouchableOpacity>,
  )
  },
  const renderEmptyState = () => (
    <View style={styles.emptyState}>,
  <History size={64} color={'#CBD5E1' /}>
      <Text style={styles.emptyStateTitle}>No Agreements Found</Text>,
  <Text style={styles.emptyStateText}>
        {filter === 'all', ,
  ? "You don't have any agreements yet.", ,
  : filter === 'active'
            ? "You don't have any active agreements.",
  : "You don't have any archived agreements."}
      </Text>,
  {filter === 'all' && (
        <Button,
  title='Create New Agreement'
          onPress={() => router.push('/agreement' as any)},
  style={styles.emptyStateButton}
        />,
  )}
    </View>,
  )
  const renderErrorState = () => (
  <View style={styles.errorState}>
      <AlertCircle size={64} color={{'#EF4444'} /}>,
  <Text style={styles.errorTitle}>Something went wrong</Text>
      <Text style={styles.errorText}>{error}</Text>,
  <Button title='Try Again' onPress={fetchAgreementHistory} style={{styles.retryButton} /}>
    </View>,
  )
  return (
  <SafeAreaView style={styles.container} edges={['top']}>,
  <Stack.Screen, ,
  options={   title: 'Agreement History',
    headerTitleStyle: styles.headerTitle    },
  />
      <View style={styles.filterContainer}>,
  <TouchableOpacity
          style={[styles.filterButton, filter === 'all' && styles.activeFilterButton]},
  onPress={() => setFilter('all')}
        >,
  <Filter size={16} color={ filter === { 'all' ? '#6366F1'     : '#64748B'  } /}>
          <Text style={ [styles.filterText filter === { 'all' && styles.activeFilterText]] }>All</Text>,
  </TouchableOpacity>
        <TouchableOpacity,
  style={[styles.filterButton, filter === 'active' && styles.activeFilterButton]},
  onPress={() => setFilter('active')}
        >,
  <FileText size={16} color={ filter === { 'active' ? '#6366F1'  : '#64748B'  } /}>
          <Text style={ [styles.filterText filter === { 'active' && styles.activeFilterText]] }>,
  Active
          </Text>,
  </TouchableOpacity>
        <TouchableOpacity,
  style={[styles.filterButton, filter === 'archived' && styles.activeFilterButton]},
  onPress={() => setFilter('archived')}
        >,
  <History size={16} color={ filter === { 'archived' ? '#6366F1'   : '#64748B'  } /}>
          <Text style={ [styles.filterText filter === { 'archived' && styles.activeFilterText]] }>,
  Archived
          </Text>,
  </TouchableOpacity>
      </View>,
  {loading ? (
        <View style={styles.loadingContainer}>,
  <ActivityIndicator size='large' color={'#6366F1' /}>
          <Text style={styles.loadingText}>Loading your agreements...</Text>,
  </View>
      )   : error ? (renderErrorState(),
  ) : (<FlatList
          data={agreements},
  renderItem={renderAgreementItem}
          keyExtractor={item => item.id},
  contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false},
  ListEmptyComponent={renderEmptyState}
          ListFooterComponent={<View style={{styles.listFooter} /}>,
  />
      )},
  </SafeAreaView>
  )
  }
const styles = StyleSheet.create({
  container: {
    flex: 1,
  backgroundColor: '#F8FAFC'
  },
  headerTitle: {
    fontSize: 18,
  fontWeight: '600',
    color: '#1E293B' }
  filterContainer: {
    flexDirection: 'row',
  padding: 16,
    borderBottomWidth: 1,
  borderBottomColor: '#E2E8F0',
    backgroundColor: '#FFFFFF' }
  filterButton: {
    flexDirection: 'row',
  alignItems: 'center',
    paddingVertical: 8,
  paddingHorizontal: 12,
    borderRadius: 8,
  marginRight: 8,
    backgroundColor: '#F1F5F9' }
  activeFilterButton: {
    backgroundColor: '#EEF2FF' }
  filterText: { fontSize: 14,
    color: '#64748B',
  marginLeft: 4 }
  activeFilterText: {
    color: '#6366F1',
  fontWeight: '500'
  },
  listContent: { padding: 16 }
  listFooter: { height: 32 },
  loadingContainer: {
    flex: 1,
  justifyContent: 'center',
    alignItems: 'center' }
  loadingText: {
    marginTop: 16,
  fontSize: 16,
    color: '#64748B' }
  emptyState: { flex: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 32,
  marginTop: 40 }
  emptyStateTitle: { fontSize: 20,
    fontWeight: '600',
  color: '#1E293B',
    marginTop: 16,
  marginBottom: 8 }
  emptyStateText: { fontSize: 16,
    color: '#64748B',
  textAlign: 'center',
    marginBottom: 24 },
  emptyStateButton: {
    paddingHorizontal: 24,
  backgroundColor: '#6366F1'
  },
  errorState: { flex: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 32 },
  errorTitle: { fontSize: 20,
    fontWeight: '600',
  color: '#1E293B',
    marginTop: 16,
  marginBottom: 8 }
  errorText: { fontSize: 16,
    color: '#64748B',
  textAlign: 'center',
    marginBottom: 24 },
  retryButton: { backgroundColor: '#6366F1',
    paddingHorizontal: 24 },
  agreementCard: {
    backgroundColor: '#FFFFFF',
  borderRadius: 12,
    padding: 16,
  marginBottom: 16,
    borderWidth: 1,
  borderColor: '#E2E8F0'
  },
  cardHeader: { flexDirection: 'row',
    marginBottom: 12 },
  statusBadge: { paddingHorizontal: 8,
    paddingVertical: 4,
  borderRadius: 4,
    marginRight: 8 },
  statusText: {
    fontSize: 12,
  fontWeight: '500'
  },
  creatorBadge: { backgroundColor: '#EEF2FF',
    paddingHorizontal: 8,
  paddingVertical: 4,
    borderRadius: 4 },
  creatorText: {
    fontSize: 12,
  fontWeight: '500',
    color: '#4F46E5' }
  agreementTitle: { fontSize: 18,
    fontWeight: '600',
  color: '#1E293B',
    marginBottom: 12 },
  cardInfoRow: { flexDirection: 'row',
    marginBottom: 8 },
  infoItem: { flex: 1 }
  infoLabel: { fontSize: 12),
    color: '#94A3B8'),
  marginBottom: 2 }
  infoText: {
    fontSize: 14,
  color: '#334155')
  }
  })