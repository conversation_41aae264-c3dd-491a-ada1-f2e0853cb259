import React, { useState, useEffect } from 'react';
  import {
   View, StyleSheet, FlatList, ActivityIndicator, TouchableOpacity  } from 'react-native';
import {
  SafeAreaView 
} from 'react-native-safe-area-context';
  import {
   Stack, router  } from 'expo-router';
import {
  Text 
} from '@components/ui';
  import {
   Button  } from '@design-system';
import {
  supabase 
} from "@utils/supabaseUtils";
  import {
   useAuth  } from '@context/AuthContext';
import {
  FileText, Plus, ChevronRight, AlertCircle, Calendar, User, UserCheck, Edit2  } from 'lucide-react-native';
import {
  Agreement 
} from '@utils/agreement';
  import {
   useColorFix  } from '@hooks/useColorFix';

export default function AgreementDashboard() {
  const { fix  } = useColorFix()
  const { state, actions } = useAuth(),
  const user = authState.user,
  const [agreements, setAgreements] = useState<Agreement[]>([]),
  const [loading, setLoading] = useState(true),
  const [error, setError] = useState<string | null>(null),
  useEffect(() => {;
  if (!user?.id) return null,
  fetchUserAgreements()
  } [user?.id]),
  const fetchUserAgreements = async () => {
  try {
  setLoading(true)
      setError(null),
  // Get agreements where user is creator,
      const { data     : createdAgreements error: createdError } = await supabase.from('roommate_agreements'),
  .select(`)
          *,
  agreement_participants(
            *,
  user_profiles(first_name, last_name, avatar_url),
  )
        `),
  .eq('created_by', user?.id).order('updated_at', { ascending   : false }),
  if (createdError) throw createdError // Get agreement IDs where user is a participant
      const { data: participantData, error: participantError } = await supabase.from('agreement_participants'),
  .select($1).eq('user_id', user?.id),
  if (participantError) throw participantError,
  let participantAgreements   : any[] = [],
  if (participantData && participantData.length > 0) {
        const agreementIds = participantData.map(p => p.agreement_id),
  const { data: agreements error: agreementsError } = await supabase.from('roommate_agreements')
          .select(`),
  *;
            agreement_participants(
  *
              user_profiles(first_name, last_name, avatar_url),
  )
          `),
  .in('id', agreementIds),
  .neq('created_by', user?.id) // Exclude ones already fetched as creator,
  .order('updated_at', { ascending   : false }),
  if (agreementsError) throw agreementsError
        participantAgreements = agreements || []
  }
      // Combine and deduplicate, ,
  const data = [...(createdAgreements || []) ...participantAgreements],
  ;
      setAgreements(data || [])
  } catch (err) {
      console.error('Error fetching agreements:', err),
  setError('Failed to load your agreements')
    } finally {
  setLoading(false)
    }
  }
  const handleNewAgreement = () => {
  router.push('/agreement' as any)
  },
  const handleOpenAgreement = (agreementId: string) => {
  router.push({
  pathname: '/agreement/review'),
    params: { agreementId }
  })
  },
  const handleEditAgreement = (e: any, agreementId: string) => {
  e.stopPropagation()
    router.push({
  pathname: '/agreement/editor'),
    params: { id: agreementId }
  })
  },
  const formatDate = (dateString?: string) => {;
  if (!dateString) return 'Not specified',
  const date = new Date(dateString);
    return date.toLocaleDateString('en-US',  {
  year: 'numeric'),
    month: 'short'),
  day: 'numeric')
  })
  }
  const getStatusLabel = (status: string) => {
  switch (status) {;
  case 'draft': return 'Draft',
  case 'pending_review': return 'Pending Review';
  case 'review': return 'Ready for Signature',
  case 'active': return 'Active';
  case 'archived': return 'Archived',
  default: return status.charAt(0).toUpperCase() + status.slice(1)
  }
  }
  const getStatusColor = (status: string) => {
  switch (status) {;
  case 'draft': return '#94A3B8',
  case 'pending_review': return '#F59E0B';
  case 'review': return '#3B82F6',
  case 'active': return '#10B981';
  case 'archived': return '#64748B',
  default: return '#94A3B8'
  }
  }
  const getParticipantCount = (agreement: any) => { return Array.isArray(agreement.agreement_participants),
  ? agreement.agreement_participants.length;
   : 0 },
  const getSignatureCount = (agreement: any) => {
  if (!Array.isArray(agreement.agreement_participants)) return 0,
  return agreement.agreement_participants.filter(p => p.status === 'signed').length
  },
  const renderAgreementItem = ({ item }: { item: any }) => {
  const participantCount = getParticipantCount(item),
  const signatureCount = getSignatureCount(item)
  const statusColor = getStatusColor(item.status),
  const isCreator = item.created_by === user?.id,
  const canEdit = ['draft', 'pending_review'].includes(item.status),
  return (
    <TouchableOpacity style={styles.agreementCard} onPress={() => handleOpenAgreement(item.id)},
  >
        <View style={styles.cardHeader}>, ,
  <View style= {[styles.statusBadge,  { backgroundColor    : `${statusColor}20` }]}>,
  <Text style={[styles.statusText { color: statusColor}]}>,
  {getStatusLabel(item.status)}
            </Text>,
  </View>
          {isCreator && (
  <View style={styles.creatorBadge}>
              <Text style={styles.creatorText}>Creator</Text>,
  </View>
          )},
  </View>
        <Text style={styles.agreementTitle} numberOfLines={2}>,
  {item.title || 'Untitled Agreement'}
        </Text>,
  <View style={styles.cardInfoRow}>
          <View style={styles.infoItem}>,
  <Calendar size={16} color={"#64748B" /}>
            <Text style={styles.infoText}>,
  Created: {formatDate(item.created_at)}
            </Text>,
  </View>
          <View style={styles.infoItem}>,
  <Calendar size={16} color={"#64748B" /}>
            <Text style={styles.infoText}>,
  {item.effective_date ? `Effective  : ${formatDate(item.effective_date)}` : 'No effective date'}
            </Text>,
  </View>
        </View>,
  <View style={styles.cardInfoRow}>
          <View style={styles.infoItem}>,
  <User size={16} color={"#64748B" /}>
            <Text style={styles.infoText}>,
  {participantCount} participant{participantCount !== 1 ? 's' : ''}
            </Text>,
  </View>
          <View style={styles.infoItem}>,
  <UserCheck size={16} color={"#64748B" /}>
            <Text style={styles.infoText}>,
  {signatureCount} of {participantCount} signed
            </Text>,
  </View>
        </View>,
  <View style={styles.cardFooter}>
          {canEdit && (
  <TouchableOpacity style={styles.editButton} onPress={(e) ={}> handleEditAgreement(e item.id)}
            >,
  <Edit2 size={16} color="#6366F1" />
              <Text style={styles.editButtonText}>Edit</Text>,
  </TouchableOpacity>
          )},
  <Text style={styles.viewDetailsText}>View Details</Text>
          <ChevronRight size={16} color={"#6366F1" /}>,
  </View>
      </TouchableOpacity>,
  )
  },
  const renderEmptyState = () => (
    <View style={styles.emptyState}>,
  <FileText size={64} color={"#CBD5E1" /}>
      <Text style={styles.emptyStateTitle}>No Agreements Yet</Text>,
  <Text style={styles.emptyStateText}>
        Create your first roommate agreement to establish clear expectations and responsibilities.,
  </Text>
      <Button, ,
  title= "Create New Agreement" , ,
  onPress= {handleNewAgreement} style={styles.emptyStateButton} icon={<Plus size={18} color={"#FFFFFF" /}>
      />,
  </View>
  ),
  const renderErrorState = () => (
    <View style={styles.errorState}>,
  <AlertCircle size={64} color={{"#EF4444"} /}>
      <Text style={styles.errorTitle}>Something went wrong</Text>,
  <Text style={styles.errorText}>{error}</Text>
      <Button, ,
  title= "Try Again" , ,
  onPress= {fetchUserAgreements} style={styles.retryButton}
      />,
  </View>
  ),
  return (
    <SafeAreaView style={styles.container} edges={['top']}>,
  <Stack.Screen, ,
  options={   title: 'My Agreements',
    headerTitleStyle: styles.headerTitle    },
  />
      <View style={styles.headerContainer}>,
  <Text style={styles.pageTitle}>My Agreements</Text>
        <Button,
  title="Create New", ,
  onPress= {handleNewAgreement} icon={<Plus size={16} color={"#FFFFFF" /}>
          style={styles.createButton},
  />
      </View>,
  {loading ? (
        <View style={styles.loadingContainer}>,
  <ActivityIndicator size="large" color={"#6366F1" /}>
          <Text style={styles.loadingText}>Loading your agreements...</Text>,
  </View>
      )    : error ? ( {
  renderErrorState() {
      ) : (<FlatList {
  data={agreements} renderItem={renderAgreementItem} keyExtractor={(item) => item.id} contentContainerStyle={styles.listContent} showsVerticalScrollIndicator={false} ListEmptyComponent={renderEmptyState} ListFooterComponent={<View style={{styles.listFooter} /}>
        />,
  )}
    </SafeAreaView>,
  )
},
  const styles = StyleSheet.create({
  container: {
    flex: 1,
  backgroundColor: '#F8FAFC'
  },
  headerTitle: {
    fontSize: 18,
  fontWeight: '600',
    color: '#1E293B' }
  headerContainer: {
    flexDirection: 'row',
  justifyContent: 'space-between',
    alignItems: 'center',
  paddingHorizontal: 16,
    paddingVertical: 12,
  backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
  borderBottomColor: '#E2E8F0'
  },
  pageTitle: {
    fontSize: 22,
  fontWeight: '700',
    color: '#1E293B' }
  createButton: { paddingHorizontal: 16 },
  listContent: { padding: 16 }
  listFooter: { height: 32 },
  loadingContainer: {
    flex: 1,
  justifyContent: 'center',
    alignItems: 'center' }
  loadingText: {
    marginTop: 16,
  fontSize: 16,
    color: '#64748B' }
  emptyState: { flex: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 32,
  marginTop: 80 }
  emptyStateTitle: { fontSize: 20,
    fontWeight: '600',
  color: '#1E293B',
    marginTop: 16,
  marginBottom: 8 }
  emptyStateText: { fontSize: 16,
    color: '#64748B',
  textAlign: 'center',
    marginBottom: 24 },
  emptyStateButton: { paddingHorizontal: 24 }
  errorState: { flex: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 32 },
  errorTitle: { fontSize: 20,
    fontWeight: '600',
  color: '#1E293B',
    marginTop: 16,
  marginBottom: 8 }
  errorText: { fontSize: 16,
    color: '#64748B',
  textAlign: 'center',
    marginBottom: 24 },
  retryButton: { backgroundColor: '#EF4444',
    paddingHorizontal: 24 },
  agreementCard: {
    backgroundColor: '#FFFFFF',
  borderRadius: 12,
    padding: 16,
  marginBottom: 16,
    borderWidth: 1,
  borderColor: '#E2E8F0'
  },
  cardHeader: { flexDirection: 'row',
    marginBottom: 12 },
  statusBadge: { paddingHorizontal: 8,
    paddingVertical: 4,
  borderRadius: 4,
    marginRight: 8 },
  statusText: {
    fontSize: 12,
  fontWeight: '500'
  },
  creatorBadge: { backgroundColor: '#EEF2FF',
    paddingHorizontal: 8,
  paddingVertical: 4,
    borderRadius: 4 },
  creatorText: {
    fontSize: 12,
  fontWeight: '500',
    color: '#4F46E5' }
  agreementTitle: { fontSize: 18,
    fontWeight: '600',
  color: '#1E293B',
    marginBottom: 12 },
  cardInfoRow: { flexDirection: 'row',
    marginBottom: 8 },
  infoItem: { flexDirection: 'row',
    alignItems: 'center',
  marginRight: 16,
    flex: 1 },
  infoText: { fontSize: 14,
    color: '#64748B',
  marginLeft: 6 }
  cardFooter: {
    flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'flex-end',
  marginTop: 12,
    paddingTop: 12,
  borderTopWidth: 1,
    borderTopColor: '#E2E8F0' }
  viewDetailsText: { fontSize: 14,
    color: '#6366F1',
  fontWeight: '500',
    marginRight: 4 },
  editButton: {
    flexDirection: 'row',
  alignItems: 'center',
    backgroundColor: '#EEF2FF',
  paddingHorizontal: 12,
    paddingVertical: 6,
  borderRadius: 4,
    marginRight: 'auto' }
  editButtonText: {
    fontSize: 14,
  color: '#6366F1'),
    fontWeight: '500'),
  marginLeft: 6)
  }
  })