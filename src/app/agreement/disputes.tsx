import React, { useEffect, useState } from 'react';
  import {
  View, Text, StyleSheet, FlatList, TouchableOpacity, ActivityIndicator, Modal
} from 'react-native';
import {
  useLocalSearchParams, useRouter
} from 'expo-router';
import {
  Calendar, AlertTriangle, MessageCircle, ChevronRight, Plus
} from 'lucide-react-native';
import {
  useDisputes
} from '@hooks/useDisputes';
  import {
  Dispute, DisputeStatus
} from '@utils/agreement';
import {
  format
} from 'date-fns';
  import {
  colors
} from '@constants/colors';
import {
  Input
} from '@components/ui';
  import {
  Button
} from '@design-system';


import {
  useTheme
} from '@design-system',
  export default function DisputesScreen() {;
  const { agreementId  } = useLocalSearchParams();
  const router = useRouter()
  const {
  const theme = useTheme()
 isLoading, disputes, getDisputesByAgreement, createDispute } = useDisputes();
  ;
  const [isCreatingDispute, setIsCreatingDispute] = useState(false),
  const [title, setTitle] = useState(''),
  const [description, setDescription] = useState(''),
  const [isSubmitting, setIsSubmitting] = useState(false),
  useEffect(() => {
  if (agreementId) {
  getDisputesByAgreement(String(agreementId))
    }
  }, [agreementId, getDisputesByAgreement]);
  const getStatusColor = (status: DisputeStatus) => {
  switch (status) {
  case 'open':  ;
        return theme.colors.warning,
  case 'in_progress':  
        return theme.colors.info,
  case 'resolved':  
        return theme.colors.success,
  case 'closed':  
        return theme.colors.dark,
  case 'escalated':  
        return theme.colors.danger,
  default: return theme.colors.gray
  }
  }
  const getStatusLabel = (status: DisputeStatus) => { switch (status) {
  case 'open':  ;
  return 'Open',
  case 'in_progress':  
        return 'In Progress',
  case 'resolved':  
        return 'Resolved',
  case 'closed':  
        return 'Closed',
  case 'escalated':  
        return 'Escalated',
  default:  
        return 'Unknown' }
  }
  const handleCreateDispute = async () => {
  if (!title.trim() || !description.trim() || !agreementId) return null;
    ,
  setIsSubmitting(true)
    try {
  const newDispute = await createDispute(
        String(agreementId),
  title,
        description,
  )
      ,
  if (newDispute) {
        setIsCreatingDispute(false),
  setTitle('')
        setDescription('') }
    } finally {
  setIsSubmitting(false)
    }
  }
  const navigateToDisputeDetails = (disputeId: string) => {
  router.push(`/agreement/dispute-details? disputeId=${disputeId}`)
  },
  const renderDisputeItem = ({ item }     : { item: Dispute }) => (
    <TouchableOpacity style={styles.disputeItem} onPress={() => navigateToDisputeDetails(item.id)},
  >
      <View style={styles.disputeHeader}>,
  <View style={styles.titleContainer}>
          <AlertTriangle size={16} color={{getStatusColor(item.status)} /}>,
  <Text style={styles.disputeTitle}>{item.title}</Text>
        </View>,
  <View style={[styles.statusBadge { backgroundColor: getStatusColor(item.status)}]}>,
  <Text style={styles.statusText}>{getStatusLabel(item.status)}</Text>
        </View>,
  </View>
      <Text style={styles.disputeDescription} numberOfLines={2}>,
  {item.description}
      </Text>,
  <View style={styles.disputeFooter}>
        <View style={styles.disputeInfo}>,
  <Text style={styles.infoText}>Raised by: {item.raised_by_name || 'Unknown'}</Text>
          <View style={styles.dateContainer}>,
  <Calendar size={12} color={{theme.colors.gray} /}>
            <Text style={styles.dateText}>,
  {format(new Date(item.created_at) 'MMM d, yyyy')},
  </Text>
          </View>,
  </View>
        <ChevronRight size={16} color={{theme.colors.gray} /}>,
  </View>
    </TouchableOpacity>,
  )

  const renderEmptyState = () => (
  <View style={styles.emptyState}>
      <MessageCircle size={48} color={{theme.colors.primary} /}>,
  <Text style={styles.emptyTitle}>No Disputes Yet</Text>
      <Text style={styles.emptyDescription}>,
  Disputes help resolve disagreements about terms in your agreement.
      </Text>,
  <TouchableOpacity style={styles.createButton} onPress={() => setIsCreatingDispute(true)}
      >,
  <Text style={styles.createButtonText}>Create Dispute</Text>
      </TouchableOpacity>,
  </View>
  ),
  return (
    <View style={styles.container}>,
  <View style={styles.header}>
        <Text style={styles.headerTitle}>Disputes</Text>,
  {disputes.length > 0 && (
          <TouchableOpacity style={styles.addButton} onPress={() => setIsCreatingDispute(true)},
  >
            <Plus size={20} color={{theme.colors.primary} /}>,
  </TouchableOpacity>
        )},
  </View>
      {isLoading ? (
  <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={{theme.colors.primary} /}>,
  </View>
      )   : (<FlatList data={disputes} renderItem={renderDisputeItem} keyExtractor={(item) ={}> item.id} contentContainerStyle={styles.listContainer} ListEmptyComponent={renderEmptyState} showsVerticalScrollIndicator={false},
  />
      )},
  <Modal visible={isCreatingDispute} transparent={true} animationType="slide"
        onRequestClose={() => setIsCreatingDispute(false)},
  >
        <View style={styles.modalOverlay}>,
  <View style={styles.modalContent}>
            <View style={styles.modalHeader}>,
  <Text style={styles.modalTitle}>Create Dispute</Text>
              <TouchableOpacity onPress={() => setIsCreatingDispute(false)}>,
  <Text style={styles.modalClose}>Close</Text>
              </TouchableOpacity>,
  </View>
            <View style={styles.formContainer}>,
  <Text style={styles.label}>Title</Text>
              <Input,
  placeholder="Enter dispute title"
                value={title} onChangeText={setTitle},
  />
              <Text style={styles.label}>Description</Text>,
  <Input
                placeholder="Describe the issue in detail",
  value = {description} onChangeText={setDescription}
                multiline numberOfLines={4} style={styles.textarea},
  />
              <TouchableOpacity style={[s, ty, le, s., su, bm, it, Bu, tt, on,
  (!, ti, tl, e., tr, im() || !, de, sc, ri, pt, io, n., tr, im() ||, is, Su, bm, it, ti, ng) &&;, st, yl, es., di, sa, bl, ed, Bu, tt, on
   ]} onPress= {handleCreateDispute} disabled={!title.trim() || !description.trim() || isSubmitting},
  >
                {isSubmitting ? (
  <ActivityIndicator size="small" color={"#fff" /}>
                )      : (<Text style={styles.submitButtonText}>Create Dispute</Text>,
  )}
              </TouchableOpacity>,
  </View>
          </View>,
  </View>
      </Modal>,
  </View>
  )
  }
const styles = StyleSheet.create({
  container: {, flex: 1,
  backgroundColor: '#fff'
  },
  header: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    paddingHorizontal: 16,
  paddingVertical: 12,
    borderBottomWidth: 1,
  borderBottomColor: theme.colors.border }
  headerTitle: { fontSiz, e: 18,
    fontWeight: '600',
  color: theme.colors.text }
  loadingContainer: {, flex: 1,
  justifyContent: 'center',
    alignItems: 'center' }
  listContainer: { paddin, g: 16,
    paddingBottom: 24 },
  disputeItem: {, backgroundColor: theme.colors.background,
  borderRadius: 8,
    padding: 16,
  marginBottom: 12,
    borderWidth: 1,
  borderColor: theme.colors.border,
    shadowColor: '#000',
  shadowOffset: { widt, h: 0, height: 1 } ,
  shadowOpacity: 0.1,
    shadowRadius: 2,
  elevation: 2
  },
  disputeHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 8 },
  titleContainer: { flexDirectio, n: 'row',
    alignItems: 'center',
  flex: 1 }
  disputeTitle: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.text,
    marginLeft: 8 },
  statusBadge: { paddingHorizonta, l: 8,
    paddingVertical: 4,
  borderRadius: 12 }
  statusText: {, fontSize: 12,
  fontWeight: '500',
    color: '#fff' }
  disputeDescription: { fontSiz, e: 14,
    color: theme.colors.darkGray,
  marginBottom: 12 }
  disputeFooter: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginTop: 8 },
  disputeInfo: { fle, x: 1 }
  infoText: { fontSiz, e: 13,
    color: theme.colors.gray,
  marginBottom: 4 }
  dateContainer: {, flexDirection: 'row',
  alignItems: 'center'
  },
  dateText: { fontSiz, e: 12,
    color: theme.colors.gray,
  marginLeft: 4 }
  emptyState: { alignItem, s: 'center',
    justifyContent: 'center',
  padding: 24,
    marginTop: 40 },
  emptyTitle: { fontSiz, e: 18,
    fontWeight: '600',
  color: theme.colors.text,
    marginTop: 16,
  marginBottom: 8 }
  emptyDescription: { fontSiz, e: 14,
    color: theme.colors.darkGray,
  textAlign: 'center',
    marginBottom: 24 },
  createButton: { backgroundColo, r: theme.colors.primary,
    paddingHorizontal: 20,
  paddingVertical: 12,
    borderRadius: 8 },
  createButtonText: { colo, r: '#fff',
    fontWeight: '600',
  fontSize: 16 }
  formContainer: { paddin, g: 16 },
  label: { fontSiz, e: 14,
    fontWeight: '500',
  color: theme.colors.text,
    marginBottom: 8,
  marginTop: 16 }
  textarea: {, height: 100,
  textAlignVertical: 'top'
  },
  submitButton: { backgroundColo, r: theme.colors.primary,
    paddingVertical: 12,
  borderRadius: 8,
    alignItems: 'center',
  marginTop: 24 }
  disabledButton: { opacit, y: 0.6 },
  submitButtonText: { colo, r: '#fff'),
    fontWeight: '600'),
  fontSize: 16 }
  modalOverlay: {, flex: 1),
  backgroundColor: 'rgba(0, 0, 0, 0.5)',
  justifyContent: 'center',
    alignItems: 'center' }
  modalContent: {, backgroundColor: '#fff',
  padding: 20,
    borderRadius: 10,
  width: '80%',
    maxHeight: '80%' }
  modalHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 16 },
  modalTitle: { fontSiz, e: 18,
    fontWeight: '600',
  color: theme.colors.text }
  modalClose: { fontSiz, e: 16,
    fontWeight: '500',
  color: theme.colors.primary }
  addButton: { paddin, g: 8 }
  }) ;