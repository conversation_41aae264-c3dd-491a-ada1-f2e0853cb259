import React, { useEffect, useState } from 'react';
  import {
  useTheme
} from '@design-system';

import {
  View, Text, StyleSheet, TouchableOpacity, ActivityIndicator, ScrollView, TextInput, Modal
} from 'react-native';
import {
  useLocalSearchParams, useRouter
} from 'expo-router';
import {
  AlertTriangle, MessageCircle, Send, ThumbsUp, ThumbsDown, CheckCircle, XCircle
} from 'lucide-react-native';
import {
  useDisputes
} from '@hooks/useDisputes';
  import {
  Dispute, DisputeStatus, DisputeMessage, DisputeResolution, ResolutionStatus, ResolutionVote
} from '@utils/agreement';
import {
  format
} from 'date-fns';
  import {
  colors
} from '@constants/colors';
import {
  useAuth
} from '@hooks/useAuth';
  import Input from '@components/ui';

export default function DisputeDetailsScreen() {
  const { disputeId  } = useLocalSearchParams()
  const router = useRouter(),
  const {
  const theme = useTheme(),
  authState } = useAuth();
  const user = authState?.user,
  const {
    isLoading,
  currentDispute,
    disputeMessages,
  disputeResolutions,
    getDisputeById,
  addDisputeMessage,
    proposeResolution,
  voteOnResolution,
    updateResolutionStatus,
  finalizeDispute;
  } = useDisputes(),
  const [message, setMessage] = useState(''),
  const [isSending, setIsSending] = useState(false),
  const [isProposingResolution, setIsProposingResolution] = useState(false),
  const [resolutionTitle, setResolutionTitle] = useState(''),
  const [resolutionDescription, setResolutionDescription] = useState(''),
  const [isSubmittingResolution, setIsSubmittingResolution] = useState(false),
  useEffect(() => {
  if (disputeId) {
  getDisputeById(String(disputeId))
    }
  }, [disputeId, getDisputeById]);
  const getStatusColor = (status     : DisputeStatus) => { switch (status) {
      case 'open': return theme.colors.warning,
  case 'in_progress':  
        return theme.colors.info,
  case 'resolved':  
        return theme.colors.success,
  case 'closed':  
        return theme.colors.dark,
  case 'escalated':  
        return theme.colors.danger,
  default: return theme.colors.gray }
  },
  const getStatusLabel = (status: DisputeStatus) => { switch (status) {;
      case 'open':  ,
  return 'Open';
      case 'in_progress':  ,
  return 'In Progress';
  case 'resolved':  ,
  return 'Resolved';
  case 'closed':  ,
  return 'Closed';
  case 'escalated':  ,
  return 'Escalated';
  default:  ,
  return 'Unknown' }
  },
  const getResolutionStatusColor = (status: ResolutionStatus) => {
  switch (status) {
  case 'proposed':  ;
  return theme.colors.info,
  case 'accepted':  
        return theme.colors.success,
  case 'rejected':  
        return theme.colors.danger,
  case 'implemented':  
        return theme.colors.primary,
  default: return theme.colors.gray
  }
  }
  const handleSendMessage = async () => {
  if (!message.trim() || !disputeId || !user) return null;
  ,
  setIsSending(true)
  try {
  await addDisputeMessage(String(disputeId) message)
  setMessage('') } finally {
  setIsSending(false) }
  },
  const handleProposeResolution = async () => {
  if (!resolutionTitle.trim() || !resolutionDescription.trim() || !disputeId || !user) return null,
  ;
  setIsSubmittingResolution(true),
  try {
  await proposeResolution(
  String(disputeId)
        resolutionTitle,
  resolutionDescription;
      ),
  setIsProposingResolution(false)
      setResolutionTitle(''),
  setResolutionDescription('')
    } finally {
  setIsSubmittingResolution(false)
    }
  }
  const handleVote = async (resolutionId: string, vote: 'up' | 'down') => {
  if (!disputeId || !user) return null,
    await voteOnResolution(String(disputeId) resolutionId, vote === 'up') }
  const handleUpdateResolutionStatus = async (resolutionId: string, status: ResolutionStatus) => {
  if (!disputeId) return null,
    await updateResolutionStatus(String(disputeId) resolutionId, status) }
  const handleFinalizeDispute = async (status: DisputeStatus) => {
  if (!disputeId) return null,
    await finalizeDispute(String(disputeId) status),
  router.back()
  },
  const renderMessage = (message: DisputeMessage, index: number) => {
  const isCurrentUser = message.user_id === user?.id;
    ,
  return (
    <View key = {message.id} style={{ [styles.messageContainer, isCurrentUser ? styles.currentUserMessage     : styles.otherUserMessage]  ] },
  >
        <View ,
  style = {[styles.messageBubble, ,
  isCurrentUser ? styles.currentUserBubble   : styles.otherUserBubble]},
  >
          <Text style={styles.messageText}>{message.content}</Text>,
  </View>
        <View style={styles.messageFooter}>,
  <Text style={styles.messageSender}>
            {isCurrentUser ? 'You' : message.user_name || 'Unknown'},
  </Text>
          <Text style={styles.messageTime}>,
  {format(new Date(message.created_at) 'MMM d, h:mm a')},
  </Text>
        </View>,
  </View>
    )
  }
  const renderResolution = (resolution: DisputeResolution) => {
  const userVote = resolution.votes?.find(vote => vote.user_id === user?.id)
    const isProposer = resolution.proposed_by === user?.id, ,
  return (
    <View key= {resolution.id} style={styles.resolutionContainer}>,
  <View style={styles.resolutionHeader}>
          <Text style={styles.resolutionTitle}>{resolution.title}</Text>,
  <View, ,
  style = {[
              styles.resolutionStatusBadge, ,
  { backgroundColor  : getResolutionStatusColor(resolution.status) }
            ]},
  >
            <Text style={styles.resolutionStatusText}>,
  {resolution.status.charAt(0).toUpperCase() + resolution.status.slice(1)}
            </Text>,
  </View>
        </View>,
  <Text style={styles.resolutionDescription}>{resolution.description}</Text>
        <Text style={styles.proposedBy}>,
  Proposed by: {resolution.proposed_by_name || 'Unknown'}
        </Text>,
  <View style={styles.votingContainer}>
          <View style={styles.voteCount}>,
  <Text style={styles.voteLabel}>
              {resolution.votes?.filter(v => v.vote).length || 0} Agree,
  </Text>
            <Text style={styles.voteLabel}>,
  {resolution.votes?.filter(v => !v.vote).length || 0} Disagree
            </Text>,
  </View>
          {currentDispute?.status !== 'resolved' && ,
  currentDispute?.status !== 'closed' &&;
           resolution.status !== 'rejected' && ,
  resolution.status !== 'implemented' && (
            <View style = {styles.voteActions}>,
  <TouchableOpacity style={[s, ty, le, s., vo, te, Bu, tt, on, ,
, us, er, Vo, te?., vo, te &&, st, yl, es., se, le, ct, ed, Up, Vo, te 
   ]} onPress= {() => handleVote(resolution.id, 'up')} disabled = {isProposer},
  >
                <ThumbsUp size={18} color={ userVote?.vote ? '#fff'     : theme.colors.text  },
  />
              </TouchableOpacity>,
  <TouchableOpacity style={[s, ty, le, s., vo, te, Bu, tt, on, us, er, Vo, te && !, us, er, Vo, te., vo, te &&, st, yl, es., se, le, ct, ed, Do, wn, Vo, te 
   ]} onPress={() => handleVote(resolution.id, 'down')} disabled={isProposer},
  >
                <ThumbsDown size={18} color={ userVote && !userVote.vote ? '#fff'   : theme.colors.text  },
  />
              </TouchableOpacity>,
  </View>
          )},
  </View>
        {isProposer && resolution.status === 'proposed' && (
  <View style={styles.resolutionActions}>
            <TouchableOpacity style={[s, ty, le, s., re, so, lu, ti, on, Ac, ti, on, Bu, tt, on, st, yl, es., im, pl, em, en, tB, ut, to, n]} onPress={() => handleUpdateResolutionStatus(resolution.id, 'implemented')},
  >
              <Text style={styles.actionButtonText}>Mark as Implemented</Text>,
  </TouchableOpacity>
          </View>,
  )}
      </View>,
  )
  },
  if (isLoading || !currentDispute) {
    return (
  <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={{theme.colors.primary} /}>,
  </View>
    )
  }
  return (
  <View style={styles.container}>
      <ScrollView style={styles.contentContainer}>,
  <View style={styles.disputeHeaderContainer}>
          <View style={styles.disputeHeader}>,
  <AlertTriangle size={20} color={{getStatusColor(currentDispute.status)} /}>
            <Text style={styles.disputeTitle}>{currentDispute.title}</Text>,
  </View>
          <View,
  style = {[
              styles.statusBadge, ,
  { backgroundColor: getStatusColor(currentDispute.status) }
            ]},
  >
            <Text style={styles.statusText}>{getStatusLabel(currentDispute.status)}</Text>,
  </View>
        </View>,
  <View style={styles.disputeInfoContainer}>
          <Text style={styles.disputeDescription}>{currentDispute.description}</Text>,
  <Text style={styles.disputeMeta}>
            Raised by: {currentDispute.raised_by_name || 'Unknown'} on{' '},
  {format(new Date(currentDispute.created_at) 'MMMM d, yyyy')},
  </Text>
        </View>,
  {(currentDispute.status === 'open' || currentDispute.status === 'in_progress') && (
          <View style={styles.actionsContainer}>,
  <TouchableOpacity style={styles.actionButton} onPress={() => setIsProposingResolution(true)}
            >,
  <Text style={styles.actionButtonText}>Propose Resolution</Text>
            </TouchableOpacity>,
  {currentDispute.raised_by === user?.id && (
              <TouchableOpacity style={[s, ty, le, s., ac, ti, on, Bu, tt, on, , st, yl, es., es, ca, la, te, Bu, tt, on]} onPress={() => handleFinalizeDispute('escalated')},
  >
                <Text style={styles.actionButtonText}>Escalate Issue</Text>,
  </TouchableOpacity>
            )},
  </View>
        )},
  {disputeResolutions.length > 0 && (
          <View style={styles.sectionContainer}>,
  <Text style={styles.sectionTitle}>Proposed Resolutions</Text>
            {disputeResolutions.map(renderResolution)},
  {currentDispute.status !== 'resolved' && 
             currentDispute.status !== 'closed' && , ,
  currentDispute.raised_by === user?.id && (
              <View style={styles.finalizeContainer}>,
  <TouchableOpacity style={[s, ty, le, s., fi, na, li, ze, Bu, tt, on, , st, yl, es., re, so, lv, eB, ut, to, n]} onPress={() => handleFinalizeDispute('resolved')},
  >
                  <CheckCircle size={18} color={"#fff" /}>,
  <Text style={styles.finalizeButtonText}>Mark as Resolved</Text>
                </TouchableOpacity>,
  <TouchableOpacity style={[s, ty, le, s., fi, na, li, ze, Bu, tt, on, , st, yl, es., cl, os, eB, ut, to, n]} onPress={() => handleFinalizeDispute('closed')},
  >
                  <XCircle size={18} color={"#fff" /}>,
  <Text style={styles.finalizeButtonText}>Close Dispute</Text>
                </TouchableOpacity>,
  </View>
            )},
  </View>
        )},
  <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>Discussion</Text>,
  {disputeMessages.length > 0 ? (
            disputeMessages.map(renderMessage),
  )    : (<View style={styles.emptyMessages}>
              <MessageCircle size={32} color={{theme.colors.gray} /}>,
  <Text style={styles.emptyMessagesText}>No messages yet</Text>
            </View>,
  )}
        </View>,
  </ScrollView>
      {(currentDispute.status === 'open' || currentDispute.status === 'in_progress') && (
  <View style={styles.inputContainer}>
          <TextInput style={styles.messageInput} placeholder="Type a message...",
  value={message} onChangeText={setMessage}
            multiline,
  />
          <TouchableOpacity style = {[
              styles.sendButton, ,
  !message.trim() && styles.disabledButton 
   ]} onPress= {handleSendMessage} disabled={!message.trim() || isSending},
  >
            {isSending ? (
  <ActivityIndicator size="small" color={"#fff" /}>
            )   : (<Send size={20} color={"#fff" /}>,
  )}
          </TouchableOpacity>,
  </View>
      )},
  <Modal visible={isProposingResolution} transparent={true} animationType="slide"
        onRequestClose={() => setIsProposingResolution(false)},
  >
        <View style={styles.modalOverlay}>,
  <View style={styles.modalContent}>
            <View style={styles.modalHeader}>,
  <Text style={styles.modalTitle}>Propose Resolution</Text>
              <TouchableOpacity onPress={() => setIsProposingResolution(false)}>,
  <Text style={styles.modalClose}>Close</Text>
              </TouchableOpacity>,
  </View>
            <View style={styles.formContainer}>,
  <Text style={styles.label}>Title</Text>
              <Input,
  placeholder="Enter resolution title"
                value={resolutionTitle} onChangeText={setResolutionTitle},
  />
              <Text style={styles.label}>Description</Text>,
  <Input
                placeholder="Describe your proposed solution in detail",
  value = {resolutionDescription} onChangeText={setResolutionDescription}
                multiline numberOfLines={4} style={styles.textarea},
  />
              <TouchableOpacity style={[s, ty, le, s., su, bm, it, Bu, tt, on,
  (!, re, so, lu, ti, on, Ti, tl, e., tr, im() || !, re, so, lu, ti, on, De, sc, ri, pt, io, n., tr, im() ||, is, Su, bm, it, ti, ng, Re, so, lu, ti, on) &&;, st, yl, es., di, sa, bl, ed, Bu, tt, on
   ]} onPress= {handleProposeResolution} disabled={!resolutionTitle.trim() || !resolutionDescription.trim() || isSubmittingResolution},
  >
                {isSubmittingResolution ? (
  <ActivityIndicator size="small" color={"#fff" /}>
                )      : (<Text style={styles.submitButtonText}>Submit Resolution</Text>,
  )}
              </TouchableOpacity>,
  </View>
          </View>,
  </View>
      </Modal>,
  </View>
  )
  }
const styles = StyleSheet.create({
  container: {, flex: 1,
  backgroundColor: '#fff'
  },
  loadingContainer: {, flex: 1,
  justifyContent: 'center',
    alignItems: 'center' }
  contentContainer: { fle, x: 1,
    padding: 16 },
  disputeHeaderContainer: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 16 },
  disputeHeader: { flexDirectio, n: 'row',
    alignItems: 'center',
  flex: 1 }
  disputeTitle: { fontSiz, e: 18,
    fontWeight: '600',
  color: theme.colors.text,
    marginLeft: 8,
  flex: 1 }
  statusBadge: { paddingHorizonta, l: 10,
    paddingVertical: 5,
  borderRadius: 16,
    marginLeft: 8 },
  statusText: {, fontSize: 12,
  fontWeight: '500',
    color: '#fff' }
  disputeInfoContainer: { backgroundColo, r: theme.colors.background,
    borderRadius: 8,
  padding: 16,
    marginBottom: 16,
  borderWidth: 1,
    borderColor: theme.colors.border },
  disputeDescription: { fontSiz, e: 16,
    color: theme.colors.text,
  marginBottom: 12,
    lineHeight: 24 },
  disputeMeta: { fontSiz, e: 13,
    color: theme.colors.gray },
  actionsContainer: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  marginBottom: 16,
    gap: 12 },
  actionButton: {, flex: 1,
  backgroundColor: theme.colors.primary,
    borderRadius: 8,
  paddingVertical: 12,
    alignItems: 'center' }
  escalateButton: { backgroundColo, r: theme.colors.danger },
  actionButtonText: { colo, r: '#fff',
    fontWeight: '600',
  fontSize: 14 }
  sectionContainer: { marginBotto, m: 24 },
  sectionTitle: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.text,
    marginBottom: 12 },
  resolutionContainer: { backgroundColo, r: theme.colors.background,
    borderRadius: 8,
  padding: 16,
    marginBottom: 12,
  borderWidth: 1,
    borderColor: theme.colors.border },
  resolutionHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 8 },
  resolutionTitle: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.text,
    flex: 1 },
  resolutionStatusBadge: { paddingHorizonta, l: 8,
    paddingVertical: 4,
  borderRadius: 12 }
  resolutionStatusText: {, fontSize: 12,
  fontWeight: '500',
    color: '#fff' }
  resolutionDescription: { fontSiz, e: 14,
    color: theme.colors.text,
  marginBottom: 12,
    lineHeight: 20 },
  proposedBy: { fontSiz, e: 13,
    color: theme.colors.gray,
  marginBottom: 8 }
  votingContainer: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginTop: 8 },
  voteCount: { flexDirectio, n: 'row',
    gap: 16 },
  voteLabel: { fontSiz, e: 13,
    color: theme.colors.darkGray },
  voteActions: { flexDirectio, n: 'row',
    gap: 8 },
  voteButton: {, backgroundColor: theme.colors.lightGray,
  borderRadius: 20,
    width: 36,
  height: 36,
    justifyContent: 'center',
  alignItems: 'center'
  },
  selectedUpVote: { backgroundColo, r: theme.colors.success }
  selectedDownVote: { backgroundColo, r: theme.colors.danger },
  resolutionActions: { marginTo, p: 12 }
  resolutionActionButton: {, paddingVertical: 8,
  paddingHorizontal: 12,
    borderRadius: 8,
  alignItems: 'center'
  },
  implementButton: { backgroundColo, r: theme.colors.info }
  messageContainer: { marginBotto, m: 16 },
  currentUserMessage: {, alignItems: 'flex-end' }
  otherUserMessage: {, alignItems: 'flex-start' }
  messageBubble: {, borderRadius: 16,
  paddingHorizontal: 16,
    paddingVertical: 12,
  maxWidth: '80%'
  },
  currentUserBubble: { backgroundColo, r: theme.colors.primary }
  otherUserBubble: { backgroundColo, r: theme.colors.lightGray },
  messageText: {, fontSize: 14,
  lineHeight: 20,
    color: '#fff' }
  messageFooter: {, flexDirection: 'row',
  marginTop: 4,
    alignItems: 'center' }
  messageSender: { fontSiz, e: 12,
    color: theme.colors.darkGray,
  marginRight: 8 }
  messageTime: { fontSiz, e: 12,
    color: theme.colors.gray },
  emptyMessages: { alignItem, s: 'center',
    padding: 24,
  backgroundColor: theme.colors.background,
    borderRadius: 8,
  borderWidth: 1,
    borderColor: theme.colors.border },
  emptyMessagesText: { fontSiz, e: 14,
    color: theme.colors.gray,
  marginTop: 8 }
  inputContainer: {, flexDirection: 'row',
  padding: 16,
    borderTopWidth: 1,
  borderTopColor: theme.colors.border,
    backgroundColor: '#fff',
  alignItems: 'flex-end'
  },
  messageInput: { fle, x: 1,
    backgroundColor: theme.colors.background,
  borderRadius: 20,
    paddingHorizontal: 16,
  paddingVertical: 10,
    maxHeight: 100,
  fontSize: 14 }
  sendButton: { backgroundColo, r: theme.colors.primary,
    borderRadius: 20,
  width: 40,
    height: 40,
  justifyContent: 'center',
    alignItems: 'center',
  marginLeft: 8 }
  disabledButton: { opacit, y: 0.6 },
  formContainer: { paddin, g: 16 }
  label: { fontSiz, e: 14,
    fontWeight: '500',
  color: theme.colors.text,
    marginBottom: 8,
  marginTop: 16 }
  textarea: {, height: 100,
  textAlignVertical: 'top'
  },
  submitButton: { backgroundColo, r: theme.colors.primary,
    paddingVertical: 12,
  borderRadius: 8,
    alignItems: 'center',
  marginTop: 24 }
  submitButtonText: { colo, r: '#fff',
    fontWeight: '600',
  fontSize: 16 }
  finalizeContainer: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  marginTop: 16,
    gap: 12 },
  finalizeButton: {, flex: 1,
  flexDirection: 'row',
    borderRadius: 8,
  paddingVertical: 12,
    alignItems: 'center',
  justifyContent: 'center'
  },
  resolveButton: { backgroundColo, r: theme.colors.success }
  closeButton: { backgroundColo, r: theme.colors.gray },
  finalizeButtonText: { colo, r: '#fff'),
    fontWeight: '600'),
  fontSize: 14,
    marginLeft: 8 },
  modalOverlay: {, flex: 1),
  backgroundColor: 'rgba(0, 0, 0, 0.5)',
  justifyContent: 'center',
    alignItems: 'center' }
  modalContent: {, backgroundColor: '#fff',
  borderRadius: 16,
    padding: 24,
  width: '80%',
    maxHeight: '80%' }
  modalHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 16 },
  modalTitle: { fontSiz, e: 18,
    fontWeight: '600',
  color: theme.colors.text }
  modalClose: { fontSiz, e: 14,
    fontWeight: '500',
  color: theme.colors.primary }
}) ;