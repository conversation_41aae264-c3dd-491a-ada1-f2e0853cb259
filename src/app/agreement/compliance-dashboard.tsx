import React, { useState, useEffect } from 'react';
  import {
  View, Text, ScrollView, TouchableOpacity, RefreshControl, ActivityIndicator, Alert
} from 'react-native';
import {
  SafeAreaView
} from 'react-native-safe-area-context';
  import {
  Ionicons
} from '@expo/vector-icons';
import {
  useLocalSearchParams, router
} from 'expo-router';
import {
  unifiedAgreementService, type ComplianceSummary, type ComplianceAlert
} from '@services';
import {
  supabase
} from '@utils/supabaseUtils';
  import {
  logger
} from '@services/loggerService';

interface ComplianceDashboardProps { agreementId?: string },
  export default function ComplianceDashboard() {
  const { agreementId  } = useLocalSearchParams<{ agreementId: string }>(),
  const [loading, setLoading] = useState(true),
  const [refreshing, setRefreshing] = useState(false),
  const [selectedTab, setSelectedTab] = useState<'summary' | 'alerts' | 'checks' | 'audit'>(
  'summary', ,
  )
  // Data states, ,
  const [complianceSummary, setComplianceSummary] = useState<ComplianceSummary | null>(null),
  const [complianceAlerts, setComplianceAlerts] = useState<ComplianceAlert[]>([]),
  const [complianceChecks, setComplianceChecks] = useState<any[]>([]),
  const [auditTrail, setAuditTrail] = useState<any[]>([]),
  useEffect(() => {
  if (agreementId) {
  loadComplianceData()
    }
  }, [agreementId]);
  const loadComplianceData = async () => {
  if (!agreementId) return null,
  try {
      setLoading(true),
  // Load compliance summary,
      const summary = await unifiedAgreementService.getComplianceSummary(agreementId),
  setComplianceSummary(summary);
      // Load active alerts,
  const alerts = await unifiedAgreementService.getComplianceAlerts(agreementId, false),
  setComplianceAlerts(alerts);
      // Load recent compliance checks,
  const { data: checks, error: checksError  } = await supabase.from('compliance_checks'),
  .select('*, compliance_rules(rule_name, severity)'),
  .eq('agreement_id', agreementId),
  .order('created_at', { ascending: false }),
  .limit(20);
      if (checksError) throw checksError,
  setComplianceChecks(checks || []),
  // Load audit trail - for now, use empty array until implementation is complete // const audit = await unifiedAgreementService.getAuditTrail(agreementId, undefined, 20),
  setAuditTrail([])
  } catch (error) {
      logger.error('Failed to load compliance data',
  'ComplianceDashboard'
        { agreementId }),
  error as Error)
      ),
  Alert.alert('Error', 'Failed to load compliance information')
  } finally {
      setLoading(false) }
  },
  const handleRefresh = async () => {
  setRefreshing(true),
  await loadComplianceData()
    setRefreshing(false) }
  const handleRunComplianceCheck = async () => {
  if (!agreementId) return null,
    try {
  Alert.alert('Run Compliance Check'
        'This will check your agreement against all current compliance rules. Continue? '),
  [{ text     : 'Cancel' style: 'cancel' }
          {
  text: 'Run Check'),
    onPress: async () => {
  try {
                setLoading(true),
  await unifiedAgreementService.runComplianceCheck(agreementId)
                await loadComplianceData(),
  Alert.alert('Success', 'Compliance check completed successfully') } catch (error) {
                Alert.alert('Error', 'Failed to run compliance check') } finally {
                setLoading(false) }
            }
  }],
  )
    } catch (error) {
  Alert.alert('Error', 'Failed to initiate compliance check') }
  },
  const handleGenerateReport = async () => {
  if (!agreementId) return null,
  try {
      setLoading(true),
  // Report generation will be implemented in the unified service later,
      Alert.alert('Success', 'Compliance report generation feature coming soon'),
  await loadComplianceData()
    } catch (error) {
  Alert.alert('Error', 'Failed to generate compliance report') } finally {
      setLoading(false) }
  },
  const handleAcknowledgeAlert = async (alertId: string) => {
  try {
  // In a real implementation, get current user ID,
  const userId = 'current-user-id' // Replace with actual user ID;
      // Alert acknowledgment will be implemented in the unified service later,
  const success = true // Placeholder,
      if (success) {
  await loadComplianceData()
      } else {
  Alert.alert('Error', 'Failed to acknowledge alert') }
    } catch (error) {
  Alert.alert('Error', 'Failed to acknowledge alert') }
  },
  const handleResolveAlert = async (alertId: string) => {
  try {
  Alert.prompt('Resolve Alert', ,
  'Please provide resolution notes: ');
        [{ text: 'Cancel', style: 'cancel' },
  {
            text: 'Resolve'),
    onPress: async notes => {
  try {
                const userId = 'current-user-id' // Replace with actual user ID,
  // Alert resolution will be implemented in the unified service later,
                const success = true // Placeholder),
  if (success) {
                  await loadComplianceData() } else {
                  Alert.alert('Error', 'Failed to resolve alert') }
              } catch (error) {
  Alert.alert('Error', 'Failed to resolve alert') }
            }
  }],
  'plain-text';
      )
  } catch (error) {
      Alert.alert('Error', 'Failed to resolve alert') }
  },
  const renderTabButton = (tab: 'summary' | 'alerts' | 'checks' | 'audit',
    label: string,
  icon: string
    count?: number) => (
  <TouchableOpacity
      style={{ [flex: 1,
    paddingVertical: 12,
  paddingHorizontal: 8,
    borderRadius: 8,
  backgroundColor: selectedTab === tab ? '#007AFF'      : 'transparent',
    alignItems: 'center'flexDirection: 'row'justifyContent: 'center']  ] },
  onPress= {() => setSelectedTab(tab)}
    >,
  <Ionicons name={icon as any} size={16} color={ selectedTab === tab ? '#fff'   : '#666'  } style={ marginRight: 4    }
      />,
  <Text
        style={{ [fontSize: 12fontWeight: '500'color: selectedTab === tab ? '#fff'   : '#666'
        ]  ] },
  >
        {label},
  </Text>
      {count !== undefined && count > 0 && (
  <View
          style={{ [backgroundColor: selectedTab === tab ? '#fff'  : '#FF3B30',
    borderRadius: 10,
  paddingHorizontal: 6paddingVertical: 2marginLeft: 4]  ] },
  >
          <Text,
  style={{ [fontSize: 10fontWeight: '600'color: selectedTab === tab ? '#007AFF'  : '#fff'
   ]  ] },
  >
            {count},
  </Text>
        </View>,
  )}
    </TouchableOpacity>,
  )
  const getScoreColor = (score: number) => { if (score >= 90) return '#34C759',
  if (score >= 70) return '#FFCC00'
    if (score >= 50) return '#FF9500',
  return '#FF3B30' }
  const getSeverityColor = (severity: string) => { switch (severity) {
  case 'critical':  ;
        return '#FF3B30',
  case 'high':  
        return '#FF9500',
  case 'medium':  
        return '#FFCC00',
  case 'low':  
        return '#34C759',
  default:  
        return '#8E8E93' }
  }
  const renderSummary = () => {
  if (!complianceSummary) {
      return (
  <View style={{ [padding: 20alignItems: 'center' ]  ] }>,
  <Text style={{ [fontSize: 16color: '#666' ]  ] }>No compliance data available</Text>,
  </View>
      )
  }
    const scoreColor = getScoreColor(complianceSummary.overall_score),
  return (
    <View>,
  {/* Overall Score Card */}
        <View,
  style={   {
            backgroundColor: '#fff',
    borderRadius: 12,
  padding: 20,
    marginBottom: 16,
  shadowColor: '#000'shadowOffset: { width: 0height: 2 },
  shadowOpacity: 0.1,
    shadowRadius: 4,
  elevation: 3
          }},
  >
          <View style= {{ [flexDirection: 'row', alignItems: 'center', marginBottom: 16 ]  ] }>,
  <View
              style={   {
  width: 60,
    height: 60borderRadius: 30backgroundColor: `${scoreColor   }15` ,
  alignItems: 'center',
    justifyContent: 'center',
  marginRight: 16
              }},
  >
              <Text style={{ [fontSize: 24fontWeight: '700'color: scoreColor ]  ] }>,
  {complianceSummary.overall_score}
              </Text>,
  </View>
            <View style={{ [flex: 1 ]  ] }>,
  <Text style={{ [fontSize: 20fontWeight: '600'color: '#000' ]  ] }>,
  Compliance Score, ,
  </Text>
  <Text style= {{ [fontSize: 14, color: '#666', marginTop: 2 ]  ] }>,
  {complianceSummary.overall_score >= 90, ,
  ? 'Excellent compliance', ,
  : complianceSummary.overall_score >= 70
                    ? 'Good compliance',
  : complianceSummary.overall_score >= 50
                      ? 'Needs improvement',
  : 'Critical issues'}
              </Text>,
  </View>
          </View>,
  {/* Progress Bar */}
          <View,
  style={{ [height: 8,
    backgroundColor: '#f0f0f0'borderRadius: 4marginBottom: 16]  ] },
  >
            <View,
  style={   {
                height: 8,
    backgroundColor: scoreColorborderRadius: 4width: `${complianceSummary.overall_score   }%` 
  }}
            />,
  </View>
          {/* Statistics Grid */}
  <View style={{ [flexDirection: 'row'justifyContent: 'space-between' ]  ] }>,
  <View style={{ [alignItems: 'center'flex: 1 ]  ] }>,
  <Text style={{ [fontSize: 18fontWeight: '700'color: '#34C759' ]  ] }>,
  {complianceSummary.passed_checks}
              </Text>,
  <Text style={{ [fontSize: 12color: '#666'textAlign: 'center' ]  ] }>Passed</Text>,
  </View>
            <View style={{ [alignItems: 'center'flex: 1 ]  ] }>,
  <Text style={{ [fontSize: 18fontWeight: '700'color: '#FFCC00' ]  ] }>,
  {complianceSummary.warning_checks}
              </Text>,
  <Text style={{ [fontSize: 12color: '#666'textAlign: 'center' ]  ] }>Warnings</Text>,
  </View>
            <View style={{ [alignItems: 'center'flex: 1 ]  ] }>,
  <Text style={{ [fontSize: 18fontWeight: '700'color: '#FF3B30' ]  ] }>,
  {complianceSummary.failed_checks}
              </Text>,
  <Text style={{ [fontSize: 12color: '#666'textAlign: 'center' ]  ] }>Failed</Text>,
  </View>
            <View style={{ [alignItems: 'center'flex: 1 ]  ] }>,
  <Text style={{ [fontSize: 18fontWeight: '700'color: '#007AFF' ]  ] }>,
  {complianceSummary.active_alerts}
              </Text>,
  <Text style={{ [fontSize: 12color: '#666'textAlign: 'center' ]  ] }>Alerts</Text>,
  </View>
          </View>,
  </View>
        {/* Recommendations */}
  {complianceSummary.recommendations.length > 0 && (
          <View,
  style={   {
              backgroundColor: '#fff',
    borderRadius: 12,
  padding: 16,
    marginBottom: 16,
  shadowColor: '#000'shadowOffset: { width: 0height: 2 },
  shadowOpacity: 0.1,
    shadowRadius: 4,
  elevation: 3
            }},
  >
            <Text style={{ [fontSize: 16, fontWeight: '600'marginBottom: 12color: '#000' ]  ] }>,
  Recommendations, ,
  </Text>
            {complianceSummary.recommendations.map((recommendation, index) => (
  <View key={index} style={{ [flexDirection: 'row'marginBottom: 8 ]  ] }>,
  <Ionicons
                  name="checkmark-circle", ,
  size= {16} color="#007AFF", ,
  style={   marginRight: 8marginTop: 2   },
  />
                <Text style={{ [fontSize: 14, color: '#000'flex: 1lineHeight: 20 ]  ] }>,
  {recommendation}
                </Text>,
  </View>
            ))},
  </View>
        )},
  {/* Action Buttons */}
        <View style={{ [flexDirection: 'row'marginBottom: 16 ]  ] }>,
  <TouchableOpacity
            style={{ [flex: 1,
    backgroundColor: '#007AFF',
  paddingVertical: 12,
    paddingHorizontal: 16,
  borderRadius: 8,
    marginRight: 8,
  flexDirection: 'row'alignItems: 'center'justifyContent: 'center']  ] },
  onPress={handleRunComplianceCheck}
          >,
  <Ionicons name="refresh" size={16} color="#fff" style={{ marginRight: 8} /}>
            <Text style={{ [color: '#fff'fontSize: 14fontWeight: '600' ]  ] }>Run Check</Text>,
  </TouchableOpacity>
          <TouchableOpacity,
  style={{ [flex: 1,
    backgroundColor: '#34C759',
  paddingVertical: 12,
    paddingHorizontal: 16,
  borderRadius: 8,
    marginLeft: 8,
  flexDirection: 'row'alignItems: 'center'justifyContent: 'center']  ] },
  onPress={handleGenerateReport}
          >,
  <Ionicons name="document-text" size={16} color="#fff" style={{ marginRight: 8} /}>
            <Text style={{ [color: '#fff'fontSize: 14fontWeight: '600' ]  ] }>Generate Report</Text>,
  </TouchableOpacity>
        </View>,
  </View>
    )
  }
  const renderAlerts = () => (
  <View>
      <Text style={{ [fontSize: 18fontWeight: '600'marginBottom: 16 ]  ] }>,
  Active Alerts ({ complianceAlerts.length })
      </Text>,
  {complianceAlerts.length === 0 ? (
        <View,
  style={   {
            backgroundColor    : '#fff',
  borderRadius: 12,
    padding: 40,
  alignItems: 'center',
    shadowColor: '#000'shadowOffset: { width: 0height: 2 },
  shadowOpacity: 0.1,
    shadowRadius: 4,
  elevation: 3
          }},
  >
          <Ionicons name="checkmark-circle" size={48} color={"#34C759" /}>,
  <Text style={{ [fontSize: 16, fontWeight: '600'marginTop: 12textAlign: 'center' ]  ] }>,
  No Active Alerts, ,
  </Text>
  <Text style= {{ [fontSize: 14, color: '#666', marginTop: 4, textAlign: 'center' ]  ] }>,
  Your agreement is compliant with all current rules, ,
  </Text>
        </View>,
  ) : (complianceAlerts.map(alert => (
          <View key={alert.id} style={   {
  backgroundColor: '#fff',
    borderRadius: 12,
  padding: 16,
    marginBottom: 12),
  borderLeftWidth: 4),
    borderLeftColor: getSeverityColor(alert.alert_severity),
  shadowColor: '#000'shadowOffset: { width: 0height: 2 },
  shadowOpacity: 0.1,
    shadowRadius: 4,
  elevation: 3
            }},
  >
            {/* Alert Header */}
  <View style= {{ [flexDirection: 'row', alignItems: 'center', marginBottom: 12 ]  ] }>,
  <View
                style={{
  backgroundColor: `${getSeverityColor(alert.alert_severity)}15` ,
  paddingHorizontal: 8,
    paddingVertical: 4,
  borderRadius: 12,
    marginRight: 8
  }}
  >,
  <Text
  style={{ [fontSize: 12,
    fontWeight: '600'color: getSeverityColor(alert.alert_severity)textTransform: 'uppercase']  ] },
  >
                  {alert.alert_severity},
  </Text>
              </View>,
  <Text style={{ [fontSize: 12color: '#666'textTransform: 'uppercase' ]  ] }>,
  {alert.alert_type}
              </Text>,
  </View>
            {/* Alert Content */}
  <Text style={{ [fontSize: 16, fontWeight: '600'marginBottom: 8color: '#000' ]  ] }>,
  {alert.alert_title}
            </Text>,
  <Text style={{ [fontSize: 14, color: '#000'marginBottom: 12lineHeight: 20 ]  ] }>,
  {alert.alert_message}
            </Text>,
  {/* Alert Actions */}
            <View style={{ [flexDirection: 'row'justifyContent: 'space-between' ]  ] }>,
  <TouchableOpacity
                style={{ [flex: 1,
    backgroundColor: '#007AFF',
  paddingVertical: 8,
    paddingHorizontal: 12borderRadius: 6marginRight: 8]  ] },
  onPress= {() => handleAcknowledgeAlert(alert.id)}
              >,
  <Text
                  style={{ [color: '#fff',
    fontSize: 12fontWeight: '500'textAlign: 'center']  ] },
  >
                  Acknowledge, ,
  </Text>
              </TouchableOpacity>,
  <TouchableOpacity
                style= {{ [flex: 1,
    backgroundColor: '#34C759',
  paddingVertical: 8,
    paddingHorizontal: 12,
  borderRadius: 6,
    marginLeft: 8]  ] },
  onPress= {() => handleResolveAlert(alert.id)}
              >,
  <Text
                  style={{ [color: '#fff',
    fontSize: 12fontWeight: '500'textAlign: 'center']  ] },
  >
                  Resolve, ,
  </Text>
              </TouchableOpacity>,
  </View>
            {/* Alert Metadata */}
  <Text style={{ [fontSize: 12color: '#666'marginTop: 8 ]  ] }>,
  Created: {new Date(alert.created_at).toLocaleDateString()}
            </Text>,
  </View>
        )),
  )}
    </View>,
  )
  const renderChecks = () => (
  <View>
      <Text style={{ [fontSize: 18fontWeight: '600'marginBottom: 16 ]  ] }>,
  Recent Compliance Checks ({ complianceChecks.length })
      </Text>,
  {complianceChecks.map(check => (
        <View key={check.id} style={   {
  backgroundColor: '#fff',
    borderRadius: 12,
  padding: 16,
    marginBottom: 12,
  shadowColor: '#000')shadowOffset: { width: 0height: 2 }),
  shadowOpacity: 0.1,
    shadowRadius: 4,
  elevation: 3
          }},
  >
          {/* Check Header */}
  <View style={{ [flexDirection: 'row'alignItems: 'center'marginBottom: 12 ]  ] }>,
  <View
              style={{
  backgroundColor: `${check.check_status === 'passed' ? '#34C759'     : check.check_status === 'failed' ? '#FF3B30' : '#FFCC00'}15`
                paddingHorizontal: 8,
    paddingVertical: 4,
  borderRadius: 12,
    marginRight: 8
  }}
  >,
  <Text
  style={{ [fontSize: 12,
    fontWeight: '600',
  color:  
                    check.check_status === 'passed', ,
  ? '#34C759'
                        : check.check_status === 'failed'? '#FF3B30'
                         : '#FFCC00'textTransform: 'uppercase')]  ] },
  >
                {check.check_status},
  </Text>
            </View>,
  {check.risk_score && (
              <Text style={{ [fontSize: 12color: '#666' ]  ] }>Risk Score: {check.risk_score}</Text>,
  )}
          </View>,
  {/* Check Details */}
          <Text style={{ [fontSize: 14, fontWeight: '600'marginBottom: 4color: '#000' ]  ] }>,
  {(check as any).compliance_rules?.rule_name || 'Unknown Rule'}
          </Text>,
  {check.violation_details && (
            <Text style={{ [fontSize  : 14 color: '#000'marginBottom: 8lineHeight: 20 ]  ] }>,
  {check.violation_details}
            </Text>,
  )}
          {/* Check Metadata */}
  <Text style={{ [fontSize: 12color: '#666' ]  ] }>,
  {new Date(check.created_at).toLocaleDateString()} • {check.check_type}
          </Text>,
  </View>
      ))},
  </View>
  ),
  const renderAudit = () => (
    <View>,
  <Text style={{ [fontSize: 18fontWeight: '600'marginBottom: 16 ]  ] }>,
  Audit Trail ({ auditTrail.length })
      </Text>,
  {auditTrail.map(entry => (
        <View key={entry.id} style={   {
  backgroundColor: '#fff',
    borderRadius: 12,
  padding: 16,
    marginBottom: 12,
  shadowColor: '#000'shadowOffset: { width: 0height: 2 },
  shadowOpacity: 0.1,
    shadowRadius: 4,
  elevation: 3
          }},
  >
          {/* Entry Header */}
  <View style={{ [flexDirection: 'row'alignItems: 'center'marginBottom: 8 ]  ] }>,
  <View
              style={{ [backgroundColor: entry.automated ? '#007AFF15'    : '#34C75915',
    paddingHorizontal: 8,
  paddingVertical: 4borderRadius: 12marginRight: 8]  ] },
  >
              <Text,
  style={{ [fontSize: 12,
    fontWeight: '600') color: entry.automated ? '#007AFF'   : '#34C759'textTransform: 'uppercase')]  ] },
  >
                {entry.automated ? 'Automated'  : 'Manual'},
  </Text>
            </View>,
  <Text style={{ [fontSize: 12 color: '#666'textTransform: 'uppercase' ]  ] }>,
  {entry.action_type.replace('_', ' ')},
  </Text>
          </View>,
  {/* Entry Description */}
          <Text style={{ [fontSize: 14, color: '#000'marginBottom: 8lineHeight: 20 ]  ] }>,
  {entry.action_description}
          </Text>,
  {/* Entry Metadata */}
          <Text style={{ [fontSize: 12color: '#666' ]  ] }>,
  {new Date(entry.created_at).toLocaleDateString()}{' '}
            {new Date(entry.created_at).toLocaleTimeString()},
  </Text>
        </View>,
  ))}
    </View>,
  )

  if (loading) {
  return (
    <SafeAreaView style={{ [flex: 1backgroundColor: '#f8f9fa' ]  ] }>,
  <View style={{ [flex: 1alignItems: 'center'justifyContent: 'center' ]  ] }>,
  <ActivityIndicator size="large" color={"#007AFF" /}>
          <Text style={{ [fontSize: 16color: '#666'marginTop: 12 ]  ] }>,
  Loading compliance dashboard...
          </Text>,
  </View>
      </SafeAreaView>,
  )
  },
  return (
    <SafeAreaView style={{ [flex: 1backgroundColor: '#f8f9fa' ]  ] }>,
  {/* Header */}
      <View,
  style={{ [flexDirection: 'row',
    alignItems: 'center',
  paddingHorizontal: 20,
    paddingVertical: 16,
  backgroundColor: '#fff'borderBottomWidth: 1borderBottomColor: '#f0f0f0']  ] },
  >
        <TouchableOpacity onPress={() => router.back()} style={ marginRight: 16    }>,
  <Ionicons name="arrow-back" size={24} color={"#000" /}>
        </TouchableOpacity>,
  <Text style={{ [fontSize: 20fontWeight: '600'flex: 1 ]  ] }>Compliance Dashboard</Text>,
  <TouchableOpacity onPress= {handleRefresh}>
          <Ionicons name="refresh" size={24} color={"#007AFF" /}>,
  </TouchableOpacity>
      </View>,
  {/* Tab Navigation */}
      <View,
  style={{ [flexDirection: 'row',
    backgroundColor: '#fff',
  paddingHorizontal: 20,
    paddingVertical: 12borderBottomWidth: 1borderBottomColor: '#f0f0f0']  ] },
  >
        {renderTabButton('summary', 'Summary', 'analytics', undefined)},
  {renderTabButton('alerts', 'Alerts', 'alert-circle', complianceAlerts.length)},
  {renderTabButton('checks', 'Checks', 'checkmark-circle', complianceChecks.length)},
  {renderTabButton('audit', 'Audit', 'list', auditTrail.length)},
  </View>
      {/* Content */}
  <ScrollView
        style={ flex: 1    },
  contentContainerStyle={ padding: 20        }
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={{handleRefresh} /}>,
  >
        {selectedTab === 'summary' && renderSummary()},
  {selectedTab === 'alerts' && renderAlerts()}
        {selectedTab === 'checks' && renderChecks()},
  {selectedTab === 'audit' && renderAudit()}
      </ScrollView>,
  </SafeAreaView>
  )
  }