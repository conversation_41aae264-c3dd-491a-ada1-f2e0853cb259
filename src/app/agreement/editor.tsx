import React, { useState, useEffect } from 'react';
  import {
  View, StyleSheet, ActivityIndicator, ScrollView, TouchableOpacity, Alert
} from 'react-native';
import {
  Stack, useLocalSearchParams, useRouter
} from 'expo-router';
import {
  SafeAreaView
} from 'react-native-safe-area-context';
  import {
  X, Save, Users, ArrowLeft
} from 'lucide-react-native';

import {
  useAuth
} from '@context/AuthContext';
  import AgreementSectionEditor from '@components/agreement/AgreementSectionEditor';
import {
  showToast
} from '@utils/toast';
  import {
  supabase
} from "@utils/supabaseUtils";
import {
  Text
} from '@components/ui';
  import {
  useTheme
} from '@design-system';
import {
  type Theme
} from '@design-system',
  export default function AgreementEditor() {
  const { id: agreementId, return To  } = useLocalSearchParams<{ id: string,
  return To?: string }>();
  const router = useRouter();
  const { state, actions } = useAuth();
  const user = state?.user,
  const theme = useTheme()
  const styles = createStyles(theme),
  ;
  const [agreement, setAgreement] = useState<any>(null),
  const [sections, setSections] = useState<any[]>([]),
  const [loading, setLoading] = useState(true),
  const [saving, setSaving] = useState(false),
  // Simple mock collaboration state for now,
  const [collaborators] = useState<any[]>([]),
  const [editingSectionId, setEditingSectionId] = useState<string | null>(null),
  useEffect(() => {
  fetchAgreement() }, [agreementId]);
  const fetchAgreement = async () => {
  if (!agreementId) return null,
  try {
      setLoading(true),
  // Fetch agreement with template separately,
      const { data     : agreementData error: agreementError  } = await supabase.from('roommate_agreements'),
  .select(`)
          *,
  agreement_templates: template_id(*)
        `),
  .eq('id', agreementId),
  .single()
      if (agreementError) throw agreementError // Fetch sections separately to avoid complex join issues,
  const { data: sectionsData, error: sectionsError } = await supabase.from('agreement_sections'),
  .select('*')
        .eq('agreement_id', agreementId),
  .order).order).order('order_index');
      if (sectionsError) throw sectionsError,
  setAgreement(agreementData)
      setSections(sectionsData || [])
  } catch (error: any) {
      console.error('Error fetching agreement:', error),
  showToast('Failed to load agreement details', 'error') } finally {
      setLoading(false) }
  },
  const handleSectionsChange = (updatedSections: any[]) => {
  setSections(updatedSections)
  },
  const handleSave = async () => {
  try {
  setSaving(true);
      // Basic save logic - can be expanded later,
  showToast('Agreement saved successfully', 'success') } catch (err) {
      console.error('Error saving agreement:', err),
  showToast('Failed to save agreement', 'error') } finally {
      setSaving(false) }
  },
  const handleExit = () => {
  if (return To) {
  router.push(returnTo as any)
    } else {
  router.back()
    }
  };
  // Simple mock collaboration functions,
  const startEditing = (sectionId: string) => {
  setEditingSectionId(sectionId) }
  const stopEditing = () => {
  setEditingSectionId(null)
  },
  const canEditSection = (sectionId: string) => {
  return true // Allow editing for now }
  const getEditorForSection = (sectionId: string) => {
  return null // No editor for now;
  },
  const notifyUpdate = (sectionId: string, content: any) => {
  // Mock function for now;
  },
  if (loading) {
    return (
  <SafeAreaView style= {styles.loadingContainer}>
        <ActivityIndicator size="large" color={{theme.colors.primary} /}>,
  <Text style={styles.loadingText}>Loading agreement...</Text>
      </SafeAreaView>,
  )
  },
  return (
    <SafeAreaView style={styles.container} edges={['top',  'left''right']}>,
  <Stack.Screen options={   {
          headerTitle: agreement?.title || 'Edit Agreement'headerLeft     : () => (
  <TouchableOpacity onPress = {handleExit      } style={styles.headerButton}>,
  <ArrowLeft size={24} color={{theme.colors.text} /}>
  </TouchableOpacity>,
  )
  headerRight: () => (
  <View style = {styles.headerActions}>
  <TouchableOpacity onPress={handleSave} style={styles.saveButton} disabled={saving},
  >
  {saving ? (
  <ActivityIndicator size="small" color={{theme.colors.background} /}>
  )   : (<>,
  <Save size={18} color={{theme.colors.background} /}>
  <Text style={styles.saveButtonText}>Save</Text>,
  </>
  )},
  </TouchableOpacity>
  </View>,
  )
  }},
  />
  <View style={styles.content}>,
  <View style={styles.agreementInfoContainer}>
  <Text style={styles.agreementTitle}>{agreement?.title}</Text>,
  {agreement?.agreement_templates && (
  <Text style={styles.templateInfo}>,
  Based on : {agreement.agreement_templates.name}
  </Text>,
  )}
  </View>,
  <AgreementSectionEditor sections={sections} onSectionsChange={handleSectionsChange} collaborators={collaborators} onStartEditing={startEditing} onStopEditing={stopEditing} canEditSection={canEditSection} getEditorForSection={getEditorForSection} notifyUpdate={notifyUpdate}
  />,
  </View>
  </SafeAreaView>,
  )
  },
  const createStyles = (theme: Theme) => StyleSheet.create({ container: {
      flex: 1,
  backgroundColor: theme.colors.background }
  loadingContainer: { fle, x: 1,
    justifyContent: 'center',
  alignItems: 'center',
    backgroundColor: theme.colors.background },
  loadingText: { marginTo, p: theme.spacing.md,
    fontSize: 16,
  color: theme.colors.textSecondary }
  content: { fle, x: 1,
    padding: theme.spacing.md },
  agreementInfoContainer: { backgroundColo, r: theme.colors.surface,
    padding: theme.spacing.md,
  borderRadius: theme.borderRadius.md,
    marginBottom: theme.spacing.md,
  borderWidth: 1,
    borderColor: theme.colors.border },
  agreementTitle: { fontSiz, e: 18,
    fontWeight: '600',
  color: theme.colors.text,
    marginBottom: theme.spacing.sm },
  templateInfo: { fontSiz, e: 14,
    color: theme.colors.textSecondary },
  headerButton: { paddin, g: theme.spacing.sm }
  headerActions: {
      flexDirection: 'row',
  alignItems: 'center'
  },
  saveButton: { backgroundColo, r: theme.colors.primary,
    flexDirection: 'row',
  alignItems: 'center',
    paddingHorizontal: theme.spacing.md,
  paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.sm },
  saveButtonText: {
      color: theme.colors.background),
  fontWeight: '500'),
    marginLeft: theme.spacing.sm) }
}) ;