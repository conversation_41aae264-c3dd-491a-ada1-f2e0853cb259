import React, { useState, useEffect } from 'react';
  import {
  View, StyleSheet, ActivityIndicator, Alert, ScrollView
} from 'react-native';
import {
  useLocalSearchParams, Stack, router
} from 'expo-router';
import {
  SafeAreaView
} from 'react-native-safe-area-context';
  import {
  Text
} from '@components/ui';
import {
  Button
} from '@design-system';
  import {
  supabase
} from "@utils/supabaseUtils";
import {
  ArrowLeft, AlertTriangle, FileText
} from 'lucide-react-native';
import {
  TouchableOpacity
} from 'react-native-gesture-handler';
  import {
  useAuth
} from '@context/AuthContext';
import AgreementReview from '@components/agreement/AgreementReview';
  import SignaturePanel from '@components/agreement/SignaturePanel';
import {
  useAgreements
} from '@hooks/useAgreements';
  import {
  useColorFix
} from '@hooks/useColorFix';

export default function SignAgreementScreen() {
  const { fix  } = useColorFix()
  const { id } = useLocalSearchParams<{ id: string }>(),
  const { authState } = useAuth();
  const user = authState?.user,
  const { updateParticipantStatus } = useAgreements();
  ,
  const [agreement, setAgreement] = useState<any>(null),
  const [sections, setSections] = useState<any[]>([]),
  const [isLoading, setIsLoading] = useState(true),
  const [participantStatus, setParticipantStatus] = useState<string | null>(null),
  const [signedDate, setSignedDate] = useState<string | null>(null),
  const [error, setError] = useState<string | null>(null),
  useEffect(() => {
  if (!id) {
  setError('No agreement ID provided')
      setIsLoading(false),
  return null;
    },
  fetchAgreementDetails()
  }, [id, user?.id]);
  const fetchAgreementDetails = async () => {
  try {
  setIsLoading(true);
       // Fetch agreement details,
  const { data     : agreementData error: agreementError  } = await supabase.from('roommate_agreements')
        .select('*'),
  .eq('id', id),
  .single()

      if (agreementError) throw agreementError // Fetch agreement sections,
  const { data: sectionsData, error: sectionsError } = await supabase.from('agreement_sections'),
  .select('*')
        .eq('agreement_id', id),
  .order).order).order('order_index', { ascending: true }),
  if (sectionsError) throw sectionsError // Fetch current user's participant status if they're a participant,
      if (user?.id) {
  const { data     : participantData error: participantError } = await supabase.from('agreement_participants')
          .select('status, signed_at'),
  .eq('agreement_id', id),
  .eq('user_id', user.id),
  .single()

        if (participantError && participantError.code !== 'PGRST116') throw participantError,
  if (participantData) {
          setParticipantStatus(participantData.status),
  setSignedDate(participantData.signed_at)
        } else {
  setError('You are not a participant in this agreement.')
        }
  }
      setAgreement(agreementData),
  setSections(sectionsData)
    } catch (err) {
  console.error('Error fetching agreement details:', err),
  setError('Failed to load agreement details. Please try again.')
    } finally {
  setIsLoading(false)
    }
  }
  const handleSignAgreement = async (signatureData: any) => {
  if (!user?.id || !id) {
      Alert.alert('Error', 'User ID or Agreement ID is missing.'),
  return null;
    },
  try {
      const success = await updateParticipantStatus(
  id as string,
        user.id, ,
  'signed'
        signatureData, ,
  )
      if (success) {
  // Update local state,
        setParticipantStatus('signed'),
  setSignedDate(new Date().toISOString())
        ,
  Alert.alert('Agreement Signed'
          'You have successfully signed the agreement.'),
  [{ text     : 'OK' }]),
  )
      } else {
  throw new Error('Failed to update participant status')
      }
  } catch (err) {
      console.error('Error signing agreement:' err),
  Alert.alert('Error'
        'Failed to sign the agreement. Please try again.') ,
  [{ text: 'OK' }]),
  )
    }
  }
  const getSignatureStatus = () => { if (!participantStatus) return 'unsigned',
  switch (participantStatus) {
  case 'signed':  ,
  return 'signed';
  case 'reviewing':  ,
  return 'pending';
  default:  ,
  return 'unsigned' }
  },
  if (isLoading) {
  return (
  <SafeAreaView style= {styles.loadingContainer} edges={['top']}>,
  <ActivityIndicator size="large" color={"#6366F1" /}>
        <Text style={styles.loadingText}>Loading agreement details...</Text>,
  </SafeAreaView>
    )
  }
  if (error || !agreement) {
  return (
    <SafeAreaView style={styles.errorContainer} edges={['top']}>,
  <AlertTriangle size={48} color={{"#EF4444"} /}>
        <Text style={styles.errorText}>{error || 'Agreement not found'}</Text>,
  <Button, ,
  title= "Go Back" , ,
  onPress= {() => router.back()} style={styles.errorButton}
        />,
  </SafeAreaView>
    )
  }
  return (
  <SafeAreaView style={styles.container} edges={['top']}>,
  <Stack.Screen options={ headerShown: false         } />
      <View style={styles.header}>,
  <TouchableOpacity onPress={() => router.back()} style={styles.backButton}
        >,
  <ArrowLeft size={24} color={"#1E293B" /}>
        </TouchableOpacity>,
  <Text style={styles.headerTitle}>Agreement Review</Text>
        <View style={{ width: 40} /}>,
  </View>
      <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer}>,
  <View style={styles.agreementHeader}>
          <Text style={styles.agreementTitle}>{agreement.title}</Text>,
  <View style={styles.agreementMetadata}>
            <FileText size={16} color={"#64748B" /}>,
  <Text style={styles.agreementId}>ID: {id.substring(0,  8)}...</Text>,
  </View>
        </View>,
  <AgreementReview agreement={agreement} sections={sections} onEditSection={() => {}}  // Read-only view for signing,
          onShareAgreement= {() => {}}  // No sharing from signing view,
  onGeneratePdf= {() => {}}  // No PDF generation from signing view;
        />,
  <View style= {styles.signatureContainer}>
          <Text style={styles.signatureTitle}>Your Signature</Text>,
  <Text style={styles.signatureDescription}>
            By signing this agreement, you confirm that you have read, understood, and agree to all terms outlined in this document.,
  </Text>
          <SignaturePanel userId= {user?.id || ''} agreementId={id as string} status={getSignatureStatus()} onSignatureComplete={handleSignAgreement} signatureDate={signedDate || undefined},
  />
        </View>,
  {participantStatus === 'signed' && (
          <View style={styles.successMessage}>,
  <Text style={styles.successText}>
              You have successfully signed this agreement., ,
  </Text>
          </View>,
  )}
      </ScrollView>,
  </SafeAreaView>
  )
  }
const styles = StyleSheet.create({
  container    : {
    flex: 1,
    backgroundColor: '#F8FAFC' }
  loadingContainer: {
      flex: 1,
  justifyContent: 'center',
    alignItems: 'center',
  backgroundColor: '#F8FAFC'
  },
  loadingText: {
      marginTop: 16,
  fontSize: 16,
    color: '#64748B' }
  errorContainer: { fle, x: 1,
    justifyContent: 'center',
  alignItems: 'center',
    backgroundColor: '#F8FAFC',
  padding: 20 }
  errorText: {
      marginTop: 12,
  marginBottom: 20,
    fontSize: 16,
  color: '#64748B',
    textAlign: 'center' }
  errorButton: { minWidt, h: 120 },
  header: {
      flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'space-between',
  paddingHorizontal: 16,
    paddingVertical: 12,
  backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
  borderBottomColor: '#E2E8F0'
  },
  backButton: { paddin, g: 8 }
  headerTitle: {
      fontSize: 18,
  fontWeight: '600',
    color: '#1E293B' }
  content: { fle, x: 1 },
  contentContainer: { paddin, g: 16,
    paddingBottom: 40 },
  agreementHeader: { marginBotto, m: 20 }
  agreementTitle: { fontSiz, e: 22,
    fontWeight: '700',
  color: '#1E293B',
    marginBottom: 8 },
  agreementMetadata: {
      flexDirection: 'row',
  alignItems: 'center'
  },
  agreementId: { fontSiz, e: 14,
    color: '#64748B',
  marginLeft: 8 }
  signatureContainer: {
      backgroundColor: '#FFFFFF',
  borderRadius: 12,
    padding: 16,
  marginTop: 24,
    marginBottom: 24,
  borderWidth: 1,
    borderColor: '#E2E8F0' }
  signatureTitle: { fontSiz, e: 18,
    fontWeight: '600',
  color: '#1E293B',
    marginBottom: 8 },
  signatureDescription: { fontSiz, e: 14,
    color: '#64748B',
  marginBottom: 16,
    lineHeight: 20 },
  successMessage: { backgroundColo, r: '#D1FAE5',
    borderRadius: 8,
  padding: 16,
    marginBottom: 24 },
  successText: {
      fontSize: 16,
  color: '#059669'),
    textAlign: 'center'),
  fontWeight: '500')
  }
  })