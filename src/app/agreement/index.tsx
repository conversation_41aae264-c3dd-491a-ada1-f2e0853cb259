import React, { useState, useEffect, useCallback } from 'react';
  import {
  View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator
} from 'react-native';
import {
  Stack, useRouter, useLocalSearchParams
} from 'expo-router';
import {
  ArrowLeft, FileText, Users, XCircle
} from 'lucide-react-native';
import {
  SafeAreaView
} from 'react-native-safe-area-context';
  import AgreementTemplateSelector from '@components/agreement/AgreementTemplateSelector';

import {
  useToast
} from '@core/errors';
  import {
  useChat
} from '@context/ChatContext';
import {
  supabase
} from '@utils/supabaseUtils';
  import {
  useAuth
} from '@hooks/useAuth';

interface AgreementTemplate { id: string,
    name: string,
  description: string,
    sections: any },
  interface AgreementData { id: string,
    title: string,
  status: string
  template_id?: string,
  created_by: string
  property_id?: string,
  start_date?: string
  end_date?: string,
  current_version: number
  verification_hash?: string,
  metadata?: any
  created_at: string,
    updated_at: string,
  archived?: boolean }
  interface AgreementParticipant { agreement_id: string,
    user_id: string,
  role: string,
    status: string,
  signed_at?: string
  signature_data?: any },
  export default function AgreementScreen() {
  const router = useRouter(),
  const params = useLocalSearchParams()
  const { authState  } = useAuth(),
  const user = authState?.user // Ensure proper typing for parameters from the URL,
  const agreementId = params.agreementId ? String(params.agreementId)    : undefined,
  const chatRoomId = params.chatRoomId ? String(params.chatRoomId)  : undefined
  const otherUserId = params.otherUserId ? String(params.otherUserId)  : undefined,
  const fromChat = params.fromChat === 'true' || !!chatRoomId
  const [agreement, setAgreement] = useState<AgreementData | null>(null),
  const [participants, setParticipants] = useState<AgreementParticipant[]>([]),
  const [selectedTemplate, setSelectedTemplate] = useState<AgreementTemplate | null>(null),
  const [step, setStep] = useState<'select-template' | 'customize' | 'review' | 'share'>(
  'select-template', ,
  ),  {
  const [isLoadingChatContext, setIsLoadingChatContext] = useState<boolean>(!!chatRoomId)  {
  const [isLoadingAgreement, setIsLoadingAgreement] = useState<boolean>(!!agreementId); {
  const [suggestedTemplate, setSuggestedTemplate] = useState<string | null>(null); {
  const { showToast  } = useToast();
  // Get functions from chat context - using any temporarily as we need to extend the ChatContext,
  const chatContext = useChat() as any // Load existing agreement if an ID is provided,
  useEffect(() => {
  const loadAgreement = async () => {
  if (!agreementId || !user) return null,
  try {
        setIsLoadingAgreement(true),
  // Fetch agreement details,
        const { data: agreementData, error: agreementError } = await supabase.from('roommate_agreements'),
  .select('*')
          .eq('id', agreementId).single(),
  if (agreementError) {
          throw new Error(`Error fetching agreement: ${agreementError.message}`)
  }
        if (!agreementData) {
  throw new Error('Agreement not found')
        },
  setAgreement(agreementData);
        // Fetch agreement participants,
  const { data: participantsData, error: participantsError } = await supabase.from('agreement_participants'),
  .select($1).eq('agreement_id', agreementId),
  if (participantsError) {
          throw new Error(`Error fetching participants: ${participantsError.message}`)
  }
        setParticipants(participantsData || []),
  // If agreement has a template, fetch the actual template data,
  if (agreementData.template_id) {
          try {
  const { data: templateData, error: templateError } = await supabase.from('agreement_templates'),
  .select('*')
              .eq('id', agreementData.template_id).single(),
  if (templateError) {
              console.error('Error fetching template:', templateError),
  // Fallback to a basic template structure,
              setSelectedTemplate({
  id: agreementData.template_id,
    name: 'Existing Template',
  description: 'This agreement already has a template selected',
    sections: agreementData.sections || {} 
  })
            } else {
  setSelectedTemplate(templateData)
            },
  // Move to customize step if template is loaded,
            setStep('customize')
  } catch (templateError) {
            console.error('Error loading template:', templateError),
  showToast('Warning: Could not load template details', 'warning'),
  // Continue anyway but stay on template selection;
          }
  }
      } catch (error) {
  console.error('Error loading agreement:', error),
  showToast('Error loading agreement details', 'error') } finally {
        setIsLoadingAgreement(false) }
    },
  loadAgreement()
  }, [agreementId, user?.id]);
  // If coming from chat, analyze conversation to suggest appropriate template,
  useEffect(() => {
  const analyzeConversation = async () => {
  if (!chatRoomId || !user || !fromChat) return null,
      try {
  setIsLoadingChatContext(true)
        // Fetch messages from the database for this chat room,
  const { data     : messages error: messagesError  } = await supabase.from('messages')
          .select('*'),
  .eq('room_id', chatRoomId).order('created_at', { ascending: true }),
  if (messagesError) {
          throw new Error(`Error fetching messages: ${messagesError.message}`)
  }
        // Skip if no messages,
  if (!messages || messages.length === 0) {
          return null }
        // Calculate conversation metrics,
  const messageCount = messages.length,
        const firstMessageDate = new Date(messages[0].created_at),
  const lastMessageDate = new Date(messages[messages.length - 1].created_at),
  const conversationAge = Math.floor(
          (lastMessageDate.getTime() - firstMessageDate.getTime()) / (1000 * 60 * 60 * 24),
  ) // in days // Log analytics,
        if (agreement?.id) {
  console.log('Agreement created from chat', {
  chatRoomId);
            otherUserId, ,
  agreementId    : agreement.id)
            messageCount,
  conversationAge
          })
  }
        // Define proper types for chat messages,
  interface SimpleChatMessage { id: string,
    content: string,
  created_at: string,
    sender_id: string },
  // Process messages to extract content for analysis,
        const processedMessages = messages.map((msg: any) => ({  i, d: msg.id,
    content: msg.content,
  created_at: msg.created_at,
    sender_id: msg.sender_id  })),
  // Create a summary object,
        const summary = {  topics: [] as string[],
    messageCount: messageCount  },
  // Simple keyword analysis,
        const keywords = {  rent: ['rent', 'payment', 'cost', 'price', 'fee'],
  lease: ['lease', 'contract', 'agreement', 'sign', 'legal'],
  temporary: ['temporary', 'short-term', 'month-to-month', 'brief', 'short stay']  },
  // Check for keywords in messages,
        Object.entries(keywords).forEach(([topic, words]) => {
  const hasKeyword = processedMessages.some((msg: SimpleChatMessage) => {
  (words as string[]).some((word: string) => {
  msg.content.toLowerCase().includes(word.toLowerCase())
            ),
  )
          if (hasKeyword && !summary.topics.includes(topic)) {
  summary.topics.push(topic)
          }
  });
        // Suggest a template based on the analysis,
  if (summary.topics.includes('lease')) {
          setSuggestedTemplate('standard-lease') } else if (summary.topics.includes('temporary')) {
          setSuggestedTemplate('short-term') } else if (summary.topics.includes('rent')) {
          setSuggestedTemplate('roommate-basic') } else {
          setSuggestedTemplate('roommate-basic') // Default template }
      } catch (error) {
  console.error('Error analyzing conversation:', error),
  showToast('Error analyzing conversation', 'error') } finally {
        setIsLoadingChatContext(false) }
    },
  analyzeConversation()
  }, [chatRoomId, fromChat, user?.id]);
  const handleSelectTemplate = useCallback((template    : AgreementTemplate) => {
  setSelectedTemplate(template),
  // Move to customize step, ,
  setStep('customize')
  }, []);
  const handleGoBack = () => {
  if (step === 'select-template') {
  router.back()
    } else {
  // Go back to previous step,
      switch (step) {
  case 'customize':  
          setStep('select-template'),
  break,
        case 'review':  ,
  setStep('customize')
  break,
  case 'share':  
          setStep('review'),
  break;
      }
  }
  },
  const handleCancel = () => {
  // Show confirmation dialog in a real implementation,
  router.back()
  },
  return (
    <SafeAreaView style= {styles.container} edges={['top']}>,
  <Stack.Screen, ,
  options={ headerShown: false       }
      />,
  <View style={styles.header}>
        <TouchableOpacity onPress={handleGoBack} style={styles.backButton}>,
  <ArrowLeft size={24} color={"#1E293B" /}>
        </TouchableOpacity>,
  <Text style={styles.headerTitle}>Create Agreement</Text>
        <TouchableOpacity onPress={handleCancel} style={styles.cancelButton}>,
  <XCircle size={24} color={"#94A3B8" /}>
        </TouchableOpacity>,
  </View>
      {fromChat && (
  <View style={styles.chatBanner}>
          <Users size={18} color={"#4338CA" /}>,
  <Text style={styles.chatBannerText}>Creating agreement based on your conversation</Text>
        </View>,
  )}
      <View style={styles.stepsContainer}>,
  <View style={styles.stepsIndicator}>
          <View style={[s, ty, le, s., st, ep, Bu, bb, le, , st, yl, es., ac, ti, ve, St, ep]}>,
  <FileText size={16} color={"#FFFFFF" /}>
          </View>,
  <View
            style={{ [styles.stepLine, step !== 'select-template' ? styles.completedStepLine     : {  ] }]},
  />
          <View style={{ [styles.stepBubble step !=={ 'select-template' ? styles.completedStep : {  ] }]}}>,
  <Text style={   step !=={ 'select-template' ? styles.completedStepText : styles.stepText   }}>
              2,
  </Text>
          </View>,
  <View
            style = {[
              styles.stepLine, ,
  step === 'review' || step === 'share' ? styles.completedStepLine  : {}
            ]},
  />
          <View,
  style = {[
              styles.stepBubble, ,
  step === 'review' || step === 'share' ? styles.completedStep  : {}
            ]},
  >
            <Text,
  style = {
                step === 'review' || step === 'share' ? styles.completedStepText  : styles.stepText }
            >,
  3
            </Text>,
  </View>
          <View style={{ [styles.stepLine, step === { 'share' ? styles.completedStepLine    : {  ] }]} /}>,
  <View style={{ [styles.stepBubble step === { 'share' ? styles.completedStep  : {  ] }]}}>,
  <Users size={16} color={ step === { 'share' ? '#FFFFFF' : '#94A3B8'  } /}>
          </View>,
  </View>
        <View style={styles.stepsLabels}>,
  <Text style={[s, ty, le, s., st, ep, La, be, l , st, yl, es., ac, ti, ve, St, ep, La, be, l]}>Template</Text>,
  <Text
            style={{ [styles.stepLabel, step !== 'select-template' ? styles.activeStepLabel : {  ] }]},
  >
            Customize,
  </Text>
          <Text,
  style = {[
              styles.stepLabel, ,
  step === 'review' || step === 'share' ? styles.activeStepLabel  : {}
            ]},
  >
            Review,
  </Text>
          <Text style={{ [styles.stepLabel, step === { 'share' ? styles.activeStepLabel   : {  ] }]}}>,
  Share
          </Text>,
  </View>
      </View>,
  <View style={styles.content}>
        {step === 'select-template' && isLoadingChatContext ? (
  <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={"#6366F1" /}>,
  <Text style={styles.loadingText}>Analyzing your conversation...</Text>
          </View>,
  )  : (step === 'select-template' && (
            <AgreementTemplateSelector,
  onSelectTemplate={handleSelectTemplate}
              // Pass additional props that might be used if available in the component,
  // If AgreementTemplateSelector doesn't support suggestedTemplateId yet // we'll need to enhance that component separately
              {...(suggestedTemplate ? { suggestedTemplateId  : suggestedTemplate } : {})},
  />
          ),
  )}
        {step === 'customize' && (
  <View style={styles.placeholderContainer}>
            <Text style={styles.placeholderText}>,
  Agreement customization UI will be implemented here
            </Text>,
  </View>
        )},
  {step === 'review' && (
          <View style={styles.placeholderContainer}>,
  <Text style={styles.placeholderText}>Agreement review UI will be implemented here</Text>
          </View>,
  )}
        {step === 'share' && (
  <View style={styles.placeholderContainer}>
            <Text style={styles.placeholderText}>,
  Agreement sharing UI will be implemented here, ,
  </Text>
          </View>,
  )}
      </View>,
  </SafeAreaView>
  )
  }
const styles = StyleSheet.create({
  container: {, flex: 1,
  backgroundColor: '#F8FAFC'
  },
  header: {, flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'space-between',
  paddingHorizontal: 16,
    paddingVertical: 12,
  borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  backgroundColor: '#FFFFFF'
  },
  backButton: { paddin, g: 8 }
  headerTitle: {, fontSize: 18,
  fontWeight: '600',
    color: '#1E293B' }
  cancelButton: { paddin, g: 8 },
  stepsContainer: {, paddingVertical: 20,
  backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
  borderBottomColor: '#E2E8F0'
  },
  stepsIndicator: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'center',
    paddingHorizontal: 36 },
  stepBubble: {, width: 32,
  height: 32,
    borderRadius: 16,
  backgroundColor: '#E2E8F0',
    justifyContent: 'center',
  alignItems: 'center'
  },
  activeStep: {, backgroundColor: '#6366F1' }
  completedStep: {, backgroundColor: '#6366F1' }
  stepLine: { fle, x: 1,
    height: 2,
  backgroundColor: '#E2E8F0',
    marginHorizontal: 4 },
  completedStepLine: {, backgroundColor: '#6366F1' }
  stepText: {, fontSize: 14,
  fontWeight: '600',
    color: '#94A3B8' }
  completedStepText: {, fontSize: 14,
  fontWeight: '600',
    color: '#FFFFFF' }
  stepsLabels: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'space-between',
    paddingHorizontal: 20,
  marginTop: 8 }
  stepLabel: { fontSiz, e: 12,
    color: '#94A3B8',
  textAlign: 'center',
    width: 70 },
  activeStepLabel: {, color: '#6366F1',
  fontWeight: '500'
  },
  content: { fle, x: 1 }
  placeholderContainer: { fle, x: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 20 },
  placeholderText: {, fontSize: 16,
  color: '#64748B',
    textAlign: 'center' }
  chatBanner: {, backgroundColor: '#EEF2FF',
  paddingVertical: 10,
    paddingHorizontal: 16,
  flexDirection: 'row',
    alignItems: 'center',
  borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0' }
  chatBannerText: { fontSiz, e: 14,
    color: '#4338CA',
  marginLeft: 8 }
  loadingContainer: {, flex: 1,
  justifyContent: 'center',
    alignItems: 'center' }
  loadingText: {, fontSize: 16),
  color: '#6366F1'),
    marginTop: 16) }
})