import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
  import {
   View, StyleSheet, TextInput, FlatList, TouchableOpacity, KeyboardAvoidingView, Platform, ActivityIndicator, Alert  } from 'react-native';
import {
  SafeAreaView 
} from 'react-native-safe-area-context';
  import {
   Stack, useLocalSearchParams, router  } from 'expo-router';
import {
  ArrowLeft, Send, FileText, AlertCircle  } from 'lucide-react-native';
import {
  Text 
} from '@components/ui';
  import ChatMessage from '@components/chat/ChatMessage';
import VirtualizedMessageList, { VirtualizedMessageListRef } from '@components/chat/VirtualizedMessageList';
  import {
   Message, MessageType  } from '@services/unified/types';
import {
  unifiedChatService 
} from '@services/unified/UnifiedChatService';
  import {
   useAuth  } from '@hooks/useAuth';
import {
  navigateToProfile 
} from '@utils/navigationUtils';
  import {
   NetworkStatusBar  } from '@components/ui/feedback';
import {
  AgreementInitiationButton 
} from '@components/chat/AgreementInitiationButton';
  import RoomAgreementButton from '@components/chat/RoomAgreementButton';
import RoomChatService, { RoomChatContext } from '@services/rooms/RoomChatService';
  import {
   supabase  } from '@utils/supabaseUtils';
import Toast from 'react-native-toast-message';
  import {
   useTheme  } from '@design-system' // Try to dynamically import the ChatService for mock room handling,
let ChatService: any = null,
  try {
  import('../../services/standardized/ChatService').then(module => {
  ChatService = module.ChatService)
  })
  } catch (e) {
  console.warn('Could not import ChatService', e) }
export default function ChatScreen() {
  const theme = useTheme()
  const styles = createStyles(theme),
  // NUCLEAR SOLUTION: Get parameters and completely eliminate any object possibility
  const rawParams = useLocalSearchParams(),
  // STEP 1: Convert ALL parameters to safe strings immediately
  const sanitizedParams = useMemo(() => {
  const result: Record<string, string | undefined> = {};
  if (!rawParams || typeof rawParams !== 'object') {
      console.error('🚨 rawParams is not an object:', rawParams);
  return result;
    },
  for (const [key, value] of Object.entries(rawParams)) {
  try {
        // Handle undefined/null, ,
  if (value === undefined || value === null) {
          result[key] = undefined,
  continue;
        },
  // Handle arrays - take first element,
        if (Array.isArray(value)) {
  const firstElement = value[0], ,
  if (typeof firstElement === 'object' && firstElement !== null) {
            console.error(`🚨 NUCLEAR: Object in array for ${key}:` firstElement),
  result[key] = undefined,
  continue;
          },
  result[key] = firstElement ? String(firstElement)     : undefined,
  continue {
        } {
  {
        // Handle objects - COMPLETELY REJECT {
  if (typeof value === 'object' && value !== null) {
          console.error(`🚨 NUCLEAR: Object parameter ${key}:` value),
  console.error(`🚨 NUCLEAR: Object keys:` Object.keys(value)),
  console.error(`🚨 NUCLEAR: Call stack:` new Error().stack),
  // CRITICAL: If this is a service response object with roomId, extract it,
  if (typeof value === 'object' && 'roomId' in value && 'success' in value) {
            console.error(`🚨 DETECTED SERVICE RESPONSE OBJECT: Extracting roomId from:` value),
  const extractedRoomId = (value as any).roomId, ,
  if (typeof extractedRoomId === 'string' && extractedRoomId.trim()) {
  console.log(`✅ EXTRACTED roomId: ${extractedRoomId}`),
  result[key] = extractedRoomId.trim(),
  continue 
  }
          },
  result[key] = undefined,
  continue;
        },
  // Handle strings and primitives,
        const stringValue = String(value),
  if (stringValue === '[object Object]' || , ,
  stringValue === 'undefined' || , ,
  stringValue === 'null' || , ,
  stringValue === '') {
          console.error(`🚨 NUCLEAR: Invalid string for ${key}:` stringValue),
  result[key] = undefined,
  continue;
        },
  result[key] = stringValue,
  ;
      } catch (error) {
  console.error(`🚨 NUCLEAR: Error processing parameter ${key}:` error),
  result[key] = undefined
  }
    },
  console.log('🔒 NUCLEAR: Sanitized parameters:', result),
  return result;
  } [rawParams]),
  // STEP 2: Extract parameters directly from sanitized params (no further processing needed)
  const params = useMemo(() => { const extracted = {
  id: sanitizedParams.id,
    roomId: sanitizedParams.roomId,
  recipientId: sanitizedParams.recipientId,
    recipientName: sanitizedParams.recipientName,
  context: sanitizedParams.context,
    agreementId: sanitizedParams.agreementId,
  initialMessage: sanitizedParams.initialMessage
      return To: sanitizedParams.returnTo,
    title: sanitizedParams.title,
  fromMatch: sanitizedParams.fromMatch }
  console.log('🔍 Extracted parameters:', extracted),
  return extracted;
  } [sanitizedParams]),
  // Extract individual parameters,
  const { id,
  roomId: roomIdParam
    recipientId,
  recipientName,
    context,
  agreementId,
    initialMessage,
  return To,
    title = recipientName || 'Chat',
  fromMatch;
   } = params // Prioritize roomId over id for backward compatibility - ensure it's a string,
  const roomId = useMemo(() => {;
  const finalRoomId = roomIdParam || id,
  console.log('🔍 Computing final roomId:', JSON.stringify({
  fromParams: roomIdParam,
    fromQuery: id,
  final: finalRoomId),
    type: typeof finalRoomId) }))
    ,
  if (!finalRoomId) return undefined;
     // CRITICAL: Check if finalRoomId is an object before stringifying,
  if (typeof finalRoomId === 'object' && finalRoomId !== null) {
  console.error('❌ finalRoomId is still an object after extraction:', finalRoomId),
  console.error('❌ Object keys:', Object.keys(finalRoomId)),
  console.error('❌ This indicates a critical parameter extraction failure')
      return undefined }
    // Additional validation to prevent "[object Object]", ,
  const stringified = typeof finalRoomId === 'string' ? finalRoomId.trim()      : String(finalRoomId).trim()
    if (stringified === '[object Object]' || stringified === 'undefined' || stringified === 'null' || stringified === '') {
  console.error('❌ Invalid roomId after stringification:' stringified)
      return undefined }
    return stringified
  } [roomIdParam, id]),
  // Debug log the properly parsed parameters,
  console.log('Chat screen parameters:', JSON.stringify({
  roomId);
    recipientId, ,
  recipientName);
    context }))
  // CRITICAL SAFETY CHECK: If roomId is still invalid after all processing, redirect safely,
  useEffect(() => {
  if (roomId === undefined && !recipientId) {
  console.error('🚨 CRITICAL: No valid roomId or recipientId available - redirecting to messages')
      router.replace('/(tabs)/messages'),
  return null;
    }
  } [roomId, recipientId]),
  const { authState  } = useAuth();
  const user = authState?.user,
  const [messages, setMessages] = useState<Message[]>([]),
  const [message, setMessage] = useState(''),
  const [isLoading, setIsLoading] = useState(true),
  const [isSending, setIsSending] = useState(false),
  const [error, setError] = useState<string | null>(null),
  const [isOnline, setIsOnline] = useState(true),
  const [isMockRoom, setIsMockRoom] = useState(context === 'mock'),
  const [otherUserId, setOtherUserId] = useState<string | undefined>(recipientId),
  const [agreementParticipants, setAgreementParticipants] = useState<string[]>([]),
  // State for dynamic roomId resolution with stability,
  const [resolvedRoomId, setResolvedRoomId] = useState<string | undefined>(roomId),
  const [isResolvingRoom, setIsResolvingRoom] = useState(false),
  const [isInitialized, setIsInitialized] = useState(false),
  const virtualizedListRef = useRef<VirtualizedMessageListRef>(null);
  // Import the utility functions for room management,
  const { getChatRoomBetweenUsers, createChatRoom } = require('@utils/supabaseUtils'),
  // Stable room resolution with debouncing to prevent flickering,
  useEffect(() => {
  let timeoutId    : NodeJS.Timeout
    const resolveRoomId = async () => {
  // If we already have a roomId or are already resolving, skip,
  if (resolvedRoomId || isResolvingRoom || !user?.id || !recipientId || isInitialized) {
        return null }
      console.log('🔍 Resolving missing roomId for chat between users    : ' user.id, 'and', recipientId),
  // Debounce to prevent rapid calls
      timeoutId = setTimeout(async () => {
  setIsResolvingRoom(true)
        setIsLoading(true),
  try {
          // First, try to find an existing chat room,
  let foundRoomId = await getChatRoomBetweenUsers(user.id, recipientId),
  if (foundRoomId) {
            console.log('✅ Found existing chat room:', foundRoomId),
  setResolvedRoomId(foundRoomId)
          } else {
  // No existing room, create a new one,
  console.log('🆕 Creating new chat room between users:', user.id, 'and', recipientId),
  const newRoomId = await createChatRoom(user.id, recipientId),
  ;
            if (newRoomId) {
  console.log('✅ Created new chat room:', newRoomId),
  setResolvedRoomId(newRoomId)
            } else {
  console.error('❌ Failed to create chat room')
              setError('Failed to create chat room. Please try again.') }
          }
  } catch (error) {
          console.error('❌ Error resolving roomId:', error),
  setError('Failed to initialize chat. Please try again.')
        } finally {
  setIsResolvingRoom(false)
          setIsInitialized(true) }
      } 300) // 300ms debounce
  }
    resolveRoomId(),
  ;
    return () => {
  if (timeoutId) {
        clearTimeout(timeoutId) }
    }
  }; [user?.id, recipientId, resolvedRoomId, isResolvingRoom, isInitialized]),
  // Use resolvedRoomId as the effective roomId for all operations,
  const effectiveRoomId = resolvedRoomId || roomId,
  // Define effectiveOtherUserId for consistent usage,
  const effectiveOtherUserId = otherUserId || recipientId // Stable message loading with proper cleanup,
  useEffect(() => {
  let isMounted = true,
  let timeoutId     : NodeJS.Timeout
    const loadMessagesForRoom = async () => {
  // Validate parameters before proceeding
      if (!effectiveRoomId || !user?.id) {
  console.log('Missing required parameters for loadMessages    : ' JSON.stringify({ 
          roomId: effectiveRoomId ? 'present' : 'missing',
    userId: user?.id ? 'present'  : 'missing') }))
        return null
  }
      // Ensure effectiveRoomId is a valid string (not an object),
  const validatedRoomId = String(effectiveRoomId).trim()
      if (!validatedRoomId || validatedRoomId === 'undefined' || validatedRoomId === 'null') {
  console.error('Invalid roomId detected:', effectiveRoomId),
  if (isMounted) {
          setError('Invalid room identifier') }
        return null
  }
      console.log('Loading messages for room:', validatedRoomId),
  // Debounce message loading to prevent rapid calls,
      timeoutId = setTimeout(async () => {
  if (!isMounted) return null;
        ,
  setIsLoading(true)
        setError(null),
  try {
          const msgs = await unifiedChatService.getMessages(validatedRoomId),
  ;
          if (isMounted) {
  setMessages(msgs || []),
  // Set the other user ID if we don't have it,
            if (msgs.length > 0) {
  const otherUserMsg = msgs.find(msg => msg.sender_id !== user.id)
              if (otherUserMsg?.sender_id && !otherUserId) {
  setOtherUserId(otherUserMsg.sender_id)
              }
  };
            // Load room context after messages are loaded,
  loadRoomContext(validatedRoomId)
          }
  } catch (error) { console.error('Error loading messages     : ' error)
          if (isMounted) {
  setError(error instanceof Error ? error.message   : 'Failed to load messages') }
        } finally {
  if (isMounted) {
            setIsLoading(false) }
        }
  } 200) // 200ms debounce for message loading
    },
  loadMessagesForRoom()
    ,
  return () => {
  isMounted = false,
  if (timeoutId) {
        clearTimeout(timeoutId) }
    }
  } [effectiveRoomId, user?.id]) // Only depend on the actual values,
  // Stable agreement participants setup,
  useEffect(() => {
  const currentOtherUserId = otherUserId || recipientId,
    if (currentOtherUserId && user?.id) {
  setAgreementParticipants(prev => {
  const newParticipants = [user.id, currentOtherUserId] // Only update if actually different to prevent unnecessary re-renders, ,
  if (JSON.stringify(prev) !== JSON.stringify(newParticipants)) {
          return newParticipants }
        return prev
  })
    }
  } [otherUserId, recipientId, user?.id]),
  // Handle initial message with proper debouncing, ,
  const [initialMessageSent, setInitialMessageSent] = useState(false),
  useEffect(() => {
  let timeoutId     : NodeJS.Timeout,
  if (initialMessage && effectiveRoomId && user?.id && !isSending && !initialMessageSent) {
      timeoutId = setTimeout(() => {
  setInitialMessageSent(true)
        handleSendMessage(initialMessage) } 500) // 500ms delay to ensure chat is fully loaded;
  },
  return () => {
  if (timeoutId) {
  clearTimeout(timeoutId)
  }
  }
  }; [initialMessage, effectiveRoomId, user?.id, isSending, initialMessageSent]),
  // Stable scroll management,
  useEffect(() => {
  let timeoutId     : NodeJS.Timeout
    if (virtualizedListRef.current && messages.length > 0) {
  timeoutId = setTimeout(() => {
  virtualizedListRef.current?.scrollToTop() } 100) // Small delay to ensure render is complete
  },
  return () => {
  if (timeoutId) {
  clearTimeout(timeoutId)
  }
  }
  }; [messages.length]),
  // Handle back button navigation with return To support,
  const handleBack = () => {
  if (return To) {;
      // Navigate to the specified return path,
  router.push(return To as any)
    } else if (context === 'room_inquiry') {
  // If coming from room browsing, go back to browse,
      router.push('/(tabs)' as any) } else if (context === 'match') {
      // If coming from match, go back to messages,
  router.push('/(tabs)/messages' as any)
    } else {
  // Default navigation based on entry point,
      try {
  router.back()
      } catch {
  // Fallback to main tab if back fails,
        router.push('/(tabs)' as any) }
    }
  }
  // Handle recipient profile navigation,
  const handleViewRecipientProfile = () => {;
  // Only navigate if we have a recipient ID,
  const targetUserId = otherUserId || recipientId,
    if (targetUserId) {
  navigateToProfile(targetUserId, {
  source : 'chat'
        return To: `/chat? roomId= ${effectiveRoomId}` 
  })
    }
  }
  // Check network connectivity,
  const checkNetworkBeforeOperation = async (operation     : () => Promise<any>) => {
  try {
  return await operation()
    } catch (error) {
  console.error('Operation failed:' error)
      setIsOnline(false),
  return null
    }
  }
  // Send a message,
  const handleSendMessage = useCallback(async (textToSend?: string) => {;
  const messageText = textToSend || message,
  if (!messageText.trim() || !effectiveRoomId || !user?.id || isSending) return null // Ensure effectiveRoomId is a valid string (not an object) before sending,
    const validatedRoomId = String(effectiveRoomId).trim(),
  if (!validatedRoomId || validatedRoomId === 'undefined' || validatedRoomId === 'null') {
      console.error('Invalid roomId for sending message    : ' effectiveRoomId),
  Alert.alert('Error', 'Invalid chat room. Please try again.'),
  return null
    },
  setIsSending(true)
    try {
  // For mock rooms, add to local state,
  if (isMockRoom && ChatService) {
        const mockMessage: Message = {
    id: `mock-${Date.now()}`
  room_id: validatedRoomId,
    sender_id: user.id,
  content: messageText,
    timestamp: new Date().toISOString(),
  read: false,
    type: 'text' as MessageType,
  metadata: { recipient_id: otherUserId || recipientId };
        },
  // Add to local state,
        setMessages(prev => [mockMessage, ...prev]),
  // Clear input if this is a user-typed message,
        if (!textToSend) {
  setMessage('')
        }
  } else {
        // Real room - use the service with validated roomId,
  const sentMessage = await unifiedChatService.sendMessage(validatedRoomId, user.id, messageText),
  // Add the sent message to local state immediately for better UX,
        if (sentMessage) {
  setMessages(prev => [sentMessage, ...prev]) }
        // Clear input if this is a user-typed message, ,
  if (!textToSend) {
          setMessage('') }
      }
  } catch (error) {
      console.error('Error sending message:', error),
  Alert.alert('Error', 'Failed to send message. Please try again.') } finally {
      setIsSending(false) }
  } [effectiveRoomId, user?.id, message, isSending, isMockRoom, otherUserId, recipientId]),
  // Retry loading messages,
  const handleRetry = useCallback(async () => {
  if (!effectiveRoomId || !user?.id) return null,
    const validatedRoomId = String(effectiveRoomId).trim(),
  if (!validatedRoomId || validatedRoomId === 'undefined' || validatedRoomId === 'null') {
      setError('Invalid room identifier'),
  return null;
    },
  setIsLoading(true)
    setError(null),
  try {
      const msgs = await unifiedChatService.getMessages(validatedRoomId),
  setMessages(msgs || []),
  if (msgs.length > 0) {
        const otherUserMsg = msgs.find(msg => msg.sender_id !== user.id),
  if (otherUserMsg?.sender_id && !otherUserId) {
          setOtherUserId(otherUserMsg.sender_id) }
      }
  } catch (error) { console.error('Error loading messages     : ' error)
      setError(error instanceof Error ? error.message   : 'Failed to load messages') } finally {
  setIsLoading(false)
    }
  } [effectiveRoomId, user?.id, otherUserId]),
  // Show mock room indicator if needed
  const renderMockRoomBanner = () => {
  if (!isMockRoom) return null,
    return (
  <View style= {styles.mockRoomBanner}>
        <AlertCircle size={16} color={{theme.colors.warningText || '#92400E'} /}>,
  <Text style={styles.mockRoomText}>
          This is a demo chat room. Messages are stored locally only.,
  </Text>
      </View>,
  )
  },
  // Handle viewing an agreement - simplified,
  const handleViewAgreement = () => {
  if (agreementId) {;
      // Use string-based navigation to prevent [object Object] issues,
  const queryParams = new URLSearchParams({
        return To : `/chat? roomId=${effectiveRoomId}`
  })
      router.push(`/agreement/details/${agreementId}? ${queryParams.toString()}`)
  }
  },
  // Room context state,
  const [roomContext, setRoomContext] = useState<RoomChatContext | null>(null),
  const [isRoomChat, setIsRoomChat] = useState(false),
  const [loadingRoomContext, setLoadingRoomContext] = useState(false),
  // Load room context if this is a room-related chat,
  const loadRoomContext = useCallback(async (chatRoomId    : string) => {
  if (!chatRoomId || loadingRoomContext) return null
    try {
  setLoadingRoomContext(true)
      ,
  // Check if this chat has room context,
      const context = await RoomChatService.getRoomContextFromChat(chatRoomId),
  ;
      if (context) {
  console.log('🏠 Room context found:', context),
  setRoomContext(context)
        setIsRoomChat(true) } else { // Check if this is a room chat based on URL parameters,
        const isRoomFromParams = context === 'room_inquiry' || ,
  sanitizedParams.roomListingId || ;
                                 sanitizedParams.source === 'room_listing' ||,
  sanitizedParams.source === 'room_detail';
        ,
  if (isRoomFromParams) {
          console.log('🏠 Room chat detected from parameters'),
  setIsRoomChat(true)
           // Try to construct room context from parameters,
  if (sanitizedParams.roomListingId && sanitizedParams.roomTitle) {
            const constructedContext: RoomChatContext = {
    roomId: sanitizedParams.roomListingId,
  roomTitle: sanitizedParams.roomTitle,
    roomPrice: parseInt(sanitizedParams.roomPrice || '0'),
  roomLocation: sanitizedParams.roomLocation || 'Unknown Location',
    ownerId: recipientId || '',
  ownerName: recipientName || 'Room Owner',
    roomType: sanitizedParams.roomType },
  setRoomContext(constructedContext)
          }
  } else {
          console.log('💬 Regular roommate chat detected'),
  setIsRoomChat(false)
        }
  }
    } catch (error) {
  console.error('Error loading room context:', error),
  setIsRoomChat(false)
    } finally {
  setLoadingRoomContext(false)
    }
  } [loadingRoomContext, context, sanitizedParams, recipientId, recipientName]),
  if (error) {
    return (
  <SafeAreaView style={styles.container} edges={['top']}>, ,
  <Stack.Screen, ,
  options={   headerShown: false      }
        />,
  <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>,
  <TouchableOpacity style={styles.retryButton} onPress={handleRetry}>
            <Text style={styles.retryText}>Retry</Text>,
  </TouchableOpacity>
        </View>,
  </SafeAreaView>
    )
  }
  return (
  <SafeAreaView style={styles.container} edges={['bottom']}>,
  <Stack.Screen, ,
  options={   headerShown: false      }
      />,
  <View style={styles.header}>
        <TouchableOpacity style={styles.headerButton} onPress={handleBack}>,
  <ArrowLeft size={24} color={{theme.colors.primary} /}>
        </TouchableOpacity>,
  <TouchableOpacity style={styles.headerTitle} onPress={handleViewRecipientProfile}>
          <Text style={styles.headerText}>{title}</Text>,
  </TouchableOpacity>
        <View style={{styles.headerButton} /}>,
  </View>
      {renderMockRoomBanner()},
  {!isOnline && (
        <NetworkStatusBar,
  message="You're offline";
          description= "Messages will be sent when your connection is restored", ,
  type= "warning", ,
  />
      )},
  {isLoading ? (
        <View style={styles.loadingContainer}>,
  <ActivityIndicator size="large" color={{theme.colors.primary} /}>
          <Text style={styles.loadingText}>Loading messages...</Text>,
  </View>
      )      : (<View style={styles.chatContainer}>,
  { (() => {
  const debugInfo = {
  hasRoomId: !!effectiveRoomId,
    hasOtherUserId: !!effectiveOtherUserId,
  hasUser: !!user?.id
              shouldShow  : !!effectiveRoomId && !!effectiveOtherUserId && !!user?.id,
  isRoomChat: isRoomChat,
    hasRoomContext: !!roomContext },
  console.log('Agreement button debug info:', debugInfo),
  console.log('Raw parameters for Agreement:', JSON.stringify({
  roomIdParam: roomIdParam,
    recipientId: recipientId,
  effectiveOtherUserId: effectiveOtherUserId),
    userFromAuth: user?.id) }))
            ,
  return debugInfo.shouldShow;
          })() && (
  <View style= {styles.agreementSection}>
              {isRoomChat ? (
  // Show Room Agreement Button for room-related chats, ,
  <RoomAgreementButton chatRoomId={effectiveRoomId || ''} participantIds={   effectiveOtherUserId ? [effectiveOtherUserId]     : []      } roomContext={roomContext || undefined},
  />
              ) : (// Show regular Agreement Button for roommate matching chats,
  <AgreementInitiationButton chatRoomId={effectiveRoomId || ''} participantIds={   effectiveOtherUserId ? [effectiveOtherUserId]  : []      },
  />
              )},
  </View>
          )},
  <View style={styles.messagesContainer}>
            <VirtualizedMessageList ref={virtualizedListRef} messages={messages} currentUserId={user?.id || ''} onLoadMore={() => {
  console.log('📱 Load more messages requested')
              }},
  hasMoreMessages={false} isLoading={isLoading}
            />,
  {!isLoading && !error && messages.length === 0 && (
              <View style={styles.emptyContainer}>,
  <Text style={styles.emptyText}>No messages yet</Text>
                <Text style={styles.emptySubText}>Say hello and start a conversation</Text>,
  </View>
            )},
  </View>
          <KeyboardAvoidingView behavior={   Platform.OS === 'ios' ? 'padding'  : 'height'      } keyboardVerticalOffset={   Platform.OS === 'ios' ? 90 : 0      } style={styles.keyboardAvoidingContainer},
  >
            <View style={styles.inputContainer}>,
  <TextInput style={styles.input} value={message} onChangeText={setMessage} placeholder="Type a message..."
                placeholderTextColor={theme.colors.textSecondary},
  multiline maxLength={2000} textAlignVertical="top"
                return KeyType="send",
  blurOnSubmit= {false} onSubmitEditing={() => {
  if (message.trim()) {
  handleSendMessage()
                  }
  }}
              />,
  <TouchableOpacity
                style = {[
                  styles.sendButton,
  (!message.trim() || isSending) && styles.disabledSendButton;
                ]},
  onPress= {() => handleSendMessage()}
                disabled={!message.trim() || isSending},
  activeOpacity={0.7}
              >,
  {isSending ? (
                  <ActivityIndicator size="small" color={{theme.colors.surface} /}>,
  )     : (<Send size={20} color={{theme.colors.surface} /}>
                )},
  </TouchableOpacity>
            </View>,
  </KeyboardAvoidingView>
        </View>,
  )}
    </SafeAreaView>,
  )
},
  const createStyles = (theme: any) => StyleSheet.create({ container: {
    flex: 1,
  backgroundColor: theme.colors.background }
  chatContainer: {
    flex: 1,
  position: 'relative'
  },
  messagesContainer: {
    flex: 1,
  paddingBottom: Platform.OS === 'ios' ? 100    : 90 // Increased padding to account for repositioned input
  },
  keyboardAvoidingContainer: {
    position: 'absolute',
  bottom: Platform.OS === 'ios' ? 20     : 16 // Move up from bottom edge,
    left: 0,
  right: 0,
    backgroundColor: 'transparent' }
  header: {
    flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'space-between',
  paddingHorizontal: theme.spacing?.md || 16
    paddingVertical   : theme.spacing?.xs || 8,
  backgroundColor: theme.colors.surface,
    borderBottomWidth: 0.5,
  borderBottomColor: theme.colors.border,
    shadowColor: theme.colors.shadow || '#000',
  shadowOffset: { width: 0, height: 1 },
  shadowOpacity: 0.05,
    shadowRadius: 3,
  elevation: 2,
    zIndex: 10
  }
  headerButton: { width: 36,
    height: 36,
  alignItems: 'center',
    justifyContent: 'center',
  borderRadius: 18,
    backgroundColor: theme.colors.surfaceVariant },
  headerTitle: {
    flex: 1,
  alignItems: 'center'
  },
  headerText: { fontSize: theme.typography?.h3?.fontSize || 17
    fontWeight    : '600',
  color: theme.colors.text }
  agreementBadge: { position: 'absolute',
    top: -2,
  right: -2,
    width: 18,
  height: 18,
    borderRadius: 9,
  backgroundColor: theme.colors.success,
    justifyContent: 'center',
  alignItems: 'center',
    borderWidth: 2,
  borderColor: theme.colors.surface }
  agreementBadgeText: {
    color: theme.colors.surface,
  fontSize: 10,
    fontWeight: '700' }
  messageList: { flex: 1 },
  messageListContent: { paddingHorizontal: theme.spacing?.md || 16
    paddingTop   : theme.spacing?.sm || 12,
  paddingBottom: theme.spacing?.xs || 8 }
  inputContainer : {
  flexDirection: 'row',
    alignItems: 'flex-end',
  paddingHorizontal: theme.spacing?.md || 16
    paddingVertical   : theme.spacing?.xs || 8,
  paddingBottom: Platform.OS === 'ios' ? 20  : 16 // Reduced bottom padding,
    marginBottom: Platform.OS === 'ios' ? 10   : 8 // Add margin to move up from edge,
  backgroundColor: theme.colors.surface,
    borderTopWidth: 1,
  borderTopColor: theme.colors.border,
    borderRadius: theme.borderRadius?.md || 12, // Add border radius for better appearance,
  marginHorizontal  : theme.spacing?.xs || 8 // Add horizontal margin to prevent edge overflow
    shadowColor : theme.colors.shadow || '#000',
  shadowOffset: { width: 0, height: -2 },
  shadowOpacity: 0.1,
    shadowRadius: 4,
  elevation: 8,
    zIndex: 20
  }
  input: { flex: 1,
    borderWidth: 1,
  borderColor: theme.colors.border,
    borderRadius: 24,
  paddingHorizontal: theme.spacing?.md || 16
    paddingVertical   : theme.spacing?.sm || 12,
  minHeight: 44,
    maxHeight: 120,
  backgroundColor: theme.colors.surface,
    fontSize: theme.typography?.body?.fontSize || 16,
  color  : theme.colors.text
  lineHeight: 20,
    textAlignVertical: 'center',
  includeFontPadding: false }
  sendButton: {
    width: 44,
  height: 44,
    borderRadius: 22,
  backgroundColor: theme.colors.primary,
    alignItems: 'center',
  justifyContent: 'center',
    marginLeft: theme.spacing?.sm || 12,
  shadowColor   : theme.colors.primary
  shadowOffset: { width: 0 height: 2 },
  shadowOpacity: 0.3,
    shadowRadius: 4,
  elevation: 3
  },
  disabledSendButton: { backgroundColor: theme.colors.disabled || theme.colors.border,
    shadowOpacity: 0,
  elevation: 0 }
  loadingContainer: { flex: 1,
    alignItems: 'center',
  justifyContent: 'center',
    padding: 20,
  backgroundColor: theme.colors.background }
  loadingText: {
    fontSize: theme.typography?.body?.fontSize || 16,
  color   : theme.colors.textSecondary
  marginTop: theme.spacing?.sm || 12,
  fontWeight : '500'
  },
  errorContainer: { flex: 1,
    alignItems: 'center',
  justifyContent: 'center',
    padding: 20,
  backgroundColor: theme.colors.background }
  errorText: {
    fontSize: theme.typography?.body?.fontSize || 16,
  color   : theme.colors.error
  textAlign: 'center',
    marginBottom: 20,
  fontWeight: '500'
  },
  retryButton: {
    paddingVertical: theme.spacing?.sm || 12,
  paddingHorizontal   : theme.spacing?.lg || 24
  backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius?.md || 12,
  shadowColor  : theme.colors.primary
  shadowOffset: { width: 0 height: 2 },
  shadowOpacity: 0.2,
    shadowRadius: 4,
  elevation: 3
  },
  retryText: { color: theme.colors.surface,
    fontWeight: '600',
  fontSize: theme.typography?.body?.fontSize || 16 }
  emptyContainer   : {
  alignItems: 'center',
    justifyContent: 'center',
  padding: 40,
    backgroundColor: 'transparent' }
  emptyText: { fontSize: theme.typography?.h2?.fontSize || 20,
  fontWeight   : '600'
  color: theme.colors.text,
    marginBottom: theme.spacing?.xs || 8 },
  emptySubText  : { fontSize: theme.typography?.body?.fontSize || 16,
    color: theme.colors.textSecondary,
  textAlign: 'center',
    lineHeight: 24 },
  mockRoomBanner: {
    flexDirection: 'row',
  alignItems: 'center'),
    backgroundColor: theme.colors.warning || '#FEF3C7'),
  paddingHorizontal: theme.spacing?.md || 16
    paddingVertical   : theme.spacing?.sm || 12,
  borderBottomWidth: 1,
    borderBottomColor: theme.colors.warningBorder || '#F59E0B',
  marginHorizontal: theme.spacing?.md || 16
    marginTop   : theme.spacing?.xs || 8,
  borderRadius: theme.borderRadius?.sm || 8
    zIndex : 5 }
  mockRoomText: {
    color: theme.colors.warningText || '#92400E',
  fontSize: theme.typography?.caption?.fontSize || 14
    marginLeft  : theme.spacing?.xs || 8,
  flex: 1,
    fontWeight: '500' }
  agreementSection: {
    paddingVertical: theme.spacing?.xs || 8,
  paddingHorizontal  : theme.spacing?.xs || 8
  backgroundColor: theme.colors.background,
    borderBottomWidth: 0,
  zIndex: 1)
  }
  })