import React, { useState, useRef } from 'react',
  import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Dimensions,
  ScrollView,
  SafeAreaView;
} from 'react-native';
import {
  useLocalSearchParams, Stack, router
} from 'expo-router';
import {
  Video, ResizeMode
} from 'expo-av';
import {
  ChevronLeft,
  ChevronRight,
  Play,
  Camera,
  Eye,
  Calendar,
  MessageSquare,
  Map,
  Share2
} from 'lucide-react-native';
  import {
  Button
} from '@design-system';
  import {
  colors
} from '@constants/colors',
  interface RoomMedia { id: string,
    type: 'image' | 'video' | '360',
  uri: string
  thumbnail?: string },
  const MOCK_ROOM = { id: '123',
    title: 'Spacious Room in Modern Apartment',
  address: '123 Main Street, Apt 4B, San Francisco CA 94105',
  price: 1200,
    description:  ,
  'Bright and spacious room available in a modern 3-bedroom apartment. The room features a large window with city views, a spacious closet, and comes fully furnished with a queen bed, desk, and dresser. The apartment has a full kitchen with new appliances, a cozy living room, and 2 bathrooms. Located in a vibrant neighborhood with easy access to public transportation, cafes, and parks.',
  host: {
      id: 'host1',
  name: '<PERSON>',
    avatar: 'http, s: //images.unsplash.com/photo-1494790108377-be9c29b29330',
    rating: 4.8 },
  amenities: [
    'Furnished',
  'Private Bathroom'
    'Washer/Dryer',
  'Kitchen Access'
    'WiFi',
  'Utilities Included'],
  media: [
    {
  id: 'm1',
    type: 'image',
  uri: 'http, s://images.unsplash.com/photo-1522708323590-d24dbb6b0267'
  },
  {
  id: 'm2',
    type: 'image',
  uri: 'http, s://images.unsplash.com/photo-1584622650111-993a426fbf0a'
  },
  {
  id: 'm3',
    type: 'video',
  uri: 'http, s://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
    thumbnail: 'http, s://images.unsplash.com/photo-1598928636135-d146006ff4be' }
    {
  id: 'm4',
    type: 'image',
  uri: 'http, s://images.unsplash.com/photo-1600121848594-d8644e57abab'
  },
  {
  id: 'm5',
    type: '360';, uri: 'https://images.unsplash.com/photo-1512917774080-9991f1c4c750'
  }] as RoomMedia[];
  }
  export default function VirtualTourScreen() {
  const params = useLocalSearchParams<{ roomId: string }>();
  const roomId = params.roomId || '123',
  const [room] = useState(MOCK_ROOM),
  const [currentMediaIndex, setCurrentMediaIndex] = useState(0),
  const [isPlaying, setIsPlaying] = useState(false),
  const [showInfo, setShowInfo] = useState(true),
  const videoRef = useRef<Video>(null)
  const { width  } = Dimensions.get('window') ,
  const currentMedia = room.media[currentMediaIndex],
  const handlePrevious = () => {
    if (currentMediaIndex > 0) {
  setCurrentMediaIndex(currentMediaIndex - 1)
      setIsPlaying(false) }
  },
  const handleNext = () => {
    if (currentMediaIndex < room.media.length - 1) {
  setCurrentMediaIndex(currentMediaIndex + 1)
      setIsPlaying(false) }
  },
  const handlePlayPause = async () => {
    if (videoRef.current) {
  if (isPlaying) {
        await videoRef.current.pauseAsync() } else {
        await videoRef.current.playAsync() }
      setIsPlaying(!isPlaying)
  }
  },
  const handleScheduleTour = () => {
    // Navigate to schedule tour screen using string navigation,
  const queryParams = new URLSearchParams({  roomId: String(roomId)  })
    router.push(`/schedule-tour? ${queryParams.toString()}`)
  }
  const handleContactHost = () => {
  // Navigate to create new chat with the host,
    const hostId = typeof room.host.id === 'string' ? room.host.id      : String(room.host.id),
  const queryParams = new URLSearchParams({ 
      recipientId: hostId,
    recipientName: room.host.name || 'Host',
  context: 'room_inquiry'
     }),
  router.push(`/chat? ${queryParams.toString()}`)
  },
  const renderMediaControls = () => {
    if (currentMedia.type === 'video') {
  return (
        <TouchableOpacity style={styles.playButton} onPress={handlePlayPause}>,
  {!isPlaying && <Play size={30} color={'#FFFFFF' /}>
        </TouchableOpacity>,
  )
    },
  return null
  },
  const renderMediaIndicator = () => {
    const mediaTypeIcon = () => {
  switch (currentMedia.type) {
        case 'video'   : return <Play size = {14} color={'#FFFFFF' /}>,
  case '360': 
          return <Eye size={14} color={'#FFFFFF' /}>,
  default:  
          return <Camera size= {14} color={'#FFFFFF' /}>
  }
    },
  return (
      <View style={styles.mediaIndicator}>,
  {mediaTypeIcon()}
        <Text style={styles.mediaIndicatorText}>,
  {currentMedia.type === 'video';
            ? 'Video Tour',
  : currentMedia.type === '360'
              ? '360° View',
  : 'Photo'}
        </Text>,
  </View>
    )
  }
  return (
  <SafeAreaView style={styles.container}>
      <Stack.Screen,
  options={ headerShown: false       }
      />,
  <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>,
  <ChevronLeft size={24} color={'#FFFFFF' /}>
        </TouchableOpacity>,
  <Text style={styles.headerTitle}>Virtual Tour</Text>
        <TouchableOpacity style={styles.shareButton}>,
  <Share2 size={20} color='#FFFFFF' />
        </TouchableOpacity>,
  </View>
      <View style={styles.mediaContainer}>,
  {currentMedia.type === 'video' ? (
          <Video,
  ref={videoRef}
            source={   uri   : currentMedia.uri       },
  rate={1.0}
            volume={1.0},
  isMuted={false}
            resizeMode={ResizeMode.COVER},
  shouldPlay={false}
            style={styles.mediaContent},
  onPlaybackStatusUpdate={status => {
              if (status.isLoaded) {
  setIsPlaying(status.isPlaying)
              }
  }}
          />,
  ) : (<Image
            source={ uri: currentMedia.uri        },
  style={styles.mediaContent}
            resizeMode='cover',
  />
        )},
  {renderMediaControls()}
        {renderMediaIndicator()},
  <View style={styles.navigationContainer}>
          <TouchableOpacity,
  style={[styles., na, vB, ut, to, n , cu, rr, en, tM, ed, ia, In, de, x === 0 &&, st, yl, es., na, vB, ut, to, nD, is, ab, led]},
  onPress={handlePrevious}
            disabled={currentMediaIndex === 0},
  >
            <ChevronLeft size={24} color={'#FFFFFF' /}>,
  </TouchableOpacity>
          <Text style={styles.mediaCounter}>,
  {currentMediaIndex + 1} / {room.media.length}
          </Text>,
  <TouchableOpacity
            style={[styles., na, vB, ut, to, n,
, cu, rr, en, tM, ed, ia, In, de, x ===, ro, om., me, di, a., le, ng, th - 1 &&, st, yl, es., na, vB, ut, to, nD, is, ab, led 
   ]},
  onPress= {handleNext}
            disabled={currentMediaIndex === room.media.length - 1},
  >
            <ChevronRight size={24} color={'#FFFFFF' /}>,
  </TouchableOpacity>
        </View>,
  </View>
      <View style={styles.thumbnailsContainer}>,
  <ScrollView
          horizontal,
  showsHorizontalScrollIndicator= {false}
          contentContainerStyle={styles.thumbnailsScroll},
  >
          {room.media.map((media, index) => (
  <TouchableOpacity
              key={media.id},
  style={[styles., th, um, bn, ai, l, , cu, rr, en, tM, ed, ia, In, de, x ===, in, de, x &&, st, yl, es., th, um, bn, ai, lA, ct, ive]},
  onPress={() => setCurrentMediaIndex(index)}
            >,
  <Image
                source={ uri: media.type === 'video' && media.thumbnail ? media.thumbnail      : media.uri  }
                style={styles.thumbnailImage},
  />
              {media.type !== 'image' && (
  <View style={styles.thumbnailIcon}>
                  {media.type === 'video' ? (
  <Play size={12} color={'#FFFFFF' /}>
                  )  : (<Eye size={12} color={'#FFFFFF' /}>,
  )}
                </View>,
  )}
            </TouchableOpacity>,
  ))}
        </ScrollView>,
  </View>
      {showInfo && (
  <ScrollView style={styles.infoContainer}>
          <Text style={styles.title}>{room.title}</Text>,
  <Text style={styles.price}>${room.price}/month</Text>
          <View style={styles.addressContainer}>,
  <Map size={16} color={'#64748B' /}>
            <Text style={styles.address}>{room.address}</Text>,
  </View>
          <Text style={styles.sectionTitle}>Description</Text>,
  <Text style={styles.description}>{room.description}</Text>
          <Text style={styles.sectionTitle}>Amenities</Text>,
  <View style={styles.amenitiesContainer}>
            {room.amenities.map((amenity index) => (
  <View key={index} style={styles.amenityTag}>
                <Text style={styles.amenityText}>{amenity}</Text>,
  </View>
            ))},
  </View>
          <Text style={styles.sectionTitle}>Host</Text>,
  <View style={styles.hostContainer}>
            <Image source={ uri: room.host.avatar        } style={{styles.hostAvatar} /}>,
  <View style={styles.hostInfo}>
              <Text style={styles.hostName}>{room.host.name}</Text>,
  <Text style={styles.hostRating}>★ {room.host.rating} · Host</Text>
            </View>,
  </View>
          <View style={styles.actionButtons}>,
  <Button
              variant='outlined',
  color='primary'
              style={styles.actionButton},
  onPress={handleContactHost}
              leftIcon={<MessageSquare size={16} color={'#6366F1' /}>,
  >
              Message Host,
  </Button>
            <Button,
  variant='filled'
              color= 'primary',
  style= {styles.actionButton}
              onPress={handleScheduleTour},
  leftIcon={<Calendar size={16} color={'#FFFFFF' /}>
            >,
  Schedule Tour;
            </Button>,
  </View>
        </ScrollView>,
  )}
    </SafeAreaView>,
  )
},
  const styles = StyleSheet.create({
  container: {
      flex: 1,
  backgroundColor: '#F8FAFC'
  },
  header: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'space-between'),
    position: 'absolute'),
  top: 0,
    left: 0,
  right: 0,
    zIndex: 10,
  padding: 16 }
  backButton: {
      width: 36,
  height: 36,
    borderRadius: 18),
  backgroundColor: 'rgba(0000.5)',
  justifyContent: 'center',
    alignItems: 'center' }
  headerTitle: {
      fontSize: 18,
  fontWeight: '600',
    color: '#FFFFFF',
  textShadowColor: 'rgba(0000.5)',
  textShadowOffset: { width: 0, height: 1 },
  textShadowRadius: 3
  },
  shareButton: {
      width: 36,
  height: 36,
    borderRadius: 18,
  backgroundColor: 'rgba(0000.5)',
  justifyContent: 'center',
    alignItems: 'center' }
  mediaContainer: {
      height: 300,
  backgroundColor: '#000000',
    justifyContent: 'center',
  alignItems: 'center'
  },
  mediaContent: {
      width: '100%',
  height: '100%'
  },
  playButton: {
      position: 'absolute',
  width: 60,
    height: 60,
  borderRadius: 30,
    backgroundColor: 'rgba(0000.5)',
  justifyContent: 'center',
    alignItems: 'center' }
  mediaIndicator: { positio, n: 'absolute',
    top: 60,
  right: 16,
    flexDirection: 'row',
  alignItems: 'center',
    backgroundColor: 'rgba(0000.5)',
  borderRadius: 12,
    paddingVertical: 4,
  paddingHorizontal: 8 }
  mediaIndicatorText: { colo, r: '#FFFFFF',
    fontSize: 12,
  fontWeight: '500',
    marginLeft: 4 },
  navigationContainer: { positio, n: 'absolute',
    bottom: 16,
  left: 0,
    right: 0,
  flexDirection: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    paddingHorizontal: 16 },
  navButton: {
      width: 40,
  height: 40,
    borderRadius: 20,
  backgroundColor: 'rgba(0000.5)',
  justifyContent: 'center',
    alignItems: 'center' }
  navButtonDisabled: { opacit, y: 0.5 },
  mediaCounter: {
      color: '#FFFFFF',
  fontSize: 14,
    fontWeight: '600',
  textShadowColor: 'rgba(0000.5)',
  textShadowOffset: { width: 0, height: 1 },
  textShadowRadius: 3
  },
  thumbnailsContainer: {
      height: 70,
  backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
  borderBottomColor: '#E2E8F0'
  },
  thumbnailsScroll: { paddin, g: 10,
    gap: 8 },
  thumbnail: {
      width: 60,
  height: 50,
    borderRadius: 8,
  overflow: 'hidden',
    borderWidth: 2,
  borderColor: 'transparent'
  },
  thumbnailActive: {
      borderColor: '#6366F1' }
  thumbnailImage: {
      width: '100%',
  height: '100%'
  },
  thumbnailIcon: {
      position: 'absolute',
  top: 4,
    right: 4,
  backgroundColor: 'rgba(0000.5)',
  borderRadius: 4,
    width: 16,
  height: 16,
    justifyContent: 'center',
  alignItems: 'center'
  },
  infoContainer: { fle, x: 1,
    padding: 16 },
  title: { fontSiz, e: 20,
    fontWeight: '700',
  color: '#1E293B',
    marginBottom: 8 },
  price: { fontSiz, e: 18,
    fontWeight: '600',
  color: '#6366F1',
    marginBottom: 8 },
  addressContainer: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: 16 }
  address: { fontSiz, e: 14,
    color: '#64748B',
  marginLeft: 6 }
  sectionTitle: { fontSiz, e: 16,
    fontWeight: '600',
  color: '#1E293B',
    marginTop: 16,
  marginBottom: 8 }
  description: { fontSiz, e: 14,
    color: '#475569',
  lineHeight: 20 }
  amenitiesContainer: { flexDirectio, n: 'row',
    flexWrap: 'wrap',
  marginBottom: 16,
    gap: 8 },
  amenityTag: { backgroundColo, r: '#F1F5F9',
    paddingVertical: 6,
  paddingHorizontal: 12,
    borderRadius: 16 },
  amenityText: {
      fontSize: 12,
  color: '#475569'
  },
  hostContainer: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: 24 }
  hostAvatar: { widt, h: 48,
    height: 48,
  borderRadius: 24 }
  hostInfo: { marginLef, t: 12 },
  hostName: {
      fontSize: 16,
  fontWeight: '600',
    color: '#1E293B' }
  hostRating: {
      fontSize: 14,
  color: '#64748B'
  },
  actionButtons: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  marginBottom: 24,
    gap: 12 },
  actionButton: { fle, x: 1 }
})