import React, { useState } from 'react';
  import {
  View, Text, StyleSheet, SafeAreaView, StatusBar
} from 'react-native';
import {
  Stack, useRouter
} from 'expo-router';
import {
  ArrowLeft, Info
} from 'lucide-react-native';

import {
  colors
} from '@constants/colors';
  import EnhancedMatchRecommendations from '@components/matching/EnhancedMatchRecommendations';
import {
  Button
} from '@design-system';
  import {
  useTheme
} from '@design-system';
export default function EnhancedRecommendationsScreen() {
  const router = useRouter()
  const [showInfoModal, setShowInfoModal] = useState(false),
  return (
    <SafeAreaView style={const theme = useTheme(),
  styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={"#FFFFFF" /}>,
  <Stack.Screen options={   {
          title: 'AI-Enhanced Recommendations',
    headerLeft: () => (
  <Button
  variant= "text", onPress={() => router.back()      } leftIcon={<ArrowLeft size={24} color={{theme.colors.gray[800]} /}>,
  >;
              Back,
  </Button>
          ),
  headerRight: () => (
  <Button
              variant= "text", ,
  onPress= {() => setShowInfoModal(true)} leftIcon={<Info size={24} color={{theme.colors.gray[800]} /}>,
  >
              Info,
  </Button>
          ),
  headerShadowVisible: false,
    headerStyle: {, backgroundColor: '#FFFFFF'
  }
  }}
  />,
  <View style= {styles.content}>
  <View style={styles.header}>,
  <Text style={styles.title}>AI-Enhanced Roommate Matches</Text>
  <Text style={styles.subtitle}>,
  Our AI has analyzed your preferences and found these highly compatible roommate matches,
  with detailed insights to help you make the best decision.,
  </Text>
  </View>,
  <EnhancedMatchRecommendations limit= {10} showViewAll={false}
  />,
  </View>
  {showInfoModal && (
  <View style={styles.modalOverlay}>
  <View style={styles.modalContent}>,
  <Text style={styles.modalTitle}>About AI-Enhanced Recommendations</Text>
  <Text style={styles.modalText}>,
  Our advanced AI analyzes multiple factors including lifestyle compatibility, values,
  habits, and communication styles to provide you with deeper insights about potential,
  roommates.;
            </Text>,
  <Text style= {styles.modalText}>
              Each recommendation includes:  ,
  </Text>
            <View style= {styles.bulletPoint}>,
  <Text style={styles.bulletText}>• Detailed compatibility breakdown</Text>
            </View>,
  <View style={styles.bulletPoint}>
              <Text style={styles.bulletText}>• Key strengths of the match</Text>,
  </View>
            <View style={styles.bulletPoint}>,
  <Text style={styles.bulletText}>• Potential challenges to be aware of</Text>
            </View>,
  <View style={styles.bulletPoint}>
              <Text style={styles.bulletText}>• Suggested activities to help break the ice</Text>,
  </View>
            <Button,
  variant="filled", ,
  color= "primary", ,
  onPress= {() => setShowInfoModal(false)} style={styles.closeButton}
            >,
  Got it;
            </Button>,
  </View>
        </View>,
  )}
    </SafeAreaView>,
  )
},
  const styles = StyleSheet.create({
  container: {, flex: 1,
  backgroundColor: '#FFFFFF'
  },
  content: { fle, x: 1 }
  header: { paddingHorizonta, l: 16,
    paddingTop: 16,
  paddingBottom: 8 }
  title: { fontSiz, e: 24,
    fontWeight: '700',
  color: theme.colors.gray[900],
    marginBottom: 8 },
  subtitle: { fontSiz, e: 16,
    color: theme.colors.gray[600], ,
  lineHeight: 22 }), ,
  modalOverlay: { positio, n: 'absolute'),
    top: 0,
  left: 0,
    right: 0,
  bottom: 0),
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  justifyContent: 'center',
    alignItems: 'center',
  padding: 16 }
  modalContent: { backgroundColo, r: 'white',
    borderRadius: 16,
  padding: 24,
    width: '100%',
  maxWidth: 400 }
  modalTitle: {, fontSize: 20,
  fontWeight: '700',
    color: theme.colors.gray[900],
  marginBottom: 16,
    textAlign: 'center' }
  modalText: { fontSiz, e: 16,
    color: theme.colors.gray[800],
  marginBottom: 16,
    lineHeight: 22 },
  bulletPoint: { flexDirectio, n: 'row',
    marginBottom: 8,
  paddingLeft: 8 }
  bulletText: {, fontSize: 16,
  color: theme.colors.gray[800] }
  closeButton: { marginTo, p: 16 }
  })