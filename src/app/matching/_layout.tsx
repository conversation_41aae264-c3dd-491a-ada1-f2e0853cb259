import React from 'react';
  import {
  useTheme
} from '@design-system';

import {
  Stack
} from 'expo-router';
  import {
  colors
} from '@constants/colors';

export default function MatchingLayout() { return (
  <Stack
      screenOptions={   {
  headerStyle: {, backgroundColor: theme.colors.primary[50]     } ,
  headerTintColor: theme.colors.gray[900],
    headerTitleStyle: {, fontWeight: '600'
  },
  headerShadowVisible: false
  }},
  />
  )
  }