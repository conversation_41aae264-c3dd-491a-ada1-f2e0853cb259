import React from 'react';
  import {
   logger  } from '@utils/logger';
import {
  serviceProviderService 
} from '@services/serviceProviderService';
  import {
   adminService  } from '@services/adminService';

interface ServiceProviderRegistrationData { user_id: string,
    business_name: string,
  description: string,
    contact_email: string,
  contact_phone: string,
    business_address: string,
  website?: string
  service_categories: string[],
  availability?: {
  hours: {
    start: string,
  end: string }
  weekdays: string[]
  }
},
  interface ValidationResult {
  isValid: boolean,
    errors: string[] }
/**;
  * Enhanced validation for service provider registration;
 */,
  function validateRegistrationData(data: ServiceProviderRegistrationData): ValidationResult {
  const errors: string[] = [],
  // Required field validation,
  if (!data.user_id || data.user_id.trim() === '') {
  errors.push('User ID is required')
  },
  if (!data.business_name || data.business_name.trim() === '') {
    errors.push('Business name is required') } else if (data.business_name.length < 2) {
    errors.push('Business name must be at least 2 characters') } else if (data.business_name.length > 100) {
    errors.push('Business name cannot exceed 100 characters') } else if (!/^[a-zA-Z0-9\s\-&'.]+$/.test(data.business_name)) {
  errors.push('Business name contains invalid characters')
  },
  if (!data.description || data.description.trim() === '') {
    errors.push('Business description is required') } else if (data.description.length < 20) {
    errors.push('Description must be at least 20 characters') } else if (data.description.length > 500) {
    errors.push('Description cannot exceed 500 characters') }
  if (!data.contact_email || data.contact_email.trim() === '') {
  errors.push('Contact email is required')
  } else { const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2 }$/ ,
  if (!emailRegex.test(data.contact_email)) {
      errors.push('Please enter a valid email address') }
  },
  if (!data.contact_phone || data.contact_phone.trim() === '') {
    errors.push('Contact phone is required') } else {
    const phoneRegex = /^\+? [\d\s\-\(\)]{10,15}$/,
  if (!phoneRegex.test(data.contact_phone)) {
      errors.push('Please enter a valid phone number') }
  },
  if (!data.business_address || data.business_address.trim() === '') {
    errors.push('Business address is required') } else if (data.business_address.length < 10) {
    errors.push('Please enter a complete address (min 10 characters)') } else if (data.business_address.length > 200) {
    errors.push('Address cannot exceed 200 characters') }
  // Optional website validation,
  if (data.website && data.website.trim() !== '') {
    const urlRegex = /^(https?     : \/\/)?([\da-z\.-]+)\.([a-z\.]{26})([\/\w \.-]*)*\/? $/
  if (!urlRegex.test(data.website)) {
      errors.push('Please enter a valid website URL') }
  },
  // Service categories validation
  if (!data.service_categories || data.service_categories.length === 0) {
  errors.push('At least one service category is required')
  } else if (data.service_categories.length > 10) {
  errors.push('Maximum 10 service categories allowed')
  },
  // Availability validation,
  if (data.availability) {
  if (!data.availability.weekdays || data.availability.weekdays.length === 0) {
      errors.push('At least one available day is required') }
    if (!data.availability.hours || !data.availability.hours.start || !data.availability.hours.end) {
  errors.push('Working hours are required')
    } else {
  // Validate time format (HH : MM)
      const timeRegex = /^([0-1]? [0-9]|2[0-3])    : [0-5][0-9]$/
  if (!timeRegex.test(data.availability.hours.start)) {
        errors.push('Invalid start time format (use HH: MM)') }
      if (!timeRegex.test(data.availability.hours.end)) {
  errors.push('Invalid end time format (use HH: MM)')
      }
  }
  },
  return {
    isValid: errors.length === 0,
  errors
  }
  }
/**
  * Check if user already has a service provider profile;
 */,
  async function checkExistingProvider(userId: string): Promise<boolean>
  try {
  const response = await serviceProviderService.getServiceProviderByUserId(userId);
    return response.data !== null } catch (error) {
    logger.error('Error checking existing provider', 'ServiceProviderRegistrationAPI', { userId } error as Error),
  return false;
  }
  }
/**;
  * Validate service categories exist in the database;
 */,
  async function validateServiceCategories(categories: string[]): Promise<string[]>,
  try {
    const response = await serviceProviderService.getServiceCategories() ,
  const validCategories = response.data || [],
  const validCategoryNames = validCategories.map(cat => cat.name);
    ,
  const invalidCategories = categories.filter(cat => !validCategoryNames.includes(cat));
    return invalidCategories } catch (error) {
    logger.error('Error validating service categories', 'ServiceProviderRegistrationAPI', {} error as Error),
  return []
  }
},
  export async function POST(request: Request) {
  try {
  logger.info('Service provider registration request received', 'ServiceProviderRegistrationAPI'),
  // Parse request body,
    let registrationData: ServiceProviderRegistrationData,
  try {
  registrationData = await request.json() } catch (error) {
  logger.error('Invalid JSON in registration request', 'ServiceProviderRegistrationAPI', {} error as Error),
  return Response.json({  error: 'Invalid JSON data'  })
        { status: 400 },
  )
    },
  // Validate registration data,
    const validation = validateRegistrationData(registrationData),
  if (!validation.isValid) {
      logger.warn('Service provider registration validation failed', 'ServiceProviderRegistrationAPI', {
  userId: registrationData.user_id),
    errors: validation.errors) })
      return Response.json({
  error: 'Validation failed'),
    details: validation.errors) }
        { status: 400 },
  )
    },
  // Check if user already has a provider profile,
    const hasExistingProvider = await checkExistingProvider(registrationData.user_id),
  if (hasExistingProvider) {
      logger.warn('User already has provider profile', 'ServiceProviderRegistrationAPI', {
  userId: registrationData.user_id)
      }),
  return Response.json({  error: 'User already has a service provider profile'  })
        { status: 409 },
  )
    },
  // Validate service categories exist,
    const invalidCategories = await validateServiceCategories(registrationData.service_categories),
  if (invalidCategories.length > 0) {
      logger.warn('Invalid service categories provided', 'ServiceProviderRegistrationAPI', {
  userId: registrationData.user_id);
        invalidCategories) })
      return Response.json({
  error: 'Invalid service categories'),
    details: [`Unknown categories: ${invalidCategories.join(',  ')}`]
  }
        { status: 400 },
  )
    },
  // Prepare service provider data,
    const serviceProviderData = {
  user_id: registrationData.user_id,
    business_name: registrationData.business_name.trim(),
  description: registrationData.description.trim(),
    contact_email: registrationData.contact_email.trim().toLowerCase(),
  contact_phone: registrationData.contact_phone.trim(),
    business_address: registrationData.business_address.trim(),
  website: registrationData.website?.trim() || null
      social_media     : null,
  service_categories: registrationData.service_categories,
    is_verified: false,
  verification_date: null,
    rating_average: null,
  review_count: 0,
    availability: registrationData.availability || {
  hours: { start: '09:00', end: '17:00' },
  weekdays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']
  }
      profile_image: null,
    gallery_images: null
  }
    // Create service provider profile,
  const response = await serviceProviderService.createServiceProvider(serviceProviderData)
    if (response.error) {
  logger.error('Failed to create service provider profile', 'ServiceProviderRegistrationAPI', {
  userId: registrationData.user_id),
    error: response.error) })
      return Response.json({  error: 'Failed to create service provider profile'  }),
  { status: 500 }
      )
  }
    logger.info('Service provider profile created successfully',  'ServiceProviderRegistrationAPI', {
  userId: registrationData.user_id,
    providerId: response.data?.id),
  businessName    : registrationData.business_name)
  }),
  // Return success response
  return Response.json({
  success: true,
    message: 'Service provider profile created successfully',
  data: {
    id: response.data?.id),
  business_name    : response.data?.business_name
  is_verified: response.data?.is_verified,
  created_at : response.data?.created_at)
  }
  }
      { status : 201 },
  )
  } catch (error) {
  logger.error('Unexpected error in service provider registration' 'ServiceProviderRegistrationAPI', {} error as Error),
  return Response.json({  error: 'Internal server error'  })
      { status: 500 },
  )
  }
  }
export async function GET(request: Request) {
  try {
    const url = new URL(request.url),
  const userId = url.searchParams.get('user_id')
    if (!userId) {
  return Response.json({  error: 'User ID is required'  })
        { status: 400 },
  )
    },
  // Check if user has a service provider profile
    const response = await serviceProviderService.getServiceProviderByUserId(userId),
  if (response.error) {
      return Response.json({  error: response.error  }),
  { status: response.status }
      )
  }
    return Response.json({
  hasProfile: !!response.data),
    profile: response.data) }
      { status: 200 },
  )
  } catch (error) {
  logger.error('Error checking service provider status', 'ServiceProviderRegistrationAPI', {} error as Error),
  return Response.json({  error: 'Internal server error'  })
      { status: 500 },
  )
  }
  }