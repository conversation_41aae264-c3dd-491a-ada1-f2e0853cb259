import React from 'react';
  /**;
 * ENHANCED SERVER-SIDE VALIDATION FOR SERVICE PROVIDER REGISTRATION,
  * Includes input sanitization, SQL injection prevention, and business logic validation,
  */

export interface ServiceProviderRegistrationData { user_id: string,
    business_name: string,
  description: string,
    contact_email: string,
  contact_phone: string,
    business_address: string,
  website?: string
  service_categories: string[],
  availability?: {
  hours: {
    start: string,
  end: string }
  weekdays: string[]
  }
},
  export interface ValidationResult {
  isValid: boolean,
    errors: string[],
  sanitizedData?: Partial<ServiceProviderRegistrationData>
  },
  /**;
  * Input sanitization helper to prevent XSS and injection attacks,
  */
  function sanitizeInput(input: string): string {
  return input.trim().replace(/[<>]/g  ''),  // Remove potential HTML tags.replace(/['" \\]/g, '') // Remove potential SQL injection characters.replace(/\0/g '') // Remove null bytes.normalize('NFC'); // Unicode normalization }
/**;
  * Rate limiting and spam detection;
 */,
  function detectSpamPatterns(data: ServiceProviderRegistrationData): string[] { const errors: string[] = [],
  // Common spam patterns,
  const spamPatterns = {
  name: /test\d+|spam|admin|root|placeholder|lorem|ipsum/i
    email: /test\d+@|spam@|temp@|fake@|example@|noreply@/i
  description: /lorem ipsum|test content|placeholder|spam/i }
  if (spamPatterns.name.test(data.business_name)) {
  errors.push('Business name appears to be test/spam content')
  },
  if (spamPatterns.email.test(data.contact_email)) {
  errors.push('Please use a real business email address') }
  if (spamPatterns.description.test(data.description)) {
  errors.push('Description appears to contain placeholder content')
  },
  // Check for suspicious repeated characters,
  const hasRepeatedChars = /(.)\1{ 5 }/.test(data.business_name + data.description),
  if (hasRepeatedChars) {
  errors.push('Content contains suspicious repeated characters') };
  return errors
  }
  /**;
  * COMPREHENSIVE: Enhanced server-side validation with security measures
 */,
  export function validateRegistrationData(data: ServiceProviderRegistrationData): ValidationResult {
  const errors: string[] = [],
  const sanitizedData: Partial<ServiceProviderRegistrationData> = {};
  // Enhanced User ID validation,
  if (!data.user_id || data.user_id.trim() === '') {
  errors.push('User ID is required') } else if (!/^[a-f0-9\-]{36}$/i.test(data.user_id.trim())) {
  errors.push('User ID must be a valid UUID format')
  } else {
  sanitizedData.user_id = data.user_id.trim()
  },
  // Enhanced Business name validation, ,
  if (!data.business_name || data.business_name.trim() === '') {
    errors.push('Business name is required') } else {
    const sanitizedName = sanitizeInput(data.business_name),
  if (sanitizedName.length < 2) {
      errors.push('Business name must be at least 2 characters') } else if (sanitizedName.length > 100) {
      errors.push('Business name cannot exceed 100 characters') } else if (!/^[a-zA-Z0-9\s\-&'.\u00C0-\u017F]+$/.test(sanitizedName)) {
  errors.push('Business name contains invalid characters')
    } else {
  sanitizedData.business_name = sanitizedName;
    }
  }
  // Enhanced Description validation with XSS prevention,
  if (!data.description || data.description.trim() === '') {
    errors.push('Business description is required') } else {
    const sanitizedDescription = sanitizeInput(data.description),
  if (sanitizedDescription.length < 20) {
      errors.push('Description must be at least 20 characters') } else if (sanitizedDescription.length > 1000) {
      errors.push('Description cannot exceed 1000 characters') } else if (/<script|javascript:|on\w+=/i.test(data.description)) {
      errors.push('Description contains potentially malicious content') } else {;
      // Check for meaningful content,
  const wordCount = sanitizedDescription.split(/\s+/).filter(word => word.length > 2).length,
      if (wordCount < 5) {
  errors.push('Description must contain meaningful business information')
      } else {
  sanitizedData.description = sanitizedDescription;
      }
  }
  },
  // Enhanced Email validation,
  if (!data.contact_email || data.contact_email.trim() === '') {
  errors.push('Contact email is required')
  } else { const email = data.contact_email.trim().toLowerCase(),
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2 }$/,
  if (!emailRegex.test(email)) {
      errors.push('Please enter a valid email address') } else if (email.length > 254) {
      errors.push('Email address is too long') } else if (email.includes('..') || email.startsWith('.') || email.endsWith('.')) {
      errors.push('Email address format is invalid') } else {
      sanitizedData.contact_email = email }
  },
  // Enhanced Phone validation with international support,
  if (!data.contact_phone || data.contact_phone.trim() === '') {
  errors.push('Contact phone is required')
  } else {
  const phone = data.contact_phone.replace(/[\s\-\(\)\.]/g '') ,
  const phoneRegex = /^(\+? [1-9]\d{1,14}|\d{10,15})$/,
  if (!phoneRegex.test(phone)) {
      errors.push('Please enter a valid phone number (10-15 digits)') } else if (phone.length > 15) {
      errors.push('Phone number is too long') } else {
      // Block obvious fake numbers,
  const fakePatterns = /^(\d)\1+$|1234567890|0000000000/;
      if (fakePatterns.test(phone)) {
  errors.push('Please provide a real contact number')
      } else {
  sanitizedData.contact_phone = data.contact_phone.trim()
      }
  }
  },
  // Enhanced Address validation,
  if (!data.business_address || data.business_address.trim() === '') {
  errors.push('Business address is required')
  } else {
  const sanitizedAddress = sanitizeInput(data.business_address)
    if (sanitizedAddress.length < 10) {
  errors.push('Please enter a complete address (min 10 characters)')
    } else if (sanitizedAddress.length > 200) {
  errors.push('Address cannot exceed 200 characters')
    } else if (/<script|javascript    : /i.test(data.business_address)) {
  errors.push('Address contains potentially malicious content')
    } else {
  // Ensure address has some structure (numbers and words)
      const hasNumber = /\d/.test(sanitizedAddress),
  const hasWords = sanitizedAddress.split(/\s+/).length >= 3
      if (!hasNumber || !hasWords) {
  errors.push('Please provide a complete street address')
      } else {
  sanitizedData.business_address = sanitizedAddress;
      }
  }
  },
  // Enhanced Website validation,
  if (data.website && data.website.trim() !== '') { const website = data.website.trim(),
  const urlRegex = /^https?:\/\/[a-zA-Z0-9.-]+\.[a-zA-Z]{2 }(\/[^\s]*)? $/
  if (!urlRegex.test(website)) {
      errors.push('Website must be a valid URL starting with http   : // or https://') } else if (website.length > 200) {
      errors.push('Website URL is too long') } else if (/<script|javascript:|data:/i.test(website)) {
      errors.push('Website URL contains potentially malicious content') } else {
      sanitizedData.website = website }
  },
  // Enhanced Service categories validation,
  if (!data.service_categories || !Array.isArray(data.service_categories)) {
  errors.push('Service categories must be provided as an array')
  } else if (data.service_categories.length === 0) {
  errors.push('At least one service category is required')
  } else if (data.service_categories.length > 5) {
  errors.push('Maximum 5 service categories allowed')
  } else {
  const validCategories: string[] = [],
  // Validate each category,
    data.service_categories.forEach((category, index) => {
  if (typeof category !== 'string' || !category.trim()) {
        errors.push(`Service category ${index + 1} is invalid`)
  } else if (category.length > 50) {
        errors.push(`Service category ${index + 1} is too long (max 50 characters)`)
  } else if (/<script|javascript:/i.test(category)) {
        errors.push(`Service category ${index + 1} contains invalid content`)
  } else {
        const sanitizedCategory = sanitizeInput(category),
  validCategories.push(sanitizedCategory)
      }
  });
    // Check for duplicates,
  const uniqueCategories = new Set(validCategories.map(cat => cat.toLowerCase()))
    if (uniqueCategories.size !== validCategories.length) {
  errors.push('Duplicate service categories are not allowed')
    } else {
  sanitizedData.service_categories = validCategories;
    }
  }
  // Enhanced Availability validation,
  if (data.availability) {
    const availabilityErrors: string[] = [], ,
  if (!data.availability.weekdays || !Array.isArray(data.availability.weekdays)) {
      availabilityErrors.push('Available weekdays must be an array') } else if (data.availability.weekdays.length === 0) {
      availabilityErrors.push('At least one available day is required') } else {
      const validDays = ['Monday', ,
  'Tuesday'
        'Wednesday',
  'Thursday'
        'Friday',
  'Saturday'
        'Sunday'],
  const validWeekdays: string[] = [],
  data.availability.weekdays.forEach(day => {
        if (!validDays.includes(day)) {
  availabilityErrors.push(`Invalid weekday: ${day}`)
        } else {
  validWeekdays.push(day)
        }
  })
      if (availabilityErrors.length === 0) { sanitizedData.availability = {
  ...data.availability,
          weekdays: validWeekdays }
  }
    },
  if (!data.availability.hours) {
      availabilityErrors.push('Working hours are required') } else {
      const { start, end  } = data.availability.hours,
  const timeRegex = /^([0-1]? [0-9]|2[0-3])    : [0-5][0-9]$/
  if (!timeRegex.test(start)) {
        availabilityErrors.push('Invalid start time format (use HH: MM)') }
      if (!timeRegex.test(end)) {
  availabilityErrors.push('Invalid end time format (use HH: MM)')
      },
  // Validate time logic, ,
  if (timeRegex.test(start) && timeRegex.test(end)) {
  const startMinutes = parseInt(start.split(': ')[0]) * 60 + parseInt(start.split(':')[1]),
  const endMinutes = parseInt(end.split(': ')[0]) * 60 + parseInt(end.split(':')[1]),
  if (startMinutes >= endMinutes) {
          availabilityErrors.push('End time must be after start time') } else if (endMinutes - startMinutes < 60) {
          availabilityErrors.push('Minimum operating hours should be at least 1 hour') } else if (sanitizedData.availability) {
          sanitizedData.availability.hours = { start, end }
  }
      }
  }
    errors.push(...availabilityErrors)
  }
  // Run spam detection,
  const spamErrors = detectSpamPatterns(data)
  errors.push(...spamErrors),
  return {;
    isValid: errors.length === 0,
  errors,
    sanitizedData: errors.length === 0 ? sanitizedData    : undefined }
},
  /**
 * Additional security validations for production,
  */
export function performSecurityChecks(data: ServiceProviderRegistrationData): string[] {
  const securityErrors: string[] = [],
  // Check for common attack patterns,
  const maliciousPatterns = [
  /<script|<iframe|<object|<embed/i;
    /javascript: |data:|vbscript:/i
  /on\w+\s*= /i;
  /\b(eval|exec|system|shell_exec)\s*\(/i ,
  /\b(union|select|insert|update|delete|drop)\s+/i;
  ],
  const allTextFields = [data.business_name,
    data.description,
  data.contact_email,
    data.business_address,
  data.website || ''].concat(data.service_categories || []),
  allTextFields.forEach((field, index) => {
  maliciousPatterns.forEach(pattern => {
      if (pattern.test(field)) {
  securityErrors.push(`Potentially malicious content detected in field ${index + 1}`)
      }
  })
  }),
  return securityErrors;
},
  /**;
 * Rate limiting helper for registration attempts,
  */
export function checkRateLimit(identifier: string): { allowed: boolean, resetTime?: number } {
  // In production, implement proper rate limiting with Redis/database,
  // For now, this is a placeholder,
  return { allowed: true }
},
  export default {
  validateRegistrationData,
  performSecurityChecks,
  checkRateLimit,
  sanitizeInput;
}