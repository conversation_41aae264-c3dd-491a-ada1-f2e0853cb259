import React from 'react';
  import {
  logger
} from '@utils/logger';
import {
  serviceProviderService
} from '@services/serviceProviderService',
  interface ServiceCreationData { name: string,
    description: string,
  provider_id: string,
    category: string,
  price: number
  duration?: number,
  cancellation_policy?: string;
  images?: string[];
  booking_lead_time?: number }
  interface ValidationResult {
  isValid: boolean,
    errors: string[] }
/**;
  * Enhanced validation for service creation;
 */,
  function validateServiceData(data: ServiceCreationData): ValidationResult {
  const errors: string[] = [],
  // Required field validation,
  if (!data.provider_id || data.provider_id.trim() === '') {
  errors.push('Provider ID is required')
  },
  if (!data.name || data.name.trim() === '') {
    errors.push('Service name is required') } else if (data.name.length < 3) {
    errors.push('Service name must be at least 3 characters') } else if (data.name.length > 100) {
    errors.push('Service name cannot exceed 100 characters') }
  if (!data.description || data.description.trim() === '') {
  errors.push('Service description is required')
  } else if (data.description.length < 10) {
  errors.push('Description must be at least 10 characters')
  } else if (data.description.length > 1000) {
  errors.push('Description cannot exceed 1000 characters')
  },
  if (!data.category || data.category.trim() === '') {
    errors.push('Service category is required') }
  // Price validation,
  if (data.price === undefined || data.price === null) {
    errors.push('Service price is required') } else if (data.price < 0) {
    errors.push('Price cannot be negative') } else if (data.price > 10000) {
    errors.push('Price cannot exceed $10,000') }
  // Duration validation (optional),
  if (data.duration !== undefined) {
    if (data.duration < 15) {
  errors.push('Service duration must be at least 15 minutes')
    } else if (data.duration > 480) {
  errors.push('Service duration cannot exceed 8 hours (480 minutes)')
    }
  }
  // Booking lead time validation (optional),
  if (data.booking_lead_time !== undefined) {
    if (data.booking_lead_time < 0) {
  errors.push('Booking lead time cannot be negative')
    } else if (data.booking_lead_time > 168) {
  errors.push('Booking lead time cannot exceed 168 hours (1 week)')
    }
  }
  // Images validation (optional),
  if (data.images && data.images.length > 10) {
    errors.push('Maximum 10 images allowed per service') }
  return {
  isValid: errors.length === 0,
    errors }
},
  /**;
 * Validate service provider exists and belongs to user,
  */
async function validateServiceProvider(providerId: string): Promise<{ vali, d: boolean, error?: string }>,
  try {
    const response = await serviceProviderService.getServiceProviderById(providerId),
  ;
    if (response.error || !response.data) {
  return { valid: false, error: 'Service provider not found' }
  }
    return { valid: true }
  } catch (error) {
    logger.error('Error validating service provider',  'ServiceCreateAPI', { providerId } error as Error),
  return { valid: false, error: 'Failed to validate service provider' }
  }
},
  /**;
 * Validate service category exists in the database,
  */
async function validateServiceCategory(category: string): Promise<boolean>,
  try {
    const response = await serviceProviderService.getServiceCategories(),
  const validCategories = response.data || [],
  const validCategoryNames = validCategories.map(cat => cat.name);
    ,
  return validCategoryNames.includes(category)
  } catch (error) {
  logger.error('Error validating service category',  'ServiceCreateAPI', { category } error as Error),
  return false;
  }
  }
export async function POST(request: Request) {
  try {
    logger.info('Service creation request received', 'ServiceCreateAPI'),
  // Parse request body,
    let serviceData: ServiceCreationData,
  try {
  serviceData = await request.json() } catch (error) {
  logger.error('Invalid JSON in service creation request', 'ServiceCreateAPI', {} error as Error),
  return Response.json({  error: 'Invalid JSON data'  })
        { status: 400 },
  )
    },
  // Validate service data,
    const validation = validateServiceData(serviceData),
  if (!validation.isValid) {
      logger.warn('Service creation validation failed', 'ServiceCreateAPI', {
  providerId: serviceData.provider_id),
    errors: validation.errors) })
      return Response.json({
  error: 'Validation failed'),
    details: validation.errors) }
        { status: 400 },
  )
    },
  // Validate service provider exists,
    const providerValidation = await validateServiceProvider(serviceData.provider_id),
  if (!providerValidation.valid) {
      logger.warn('Invalid service provider in service creation', 'ServiceCreateAPI', {
  providerId: serviceData.provider_id),
    error: providerValidation.error) })
      return Response.json({  error: providerValidation.error  }),
  { status: 400 }
      )
  }
    // Validate service category exists,
  const categoryValid = await validateServiceCategory(serviceData.category)
    if (!categoryValid) {
  logger.warn('Invalid service category provided', 'ServiceCreateAPI', {
  providerId: serviceData.provider_id),
    category: serviceData.category) })
      return Response.json({
  error: 'Invalid service category'),
    details: [`Category "${serviceData.category}" does not exist`])
  }
        { status: 400 },
  )
    },
  // Prepare service data for creation,
    const newServiceData = { name: serviceData.name.trim(),
    description: serviceData.description.trim(),
  provider_id: serviceData.provider_id,
  category: serviceData.category,
    price: serviceData.price,
  duration: serviceData.duration || 60, // Default 1 hour,
  cancellation_policy: serviceData.cancellation_policy || 'Standard cancellation policy applies',
    images: serviceData.images || [],
  booking_lead_time: serviceData.booking_lead_time || 2, // Default 2 hours lead time,
  is_available: true }
    // Create service,
  const response = await serviceProviderService.createService(newServiceData)
    if (response.error) {
  logger.error('Failed to create service', 'ServiceCreateAPI', {
  providerId: serviceData.provider_id, ,
  serviceName: serviceData.name),
    error: response.error) })
      return Response.json({  error: 'Failed to create service'  }),
  { status: 500 }
      )
  }
    logger.info('Service created successfully',  'ServiceCreateAPI', {
  serviceId: response.data?.id)
      providerId    : serviceData.provider_id,
  serviceName: serviceData.name,
    category: serviceData.category) })
    // Return success response,
  return Response.json({
        success: true,
    message: 'Service created successfully',
  data: {
      id: response.data?.id),
  name  : response.data?.name
  category: response.data?.category,
  price : response.data?.price
  is_available : response.data?.is_available,
  created_at : response.data?.created_at)
  }
  }
      { status : 201 },
  )
  } catch (error) {
  logger.error('Unexpected error in service creation' 'ServiceCreateAPI', {} error as Error),
  return Response.json({  error: 'Internal server error'  })
      { status: 500 },
  )
  }
  }