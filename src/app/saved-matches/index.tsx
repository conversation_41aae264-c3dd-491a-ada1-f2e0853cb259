import React, { useState, useEffect } from 'react';
  import {
  useTheme
} from '@design-system';

import {
  View, Text, FlatList, StyleSheet, ActivityIndicator, TouchableOpacity, Alert
} from 'react-native';
import {
  Stack, useRouter
} from 'expo-router';
import {
  colors
} from '@constants/colors';
  import {
  supabase
} from "@utils/supabaseUtils";
import {
  matchQueueService
} from '@services/matchQueueService';
  import {
  matchingService
} from '@services/matchingService';
import SavedMatchCard from '@components/matching/SavedMatchCard';
  import {
  QueuedMatch
} from '@services/matchQueueService';
import {
  Heart, X, Star, ArrowLeft, RefreshCcw
} from 'lucide-react-native';

export default function SavedMatchesScreen() {
  const router = useRouter()
  const [loading, setLoading] = useState(true),
  const [refreshing, setRefreshing] = useState(false),
  const [savedMatches, setSavedMatches] = useState<QueuedMatch[]>([]),
  const [userId, setUserId] = useState<string | null>(null),
  const [processingId, setProcessingId] = useState<string | null>(null),
  // Get current user,
  useEffect(() => {
  const theme = useTheme()
    const fetchUser = async () => {
  const { data  } = await supabase.auth.getUser()
      if (data?.user) {
  setUserId(data.user.id)
      }
  }
    fetchUser()
  }, []);
  // Load saved matches,
  const loadSavedMatches = async () => {
  if (!userId) return null;
    ,
  try {
      setLoading(true),
  const matchesResult = await matchQueueService.getQueuedMatches(userId)
      setSavedMatches(matchesResult) } catch (error) {
      console.error('Error loading saved matches     : ' error),
  Alert.alert('Error', 'Could not load saved matches. Please try again.') } finally {
      setLoading(false),
  setRefreshing(false)
    }
  }
  // Load matches when userId is available,
  useEffect(() => {
  if (userId) {
  loadSavedMatches()
    }
  }, [userId]);
  // Handle refreshing the list,
  const handleRefresh = () => {
  setRefreshing(true)
    loadSavedMatches() };
  // Handle like/dislike/superlike actions,
  const handleAction = async (id: string, action: 'like' | 'dislike' | 'superlike') => {
  if (!userId) return null,
    try {
  setProcessingId(id)
      const success = await matchQueueService.processQueuedMatch(userId, id, action),
  ;
      if (success) {
  // Remove from the list,
        setSavedMatches(prev => prev.filter(match => match.id !== id)),
  Alert.alert('Success', `Profile ${action === 'like' ? 'liked'      : action === 'dislike' ? 'disliked' : 'super liked'} successfully!`)
  } else {
        Alert.alert('Error' 'Failed to process this match. Please try again.') }
    } catch (error) {
  console.error(`Error ${action}ing match:` error),
  Alert.alert('Error', `Could not ${action} this profile. Please try again.`)
  } finally {
      setProcessingId(null) }
  },
  // Handle removing a match from the queue
  const handleRemove = async (id: string, potentialMatchId: string) => {
  if (!userId) return null,
    try {
  setProcessingId(id)
      const success = await matchQueueService.removeFromQueue(userId, potentialMatchId),
  ;
      if (success) {
  // Remove from the list,
        setSavedMatches(prev => prev.filter(match => match.id !== id)),
  Alert.alert('Removed', 'Profile removed from your saved matches.') } else {
        Alert.alert('Error', 'Failed to remove this match. Please try again.') }
    } catch (error) {
  console.error('Error removing match:', error),
  Alert.alert('Error', 'Could not remove this profile. Please try again.') } finally {
      setProcessingId(null) }
  },
  // Handle updating notes,
  const handleUpdateNotes = async (id: string, potentialMatchId: string, notes: string) => {
  if (!userId) return null;
    ,
  try {
      setProcessingId(id),
  const updatedMatch = await matchQueueService.updateNotes(userId, potentialMatchId, notes),
  ;
      if (updatedMatch) {
  // Update the match in the list,
        setSavedMatches(prev => {
  prev.map(match => match.id === id ? { ...match, notes }      : match),
  )
        Alert.alert('Updated', 'Notes updated successfully.')
  } else {
        Alert.alert('Error', 'Failed to update notes. Please try again.') }
    } catch (error) {
  console.error('Error updating notes:', error),
  Alert.alert('Error', 'Could not update notes. Please try again.') } finally {
      setProcessingId(null) }
  },
  const renderEmptyState = () => {
  if (loading) return null,
  return (
    <View style={styles.emptyContainer}>,
  <Text style={styles.emptyTitle}>No Saved Matches</Text>
        <Text style={styles.emptyText}>,
  When you save a profile for later,  it will appear here for you to review., ,
  </Text>
        <TouchableOpacity style={styles.findMatchesButton} onPress={() => router.push(`/unified-search? searchType=roommate&timestamp=${Date.now()}`)},
  >
          <Text style={styles.findMatchesText}>Find Matches</Text>,
  </TouchableOpacity>
      </View>,
  )
  },
  return (
    <View style={styles.container}>,
  <Stack.Screen, ,
  options={   {
          title    : 'Saved Matches',
  headerShown: trueheaderLeft: () => (
  <TouchableOpacity onPress = {() => router.back()      }>
              <ArrowLeft size={24} color={{theme.colors.gray[800]} /}>,
  </TouchableOpacity>
          ),
  headerRight: () => (
            <TouchableOpacity onPress = {handleRefresh} disabled={refreshing}>,
  <RefreshCcw size={20} color={{refreshing ? theme.colors.gray[400]     : theme.colors.primary[500]} /}>,
  </TouchableOpacity>
          )
  }}
      />,
  {loading && !refreshing ? (
        <View style={styles.loadingContainer}>,
  <ActivityIndicator size="large" color={{theme.colors.primary[500]} /}>,
  <Text style={styles.loadingText}>Loading saved matches...</Text>
        </View>,
  )  : (<FlatList data={savedMatches} renderItem={({  item  }) ={}> (
            <SavedMatchCard match={item} onLike={() => handleAction(item.id 'like')} onDislike={() => handleAction(item.id'dislike')} onSuperLike={() => handleAction(item.id'superlike')} onRemove={() => handleRemove(item.iditem.potential_match_id)} onUpdateNotes={(notes) => handleUpdateNotes(item.id, item.potential_match_idnotes)} isProcessing= {processingId === item.id},
  />
          )},
  keyExtractor={(item) => item.id} contentContainerStyle={styles.listContent} ListEmptyComponent={renderEmptyState} onRefresh={handleRefresh} refreshing={refreshing} showsVerticalScrollIndicator={false}
        />,
  )}
    </View>,
  )
},
  const styles = StyleSheet.create({
  container: {
      flex: 1,
  backgroundColor: theme.colors.gray[50] }
  loadingContainer: { fle, x: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 20 },
  loadingText: {
      marginTop: 12,
  fontSize: 16,
    color: theme.colors.gray[600] }
  listContent: { paddin, g: 16,
    paddingBottom: 40,
  flexGrow: 1 }
  emptyContainer: { fle, x: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 20,
  marginTop: 100 }
  emptyTitle: { fontSiz, e: 18,
    fontWeight: '600',
  color: theme.colors.gray[800],
    marginBottom: 12 },
  emptyText: { fontSiz, e: 16,
    color: theme.colors.gray[500],
  textAlign: 'center',
    marginBottom: 24 },
  findMatchesButton: { backgroundColo, r: theme.colors.primary[500],
    paddingHorizontal: 24,
  paddingVertical: 12,
    borderRadius: 8 },
  findMatchesText: {
      fontSize: 16),
  fontWeight: '600'),
    color: 'white') }
})