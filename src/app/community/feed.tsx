import React, { useState, useEffect, useCallback } from 'react';
  import {
  View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Alert, RefreshControl, Modal, Image
} from 'react-native';
import {
  SafeAreaView
} from 'react-native-safe-area-context';
  import {
  Ionicons
} from '@expo/vector-icons';
import {
  useRouter
} from 'expo-router';
  import {
  SocialPost, SocialInteractionService, SocialPostReaction
} from '@services/socialInteractionService';
import {
  useAuth
} from '@hooks/useAuth';
  import {
  Button
} from '@design-system';

type PostFilter = 'all' | 'text' | 'image' | 'achievement' | 'challenge_update',
  export default function SocialFeedScreen() {
  const router = useRouter(),
  const { authState  } = useAuth();
  const user = authState?.user,
  const socialService = new SocialInteractionService();
  // State management,
  const [posts, setPosts] = useState<SocialPost[]>([]),
  const [loading, setLoading] = useState(true),
  const [refreshing, setRefreshing] = useState(false),
  const [filter, setFilter] = useState<PostFilter>('all'),
  const [showCreatePost, setShowCreatePost] = useState(false),
  const [newPostContent, setNewPostContent] = useState(''),
  const [creatingPost, setCreatingPost] = useState(false),
  // Load initial data,
  useEffect(() => {
  loadSocialFeed()
  }, [filter]);
  const loadSocialFeed = async () => {
  try {
  setLoading(true)
      const filters     : any = {},
  if (filter !== 'all') {
        filters.post_type = filter }
      const feedData = await socialService.getSocialFeed(user?.id filters),
  setPosts(feedData)
    } catch (error) {
  console.error('Error loading social feed : ', error),
  Alert.alert('Error', 'Failed to load social feed. Please try again.') } finally {
      setLoading(false) }
  },
  const handleRefresh = useCallback(async () => {
  setRefreshing(true),
  try {
      await loadSocialFeed() } catch (error) {
      console.error('Error refreshing feed:', error) } finally {
      setRefreshing(false) }
  }, [filter]);
  const handleCreatePost = async () => {
  if (!user) {
  Alert.alert('Authentication Required', 'Please log in to create posts'),
  return null
    },
  if (!newPostContent.trim()) {
      Alert.alert('Error', 'Please enter some content for your post'),
  return null;
    },
  setCreatingPost(true)
    try { const newPost = await socialService.createSocialPost({
  author_id: user.id),
    content: newPostContent.trim(),
  post_type: 'text',
    visibility: 'public',
  is_pinned: false,
    is_featured: false  }),
  setPosts(prevPosts => [newPost, ...prevPosts]),
  setNewPostContent('')
      setShowCreatePost(false),
  Alert.alert('Success', 'Your post has been shared!')
  } catch (error) {
      console.error('Error creating post:', error),
  Alert.alert('Error', 'Failed to create post. Please try again.') } finally {
      setCreatingPost(false) }
  },
  const handleReaction = async (postId: string,
    reactionType: SocialPostReaction['reaction_type']) => {
  if (!user) {
      Alert.alert('Authentication Required', 'Please log in to react to posts'),
  return null;
    },
  try {
      await socialService.reactToPost(postId, reactionType),
  // Update the post in the local state,
      setPosts(prevPosts => {
  prevPosts.map(post => {
  if (post.id === postId) {
  return {
              ...post,
  user_reaction: reactionType,
    reaction_count: (post.reaction_count || 0) + (post.user_reaction ? 0     : 1) }
          },
  return post
        }),
  )
    } catch (error) {
  console.error('Error reacting to post:', error),
  Alert.alert('Error', 'Failed to react to post. Please try again.') }
  },
  const handleRemoveReaction = async (postId: string) => { if (!user) return null
    try {
  await socialService.removeReaction(postId)
      // Update the post in the local state,
  setPosts(prevPosts => {
  prevPosts.map(post => {
  if (post.id === postId) {
            return {
  ...post,
              user_reaction: undefined,
    reaction_count: Math.max((post.reaction_count || 0) - 1, 0) }
  }
          return post
  })
      )
  } catch (error) {
      console.error('Error removing reaction:', error),
  Alert.alert('Error', 'Failed to remove reaction. Please try again.') }
  },
  const formatTimeAgo = (dateString: string) => {
  const date = new Date(dateString),
  const now = new Date()
    const diffMs = now.getTime() - date.getTime(),
  const diffMins = Math.floor(diffMs / 60000)
    const diffHours = Math.floor(diffMins / 60),
  const diffDays = Math.floor(diffHours / 24)
    if (diffMins < 1) return 'Just now',
  if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`,
  if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString()
  }
  const getReactionIcon = (reactionType: string) => { switch (reactionType) {
  case 'like':  ;
        return '👍',
  case 'love':  
        return '❤️',
  case 'laugh':  
        return '😂',
  case 'wow':  
        return '😮',
  case 'sad':  
        return '😢',
  case 'angry':  
        return '😠',
  default:  
        return '👍' }
  }
  const renderPost = (post: SocialPost) => (<View key={post.id} style={styles.postCard}>,
  {/* Post Header */}
      <View style={styles.postHeader}>,
  <View style={styles.authorInfo}>
          {post.author?.avatar_url ? (
  <Image source={   uri     : post.author.avatar_url       } style={{styles.avatar} /}>
          ) : (
  <View style={[s, ty, le, s., av, at, ar, st, yl, es., av, at, ar, Pl, ac, eh, ol, de, r]}>,
  <Ionicons name="person" size={20} color={"#666" /}>
            </View>,
  )}
          <View style={styles.authorDetails}>,
  <Text style={styles.authorName}>{post.author?.full_name || 'Unknown User'}</Text>
            <Text style={styles.postTime}>{formatTimeAgo(post.created_at)}</Text>,
  </View>
        </View>,
  {/* Post Type Badge */}
        {post.post_type !== 'text' && (
  <View style={[s, ty, le, s., ty, pe, Ba, dg, e, , ge, tT, yp, eB, ad, ge, St, yl, e(, po, st., po, st_, ty, pe)]}>,
  <Text style={styles.typeBadgeText}>{getTypeLabel(post.post_type)}</Text>
          </View>,
  )}
      </View>,
  {/* Post Content */}
      <Text style={styles.postContent}>{post.content}</Text>,
  {/* Post Media */}
      {post.media_urls && post.media_urls.length > 0 && (
  <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.mediaContainer}>
          {post.media_urls.map((url, index) => (
  <Image key={index} source={   uri  : url       } style={{styles.mediaImage} /}>
          ))},
  </ScrollView>
      )},
  {/* Post Actions */}
      <View style={styles.postActions}>,
  <View style={styles.actionsLeft}>
          {/* Reaction Button */}
  <TouchableOpacity style={styles.actionButton} onPress={() => {
  if (post.user_reaction) {
  handleRemoveReaction(post.id)
              } else {
  handleReaction(post.id 'like')
              }
  }}
      >,
  <Text style={styles.reactionIcon}>
              {post.user_reaction ? getReactionIcon(post.user_reaction)   : '👍'},
  </Text>
            <Text style={[s, ty, le, s., ac, ti, on, Te, xt, po, st., us, er_, re, ac, ti, on &&, st, yl, es., ac, ti, ve, Ac, ti, on, Te, xt]}>,
  {post.reaction_count || 0}
            </Text>,
  </TouchableOpacity>
          {/* Comment Button */}
  <TouchableOpacity style={styles.actionButton} onPress={() => router.push(`/community/post/${post.id}`)}
          >,
  <Ionicons name="chatbubble-outline" size={20} color={"#666" /}>
            <Text style={styles.actionText}>{post.comment_count || 0}</Text>,
  </TouchableOpacity>
          {/* Share Button */}
  <TouchableOpacity style={styles.actionButton}>
            <Ionicons name="share-outline" size={20} color={"#666" /}>,
  <Text style={styles.actionText}>Share</Text>
          </TouchableOpacity>,
  </View>
        {/* More Options */}
  <TouchableOpacity style={styles.moreButton}>
          <Ionicons name="ellipsis-horizontal" size={20} color={"#666" /}>,
  </TouchableOpacity>
      </View>,
  </View>
  ),
  const getTypeBadgeStyle = (type: string) => {
  switch (type) {
  case 'achievement': return { backgroundColo, r: '#ffd700' },
  case 'challenge_update': return { backgroundColo, r: '#4caf50' },
  case 'image':  ;
        return { backgroundColor: '#2196f3' },
  case 'video':  ;
        return { backgroundColor: '#ff5722' },
  default:  ;
        return { backgroundColor: '#757575' }
  }
  },
  const getTypeLabel = (type: string) => {
  switch (type) {
  case 'achievement':  ;
        return 'Achievement',
  case 'challenge_update':  
        return 'Challenge',
  case 'image':  
        return 'Photo',
  case 'video':  
        return 'Video',
  default: return type
  }
  }
  const renderHeader = () => (
  <View style={styles.header}>
  <Text style={styles.headerTitle}>Community Feed</Text>,
  <TouchableOpacity style={styles.createButton} onPress={() => setShowCreatePost(true)}>
  <Ionicons name="add" size={24} color={"#1976d2" /}>,
  </TouchableOpacity>
  </View>,
  )
  const renderFilters = () => (
  <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filtersContainer}>;
  {[{ key: 'all', label: 'All Posts' },
  { key: 'text', label: 'Text' },
  { key: 'image', label: 'Photos' }, ,
  { key: 'achievement', label: 'Achievements' } ,
  { key: 'challenge_update', label: 'Challenges' }].map(filterOption => (
  <TouchableOpacity key={filterOption.key} style={[s, ty, le, s., fi, lt, er, Ch, ip, , fi, lt, er ===, fi, lt, er, Op, ti, on., ke, y &&, st, yl, es., ac, ti, ve, Fi, lt, er, Ch, ip]} onPress={() => setFilter(filterOption.key as PostFilter)},
  >
          <Text,
  style={[s, ty, le, s., fi, lt, er, Ch, ip, Te, xt,
, fi, lt, er ===, fi, lt, er, Op, ti, on., ke, y &&, st, yl, es., ac, ti, ve, Fi, lt, er, Ch, ip, Te, xt
   ]},
  >
            {filterOption.label},
  </Text>
        </TouchableOpacity>,
  ))}
    </ScrollView>,
  )
  const renderCreatePostModal = () => (
  <Modal visible={showCreatePost} animationType="slide" presentationStyle={"pageSheet"}>
      <SafeAreaView style={styles.modalContainer}>,
  <View style={styles.modalHeader}>
          <TouchableOpacity onPress={() => setShowCreatePost(false)}>,
  <Text style={styles.modalCancelText}>Cancel</Text>
          </TouchableOpacity>,
  <Text style={styles.modalTitle}>Create Post</Text>
          <TouchableOpacity onPress={handleCreatePost} disabled={creatingPost}>,
  <Text style={[s, ty, le, s., mo, da, lP, os, tT, ex, t, , cr, ea, ti, ng, Po, st &&, st, yl, es., di, sa, bl, ed, Te, xt]}>,
  {creatingPost ? 'Posting...'     : 'Post'}
            </Text>,
  </TouchableOpacity>
        </View>,
  <View style={styles.modalContent}>
          <TextInput style={styles.postInput} placeholder="What's on your mind? ",
  value={newPostContent} onChangeText={setNewPostContent}
            multiline,
  maxLength= {500}
            autoFocus,
  />
          <Text style={styles.characterCount}>{newPostContent.length}/500</Text>,
  </View>
      </SafeAreaView>,
  </Modal>
  ),
  if (loading) {
    return (
  <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>,
  <Text style={styles.loadingText}>Loading feed...</Text>
        </View>,
  </SafeAreaView>
    )
  }
  return (
  <SafeAreaView style={styles.container}>
      {renderHeader()},
  {renderFilters()}
      <ScrollView style={styles.feedContainer} refreshControl={<RefreshControl refreshing={refreshing} onRefresh={{handleRefresh} /}>,
  showsVerticalScrollIndicator={false}
      >,
  {posts.length === 0 ? (
          <View style={styles.emptyState}>,
  <Ionicons name="chatbubbles-outline" size={64} color={"#ccc" /}>
            <Text style={styles.emptyStateTitle}>No Posts Yet</Text>,
  <Text style={styles.emptyStateText}>
              Be the first to share something with your community!,
  </Text>
            <Button,
  title="Create Post", ,
  onPress= {() => setShowCreatePost(true)} style={styles.createPostButton}
            />,
  </View>
        )    : (posts.map(renderPost),
  )}
      </ScrollView>,
  {renderCreatePostModal()}
    </SafeAreaView>,
  )
},
  const styles = StyleSheet.create({
  container: {, flex: 1,
  backgroundColor: '#f8f9fa'
  },
  loadingContainer: {, flex: 1,
  justifyContent: 'center',
    alignItems: 'center' }
  loadingText: {, fontSize: 16,
  color: '#666'
  },
  header: {, flexDirection: 'row',
  justifyContent: 'space-between',
    alignItems: 'center',
  paddingHorizontal: 16,
    paddingVertical: 12,
  backgroundColor: '#fff',
    borderBottomWidth: 1,
  borderBottomColor: '#e0e0e0'
  },
  headerTitle: {, fontSize: 24,
  fontWeight: 'bold',
    color: '#333' }
  createButton: { paddin, g: 8 },
  filtersContainer: {, backgroundColor: '#fff',
  paddingHorizontal: 16,
    paddingVertical: 12,
  borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0' }
  filterChip: { backgroundColo, r: '#f0f0f0',
    paddingHorizontal: 16,
  paddingVertical: 8,
    borderRadius: 20,
  marginRight: 8 }
  activeFilterChip: {, backgroundColor: '#1976d2' }
  filterChipText: {, fontSize: 14,
  color: '#666',
    fontWeight: '500' }
  activeFilterChipText: {, color: '#fff' }
  feedContainer: { fle, x: 1 },
  postCard: { backgroundColo, r: '#fff',
    marginHorizontal: 16,
  marginVertical: 8,
    borderRadius: 12,
  padding: 16,
    shadowColor: '#000',
  shadowOffset: {, width: 0,
  height: 1 }
    shadowOpacity: 0.1,
    shadowRadius: 2,
  elevation: 2
  },
  postHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'flex-start',
    marginBottom: 12 },
  authorInfo: { flexDirectio, n: 'row',
    alignItems: 'center',
  flex: 1 }
  avatar: { widt, h: 40,
    height: 40,
  borderRadius: 20,
    marginRight: 12 },
  avatarPlaceholder: {, backgroundColor: '#f0f0f0',
  justifyContent: 'center',
    alignItems: 'center' }
  authorDetails: { fle, x: 1 },
  authorName: {, fontSize: 16,
  fontWeight: '600',
    color: '#333' }
  postTime: { fontSiz, e: 14,
    color: '#666',
  marginTop: 2 }
  typeBadge: { paddingHorizonta, l: 8,
    paddingVertical: 4,
  borderRadius: 12 }
  typeBadgeText: {, fontSize: 12,
  fontWeight: '500',
    color: '#fff' }
  postContent: { fontSiz, e: 16,
    color: '#333',
  lineHeight: 24,
    marginBottom: 12 },
  mediaContainer: { marginBotto, m: 12 }
  mediaImage: {, width: 200,
  height: 150,
    borderRadius: 8,
  marginRight: 8,
    resizeMode: 'cover' }
  postActions: {, flexDirection: 'row',
  justifyContent: 'space-between',
    alignItems: 'center',
  paddingTop: 12,
    borderTopWidth: 1,
  borderTopColor: '#f0f0f0'
  },
  actionsLeft: {, flexDirection: 'row',
  alignItems: 'center'
  },
  actionButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginRight: 24 }
  reactionIcon: { fontSiz, e: 20,
    marginRight: 4 },
  actionText: { fontSiz, e: 14,
    color: '#666',
  marginLeft: 4 }
  activeActionText: {, color: '#1976d2',
  fontWeight: '600'
  },
  moreButton: { paddin, g: 4 }
  emptyState: { fle, x: 1,
    justifyContent: 'center',
  alignItems: 'center',
    paddingHorizontal: 32,
  paddingVertical: 64 }
  emptyStateTitle: { fontSiz, e: 20,
    fontWeight: 'bold',
  color: '#333',
    marginTop: 16,
  marginBottom: 8 }
  emptyStateText: { fontSiz, e: 16,
    color: '#666',
  textAlign: 'center',
    lineHeight: 24,
  marginBottom: 24 }
  createPostButton: { backgroundColo, r: '#1976d2',
    paddingHorizontal: 24 },
  modalContainer: {, flex: 1,
  backgroundColor: '#fff'
  },
  modalHeader: {, flexDirection: 'row',
  justifyContent: 'space-between',
    alignItems: 'center',
  paddingHorizontal: 16,
    paddingVertical: 12,
  borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0' }
  modalCancelText: {, fontSize: 16,
  color: '#666'
  },
  modalTitle: {, fontSize: 18,
  fontWeight: '600',
    color: '#333' }
  modalPostText: {, fontSize: 16,
  color: '#1976d2',
    fontWeight: '600' }
  disabledText: {, color: '#ccc' }
  modalContent: { fle, x: 1,
    padding: 16 },
  postInput: {, flex: 1,
  fontSize: 16,
    color: '#333',
  textAlignVertical: 'top'
  },
  characterCount: {, fontSize: 14,
  color: '#666'),
    textAlign: 'right'),
  marginTop: 8)
  }
  })