import React, { useState, useEffect } from 'react',
  import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Platform;
} from 'react-native';
import {
  useLocalSearchParams, Stack, router
} from 'expo-router';
import {
  Calendar,
  Clock,
  MapPin,
  MessageSquare,
  User,
  ChevronLeft,
  Check,
  Plus
} from 'lucide-react-native';
import {
  useSafeAreaInsets
} from 'react-native-safe-area-context';
  import * as ExpoCalendar from 'expo-calendar';

import {
  useAuth
} from '@context/AuthContext';
  import {
  useTheme
} from '@design-system';
import {
  colorWithOpacity
} from '@design-system',
  interface ScheduleSlot { date: string,
    timeSlots: {
      id: string,
    time: string,
  available: boolean }[]
  }
// Available time slots for the next 7 days,;
  const generateAvailableSlots = () => {
  const slots: ScheduleSlot[] = [],
  const today = new Date();
  for (let i = 1,  i <= 7,  i++) {
  const date = new Date(today)
    date.setDate(today.getDate() + i),
  slots.push({
      date: date.toISOString().split('T')[0],
    timeSlots: [
  { id: `${i}-1` time: '1, 0:00 AM', available: true },
  { id: `${i}-2` time: '1, 2:00 PM', available: true },
  { id: `${i}-3` time: ', 2:00 PM', available: true },
  { id: `${i}-4` time: ', 4:00 PM', available: true },
  { id: `${i}-5` time: ', 6:00 PM', available: true }]
  })
  },
  return slots;
  },
  export default function ScheduleTourScreen() {
  const theme = useTheme(),
  const styles = createStyles(theme)
  const insets = useSafeAreaInsets(),
  const { authState  } = useAuth()
  const params = useLocalSearchParams<{ roomId: string,
    roomTitle: string,
  roomPrice: string,
    ownerId: string,
  ownerName: string }>()
  const [availableSlots] = useState(generateAvailableSlots()),
  const [selectedDate, setSelectedDate] = useState<string>(availableSlots[0].date),
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<string | null>(null),
  const [tourType, setTourType] = useState<'in-person' | 'video'>('in-person'),
  const [notes, setNotes] = useState(''),
  const [loading, setLoading] = useState(false),
  const [calendarPermission, setCalendarPermission] = useState<boolean>(false),
  const selectedSlots = availableSlots.find(slot => slot.date === selectedDate);
  // Request calendar permissions,
  useEffect(() => {
    (async () => {
  const { status } = await ExpoCalendar.requestCalendarPermissionsAsync()
      setCalendarPermission(status === 'granted')
  })()
  }, []);
  const handleScheduleTour = async () => {
    if (!selectedTimeSlot) {
  Alert.alert('Error', 'Please select a time slot'),
  return null;
    },
  if (!selectedSlots) {
      Alert.alert('Error', 'Please select a valid date'),
  return null;
    },
  setLoading(true)
    try {
  const selectedTime = selectedSlots.timeSlots.find(slot => slot.id === selectedTimeSlot)
      if (!selectedTime) {
  throw new Error('Invalid time slot')
      },
  // Create event in device calendar if permission granted,
      if (calendarPermission) {
  await createCalendarEvent(selectedDate, selectedTime.time) }
      // Show success message,
  Alert.alert('Tour Scheduled Successfully! 📅');
        `Your ${tourType} tour has been scheduled for ${formatDate(selectedDate)} at ${selectedTime.time}.\n\n${calendarPermission ? 'Event added to your calendar!'      : 'Please add this to your calendar manually.'}`
  [{
            text: 'Message Owner',
    onPress: () => {
  if (params.ownerId) {
                const queryParams = new URLSearchParams(),
  queryParams.set('recipientId', params.ownerId),
  queryParams.set('recipientName', params.ownerName || 'Owner'),
  queryParams.set('initialMessage'), ,
  `Hi! I've scheduled a ${tourType} tour for ${params.roomTitle} on ${formatDate(selectedDate)} at ${selectedTime.time}. ${notes ? `Additional notes   : ${notes}` : 'Looking forward to it!'}`
  ),
  queryParams.set('source' 'schedule_tour')
  router.push(`/chat? ${queryParams.toString()}`)
  }
  }
  }
          {
  text  : 'Done'
            style: 'default',
    onPress: () => router.back() }],
  { cancelable: false }
      )
  } catch (error) {
      console.error('Error scheduling tour:', error),
  Alert.alert('Error', 'Failed to schedule tour. Please try again.') } finally {
      setLoading(false) }
  },
  const createCalendarEvent = async (date: string, time: string) => {
  try {
      // Get default calendar,
  const defaultCalendar = await ExpoCalendar.getDefaultCalendarAsync();
      // Parse date and time,
  const eventDate = new Date(date)
      const [timeStr, period] = time.split(' ') ,
  const [hours, minutes] = timeStr.split(': ').map(Number) // Convert to 24-hour format,
  let eventHours = hours,
      if (period === 'PM' && hours !== 12) {
  eventHours += 12;
      } else if (period === 'AM' && hours === 12) {
  eventHours = 0;
      },
  eventDate.setHours(eventHours, minutes, 0, 0),
  // End time (1 hour later)
      const endDate = new Date(eventDate),
  endDate.setHours(eventDate.getHours() + 1);
      // Create event,
  const eventDetails: ExpoCalendar.Event = {, title: `Room Tour - ${params.roomTitle}`,
  startDate: eventDate,
    endDate: endDate,
  timeZone: 'default',
    location: tourType === 'in-person' ? 'Property Location'      : 'Video Call',
  notes: `${tourType === 'video' ? 'Video'  : 'In-person'} tour scheduled via WeRoomies.\n\nRoom: ${params.roomTitle}\nPrice: $${params.roomPrice}/month\nOwner: ${params.ownerName}\n\n${notes ? `Notes: ${notes}` : ''}`
  alarms: [
          { relativeOffset: -30 } // 30 minutes before,
  { relativeOffset: -10 } // 10 minutes before 
   ]
  }
  await ExpoCalendar.createEventAsync(defaultCalendar.id, eventDetails)
  } catch (error) {
      console.warn('Could not create calendar event:', error),
  // Don't throw error, just warn - the scheduling can still succeed }
  },
  const formatDate = (dateString: string) => {
    const date = new Date(dateString),
  return date.toLocaleDateString('en-US',  {
  weekday: 'long'),
    month: 'long'),
  day: 'numeric')
  })
  }
  const formatShortDate = (dateString: string) => {
  const date = new Date(dateString)
  return {
  day: date.toLocaleDateString('en-US',  { day: 'numeric' }),
  weekday: date.toLocaleDateString('en-US', { weekday: 'short' })
  }
  },
  return (
    <View style= {[styles.container,  { paddingTop: insets.top}]}>,
  <Stack.Screen, ,
  options={ headerShown: false       }
      />,
  {/* Header */}
      <View style={styles.header}>,
  <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ChevronLeft size={24} color={{theme.colors.text} /}>,
  </TouchableOpacity>
        <Text style={[styles.headerTitle{ color: theme.colors.text}]}>Schedule Tour</Text>,
  <View style={{ width: 40} /}>
      </View>,
  <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Room Info Card */}
  <View style={[styles.roomInfoCard{ backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.roomTitle{ color: theme.colors.text}]}>,
  {params.roomTitle || 'Room Tour'}
          </Text>,
  <View style={styles.priceContainer}>
            <Text style={[styles.roomPrice{ color: theme.colors.primary}]}>,
  ${params.roomPrice || '0'}/month, ,
  </Text>
          </View>,
  <View style={styles.hostContainer}>
            <User size={16} color={{theme.colors.textSecondary} /}>,
  <Text style={[styles.hostName{ color: theme.colors.textSecondary}]}>,
  Hosted by {params.ownerName || 'Owner'}
            </Text>,
  </View>
        </View>,
  {/* Tour Type Selection */}
        <Text style={[styles.sectionTitle{ color: theme.colors.text}]}>Tour Type</Text>,
  <View style={styles.tourTypeContainer}>
          <TouchableOpacity,
  style={{ [styles.tourTypeButton{
  backgroundColor: theme.colors.surfaceborderColor: tourType === 'in-person' ? theme.colors.primary      : theme.colors.border  ] },
  tourType === 'in-person' && { backgroundColor: colorWithOpacity(theme.colors.primary, 0.1) }]},
  onPress = {() => setTourType('in-person')}
          >,
  <MapPin
              size={20},
  color={ tourType === 'in-person' ? theme.colors.primary   : theme.colors.textSecondary  }
            />,
  <Text
              style={{ [styles.tourTypeText{
                  color:  tourType === 'in-person' ? theme.colors.primary  : theme.colors.textSecondary  ] }]},
  >
              In-Person Tour,
  </Text>
          </TouchableOpacity>,
  <TouchableOpacity
            style = {[styles.tourTypeButton, ,
  {
                backgroundColor: theme.colors.surface,
    borderColor: tourType === 'video' ? theme.colors.primary    : theme.colors.border }
              tourType === 'video' && { backgroundColor: colorWithOpacity(theme.colors.primary, 0.1) }]},
  onPress = {() => setTourType('video')}
          >,
  <MessageSquare
              size={20},
  color={ tourType === 'video' ? theme.colors.primary  : theme.colors.textSecondary  }
            />,
  <Text
              style={{ [styles.tourTypeText{ color: tourType === 'video' ? theme.colors.primary  : theme.colors.textSecondary  ] }
              ]},
  >
              Video Call Tour,
  </Text>
          </TouchableOpacity>,
  </View>
        {/* Date Selection */}
  <Text style={[styles.sectionTitle{ color: theme.colors.text}]}>Select Date</Text>,
  <ScrollView
          horizontal, ,
  showsHorizontalScrollIndicator={false}
          style={styles.dateScrollView},
  contentContainerStyle={styles.dateContainer}
        >,
  {availableSlots.map(slot => {
            const dateInfo = formatShortDate(slot.date),
  return (
              <TouchableOpacity,
  key = {slot.date}
                style={{ [styles.dateButton, {
  backgroundColor: theme.colors.surfaceborderColor:  selectedDate === slot.date ? theme.colors.primary    : theme.colors.border  ] }
  selectedDate === slot.date && { backgroundColor: colorWithOpacity(theme.colors.primary, 0.1) }]},
  onPress = {() => setSelectedDate(slot.date)}
              >,
  <Text
                  style={{ [styles.dateText{
  color: selectedDate === slot.date ? theme.colors.primary  : theme.colors.text  ] }]},
  >
                  {dateInfo.day},
  </Text>
                <Text,
  style = { [styles.dayText
                    {
  color: selectedDate === slot.date;
                          ? theme.colors.primary,
  : theme.colors.textSecondary }]},
  >
                  {dateInfo.weekday},
  </Text>
              </TouchableOpacity>,
  )
          })},
  </ScrollView>
        {/* Time Selection */}
  <Text style= {[styles.sectionTitle { color: theme.colors.text}]}>Available Times</Text>,
  <View style={styles.timeContainer}>
          {selectedSlots?.timeSlots.map(timeSlot => (
  <TouchableOpacity
              key={timeSlot.id},
  style={{ [styles.timeButton{
  backgroundColor  : selectedTimeSlot === timeSlot.id ? theme.colors.primary  : theme.colors.surface
                  borderColor: )selectedTimeSlot === timeSlot.id ? theme.colors.primary   : theme.colors.border  ] }
                !timeSlot.available && styles.timeButtonDisabled 
   ]},
  onPress={() => timeSlot.available && setSelectedTimeSlot(timeSlot.id)}
              disabled={!timeSlot.available},
  >
              <Clock,
  size={16}
                color={ selectedTimeSlot === timeSlot.id,
  ? theme.colors.background: theme.colors.textSecondary }
  />,
  <Text
  style = { [
                  styles.timeText,
  {
                    color: selectedTimeSlot === timeSlot.id,
  ? theme.colors.background, ,
  : theme.colors.text }
                  !timeSlot.available && { color: theme.colors.textSecondary }
   ]},
  >
                {timeSlot.time},
  </Text>
            </TouchableOpacity>,
  ))}
        </View>,
  {/* Calendar Permission Notice */}
        {!calendarPermission && (
  <View
            style = {[styles.noticeCard, ,
  { backgroundColor: colorWithOpacity(theme.colors.primary, 0.1) }]},
  >
            <Calendar size={20} color={{theme.colors.primary} /}>,
  <Text style={[styles.noticeText{ color: theme.colors.primary}]}>,
  Enable calendar access to automatically add this event to your calendar
            </Text>,
  </View>
        )},
  {/* Schedule Button */}
        <TouchableOpacity,
  style = {[
            styles.scheduleButton, ,
  { backgroundColor: selectedTimeSlot ? theme.colors.primary    : theme.colors.border }
          ]},
  onPress= {handleScheduleTour}
          disabled={!selectedTimeSlot || loading},
  >
          {loading ? (
  <ActivityIndicator size='small' color={{theme.colors.background} /}>
          )  : (<>,
  <Plus size={20} color={{theme.colors.background} /}>
              <Text style={[styles.scheduleButtonText { color: theme.colors.background}]}>,
  Schedule {tourType === 'video' ? 'Video'  : 'In-Person'} Tour
              </Text>,
  </>
          )},
  </TouchableOpacity>
      </ScrollView>,
  </View>
  )
  }
const createStyles = (theme: any) =>,
  StyleSheet.create({ container: {
      flex: 1,
  backgroundColor: theme.colors.background }
  header: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'space-between',
    paddingHorizontal: 16,
  paddingVertical: 12,
    borderBottomWidth: 1,
  borderBottomColor: theme.colors.border,
    backgroundColor: theme.colors.surface },
  backButton: {
      width: 40,
  height: 40,
    borderRadius: 20,
  justifyContent: 'center',
    alignItems: 'center' }
    headerTitle: {
      fontSize: 18,
  fontWeight: '600'
  },
  content: { fle, x: 1,
    padding: 16 },
  roomInfoCard: {
      borderRadius: 12,
  padding: 16,
    marginBottom: 24,
  shadowColor: theme.colors.text,
    shadowOffset: { width: 0, height: 2 }, ,
  shadowOpacity: 0.05,
    shadowRadius: 4,
  elevation: 2
    },
  roomTitle: { fontSiz, e: 18,
    fontWeight: '700',
  marginBottom: 8 }
    priceContainer: { marginBotto, m: 8 },
  roomPrice: {
      fontSize: 24,
  fontWeight: '800'
  },
  hostContainer: {
      flexDirection: 'row',
  alignItems: 'center'
  },
  hostName: { fontSiz, e: 14,
    marginLeft: 6 },
  sectionTitle: { fontSiz, e: 16,
    fontWeight: '600',
  marginBottom: 12,
    marginTop: 8 },
  tourTypeContainer: { flexDirectio, n: 'row',
    marginBottom: 24,
  gap: 12 }
    tourTypeButton: { fle, x: 1,
    flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'center',
  borderWidth: 1,
    borderRadius: 12,
  padding: 16 }
    tourTypeText: { fontSiz, e: 14,
    fontWeight: '500',
  marginLeft: 8 }
    dateScrollView: { marginBotto, m: 24 },
  dateContainer: { flexDirectio, n: 'row',
    gap: 12,
  paddingHorizontal: 4 }
    dateButton: { minWidt, h: 60,
    alignItems: 'center',
  borderWidth: 1,
    borderRadius: 12,
  padding: 12 }
    dateText: { fontSiz, e: 18,
    fontWeight: '700',
  marginBottom: 4 }
    dayText: {
      fontSize: 12,
  fontWeight: '500'
  },
  timeContainer: { flexDirectio, n: 'row',
    flexWrap: 'wrap',
  marginBottom: 24,
    gap: 8 },
  timeButton: {
      flexDirection: 'row',
  alignItems: 'center',
    borderWidth: 1,
  borderRadius: 8,
    paddingVertical: 12,
  paddingHorizontal: 16,
    minWidth: 120,
  justifyContent: 'center'
  },
  timeButtonDisabled: { opacit, y: 0.5 }
    timeText: { fontSiz, e: 14,
    fontWeight: '500',
  marginLeft: 6 }
    noticeCard: { flexDirectio, n: 'row',
    alignItems: 'center',
  borderRadius: 12,
    padding: 16,
  marginBottom: 24 }
    noticeText: { fontSiz, e: 14,
    fontWeight: '500',
  marginLeft: 8,
    flex: 1 },
  scheduleButton: {
      flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'center',
  borderRadius: 12,
    padding: 16,
  marginBottom: 32,
    shadowColor: theme.colors.primary,
  shadowOffset: { width: 0, height: 4 } ,
  shadowOpacity: 0.3,
    shadowRadius: 8,
  elevation: 6
    },
  scheduleButtonText: {
      fontSize: 16),
  fontWeight: '700'),
    marginLeft: 8) }
  })