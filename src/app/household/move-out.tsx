import React, { useState, useEffect, useCallback } from 'react';
  import {
   View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Alert, KeyboardAvoidingView, Platform, Switch, ViewStyle, TextStyle, StyleProp, ColorValue, ActivityIndicator, RefreshControl, Dimensions, Modal  } from 'react-native';
import {
  Stack, useRouter  } from 'expo-router';
import {
  useSafeAreaInsets 
} from 'react-native-safe-area-context';
  import {
   ChevronLeft, Star, ClipboardCheck, Home, Lightbulb, DollarSign, Calendar, MessageSquare, X, Plus, Brain, Heart, Target, TrendingUp, Award, Zap, Coffee, Settings, Users, Clock, CheckCircle, AlertTriangle, Info, BarChart3, Trophy, Flame, Gift, ChevronRight, User, MapPin, Phone, Mail, FileText, Camera, Upload, Download, Share, Bookmark, ThumbsUp, ThumbsDown, MessageCircle, Send  } from 'lucide-react-native';
import DateTimePicker, { DateTimePickerEvent } from '@react-native-community/datetimepicker';
  import {
   Button  } from '@design-system';
import {
  StepProgress 
} from '@components/ui/StepProgress';
  import {
   EnhancedDatePicker  } from '@components/ui/EnhancedDatePicker';
import {
  EnhancedToggle 
} from '@components/ui/EnhancedToggle';
  import FormInput from '@components/ui/form/Input';
import {
  EnhancedCard 
} from '@components/ui/EnhancedCard';
  import {
   EnhancedButton  } from '@components/ui/EnhancedButton';
import {
  colors 
} from '@constants/colors';
  import {
   useAuth  } from '@context/AuthContext';
import {
  moveOutService 
} from '@services/moveOutService';
  import {
   supabase  } from '@utils/supabaseUtils';
import {
  useTheme 
} from '@design-system';
  import {
   useToast  } from '@components/ui/Toast';
import {
  logger 
} from '@utils/logger';
  const { width  } = Dimensions.get('window');
// Mock data for roommates,
  const ROOMMATES = [{
    id: '1',
    name: 'Sarah Johnson',
  avatar: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330'
  },
  {
  id: '2',
    name: 'Michael Chen',
  avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d'
  },
  {
  id: '3',
    name: 'Alicia Rodriguez',
  avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80'
  }] // Enhanced move-out data structures, ,
  interface HouseholdMember { id: string,
    name: string,
  avatar_url?: string
  personality_type?: string,
  lifestyle_type?: string
  communication_style?: 'direct' | 'diplomatic' | 'supportive' | 'analytical',
  trust_score: number,
    relationship_duration: number // months,
  conflict_history: number,
    satisfaction_rating: number,
  move_out_experience?: 'first_time' | 'experienced' | 'frequent' }
  interface MoveOutAnalytics { household_impact_score: number,
    financial_impact: number,
  social_impact: number,
    transition_difficulty: 'easy' | 'moderate' | 'challenging',
  estimated_replacement_time: number // days,
    household_stability_score: number,
  member_satisfaction_impact: number,
    cost_implications: {
  security_deposit: number,
    utility_adjustments: number,
  cleaning_costs: number,
    replacement_costs: number }
  }
interface SmartRecommendation {
  id: string,
    type: 'timeline' | 'communication' | 'financial' | 'transition',
  title: string,
    description: string,
  priority: 'high' | 'medium' | 'low',
    personality_based: boolean,
  estimated_time: string,
    success_rate: number,
  reasoning: string,
    action_items: string[] }
interface MoveOutStep { id: number,
    title: string,
  description: string,
    icon: any,
  completed: boolean,
    required: boolean,
  estimated_time: string
  personality_tips?: string },
  interface EnhancedMoveOutData { move_out_date: Date,
    reason: 'personal' | 'work' | 'education' | 'relationship' | 'financial' | 'conflict' | 'other',
  reason_details: string,
    notice_period: number // days,
  financial_details: {
    final_utility_payment: string,
  security_deposit_return : string, ,
  outstanding_expenses: string,
    cleaning_deposit: string },
  cleaning_plan: string,
    property_condition: {
  damage_report: string,
    repair_needed: boolean,
  repair_cost_estimate: string,
    photo_documentation: string[] }
  transition_preferences: { gradual_move: boolean,
    overlap_period: number // days,
  help_needed: boolean,
    farewell_event: boolean },
  feedback: {
    household_rating: number,
  member_ratings: { [memberId: string]: number },
  experience_rating: number,
    would_recommend: boolean,
  improvement_suggestions: string,
    testimonial: string
  }
  smart_recommendations: SmartRecommendation[],
    analytics: MoveOutAnalytics
  }
export default function EnhancedMoveOutScreen() {
  const insets = useSafeAreaInsets()
  const router = useRouter(),
  const { state, actions  } = useAuth(),
  const theme = useTheme()
  const { showSuccess, showError, ToastComponent } = useToast(),
  const [step, setStep] = useState(1),
  const [moveOutDate, setMoveOutDate] = useState(new Date()),
  const [showDatePicker, setShowDatePicker] = useState(false),
  const [finalUtilityPayment, setFinalUtilityPayment] = useState(''),
  const [securityDepositReturn, setSecurityDepositReturn] = useState(''),
  const [cleaningPlan, setCleaningPlan] = useState(''),
  const [propertyDamage, setPropertyDamage] = useState(''),
  const [willLeaveReview, setWillLeaveReview] = useState(true),
  const [isSubmitting, setIsSubmitting] = useState(false),
  const [isLoading, setIsLoading] = useState(false),
  const [householdId, setHouseholdId] = useState<string | null>(null),
  const [roommates, setRoommates] = useState(ROOMMATES),
  const [members, setMembers] = useState<HouseholdMember[]>([]),
  const [analytics, setAnalytics] = useState<MoveOutAnalytics | null>(null),
  const [recommendations, setRecommendations] = useState<SmartRecommendation[]>([]),
  // Modal states,
  const [showAnalyticsModal, setShowAnalyticsModal] = useState(false),
  const [showRecommendationsModal, setShowRecommendationsModal] = useState(false),
  // Move-out form data,
  const [moveOutData, setMoveOutData] = useState<EnhancedMoveOutData>({
  move_out_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now,
  reason: 'personal',
    reason_details: '',
  notice_period: 30,
    financial_details: {
  final_utility_payment: ''
      security_deposit_return : '', ,
  outstanding_expenses: '',
    cleaning_deposit: '' }
    cleaning_plan: '',
    property_condition: {
  damage_report: '',
    repair_needed: false,
  repair_cost_estimate: '',
    photo_documentation: [] }
    transition_preferences: { gradual_move: false,
    overlap_period: 0,
  help_needed: false,
    farewell_event: false },
  feedback: {
    household_rating: 5,
  member_ratings: {} 
  experience_rating: 5,
    would_recommend: true,
  improvement_suggestions: '',
    testimonial: ''
  }
    smart_recommendations: [],
    analytics: { household_impact_score: 0,
    financial_impact: 0,
  social_impact: 0,
    transition_difficulty: 'moderate',
  estimated_replacement_time: 0,
    household_stability_score: 0,
  member_satisfaction_impact: 0,
    cost_implications: {
  security_deposit: 0,
    utility_adjustments: 0,
  cleaning_costs: 0,
    replacement_costs: 0 }
  }
  }),
  const moveOutSteps: MoveOutStep[] = [
  {
      id: 1,
    title: 'Move-Out Planning',
  description: 'Set your move-out date and provide initial details',
    icon: Calendar,
  completed: false,
    required: true,
  estimated_time: '10 min',
    personality_tips: 'Take time to plan thoroughly for a smooth transition' }
    {
  id: 2,
    title: 'Financial Settlement',
  description: 'Handle all financial obligations and deposits',
    icon: DollarSign,
  completed: false,
    required: true,
  estimated_time: '15 min',
    personality_tips: 'Clear communication about finances prevents future conflicts' }
    {
  id: 3,
    title: 'Property Condition',
  description: 'Document property condition and cleaning requirements',
    icon: Home,
  completed: false,
    required: true,
  estimated_time: '20 min',
    personality_tips: 'Detailed documentation protects everyone involved' }
    {
  id: 4,
    title: 'Transition Planning',
  description: 'Plan your transition timeline and support needs',
    icon: Settings,
  completed: false,
    required: false,
  estimated_time: '10 min',
    personality_tips: 'Consider your household members when planning your transition' }
    {
  id: 5,
    title: 'Feedback & Review',
  description: 'Share your experience and rate household members',
    icon: Star,
  completed: false,
    required: false,
  estimated_time: '15 min',
    personality_tips: 'Honest feedback helps improve future living experiences' }] // Fetch household data when component mounts,
  useEffect(() => {
  const fetchHouseholdData = async () => {
  if (!state.user) return null,
      setIsLoading(true),
  try {
        // Try to get user's household,
  try {
          const { data: householdData, error: householdError  } = await supabase.from('household_members'),
  .select('household_id')
            .eq('id', state.user.id),
  .single()
          if (!householdError && householdData) {
  setHouseholdId(householdData.household_id);
            // Try to get roommates,
  try {
              const { data: roommatesData, error: roommatesError } = await supabase.from('household_members'),
  .select('user_id')
                .eq('household_id', householdData.household_id),
  .neq).neq).neq('user_id', state.user.id),
  if (!roommatesError && roommatesData && roommatesData.length > 0) {;
                // Try to get roommate profiles,
  const roommateIds = roommatesData.map((r: { user_id: string }) => r.user_id)
                const { data: profilesData, error: profilesError } = await supabase.from('user_profiles'),
  .select('id, first_name, last_name, avatar_url'),
  .in('id', roommateIds),
  if (!profilesError && profilesData) {
                  setRoommates(
  profilesData.map((profile: {
    id: string,
  first_name: string,
    last_name: string),
  avatar_url: string | null)
  }) => ({
  id: profile.id,
  name: `${profile.first_name} ${profile.last_name}` ,
  avatar:  
                          profile.avatar_url ||, ,
  'https: //images.unsplash.com/photo-1494790108377-be9c29b29330'
                      }),
  )
                  )
  }
              }
  } catch (roomError) {
              console.warn('Error fetching roommates, using mock data:', roomError) }
          }
  } catch (householdError) {
          console.warn('Error fetching household, using mock data:', householdError) }
        // If no household ID was set, use a mock ID,
  if (!householdId) {
          setHouseholdId('mock-household-123') }
      } catch (error) {
  console.error('Error in fetchHouseholdData:', error) } finally {
        setIsLoading(false) }
    },
  fetchHouseholdData()
  } [state.user]),
  const handleDateChange = (event: DateTimePickerEvent, selectedDate?: Date) => {
  const currentDate = selectedDate || moveOutDate,
    setShowDatePicker(Platform.OS === 'ios'),
  setMoveOutDate(currentDate)
  },
  const handleNext = () => {
  if (step === 1) {
  // Validate move-out date,
      const today = new Date(),
  if (moveOutDate < today) {
        Alert.alert('Invalid Date', 'Please select a future date for your move-out.'),
  return null;
      }
  }
    if (step === 2) {
  // Validate financial details,
      if (!finalUtilityPayment.trim() || !securityDepositReturn.trim()) {
  Alert.alert('Missing Information', 'Please fill in all financial details.'),
  return null;
      }
  }
    if (step === 3) {
  // Validate cleaning plan,
      if (!cleaningPlan.trim()) {
  Alert.alert('Missing Information', 'Please provide a cleaning plan.'),
  return null;
      }
  }
    if (step < 5) {
  setStep(step + 1)
    }
  }
  const handleBack = () => {
  if (step > 1) {
      setStep(step - 1) } else {
      router.back() }
  },
  const handleSubmit = async () => {
  if (!state.user || !householdId) {
  Alert.alert('Error', 'Unable to submit move-out request. Please try again later.'),
  return null;
    },
  setIsSubmitting(true)
    try {
  // Submit move-out request using the service,
      const result = await moveOutService.submitMoveOutRequest({
  userId: state.user.id);
        householdId,
  moveOutDate,
        finalUtilityPayment,
  securityDepositReturn,
        cleaningPlan,
  propertyDamage,
        willLeaveReview })
      if (result) {
  Alert.alert('Move-Out Initiated'
          "Your move-out process has been initiated. We'll notify your roommates and landlord."),
  [{
              text: 'Leave Reviews'),
    onPress: () => router.push('/household/reviews' as any) } 
  {
  text: 'Done',
    onPress: () => router.push('/household' as any) }], ,
  )
      } else {
  Alert.alert('Error', 'Failed to submit move-out request. Please try again later.') }
    } catch (error) {
  console.error('Error submitting move-out request:', error),
  Alert.alert('Error', 'An unexpected error occurred. Please try again later.') } finally {
      setIsSubmitting(false) }
  },
  const renderStepIndicator = () => {
  return (
  <StepProgress steps={['Date',  'Utilities', 'Deposit', 'Cleaning', 'Review']} currentStep = {step} descriptions={['Select move-out date', ,
  'Utility transfer plan'
          'Security deposit',
  'Cleaning arrangements'
          'Final review']} activeColor= {theme.colors.primary[500]} inactiveColor={theme.colors.gray[300]} showCheckIcon={true},
  />
    )
  }
  const renderStepContent = () => {
  // Show loading indicator while data is being fetched,
    if (isLoading) {
  return (
    <View style= {styles.loadingContainer}>,
  <ActivityIndicator size="large" color={{theme.colors.primary[500]} /}>,
  <Text style={styles.loadingText}>Loading...</Text>
        </View>,
  )
    },
  switch (step) {
      case 1:  ,
  return (
          <>,
  <Text style= {styles.stepTitle}>When are you moving out? </Text>
            <Text style={styles.stepDescription}>Select your planned move-out date</Text>,
  <EnhancedDatePicker value={moveOutDate} onValueChange={date ={}> setMoveOutDate(date)} minDate={new Date()} maxDate={new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)} // One year from now,
              label= "Move-out date",
  placeholder= "Select your move-out date";
              testID= "move-out-date-picker",
  error={   step === 1 && !moveOutDate ? 'Please select a move-out date'     : undefined      }
            />,
  <EnhancedCard
              title="Important Notice",
  content="Please check your lease agreement for the required notice period before finalizing your move-out date."
              type="warning",
  icon= {<Calendar size={24} color={{theme.colors.warning[500]} /}>,
  />
          </>,
  )
      case 2:  ,
  return (
          <>,
  <Text style= {styles.stepTitle}>Financial Details</Text>
            <Text style={styles.stepDescription}>,
  Let's sort out the financial aspects of your move-out;
            </Text>,
  <FormInput
              label= "Final Utility Payments",
  placeholder= "Describe how final utility bills will be handled";
              value= {finalUtilityPayment} onChangeText={setFinalUtilityPayment},
  multiline leftIcon="Lightbulb" maxLength={300}
              // showCharCount prop was removed,
  helperText= "Include details about who will pay for what and when";
            />,
  <FormInput
              label= "Security Deposit Return",
  placeholder= "Describe how the security deposit should be return ed";
              value= {securityDepositReturn} onChangeText={setSecurityDepositReturn},
  multiline leftIcon="DollarSign" maxLength={300}
              // showCharCount prop was removed,
  helperText= "Include details about return method and timeline";
            />,
  <EnhancedCard
              title= "Tip",
  content= "Take photos of the property condition before moving out to document its state for security deposit return s.", ,
  type= "info", ,
  icon= {<DollarSign size={24} color={{theme.colors.primary[500]} /}>,
  />
          </>,
  )
      case 3:  ,
  return (
          <>,
  <Text style= {styles.stepTitle}>Cleaning & Property Condition</Text>
            <Text style={styles.stepDescription}>,
  Provide details about your cleaning plan and any property damage;
            </Text>,
  <FormInput
              label= "Cleaning Plan",
  placeholder= "Describe your plan for cleaning before move-out";
              value= {cleaningPlan} onChangeText={setCleaningPlan},
  multiline leftIcon="ClipboardCheck" maxLength={500}
              // showCharCount prop was removed,
  helperText= "Provide details about how you'll clean the property before leaving";
            />,
  <FormInput
              label= "Property Damage",
  placeholder= "List any property damage that occurred during your tenancy";
              value= {propertyDamage} onChangeText={setPropertyDamage},
  multiline leftIcon="Home" maxLength={500}
              // showCharCount prop was removed,
  helperText= "Be honest about any damage to ensure a smooth move-out process";
            />,
  <EnhancedCard
              title= "Tip",
  content= "Be honest about any damage to avoid disputes. Take photos of the condition of the property.", ,
  type= "info", ,
  icon= {<Home size={24} color={{theme.colors.primary[500]} /}>,
  />
          </>,
  )
      case 4:  ,
  return (
          <>,
  <Text style= {styles.stepTitle}>Roommate Reviews</Text>
            <Text style={styles.stepDescription}>,
  Would you like to leave reviews for your roommates? ;
            </Text>,
  <EnhancedToggle value= {willLeaveReview} onValueChange={setWillLeaveReview} label="Leave roommate reviews";
              description= "Rate and provide feedback for your roommates", ,
  icon= {<Star size={20} color={{theme.colors.primary[500]} /}>,
  />
            {willLeaveReview && (
  <View style={styles.roommatesContainer}>
                <Text style={styles.sectionTitle}>Your Roommates</Text>,
  {ROOMMATES.map(roommate => (
                  <View key={roommate.id} style={styles.roommateCard}>,
  <View style={styles.roommateInfo}>
                      <Text style={styles.roommateName}>{roommate.name}</Text>,
  </View>
                    <EnhancedButton,
  text="Review", ,
  variant= "outlined"), ,
  size= "small")
  startIcon={<Star size={16} color={{theme.colors.primary[500]} /}>,
  buttonStyle={styles.reviewButton}
                    />,
  </View>
                ))},
  </View>
            )},
  </>
        ),
  case 5     : return (
          <>,
  <Text style={styles.stepTitle}>Review & Submit</Text>
            <Text style={styles.stepDescription}>,
  Review your move-out details before submitting
            </Text>,
  <View style={styles.summaryContainer}>
              <View style={styles.summaryItem}>,
  <View style={styles.summaryIconContainer}>
                  <Calendar size={20} color={{theme.colors.primary[500] as string} /}>,
  </View>
                <View style={styles.summaryTextContainer}>,
  <Text style={styles.summaryLabel}>Move-Out Date</Text>
                  <Text style={styles.summaryValue}>,
  {moveOutDate.toLocaleDateString('en-US',  {
  weekday: 'long',
    year: 'numeric'),
  month: 'long'),
    day: 'numeric') })}
                  </Text>,
  </View>
              </View>,
  <View style={styles.summaryItem}>
                <View style={styles.summaryIconContainer}>,
  <DollarSign size={20} color={{theme.colors.primary[500] as string} /}>,
  </View>
                <View style={styles.summaryTextContainer}>,
  <Text style={styles.summaryLabel}>Financial Details</Text>
                  <Text style={styles.summaryValue}>,
  Final Utility Payment: {finalUtilityPayment}
                  </Text>,
  <Text style={styles.summaryValue}>Security Deposit: {securityDepositReturn}</Text>
                </View>,
  </View>
              <View style={styles.summaryItem}>,
  <View style={styles.summaryIconContainer}>
                  <ClipboardCheck size={20} color={{theme.colors.primary[500] as string} /}>,
  </View>
                <View style={styles.summaryTextContainer}>,
  <Text style={styles.summaryLabel}>Property Condition</Text>
                  <Text style={styles.summaryValue}>Cleaning Plan: {cleaningPlan}</Text>,
  <Text style={styles.summaryValue}>
                    Property Damage: {propertyDamage || 'None reported'},
  </Text>
                </View>,
  </View>
              <View style={styles.summaryItem}>,
  <View style={styles.summaryIconContainer}>
                  <Star size={20} color={{theme.colors.primary[500] as string} /}>,
  </View>
                <View style={styles.summaryTextContainer}>,
  <Text style={styles.summaryLabel}>Roommate Reviews</Text>
                  <Text style={styles.summaryValue}>,
  {willLeaveReview ? 'Will leave reviews'   : 'Will not leave reviews'}
                  </Text>,
  </View>
              </View>,
  </View>
            <EnhancedButton,
  text="Submit Move-Out Request"
              variant="filled",
  size="lg"
              onPress= {handleSubmit} loading={isSubmitting},
  fullWidth startIcon={<MessageSquare size={20} color={{theme.colors.white} /}>
            />,
  <View style={styles.noticeContainer}>
              <MessageSquare size={20} color={{theme.colors.warning[500] as string} /}>,
  <Text style={styles.noticeText}>
                This will notify your roommates and landlord about your intention to move out.,
  </Text>
            </View>,
  </>
        ),
  default: return null
  }
  }
  return (
  <KeyboardAvoidingView
  style= {{ [styles.container,  { paddingTop: insets.top  ] }]},
  behavior={   Platform.OS === 'ios' ? 'padding'     : 'height'      }
    >,
  <Stack.Screen
        options={   headerShown: false      },
  />
      <View style={styles.header}>,
  <EnhancedButton
          text="",
  variant="ghost"
          size= "small", ,
  onPress= {handleBack} startIcon={<ChevronLeft size={24} color={{theme.colors.gray[800]} /}>,
  buttonStyle={styles.backButton}
        />,
  <Text style={styles.headerTitle}>Move-Out Process</Text>
        <View style={{ width: 40} /}>,
  </View>
      {renderStepIndicator()},
  <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer}>
        {renderStepContent()},
  </ScrollView>
      {step < 5 && (
  <View style={styles.footer}>
          <EnhancedButton,
  text="Continue", ,
  variant= "filled", ,
  size= "md", ,
  onPress= {handleNext}
            fullWidth endIcon={
  <ChevronLeft
                size={20} color={theme.colors.white} style={{ transform: [{ rotate: '180deg'}] }},
  />
            },
  />
        </View>,
  )}
    </KeyboardAvoidingView>,
  )
},
  // Define specific style types to avoid type mismatches
type ViewStyleProps = { container: ViewStyle,
    header: ViewStyle,
  stepIndicator: ViewStyle,
    stepDot: ViewStyle,
  activeStepDot: ViewStyle,
    completedStepDot: ViewStyle,
  content: ViewStyle,
    contentContainer: ViewStyle,
  datePickerButton: ViewStyle,
    dateIcon: ViewStyle,
  infoCard: ViewStyle,
    inputContainer: ViewStyle,
  toggleContainer: ViewStyle,
    roommatesContainer: ViewStyle,
  roommateCard: ViewStyle,
    roommateInfo: ViewStyle,
  reviewButton: ViewStyle,
    summaryContainer: ViewStyle,
  summaryItem: ViewStyle,
    summaryIconContainer: ViewStyle,
  summaryTextContainer: ViewStyle,
    submitButton: ViewStyle,
  noticeContainer: ViewStyle,
    footer: ViewStyle,
  backButton: ViewStyle
  infoTitleContainer?: ViewStyle,
  sectionTitleContainer?: ViewStyle
  summaryLabelContainer?: ViewStyle,
  summaryValueContainer?: ViewStyle
  loadingContainer: ViewStyle },
  type TextStyleProps = { headerTitle: TextStyle,
    stepTitle: TextStyle,
  stepDescription: TextStyle,
    dateText: TextStyle,
  infoTitle: TextStyle,
    infoText: TextStyle,
  inputLabel: TextStyle,
    toggleLabel: TextStyle,
  sectionTitle: TextStyle,
    roommateName: TextStyle,
  reviewButtonText: TextStyle,
    summaryLabel: TextStyle,
  summaryValue: TextStyle,
    noticeText: TextStyle,
  textInput: TextStyle,
    loadingText: TextStyle },
  type CombinedStyles = ViewStyleProps & TextStyleProps,
const styles = StyleSheet.create<ViewStyleProps & TextStyleProps>({
  container: {
    flex: 1,
  backgroundColor: '#F8FAFC'
  },
  header: {
    flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'space-between',
  paddingHorizontal: 16,
    paddingVertical: 12,
  borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  backgroundColor: '#FFFFFF'
  },
  backButton: {
    width: 40,
  height: 40,
    borderRadius: 20,
  justifyContent: 'center',
    alignItems: 'center' }
  headerTitle: { fontSize: 18,
    fontWeight: '600',
  color: '#1E293B',
    marginLeft: 16 },
  stepIndicator: {
    flexDirection: 'row',
  justifyContent: 'center',
    alignItems: 'center',
  paddingVertical: 16,
    backgroundColor: '#FFFFFF',
  borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0' }
  stepDot: { width: 8,
    height: 8,
  borderRadius: 4,
    backgroundColor: '#E2E8F0',
  marginHorizontal: 6 }
  activeStepDot: { width: 10,
    height: 10,
  borderRadius: 5,
    backgroundColor: theme.colors.primary[100] as string },
  completedStepDot: { backgroundColor: theme.colors.primary[500] as string },
  content: { flex: 1 }
  contentContainer: { padding: 16 },
  stepTitle: { fontSize: 20,
    fontWeight: '700',
  color: '#1E293B',
    marginBottom: 8 },
  stepDescription: { fontSize: 14,
    color: '#64748B',
  marginBottom: 24 }
  datePickerButton: { flexDirection: 'row',
    alignItems: 'center',
  backgroundColor: '#FFFFFF',
    borderRadius: 12,
  padding: 16,
    borderWidth: 1,
  borderColor: '#E2E8F0',
    marginBottom: 24 },
  dateIcon: { marginRight: 12 }
  dateText: {
    fontSize: 16,
  color: '#1E293B'
  },
  infoCard: { backgroundColor: '#F0F9FF',
    borderRadius: 12,
  padding: 16,
    borderWidth: 1,
  borderColor: '#BAE6FD',
    marginBottom: 16 },
  infoTitle: { fontSize: 16,
    fontWeight: '600',
  color: '#0369A1',
    marginBottom: 8 },
  infoText: { fontSize: 14,
    color: '#0369A1',
  lineHeight: 20 }
  inputContainer: { marginBottom: 16 },
  inputLabel: { fontSize: 14,
    fontWeight: '600',
  color: '#475569',
    marginBottom: 8 },
  textInput: {
    backgroundColor: '#FFFFFF',
  borderRadius: 12,
    borderWidth: 1,
  borderColor: '#E2E8F0',
    padding: 12,
  fontSize: 16,
    color: '#1E293B',
  minHeight: 100,
    textAlignVertical: 'top' }
  toggleContainer: { flexDirection: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    backgroundColor: '#FFFFFF',
  borderRadius: 12,
    padding: 16,
  borderWidth: 1,
    borderColor: '#E2E8F0',
  marginBottom: 24 }
  toggleLabel: {
    fontSize: 16,
  color: '#1E293B'
  },
  roommatesContainer: {
    backgroundColor: '#FFFFFF',
  borderRadius: 12,
    padding: 16,
  borderWidth: 1,
    borderColor: '#E2E8F0' }
  sectionTitle: { fontSize: 16,
    fontWeight: '600',
  color: '#1E293B',
    marginBottom: 16 },
  roommateCard: {
    flexDirection: 'row',
  justifyContent: 'space-between',
    alignItems: 'center',
  paddingVertical: 12,
    borderBottomWidth: 1,
  borderBottomColor: '#F1F5F9'
  },
  roommateInfo: { flex: 1 }
  roommateName: {
    fontSize: 16,
  color: '#1E293B',
    fontWeight: '500' }
  reviewButton: { flexDirection: 'row',
    alignItems: 'center',
  backgroundColor: '#F1F5F9',
    borderRadius: 8,
  paddingVertical: 6,
    paddingHorizontal: 12 },
  reviewButtonText: { fontSize: 14,
    color: theme.colors.primary[500] as string,
  fontWeight: '500',
    marginLeft: 4 },
  summaryContainer: { backgroundColor: '#FFFFFF',
    borderRadius: 12,
  padding: 16,
    borderWidth: 1,
  borderColor: '#E2E8F0',
    marginBottom: 24 },
  summaryItem: { flexDirection: 'row',
    marginBottom: 16 },
  summaryIconContainer: { width: 36,
    height: 36,
  borderRadius: 18,
    backgroundColor: '#F1F5F9',
  justifyContent: 'center',
    alignItems: 'center',
  marginRight: 12 }
  summaryTextContainer: { flex: 1 },
  summaryLabel: { fontSize: 14,
    fontWeight: '600',
  color: '#64748B',
    marginBottom: 4 },
  summaryValue: { fontSize: 16,
    color: '#1E293B',
  marginBottom: 4 }
  submitButton: { marginBottom: 16 },
  noticeContainer: {
    flexDirection: 'row',
  alignItems: 'center',
    backgroundColor: '#FEF3C7',
  borderRadius: 12,
    padding: 12,
  borderWidth: 1,
    borderColor: '#FDE68A' }
  noticeText: { flex: 1,
    fontSize: 14,
  color: '#92400E',
    marginLeft: 8,
  lineHeight: 20 }
  footer: {
    padding: 16,
  backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
  borderTopColor: '#E2E8F0'
  },
  loadingContainer: { flex: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 20 },
  loadingText: {
    marginTop: 12,
  fontSize: 16,
    color: '#64748B' }
});