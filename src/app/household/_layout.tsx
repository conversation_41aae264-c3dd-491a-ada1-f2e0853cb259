import React from 'react';
  import {
  useTheme
} from '@design-system';

import {
  Stack
} from 'expo-router';
  import {
  colors
} from '@constants/colors';

export default function HouseholdLayout() { return (
  <Stack
      screenOptions={   {
  headerStyle: {, backgroundColor: theme.colors.primary[500] as string     },
  headerTintColor: '#fff',
    headerTitleStyle: {, fontWeight: 'bold'
  },
  headerBackTitleVisible: false
  }},
  >
  <Stack.Screen,
  name= 'index';
  options={ title: 'Household'     },
  />
  <Stack.Screen,
  name= 'calendar';
  options={ title: 'Household Calendar'     },
  />
  <Stack.Screen,
  name= 'chores';
  options={ title: 'Chore Management'     },
  />
  <Stack.Screen,
  name= 'expenses';
  options={ title: 'Shared Expenses'     },
  />
  <Stack.Screen,
  name= 'conflicts';
  options={   title: 'Household Conflicts',
    headerShown: false    },
  />
      <Stack.Screen,
  name= 'conflict-details';
        options={   title: 'Conflict Details',
    headerShown: false    },
  />
      <Stack.Screen,
  name= 'conflict-resolution';
        options={   title: 'Conflict Resolution',
    headerShown: false    },
  />
      <Stack.Screen,
  name= 'move-out';
        options={   title: 'Move-Out Process',
    headerShown: false    },
  />
      <Stack.Screen,
  name= 'relationship-closure';
        options={   title: 'Relationship Closure',
    headerShown: false    },
  />
      <Stack.Screen,
  name= 'reviews';
        options={   title: 'Roommate Reviews',
    headerShown: false    },
  />
      <Stack.Screen,
  name= 'settings';
        options={   title: 'Household Settings',
    headerShown: false    },
  />
      <Stack.Screen, ,
  name='communication', ,
  options={   title: 'Communication Hub',
    headerShown: false    },
  />
    </Stack>,
  )
}