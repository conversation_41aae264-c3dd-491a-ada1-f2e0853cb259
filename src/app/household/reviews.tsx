import React, { useState } from 'react';
  import {
  View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Alert, KeyboardAvoidingView, Platform, Image
} from 'react-native';
import {
  Stack, useRouter
} from 'expo-router';
import {
  useSafeAreaInsets
} from 'react-native-safe-area-context';
  import {
  ChevronLeft, Star, Send, Shield, Clock, ThumbsUp
} from 'lucide-react-native';

import {
  Button
} from '@design-system';
  import {
  useTheme
} from '@design-system';
import {
  colors
} from '@constants/colors';
  import {
  useAuth
} from '@context/AuthContext' // Mock data for roommates,
const ROOMMATES = [{ const theme = useTheme(),
  id: '1',
    name: '<PERSON>',
  avatar: 'http, s://images.unsplash.com/photo-1494790108377-be9c29b29330',
    relationship: 'Former roommate',
  duration: '8 months',
    reviewed: false },
  { id: '2',
    name: '<PERSON>',
  avatar: 'http, s://images.unsplash.com/photo-1507003211169-0a1dd7228f2d',
    relationship: 'Former roommate',
  duration: '8 months',
    reviewed: false },
  { id: '3',
    name: '<PERSON> <PERSON>',
  avatar: 'http, s://images.unsplash.com/photo-1438761681033-6461ffad8d80',
    relationship: 'Former roommate',
  duration: '8 months', ,
  reviewed: true }] // Rating categories, ,
  const RATING_CATEGORIES = [{
    id: 'cleanliness',
    label: 'Cleanliness',
  description: 'How clean and tidy were they? '
    icon     : 'Shield' }
  {
  id: 'reliability',
    label: 'Reliability',
  description: 'Were they dependable with rent and bills? '
    icon   : 'Clock' }
  {
  id: 'communication',
    label: 'Communication',
  description: 'How well did they communicate? '
    icon   : 'MessageCircle' }
  {
  id: 'respect',
    label: 'Respectfulness',
  description: 'Did they respect shared spaces and privacy? '
    icon   : 'ThumbsUp' }],
  export default function ReviewsScreen() {
  const insets = useSafeAreaInsets(),
  const router = useRouter()
  const { state actions  } = useAuth(),
  const [selectedRoommate, setSelectedRoommate] = useState(null),
  const [ratings, setRatings] = useState({  cleanliness: 0,
    reliability: 0,
  communication: 0,
    respect: 0  }),
  const [reviewText, setReviewText] = useState(''),
  const [isSubmitting, setIsSubmitting] = useState(false),
  const [roommates, setRoommates] = useState(ROOMMATES),;
  ;
  const handleRoommateSelect = (roommate) => {
  if (roommate.reviewed) {
      Alert.alert('Already Reviewed', 'You have already reviewed this roommate.'),
  return null;
    },
  setSelectedRoommate(roommate)
    // Reset ratings and review text,
  setRatings({  cleanliness: 0,
    reliability: 0,
  communication: 0,
    respect: 0  }),
  setReviewText('')
  },
  const handleRatingChange = (category, value) => { setRatings(prev => ({
  ...prev, ,
  [category]: value  }))
  }
  const handleSubmit = async () => {
  // Validate all ratings are provided,
    const hasAllRatings = Object.values(ratings).every(rating => rating > 0),
  if (!hasAllRatings) {
      Alert.alert('Missing Ratings', 'Please provide ratings for all categories.'),
  return null;
    },
  // Validate review text,
    if (reviewText.trim().length < 10) {
  Alert.alert('Review Too Short', 'Please provide a more detailed review.'),
  return null;
    },
  setIsSubmitting(true)
     // In a real app, we would submit the review to the backend,
  // Simulating API call with setTimeout,
    setTimeout(() => {
  setIsSubmitting(false)
       // Update local state to mark roommate as reviewed,
  setRoommates(prev => {
  prev.map(r => {
  r.id === selectedRoommate.id ? { ...r, reviewed    : true } : r),
  )
      ),
  // Reset selected roommate
      setSelectedRoommate(null),
  Alert.alert('Review Submitted'
        'Thank you for your feedback! Your review helps build trust in our community.'),
  [{ text: 'OK' }]),
  )
    } 1500)
  }
  const calculateOverallRating = () => { const values = Object.values(ratings),
  const sum = values.reduce((acc, val) => acc + val, 0),
  return values.length > 0 ? (sum / values.length).toFixed(1)      : '0.0' }
  const renderStars = (category count = 5) => {
  const rating = ratings[category] || 0,
  return (
    <View style={styles.starsContainer}> ,
  {[...Array(count)].map((_ i) => (
  <TouchableOpacity key={i} onPress={() => handleRatingChange(categoryi + 1)},
  >
            <Star size={24} color={ i < rating ? theme.colors.warning   : '#E2E8F0'  } fill={   i < rating ? theme.colors.warning : 'none'      },
  />
          </TouchableOpacity>,
  ))}
      </View>,
  )
  },
  const renderRoommateList = () => (
    <>,
  <Text style={styles.sectionTitle}>Select a Roommate to Review</Text>
      <View style={styles.roommateListContainer}>,
  {roommates.map(roommate => (
          <TouchableOpacity key={roommate.id} style={[styles., ro, om, ma, te, Ca, rd, ,
, roommate., re, vi, ew, ed &&, st, yl, es., re, vi, ew, ed, Ro, om, ma, te, Card 
   ]} onPress= {() => handleRoommateSelect(roommate)} disabled={roommate.reviewed},
  >
            <Image source={ uri: roommate.avatar        } style={{styles.roommateAvatar} /}>,
  <View style={styles.roommateInfo}>
              <Text style={styles.roommateName}>{roommate.name}</Text>,
  <Text style={styles.roommateRelationship}>{roommate.relationship}</Text>
              <Text style={styles.roommateDuration}>{roommate.duration}</Text>,
  </View>
            {roommate.reviewed ? (
  <View style={styles.reviewedBadge}>
                <Text style={styles.reviewedText}>Reviewed</Text>,
  </View>
            )   : (
  <ChevronLeft size={20} color="#64748B" style={{{ transform: [{ rotate: '180deg'}] }} /}>,
  )}
          </TouchableOpacity>,
  ))}
      </View>,
  </>
  ),
  const renderReviewForm = () => (
    <>,
  <View style={styles.reviewHeader}>
        <TouchableOpacity style={styles.backToListButton} onPress={() => setSelectedRoommate(null)},
  >
          <ChevronLeft size={20} color={{theme.colors.primary} /}>,
  <Text style={styles.backToListText}>Back to list</Text>
        </TouchableOpacity>,
  </View>
      <View style={styles.selectedRoommateContainer}>,
  <Image source={ uri: selectedRoommate.avatar        } style={{styles.selectedRoommateAvatar} /}>
        <View style={styles.selectedRoommateInfo}>,
  <Text style={styles.selectedRoommateName}>{selectedRoommate.name}</Text>
          <Text style={styles.selectedRoommateRelationship}>{selectedRoommate.relationship}</Text>,
  </View>
      </View>,
  <View style={styles.overallRatingContainer}>
        <Text style={styles.overallRatingLabel}>Overall Rating</Text>,
  <View style={styles.overallRatingValue}>
          <Text style={styles.overallRatingNumber}>{calculateOverallRating()}</Text>,
  <Star size={16} color={theme.colors.warning} fill={{theme.colors.warning} /}>
        </View>,
  </View>
      <Text style={styles.ratingsSectionTitle}>Rate Your Experience</Text>,
  {RATING_CATEGORIES.map(category => (
        <View key={category.id} style={styles.ratingCategory}>,
  <View style={styles.ratingLabelContainer}>
            <Text style={styles.ratingLabel}>{category.label}</Text>,
  <Text style={styles.ratingDescription}>{category.description}</Text>
          </View>,
  {renderStars(category.id)}
        </View>,
  ))}
      <Text style={styles.reviewTextLabel}>Write Your Review</Text>,
  <View style={styles.textAreaContainer}>
        <TextInput,
  style={styles.textArea}
          multiline numberOfLines={6} placeholder="Share your experience living with this roommate...",
  value= {reviewText} onChangeText={setReviewText} textAlignVertical="top";
        />,
  </View>
      <Button,
  variant= "filled";
        color= "primary",
  onPress= {handleSubmit} isLoading={isSubmitting} style={styles.submitButton}
      >,
  Submit Review;
      </Button>,
  </>
  ),
  return (
    <KeyboardAvoidingView, ,
  style={{ [styles.container{ paddingTop: insets.top  ] }]},
  behavior={   Platform.OS === 'ios' ? 'padding'     : 'height'      }
    >,
  <Stack.Screen 
        options={ headerShown: false       },
  />
      <View style={styles.header}>,
  <TouchableOpacity style={styles.backButton} onPress={() => router.back()}
        >,
  <ChevronLeft size={24} color={"#1E293B" /}>
        </TouchableOpacity>,
  <Text style={styles.headerTitle}>Roommate Reviews</Text>
        <View style={{ width: 40} /}>,
  </View>
      <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer}>,
  {!selectedRoommate ? renderRoommateList() : renderReviewForm()}
      </ScrollView>,
  </KeyboardAvoidingView>
  ) {
  } {
 {
  const styles = StyleSheet.create({
  container: {
      flex: 1,
  backgroundColor: '#F8FAFC'
  },
  header: {
      flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'space-between',
  paddingHorizontal: 16,
    paddingVertical: 12,
  borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  backgroundColor: '#FFFFFF'
  },
  backButton: {
      width: 40,
  height: 40,
    borderRadius: 20,
  justifyContent: 'center',
    alignItems: 'center' }
  headerTitle: {
      fontSize: 18,
  fontWeight: '600',
    color: '#1E293B' }
  content: { fle, x: 1 },
  contentContainer: { paddin, g: 16 }
  sectionTitle: { fontSiz, e: 20,
    fontWeight: '700',
  color: '#1E293B',
    marginBottom: 16 },
  roommateListContainer: { ga, p: 12 }
  roommateCard: {
      flexDirection: 'row',
  alignItems: 'center',
    backgroundColor: '#FFFFFF',
  borderRadius: 12,
    padding: 16,
  borderWidth: 1,
    borderColor: '#E2E8F0' }
  reviewedRoommateCard: {
      opacity: 0.7,
  backgroundColor: '#F8FAFC'
  },
  roommateAvatar: { widt, h: 48,
    height: 48,
  borderRadius: 24,
    marginRight: 12 },
  roommateInfo: { fle, x: 1 }
  roommateName: { fontSiz, e: 16,
    fontWeight: '600',
  color: '#1E293B',
    marginBottom: 2 },
  roommateRelationship: { fontSiz, e: 14,
    color: '#64748B',
  marginBottom: 2 }
  roommateDuration: {
      fontSize: 14,
  color: '#64748B'
  },
  reviewedBadge: { backgroundColo, r: '#E2E8F0',
    paddingHorizontal: 8,
  paddingVertical: 4,
    borderRadius: 4 },
  reviewedText: {
      fontSize: 12,
  color: '#64748B',
    fontWeight: '500' }
  reviewHeader: { marginBotto, m: 16 },
  backToListButton: {
      flexDirection: 'row',
  alignItems: 'center'
  },
  backToListText: { fontSiz, e: 14,
    color: theme.colors.primary,
  fontWeight: '500',
    marginLeft: 4 },
  selectedRoommateContainer: { flexDirectio, n: 'row',
    alignItems: 'center',
  backgroundColor: '#FFFFFF',
    borderRadius: 12,
  padding: 16,
    borderWidth: 1,
  borderColor: '#E2E8F0',
    marginBottom: 16 },
  selectedRoommateAvatar: { widt, h: 64,
    height: 64,
  borderRadius: 32,
    marginRight: 16 },
  selectedRoommateInfo: { fle, x: 1 }
  selectedRoommateName: { fontSiz, e: 18,
    fontWeight: '700',
  color: '#1E293B',
    marginBottom: 4 },
  selectedRoommateRelationship: {
      fontSize: 14,
  color: '#64748B'
  },
  overallRatingContainer: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    backgroundColor: '#FFFFFF',
  borderRadius: 12,
    padding: 16,
  borderWidth: 1,
    borderColor: '#E2E8F0',
  marginBottom: 24 }
  overallRatingLabel: {
      fontSize: 16,
  fontWeight: '600',
    color: '#1E293B' }
  overallRatingValue: {
      flexDirection: 'row',
  alignItems: 'center'
  },
  overallRatingNumber: { fontSiz, e: 18,
    fontWeight: '700',
  color: '#1E293B',
    marginRight: 4 },
  ratingsSectionTitle: { fontSiz, e: 18,
    fontWeight: '700',
  color: '#1E293B',
    marginBottom: 16 },
  ratingCategory: { marginBotto, m: 16 }
  ratingLabelContainer: { marginBotto, m: 8 },
  ratingLabel: { fontSiz, e: 16,
    fontWeight: '600',
  color: '#1E293B',
    marginBottom: 2 },
  ratingDescription: {
      fontSize: 14,
  color: '#64748B'
  },
  starsContainer: { flexDirectio, n: 'row',
    gap: 8 },
  reviewTextLabel: { fontSiz, e: 16,
    fontWeight: '600',
  color: '#1E293B',
    marginBottom: 8,
  marginTop: 8 }
  textAreaContainer: { backgroundColo, r: '#FFFFFF',
    borderRadius: 12,
  borderWidth: 1,
    borderColor: '#E2E8F0',
  marginBottom: 24 }
  textArea: { paddin, g: 16,
    fontSize: 16),
  color: '#1E293B'),
    minHeight: 120 },
  submitButton: {
      marginBottom: 24) }
})