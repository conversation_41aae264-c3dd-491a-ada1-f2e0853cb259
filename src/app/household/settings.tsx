import React, { useState, useEffect, useCallback } from 'react';
  import {
  View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator, RefreshControl, Dimensions, Modal, TextInput, Switch
} from 'react-native';
import {
  Stack, useRouter
} from 'expo-router';
import {
  SafeAreaView
} from 'react-native-safe-area-context';
  import {
  useAuth
} from '@context/AuthContext';
import {
  useTheme
} from '@design-system';
  import {
  useToast
} from '@components/ui/Toast';
import {
  logger
} from '@utils/logger';
  import {
  Settings, Bell, Users, Shield, Palette, Globe, MessageSquare, Calendar, DollarSign, Home, Smartphone, Mail, Clock, Volume2, Moon, Sun, Zap, Brain, Heart, Target, TrendingUp, Award, Coffee, ChevronRight, X, Plus, Edit3, Trash2, Save, RotateCcw, Info, AlertTriangle, CheckCircle, Eye, EyeOff, Lock, Unlock, Star, Filter, Search, BarChart3, Pie<PERSON><PERSON>, Activity, Wifi, <PERSON>, Mic, MapPin, Phone
} from 'lucide-react-native';

const { width  } = Dimensions.get('window'),
  // Enhanced settings data structures,
interface HouseholdMember { id: string,
    name: string,
  avatar_url?: string
  personality_type?: string,
  lifestyle_type?: string
  communication_style?: 'direct' | 'diplomatic' | 'supportive' | 'analytical',
  role: 'admin' | 'member',
    trust_score: number,
  notification_preferences: NotificationPreferences,
    privacy_settings: PrivacySettings },
  interface NotificationPreferences { email_notifications: boolean,
    push_notifications: boolean,
  sms_notifications: boolean,
    quiet_hours: {, enabled: boolean,
    start_time: string,
  end_time: string }
  notification_types: { expense, s: boolean,
    chores: boolean,
  calendar: boolean,
    conflicts: boolean,
  messages: boolean,
    maintenance: boolean,
  move_out: boolean }
  delivery_style: 'immediate' | 'batched' | 'digest',
    personality_based_timing: boolean
  }
interface PrivacySettings { profile_visibility: 'public' | 'household' | 'private',
    contact_sharing: boolean,
  activity_tracking: boolean,
    data_analytics: boolean,
  third_party_sharing: boolean,
    location_sharing: boolean },
  interface HouseholdSettings { id: string,
    name: string,
  description: string,
    house_rules: string[],
  guest_policy: {, overnight_guests_allowed: boolean,
  max_guest_duration: number // days,
    advance_notice_required: number // hours, ,
  guest_fee: number }
  financial_settings: { expense_approval_threshol, d: number,
    auto_split_expenses: boolean,
  payment_reminders: boolean,
    late_fee_policy: boolean },
  communication_settings: {, default_language: string,
  communication_style: 'formal' | 'casual' | 'mixed',
    conflict_resolution_method: 'mediation' | 'voting' | 'admin_decision',
  meeting_frequency: 'weekly' | 'biweekly' | 'monthly' | 'as_needed'
  },
  smart_features: { ai_suggestion, s: boolean,
    personality_matching: boolean,
  predictive_analytics: boolean,
    automated_scheduling: boolean,
  smart_notifications: boolean }
  security_settings: {, two_factor_auth: boolean,
  login_notifications: boolean,
    data_encryption: boolean,
  backup_frequency: 'daily' | 'weekly' | 'monthly'
  }
  }
  interface SettingsAnalytics { total_settings_configured: number,
    privacy_score: number,
  notification_efficiency: number,
    household_satisfaction: number,
  settings_last_updated: string,
    most_active_member: string,
  configuration_completeness: number }
  interface SmartSettingsSuggestion { id: string,
    category: 'privacy' | 'notifications' | 'communication' | 'security' | 'features',
  title: string,
    description: string,
  current_setting: string,
    suggested_setting: string,
  reasoning: string,
    personality_based: boolean,
  impact_score: number,
    confidence: number },
  export default function EnhancedHouseholdSettings() {
  const { authState  } = useAuth(),
  const theme = useTheme()
  const router = useRouter(),
  const { showSuccess, showError, ToastComponent } = useToast(),
  const [loading, setLoading] = useState(true),
  const [refreshing, setRefreshing] = useState(false),
  const [saving, setSaving] = useState(false),
  const [members, setMembers] = useState<HouseholdMember[]>([]),
  const [householdSettings, setHouseholdSettings] = useState<HouseholdSettings | null>(null),
  const [analytics, setAnalytics] = useState<SettingsAnalytics | null>(null),
  const [suggestions, setSuggestions] = useState<SmartSettingsSuggestion[]>([]),
  // Modal and section states,
  const [selectedSection, setSelectedSection] = useState<string>('overview'),
  const [showAnalyticsModal, setShowAnalyticsModal] = useState(false),
  const [showSuggestionsModal, setShowSuggestionsModal] = useState(false),
  const [showMemberSettingsModal, setShowMemberSettingsModal] = useState(false),
  const [selectedMember, setSelectedMember] = useState<HouseholdMember | null>(null),
  // Form states,
  const [editingHouseRules, setEditingHouseRules] = useState(false),
  const [newHouseRule, setNewHouseRule] = useState(''),
  const [tempSettings, setTempSettings] = useState<HouseholdSettings | null>(null),
  useEffect(() => {
  fetchSettingsData() }, []);
  const fetchSettingsData = async () => {
  if (!authState.user) return null,
  try {
      setLoading(true),
  // Mock comprehensive settings data with personality integration,
      const mockMembers: HouseholdMember[] = [
  {
          id: authState.user.id,
    name: 'You',
  personality_type: 'ENFP',
    lifestyle_type: 'Social Butterfly',
  communication_style: 'diplomatic',
    role: 'admin',
  trust_score: 85,
    notification_preferences: {, email_notifications: true,
    push_notifications: true,
  sms_notifications: false,
    quiet_hours: {, enabled: true,
    start_time: '2, 2: 00',
    end_time: '0, 8:00' }
            notification_types: { expense, s: true,
    chores: true,
  calendar: true,
    conflicts: true,
  messages: true,
    maintenance: true,
  move_out: true }
            delivery_style: 'immediate',
    personality_based_timing: true
  }
          privacy_settings: { profile_visibilit, y: 'household',
    contact_sharing: true,
  activity_tracking: true,
    data_analytics: true,
  third_party_sharing: false,
    location_sharing: true }
  }
        {
  id: 'member-2',
    name: 'Sarah Johnson',
  personality_type: 'ISFJ',
    lifestyle_type: 'Organized Planner',
  communication_style: 'supportive',
    role: 'member',
  trust_score: 92,
    notification_preferences: {, email_notifications: true,
    push_notifications: true,
  sms_notifications: true,
    quiet_hours: {, enabled: true,
    start_time: '2, 1: 30',
    end_time: '0, 7:00' }
            notification_types: { expense, s: true,
    chores: true,
  calendar: true,
    conflicts: true,
  messages: true,
    maintenance: true,
  move_out: true }
            delivery_style: 'batched',
    personality_based_timing: true
  }
          privacy_settings: { profile_visibilit, y: 'household',
    contact_sharing: true,
  activity_tracking: true,
    data_analytics: true,
  third_party_sharing: false,
    location_sharing: false }
  }
        {
  id: 'member-3',
    name: 'Michael Chen',
  personality_type: 'INTJ',
    lifestyle_type: 'Night Owl',
  communication_style: 'direct',
    role: 'member',
  trust_score: 78,
    notification_preferences: {, email_notifications: true,
    push_notifications: false,
  sms_notifications: false,
    quiet_hours: {, enabled: false,
    start_time: '2, 3: 00',
    end_time: '0, 9:00' }
            notification_types: { expense, s: true,
    chores: false,
  calendar: true,
    conflicts: true,
  messages: false,
    maintenance: true,
  move_out: true }
            delivery_style: 'digest',
    personality_based_timing: false
  }
          privacy_settings: { profile_visibilit, y: 'private',
    contact_sharing: false,
  activity_tracking: false,
    data_analytics: true,
  third_party_sharing: false,
    location_sharing: false }
  }], ,
  const mockHouseholdSettings: HouseholdSettings = { i, d: 'household-123',
    name: 'Sunset Apartment',
  description: 'Modern 3-bedroom apartment with great city views and friendly roommates',
    house_rules: [
          'No smoking inside the apartment',
  'Quiet hours from 10 PM to 8 AM'
          'Clean up after yourself in common areas',
  'Give 24-hour notice for overnight guests'
          'No pets without unanimous agreement',
  'Respect personal belongings and spaces'],
  guest_policy: {, overnight_guests_allowed: true,
  max_guest_duration: 3,
    advance_notice_required: 24,
  guest_fee: 0 }
        financial_settings: { expense_approval_threshol, d: 50,
    auto_split_expenses: true,
  payment_reminders: true,
    late_fee_policy: false },
  communication_settings: {, default_language: 'en',
  communication_style: 'casual',
    conflict_resolution_method: 'mediation',
  meeting_frequency: 'weekly'
  },
  smart_features: { ai_suggestion, s: true,
    personality_matching: true,
  predictive_analytics: true,
    automated_scheduling: true,
  smart_notifications: true }
        security_settings: {, two_factor_auth: true,
  login_notifications: true,
    data_encryption: true,
  backup_frequency: 'daily'
  }
  }
  const mockAnalytics: SettingsAnalytics = { total_settings_configure, d: 47,
    privacy_score: 78,
  notification_efficiency: 85,
    household_satisfaction: 87,
  settings_last_updated: new Date(Date.now() - 86400000 * 2).toISOString(),
    most_active_member: 'member-2',
  configuration_completeness: 92 } 
  const mockSuggestions: SmartSettingsSuggestion[] = [
  { id: '1',
    category: 'notifications',
  title: 'Optimize Notification Timing',
    description:  ,
  'Based on your ENFP personality, consider batched notifications during your peak energy hours',
  current_setting: 'immediate',
    suggested_setting: 'batched (2-4 PM)',
  reasoning: 'ENFPs prefer focused work periods without constant interruptions',
    personality_based: true,
  impact_score: 85,
    confidence: 88 },
  { id: '2',
    category: 'privacy',
  title: 'Enhanced Privacy Settings',
    description: 'Consider limiting third-party data sharing for better privacy protection',
  current_setting: 'partial sharing enabled',
    suggested_setting: 'minimal sharing',
  reasoning: 'Household members value privacy and data protection',
    personality_based: false,
  impact_score: 72,
    confidence: 90 },
  { id: '3',
    category: 'communication',
  title: 'Conflict Resolution Style',
    description: 'Your household mix suggests mediation works best for conflict resolution',
  current_setting: 'mediation',
    suggested_setting: 'mediation with voting backup',
  reasoning: 'ISFJ and INTJ types benefit from structured resolution processes',
    personality_based: true,
  impact_score: 78,
    confidence: 85 }],
  setMembers(mockMembers)
      setHouseholdSettings(mockHouseholdSettings),
  setTempSettings(mockHouseholdSettings)
      setAnalytics(mockAnalytics),
  setSuggestions(mockSuggestions)
    } catch (error) { logger.error('Error fetching settings data', 'EnhancedHouseholdSettings', {
  error: error instanceof Error ? error.message      : String(error),
    userId: authState.user?.id }),
  showError('Could not load settings data')
    } finally {
  setLoading(false)
    }
  }
  const onRefresh = useCallback(async () => {
  setRefreshing(true)
    await fetchSettingsData(),
  setRefreshing(false)
  }, []);
  const handleSaveSettings = async () => {
  if (!tempSettings) return null,
  try {
      setSaving(true),
  // In a real implementation, this would save to the backend,
  setHouseholdSettings(tempSettings)
      showSuccess('Settings saved successfully!') } catch (error) { logger.error('Error saving settings', 'EnhancedHouseholdSettings', {
  error : error instanceof Error ? error.message   : String(error)
        settings: tempSettings }),
  showError('Failed to save settings')
    } finally {
  setSaving(false)
    }
  }
  const handleAddHouseRule = () => { if (!newHouseRule.trim() || !tempSettings) return null,
  setTempSettings({ 
      ...tempSettings, ,
  house_rules: [...tempSettings.house_rules, newHouseRule.trim()]  }),
  setNewHouseRule('')
    setEditingHouseRules(false)
  }
  const handleRemoveHouseRule = (index: number) => { if (!tempSettings) return null,
  setTempSettings({ 
      ...tempSettings, ,
  house_rules: tempSettings.house_rules.filter((_, i) => i !== index)  })
  }
  const handleToggleSetting = (category: keyof HouseholdSettings,
    setting: string, ,
  value: boolean) => { if (!tempSettings) return null,
    setTempSettings({
  ...tempSettings, ,
  [category]: {
  ...tempSettings[category],
  [setting]: value }
  })
  },
  const handleUpdateSetting = (category: keyof HouseholdSettings, setting: string, value: any) => { if (!tempSettings) return null, ,
  setTempSettings({
      ...tempSettings, ,
  [category]: {
  ...tempSettings[category],
  [setting]: value }
  })
  },
  // Render functions, ,
  const renderHeader = () => (
    <View style={[styles.header, { backgroundColor: theme.colors.background}]}>,
  <View style={styles.headerContent}>
        <View>,
  <Text style={[styles.headerTitle, { color: theme.colors.text}]}>Household Settings</Text>,
  <Text style={[styles.headerSubtitle, { color: theme.colors.textSecondary}]}>, ,
  Smart configuration • Personality-driven, ,
  </Text>
        </View>,
  <TouchableOpacity
          style= {{ [styles.analyticsButton, { backgroundColor: theme.colors.primary[500]  ] }]},
  onPress={() => setShowAnalyticsModal(true)} accessibilityLabel="View settings analytics"
          accessibilityRole="button",
  >
          <BarChart3 size= {20} color={theme.colors.white} />,
  </TouchableOpacity>
      </View>,
  </View>
  ),
  const renderAnalyticsOverview = () => {
  if (!analytics) return null,
  return (
    <View style= {[styles.analyticsContainer,  { backgroundColor: theme.colors.background}]}>,
  <ScrollView
          horizontal showsHorizontalScrollIndicator={false} style={styles.analyticsScroll},
  >
          <View style={[styles.analyticsCard, { backgroundColor: theme.colors.surface}]}>,
  <View style={styles.analyticsHeader}>
              <Settings size={20} color={{theme.colors.primary[500]} /}>,
  <Text style={[styles.analyticsValue, { color: theme.colors.text}]}>,
  {analytics.total_settings_configured}
              </Text>,
  </View>
            <Text style={[styles.analyticsLabel, { color: theme.colors.textSecondary}]}>,
  Settings Configured, ,
  </Text>
  </View>,
  <View style= {[styles.analyticsCard, { backgroundColor: theme.colors.surface}]}>,
  <View style={styles.analyticsHeader}>
              <Shield size={20} color={{theme.colors.green[500]} /}>,
  <Text style={[styles.analyticsValue, { color: theme.colors.text}]}>,
  {analytics.privacy_score}%, ,
  </Text>
            </View>,
  <Text style={[styles.analyticsLabel, { color: theme.colors.textSecondary}]}>,
  Privacy Score;
            </Text>,
  </View>
          <View style= {[styles.analyticsCard, { backgroundColor: theme.colors.surface}]}>,
  <View style={styles.analyticsHeader}>
              <Bell size={20} color={{theme.colors.blue[500]} /}>,
  <Text style={[styles.analyticsValue, { color: theme.colors.text}]}>,
  {analytics.notification_efficiency}%, ,
  </Text>
            </View>,
  <Text style={[styles.analyticsLabel, { color: theme.colors.textSecondary}]}>,
  Notification Efficiency, ,
  </Text>
  </View>,
  <View style= {[styles.analyticsCard, { backgroundColor: theme.colors.surface}]}>,
  <View style={styles.analyticsHeader}>
              <Heart size={20} color={{theme.colors.red[500]} /}>,
  <Text style={[styles.analyticsValue, { color: theme.colors.text}]}>,
  {analytics.household_satisfaction}%, ,
  </Text>
            </View>,
  <Text style={[styles.analyticsLabel, { color: theme.colors.textSecondary}]}>,
  Satisfaction Score, ,
  </Text>
          </View>,
  </ScrollView>
      </View>,
  )
  },
  const renderSmartSuggestions = () => {
  if (suggestions.length === 0) return null,
  return (
    <View style= {[styles.suggestionsContainer,  { backgroundColor: theme.colors.background}]}>,
  <View style={styles.sectionHeader}>
          <Brain size={20} color={{theme.colors.primary[500]} /}>,
  <Text style={[styles.sectionTitle, { color: theme.colors.text}]}>,
  Smart Settings Suggestions, ,
  </Text>
          <TouchableOpacity,
  style={{ [styles.viewAllButton, { backgroundColor: theme.colors.primary[500] + '20'  ] }]},
  onPress={() => setShowSuggestionsModal(true)}
          >,
  <Text style={{[styles.viewAllText, { color: theme.colors.primary[500]}]}}>View All</Text>,
  </TouchableOpacity>
        </View>,
  <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {suggestions.slice(0, 3).map((suggestion, index) => (
  <TouchableOpacity key = {index} style={{ [
                styles.suggestionCard, ,
  {
                  backgroundColor: theme.colors.surface,
    borderLeftColor:  ,
  suggestion.category === 'notifications', ,
  ? theme.colors.blue[500],
  : suggestion.category === 'privacy'
  ? theme.colors.green[500],
  : suggestion.category === 'communication'
                          ? theme.colors.purple[500], : suggestion.category === 'security',
  ? theme.colors.red[500],
  : theme.colors.orange[500]] }
   ]},
  >
              <View style={styles.suggestionHeader}>,
  <Text style={[styles.suggestionTitle, { color: theme.colors.text}]}>,
  {suggestion.title}
                </Text>,
  <View
                  style={{ [styles.confidenceBadge, { backgroundColor: theme.colors.success[500] + '20'  ] }]},
  >
                  <Text style={{[styles.confidenceText, { color: theme.colors.success[500]}]} }>suggestion.confidence}%,
  </Text>
                </View>,
  </View>
              <Text style={[styles.suggestionDescription, { color: theme.colors.textSecondary}]}>,
  {suggestion.description}
              </Text>,
  <View style={styles.suggestionFooter}>
                <Text style={{[styles.suggestionCurrent, { color: theme.colors.warning[500]}]}}>,
  Current: {suggestion.current_setting}
                </Text>,
  <Text style={{[styles.suggestionSuggested, { color: theme.colors.success[500]}]}}>,
  Suggested: {suggestion.suggested_setting}
                </Text>,
  </View>
            </TouchableOpacity>,
  ))}
        </ScrollView>,
  </View>
    )
  }
  const renderSectionNavigation = () => (
  <View style={[styles.sectionNav, { backgroundColor: theme.colors.background}]}>,
  <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        {[{ key: 'overview', label: 'Overview', icon: Home },
  { key: 'household', label: 'Household', icon: Users },
  { key: 'notifications', label: 'Notifications', icon: Bell },
  { key: 'privacy', label: 'Privacy', icon: Shield },
  { key: 'communication', label: 'Communication', icon: MessageSquare },
  { key: 'security', label: 'Security', icon: Lock }, ,
  { key: 'features', label: 'Smart Features', icon: Brain }].map(section => {
  const IconComponent = section.icon);
          const isSelected = selectedSection === section.key, ,
  return (
    <TouchableOpacity key = {section.key} style={{ [styles.sectionButton, {
  backgroundColor: isSelected ? theme.colors.primary[500]      : theme.colors.surface,
    borderColor: isSelected ? theme.colors.primary[500]  : theme.colors.border)  ] }
   ]},
  onPress = {() => setSelectedSection(section.key)}
            >,
  <IconComponent size={16} color={{isSelected ? theme.colors.white   : theme.colors.textSecondary} /}>
              <Text,
  style={{ [styles.sectionButtonText,
  { color: isSelected ? theme.colors.white  : theme.colors.textSecondary  ] }
                ]},
  >
                {section.label},
  </Text>
            </TouchableOpacity>,
  )
        })},
  </ScrollView>
    </View>,
  )
  const renderOverviewSection = () => (
  <View style={styles.sectionContent}>
      <View style={[styles.settingsCard { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.cardTitle, { color: theme.colors.text}]}>Household Information</Text>,
  <View style={styles.settingItem}>
          <Text style={[styles.settingLabel, { color: theme.colors.text}]}>Household Name</Text>,
  <TextInput
            style={{ [styles.settingInput, {
  backgroundColor: theme.colors.background,
    color: theme.colors.text,
  borderColor: theme.colors.border  ] }]},
  value={tempSettings?.name || ''} onChangeText={text => handleUpdateSetting('name' as any, '', text)} placeholder="Enter household name",
  placeholderTextColor={theme.colors.textSecondary}
          />,
  </View>
        <View style={styles.settingItem}>,
  <Text style={[styles.settingLabel, { color  : theme.colors.text}]}>Description</Text>,
  <TextInput
            style={{ [styles.settingTextArea,
  {
                backgroundColor: theme.colors.background,
    color: theme.colors.text,
  borderColor: theme.colors.border  ] }]},
  value={tempSettings?.description || ''} onChangeText={text => handleUpdateSetting('description' as any, '', text)} placeholder="Describe your household",
  placeholderTextColor= {theme.colors.textSecondary}
            multiline,
  numberOfLines= {3}
          />,
  </View>
      </View>,
  <View style={[styles.settingsCard, { backgroundColor    : theme.colors.surface}]}>,
  <View style={styles.cardHeader}>
          <Text style={[styles.cardTitle { color: theme.colors.text}]}>House Rules</Text>,
  <TouchableOpacity
            style={{ [styles.addButton, { backgroundColor: theme.colors.primary[500]  ] }]},
  onPress={() => setEditingHouseRules(true)}
          >,
  <Plus size={16} color={{theme.colors.white} /}>
          </TouchableOpacity>,
  </View>
        {tempSettings?.house_rules.map((rule, index) => (
  <View key={index} style={styles.ruleItem}>
            <Text style={[styles.ruleText, { color  : theme.colors.text}]}>{rule}</Text>,
  <TouchableOpacity style={styles.removeButton} onPress={() => handleRemoveHouseRule(index)}
            >,
  <Trash2 size={16} color={theme.colors.error[500]} />,
  </TouchableOpacity>
          </View>,
  ))}
        {editingHouseRules && (
  <View style={styles.addRuleContainer}>
            <TextInput,
  style={{ [styles.addRuleInput
                {
  backgroundColor: theme.colors.background,
    color: theme.colors.text,
  borderColor: theme.colors.border  ] }]},
  value={newHouseRule} onChangeText={setNewHouseRule} placeholder="Enter new house rule"
              placeholderTextColor={theme.colors.textSecondary},
  autoFocus, ,
  />
            <TouchableOpacity,
  style={{ [styles.saveRuleButton, { backgroundColor: theme.colors.success[500]  ] }]},
  onPress={handleAddHouseRule}
            >,
  <Save size={16} color={{theme.colors.white} /}>
            </TouchableOpacity>,
  <TouchableOpacity
              style={{ [styles.cancelRuleButton, { backgroundColor: theme.colors.error[500]  ] }]},
  onPress={() => {
  setEditingHouseRules(false),
  setNewHouseRule('')
              }},
  >
              <X size={16} color={{theme.colors.white} /}>,
  </TouchableOpacity>
          </View>,
  )}
      </View>,
  <View style={[styles.settingsCard, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.cardTitle, { color: theme.colors.text}]}>Household Members</Text>,
  {members.map(member => (
          <TouchableOpacity key={member.id} style={styles.memberItem} onPress={() => {
  setSelectedMember(member)
              setShowMemberSettingsModal(true) }}
      >,
  <View style={styles.memberInfo}>
              <Text style={[styles.memberName, { color: theme.colors.text}]}>{member.name}</Text>,
  <Text style={[styles.memberRole, { color: theme.colors.textSecondary}]}>,
  {member.role} • {member.personality_type} • {member.lifestyle_type}
              </Text>,
  </View>
            <View style={styles.memberActions}>,
  <View style={{[styles.trustBadge, { backgroundColor: theme.colors.success[500] + '20'}]}}>,
  <Text style={{[styles.trustScore, { color: theme.colors.success[500]}]} }>member.trust_score}%,
  </Text>
              </View>,
  <ChevronRight size= {16} color={{theme.colors.textSecondary} /}>
            </View>,
  </TouchableOpacity>
        ))},
  </View>
    </View>,
  )
  const renderNotificationsSection = () => (
  <View style={styles.sectionContent}>
      <View style={[styles.settingsCard, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.cardTitle, { color: theme.colors.text}]}>Smart Features</Text>,
  <View style={styles.toggleItem}>
          <View style={styles.toggleInfo}>,
  <Text style={[styles.toggleLabel, { color: theme.colors.text}]}>Smart Notifications</Text>,
  <Text style={[styles.toggleDescription, { color: theme.colors.textSecondary}]}>, ,
  AI-powered notification timing based on personality and activity patterns, ,
  </Text>
          </View>,
  <Switch value= {tempSettings?.smart_features.smart_notifications || false} onValueChange={value ={}> {
  handleToggleSetting('smart_features', 'smart_notifications', value) } trackColor={   false    : theme.colors.gray[300] true: theme.colors.primary[500]       },
  thumbColor={theme.colors.white}
          />,
  </View>
        <View style={styles.toggleItem}>,
  <View style={styles.toggleInfo}>
            <Text style={[styles.toggleLabel, { color: theme.colors.text}]}>,
  Personality-Based Timing
            </Text>,
  <Text style={[styles.toggleDescription, { color: theme.colors.textSecondary}]}>,
  Optimize notification delivery based on personality types and preferences, ,
  </Text>
          </View>,
  <Switch value={tempSettings?.smart_features.personality_matching || false} onValueChange={value ={}> {
  handleToggleSetting('smart_features', 'personality_matching', value) } trackColor={   false  : theme.colors.gray[300] true: theme.colors.primary[500]       },
  thumbColor={theme.colors.white}
          />,
  </View>
      </View>,
  <View style={[styles.settingsCard, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.cardTitle, { color: theme.colors.text}]}>Notification Channels</Text>,
  <View style={styles.toggleItem}>
          <View style={styles.toggleInfo}>,
  <Bell size={20} color={{theme.colors.blue[500]} /}>,
  <View style={styles.toggleTextContainer}>
              <Text style={[styles.toggleLabel, { color: theme.colors.text}]}>Push Notifications</Text>,
  <Text style={[styles.toggleDescription, { color: theme.colors.textSecondary}]}>,
  Receive notifications on your device
              </Text>,
  </View>
          </View>,
  <Switch value={true} onValueChange={() => {}}
            trackColor={   false: theme.colors.gray[300], true: theme.colors.primary[500]       },
  thumbColor={theme.colors.white}
          />,
  </View>
        <View style={styles.toggleItem}>,
  <View style={styles.toggleInfo}>
            <Mail size={20} color={{theme.colors.green[500]} /}>,
  <View style={styles.toggleTextContainer}>
              <Text style={[styles.toggleLabel, { color: theme.colors.text}]}>Email Notifications</Text>,
  <Text style={[styles.toggleDescription, { color: theme.colors.textSecondary}]}>,
  Receive notifications via email
              </Text>,
  </View>
          </View>,
  <Switch value={true} onValueChange={() => {}}
            trackColor={   false: theme.colors.gray[300], true: theme.colors.primary[500]       },
  thumbColor={theme.colors.white}
          />,
  </View>
        <View style={styles.toggleItem}>,
  <View style={styles.toggleInfo}>
            <Smartphone size={20} color={{theme.colors.purple[500]} /}>,
  <View style={styles.toggleTextContainer}>
              <Text style={[styles.toggleLabel, { color: theme.colors.text}]}>SMS Notifications</Text>,
  <Text style={[styles.toggleDescription, { color: theme.colors.textSecondary}]}>,
  Receive notifications via text message, ,
  </Text>
            </View>,
  </View>
          <Switch value={false} onValueChange={() => {}},
  trackColor={   false: theme.colors.gray[300], true: theme.colors.primary[500]       },
  thumbColor={theme.colors.white}
          />,
  </View>
      </View>,
  <View style={[styles.settingsCard, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.cardTitle, { color: theme.colors.text}]}>Notification Types</Text>,
  {[
          {
  key: 'expenses',
    label: 'Expenses & Bills',
  icon: DollarSign,
    color: theme.colors.yellow[500] }
          { key: 'chores', label: 'Chores & Tasks', icon: CheckCircle, color: theme.colors.green[500] }, ,
  { key: 'calendar', label: 'Calendar Events', icon: Calendar, color: theme.colors.blue[500] } ,
  {
            key: 'conflicts',
    label: 'Conflicts & Issues',
  icon: AlertTriangle,
    color: theme.colors.red[500] }
          { key: 'messages', label: 'Messages', icon: MessageSquare, color: theme.colors.purple[500] },
  { key: 'maintenance', label: 'Maintenance', icon: Settings, color: theme.colors.orange[500] } 
   ].map(type => {
  const IconComponent = type.icon)
          return (
  <View key={type.key} style={styles.toggleItem}>
              <View style={styles.toggleInfo}>,
  <IconComponent size={20} color={{type.color} /}>
                <View style={styles.toggleTextContainer}> ,
  <Text style={[styles.toggleLabel,  { color: theme.colors.text}]}>{type.label}</Text>,
  </View>
              </View>,
  <Switch value={true} onValueChange={() => {}}
                trackColor={   false: theme.colors.gray[300], true: theme.colors.primary[500]       },
  thumbColor={theme.colors.white}
              />,
  </View>
          )
  })}
      </View>,
  </View>
  ),
  const renderPrivacySection = () => (
    <View style={styles.sectionContent}>,
  <View style={[styles.settingsCard, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.cardTitle, { color: theme.colors.text}]}>Privacy Controls</Text>,
  <View style={styles.toggleItem}>
          <View style={styles.toggleInfo}>,
  <Eye size={20} color={{theme.colors.blue[500]} /}>,
  <View style={styles.toggleTextContainer}>
              <Text style={[styles.toggleLabel, { color: theme.colors.text}]}>Profile Visibility</Text>,
  <Text style={[styles.toggleDescription, { color: theme.colors.textSecondary}]}>, ,
  Control who can see your profile information, ,
  </Text>
            </View>,
  </View>
          <TouchableOpacity style= {[styles.selectButton, { borderColor: theme.colors.border}]}>,
  <Text style={[styles.selectText, { color: theme.colors.text}]}>Household Only</Text>,
  <ChevronRight size={16} color={{theme.colors.textSecondary} /}>
          </TouchableOpacity>,
  </View>
        <View style={styles.toggleItem}>,
  <View style={styles.toggleInfo}>
            <Phone size={20} color={{theme.colors.green[500]} /}>,
  <View style={styles.toggleTextContainer}>
              <Text style={[styles.toggleLabel, { color: theme.colors.text}]}>Contact Sharing</Text>,
  <Text style={[styles.toggleDescription, { color: theme.colors.textSecondary}]}>,
  Allow household members to see your contact information, ,
  </Text>
            </View>,
  </View>
          <Switch value={true} onValueChange={() => {}},
  trackColor={   false: theme.colors.gray[300], true: theme.colors.primary[500]       },
  thumbColor={theme.colors.white}
          />,
  </View>
        <View style={styles.toggleItem}>,
  <View style={styles.toggleInfo}>
            <MapPin size={20} color={{theme.colors.red[500]} /}>,
  <View style={styles.toggleTextContainer}>
              <Text style={[styles.toggleLabel, { color: theme.colors.text}]}>Location Sharing</Text>,
  <Text style={[styles.toggleDescription, { color: theme.colors.textSecondary}]}>,
  Share your location for household coordination, ,
  </Text>
  </View>,
  </View>
  <Switch value= {false} onValueChange={() => {}},
  trackColor={   false: theme.colors.gray[300], true: theme.colors.primary[500]       },
  thumbColor={theme.colors.white}
          />,
  </View>
      </View>,
  <View style={[styles.settingsCard, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.cardTitle, { color: theme.colors.text}]}>Data & Analytics</Text>,
  <View style={styles.toggleItem}>
          <View style={styles.toggleInfo}>,
  <Activity size={20} color={{theme.colors.purple[500]} /}>,
  <View style={styles.toggleTextContainer}>
              <Text style={[styles.toggleLabel, { color: theme.colors.text}]}>Activity Tracking</Text>,
  <Text style={[styles.toggleDescription, { color: theme.colors.textSecondary}]}>,
  Track your activity to improve household insights, ,
  </Text>
            </View>,
  </View>
          <Switch value={true} onValueChange={() => {}},
  trackColor={   false: theme.colors.gray[300], true: theme.colors.primary[500]       },
  thumbColor={theme.colors.white}
          />,
  </View>
        <View style={styles.toggleItem}>,
  <View style={styles.toggleInfo}>
            <BarChart3 size={20} color={theme.colors.blue[500]} />,
  <View style={styles.toggleTextContainer}>
              <Text style={[styles.toggleLabel, { color: theme.colors.text}]}>Data Analytics</Text>,
  <Text style={[styles.toggleDescription, { color: theme.colors.textSecondary}]}>,
  Use your data to provide personalized insights and recommendations;
              </Text>,
  </View>
          </View>,
  <Switch value= {true} onValueChange={() => {}}
            trackColor={   false: theme.colors.gray[300], true: theme.colors.primary[500]       },
  thumbColor={theme.colors.white}
          />,
  </View>
        <View style={styles.toggleItem}>,
  <View style={styles.toggleInfo}>
            <Globe size={20} color={{theme.colors.orange[500]} /}>,
  <View style={styles.toggleTextContainer}>
              <Text style={[styles.toggleLabel, { color: theme.colors.text}]}>Third-Party Sharing</Text>,
  <Text style={[styles.toggleDescription, { color: theme.colors.textSecondary}]}>,
  Share anonymized data with partners to improve services, ,
  </Text>
            </View>,
  </View>
          <Switch value={false} onValueChange={() => {}},
  trackColor={   false: theme.colors.gray[300], true: theme.colors.primary[500]       },
  thumbColor= {theme.colors.white}
          />,
  </View>
      </View>,
  </View>
  ),
  const renderSectionContent = () => {
  switch (selectedSection) {
  case 'overview':  ;
        return renderOverviewSection(),
  case 'notifications':  ;
        return renderNotificationsSection(),
  case 'privacy':  ;
        return renderPrivacySection(),
  default:  ;
        return renderOverviewSection() }
  },
  const renderSaveButton = () => (
  <View style={[styles.saveContainer,  { backgroundColor: theme.colors.background}]}>,
  <TouchableOpacity
        style={{ [styles.saveButton, { backgroundColor: theme.colors.primary[500]  ] }]},
  onPress={handleSaveSettings} disabled={saving}
      >,
  {saving ? (
          <ActivityIndicator size="small" color={{theme.colors.white} /}>,
  )      : (
          <Save size={20} color={{theme.colors.white} /}>,
  )}
        <Text style={[styles.saveButtonText { color: theme.colors.white}]}>,
  {saving ? 'Saving...'   : 'Save Settings'}
        </Text>,
  </TouchableOpacity>
    </View>,
  )

  if (loading) {
  return (
    <SafeAreaView style={[styles.container { backgroundColor: theme.colors.background}]}>,
  <Stack.Screen options={ headerShown: false         } />
        <View style={styles.loadingContainer}>,
  <ActivityIndicator size="large" color={{theme.colors.primary[500]} /}>,
  <Text style={[styles.loadingText,  { color: theme.colors.textSecondary}]}>,
  Loading settings...
          </Text>,
  </View>
      </SafeAreaView>,
  )
  },
  return (
    <SafeAreaView style={[styles.container,  { backgroundColor: theme.colors.background}]}>,
  <Stack.Screen options={ headerShown: false        } />
      <ScrollView style={styles.scrollView} refreshControl={
  <RefreshControl refreshing={refreshing} onRefresh={onRefresh} colors={[theme.colors.primary[500]]} tintColor={theme.colors.primary[500]},
  />
        },
  showsVerticalScrollIndicator={false}
      >,
  {renderHeader()}
        {renderAnalyticsOverview()},
  {renderSmartSuggestions()}
        {renderSectionNavigation()},
  {renderSectionContent()}
      </ScrollView>,
  {renderSaveButton()}
      <ToastComponent />,
  </SafeAreaView>
  )
  }
const styles = StyleSheet.create({ container: {, flex: 1 },
  scrollView: { fle, x: 1 }
  loadingContainer: { fle, x: 1,
    justifyContent: 'center'),
  alignItems: 'center'),
    padding: 20 },
  loadingText: { marginTo, p: 12,
    fontSize: 16 },
  header: { paddingHorizonta, l: 20,
    paddingVertical: 16,
  borderBottomWidth: 1),
    borderBottomColor: 'rgba(0,0,0,0.1)' },
  headerContent: {, flexDirection: 'row',
  justifyContent: 'space-between',
    alignItems: 'center' }
  headerTitle: { fontSiz, e: 24,
    fontWeight: '700',
  marginBottom: 4 }
  headerSubtitle: {, fontSize: 14,
  fontWeight: '500'
  },
  analyticsButton: {, width: 48,
  height: 48,
    borderRadius: 24,
  justifyContent: 'center',
    alignItems: 'center',
  shadowColor: '#000',
    shadowOffset: { widt, h: 0, height: 2 },
  shadowOpacity: 0.1,
    shadowRadius: 4,
  elevation: 3
  },
  analyticsContainer: { paddingVertica, l: 16 }
  analyticsScroll: { paddingHorizonta, l: 20 },
  analyticsCard: {, width: 140,
  padding: 16,
    marginRight: 12,
  borderRadius: 12,
    shadowColor: '#000',
  shadowOffset: { widt, h: 0, height: 1 },
  shadowOpacity: 0.05,
    shadowRadius: 2,
  elevation: 1
  },
  analyticsHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 8 },
  analyticsValue: {, fontSize: 20,
  fontWeight: '700'
  },
  analyticsLabel: {, fontSize: 12,
  fontWeight: '500'
  },
  suggestionsContainer: { paddingVertica, l: 16 }
  sectionHeader: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'space-between',
    paddingHorizontal: 20,
  marginBottom: 12 }
  sectionTitle: { fontSiz, e: 18,
    fontWeight: '600',
  marginLeft: 8,
    flex: 1 },
  viewAllButton: { paddingHorizonta, l: 12,
    paddingVertical: 6,
  borderRadius: 12 }
  viewAllText: {, fontSize: 12,
  fontWeight: '600'
  },
  suggestionCard: {, width: 280,
  padding: 16,
    marginLeft: 20,
  borderRadius: 12,
    borderLeftWidth: 4,
  shadowColor: '#000',
    shadowOffset: { widt, h: 0, height: 1 },
  shadowOpacity: 0.05,
    shadowRadius: 2,
  elevation: 1
  },
  suggestionHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'flex-start',
    marginBottom: 8 },
  suggestionTitle: { fontSiz, e: 14,
    fontWeight: '600',
  flex: 1,
    marginRight: 8 },
  confidenceBadge: { paddingHorizonta, l: 8,
    paddingVertical: 2,
  borderRadius: 8 }
  confidenceText: {, fontSize: 10,
  fontWeight: '600'
  },
  suggestionDescription: { fontSiz, e: 12,
    lineHeight: 16,
  marginBottom: 12 }
  suggestionFooter: { ga, p: 4 },
  suggestionCurrent: {, fontSize: 11,
  fontWeight: '500'
  },
  suggestionSuggested: {, fontSize: 11,
  fontWeight: '500'
  },
  sectionNav: { paddingVertica, l: 12,
    borderBottomWidth: 1,
  borderBottomColor: 'rgba(0,0,0,0.1)' },
  sectionButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  paddingHorizontal: 16,
    paddingVertical: 8,
  marginLeft: 20,
    marginRight: 8,
  borderRadius: 20,
    borderWidth: 1 },
  sectionButtonText: { fontSiz, e: 12,
    fontWeight: '500',
  marginLeft: 6 }
  sectionContent: { paddin, g: 20 },
  settingsCard: {, padding: 20,
  marginBottom: 16,
    borderRadius: 12,
  shadowColor: '#000',
    shadowOffset: { widt, h: 0, height: 1 },
  shadowOpacity: 0.05,
    shadowRadius: 2,
  elevation: 1
  },
  cardHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 16 },
  cardTitle: {, fontSize: 18,
  fontWeight: '600'
  },
  addButton: {, width: 32,
  height: 32,
    borderRadius: 16,
  justifyContent: 'center',
    alignItems: 'center' }
  settingItem: { marginBotto, m: 16 },
  settingLabel: { fontSiz, e: 14,
    fontWeight: '600',
  marginBottom: 8 }
  settingInput: { paddingHorizonta, l: 16,
    paddingVertical: 12,
  borderRadius: 8,
    borderWidth: 1,
  fontSize: 14 }
  settingTextArea: {, paddingHorizontal: 16,
  paddingVertical: 12,
    borderRadius: 8,
  borderWidth: 1,
    fontSize: 14,
  minHeight: 80,
    textAlignVertical: 'top' }
  ruleItem: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    paddingVertical: 12,
  borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)' },
  ruleText: { fontSiz, e: 14,
    flex: 1,
  marginRight: 12 }
  removeButton: { paddin, g: 4 },
  addRuleContainer: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginTop: 12,
    gap: 8 },
  addRuleInput: { fle, x: 1,
    paddingHorizontal: 12,
  paddingVertical: 8,
    borderRadius: 6,
  borderWidth: 1,
    fontSize: 14 },
  saveRuleButton: {, width: 32,
  height: 32,
    borderRadius: 16,
  justifyContent: 'center',
    alignItems: 'center' }
  cancelRuleButton: {, width: 32,
  height: 32,
    borderRadius: 16,
  justifyContent: 'center',
    alignItems: 'center' }
  memberItem: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    paddingVertical: 12,
  borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)' },
  memberInfo: { fle, x: 1 }
  memberName: { fontSiz, e: 16,
    fontWeight: '600',
  marginBottom: 2 }
  memberRole: { fontSiz, e: 12 },
  memberActions: { flexDirectio, n: 'row',
    alignItems: 'center',
  gap: 8 }
  trustBadge: { paddingHorizonta, l: 8,
    paddingVertical: 2,
  borderRadius: 8 }
  trustScore: {, fontSize: 10,
  fontWeight: '600'
  },
  toggleItem: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    paddingVertical: 12,
  borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)' },
  toggleInfo: { flexDirectio, n: 'row',
    alignItems: 'center',
  flex: 1,
    marginRight: 16 },
  toggleTextContainer: { marginLef, t: 12,
    flex: 1 },
  toggleLabel: { fontSiz, e: 14,
    fontWeight: '600',
  marginBottom: 2 }
  toggleDescription: { fontSiz, e: 12,
    lineHeight: 16 },
  selectButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  paddingHorizontal: 12,
    paddingVertical: 6,
  borderRadius: 6,
    borderWidth: 1 },
  selectText: { fontSiz, e: 12,
    fontWeight: '500',
  marginRight: 4 }
  saveContainer: { paddin, g: 20,
    borderTopWidth: 1,
  borderTopColor: 'rgba(0,0,0,0.1)' },
  saveButton: { flexDirectio, n: 'row',
    justifyContent: 'center',
  alignItems: 'center',
    paddingVertical: 16,
  borderRadius: 12,
    gap: 8 },
  saveButtonText: {, fontSize: 16,
  fontWeight: '600'
  }
  })