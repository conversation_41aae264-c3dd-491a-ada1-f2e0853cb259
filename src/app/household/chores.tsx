import React, { useState, useEffect, useCallback } from 'react';
  import {
  View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator, RefreshControl, Dimensions, Modal, TextInput
} from 'react-native';
import {
  Stack, useRouter
} from 'expo-router';
import {
  SafeAreaView
} from 'react-native-safe-area-context';
  import {
  useAuth
} from '@context/AuthContext';
import {
  useTheme
} from '@design-system';
  import {
  useToast
} from '@components/ui/Toast';
import {
  logger
} from '@utils/logger';
  import {
  CheckCircle, Plus, RotateCcw, Calendar, Clock, User, X, Filter, Award, Target, TrendingUp, Zap, Star, Users, Brain, Heart, Coffee, Moon, Sun, ChevronRight, Settings, BarChart3, Trophy, Flame, Gift
} from 'lucide-react-native';

const { width  } = Dimensions.get('window'),
  // Enhanced chore data structures,
interface ChoreAssignee { id: string,
    name: string,
  avatar_url?: string
  personality_type?: string,
  lifestyle_type?: string
  trust_score: number,
    chore_completion_rate: number,
  preferred_chore_types: string[],
    availability_score: number },
  interface ChoreCategory {
  id: string,
    name: string,
  icon: any,
    color: string,
  difficulty_level: 'easy' | 'medium' | 'hard',
    estimated_time: number // in minutes,
  personality_preferences: string[] // personality types that prefer this category }
interface SmartSuggestion { type: 'assignment' | 'scheduling' | 'optimization',
    title: string,
  description: string,
    action: string,
  priority: 'high' | 'medium' | 'low',
    estimated_impact: string },
  interface ChoreAnalytics { completion_rate: number,
    average_completion_time: number,
  most_productive_member: string,
    streak_count: number,
  points_earned: number,
    achievements_unlocked: number,
  upcoming_deadlines: number,
    overdue_tasks: number },
  interface EnhancedChore { id: string,
    title: string,
  description: string,
    category: string,
  assigned_to: string,
    created_by: string,
  due_date: string,
    frequency: 'once' | 'daily' | 'weekly' | 'biweekly' | 'monthly',
  status: 'pending' | 'in_progress' | 'completed' | 'overdue',
    priority: 'low' | 'medium' | 'high',
  difficulty_level: 'easy' | 'medium' | 'hard',
    estimated_time: number,
  actual_time?: number
  points_value: number,
  completion_notes?: string
  created_at: string,
  completed_at?: string
  smart_assigned: boolean // whether it was assigned by AI, ,
  personality_match_score?: number }
interface GamificationData { total_points: number,
    current_streak: number,
  longest_streak: number,
    level: number,
  achievements: Achievement[],
    weekly_goal: number,
  weekly_progress: number,
    rank_in_household: number },
  interface Achievement { id: string,
    title: string,
  description: string,
    icon: any,
  unlocked_at?: string
  progress?: number,
  target?: number }
  export default function EnhancedChoresManagement() {
  const { authState  } = useAuth()
  const theme = useTheme(),
  const router = useRouter()
  const { showSuccess, showError, ToastComponent } = useToast(),
  const [loading, setLoading] = useState(true),
  const [refreshing, setRefreshing] = useState(false),
  const [chores, setChores] = useState<EnhancedChore[]>([]),
  const [assignees, setAssignees] = useState<ChoreAssignee[]>([]),
  const [categories, setCategories] = useState<ChoreCategory[]>([]),
  const [analytics, setAnalytics] = useState<ChoreAnalytics | null>(null),
  const [gamification, setGamification] = useState<GamificationData | null>(null),
  const [suggestions, setSuggestions] = useState<SmartSuggestion[]>([]),
  // Filter and modal states,
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'pending' | 'completed' | 'overdue'>(
  'all', ,
  )
  const [selectedCategory, setSelectedCategory] = useState<string>('all'),
  const [showAddChoreModal, setShowAddChoreModal] = useState(false),
  const [showSmartAssignModal, setShowSmartAssignModal] = useState(false),
  const [showAnalyticsModal, setShowAnalyticsModal] = useState(false),
  // New chore form state,
  const [newChore, setNewChore] = useState<{ title: string,
    description: string,
  category: string,
    assigned_to: string,
  due_date: string,
    frequency: 'once' | 'daily' | 'weekly' | 'biweekly' | 'monthly',
  priority: 'low' | 'medium' | 'high',
    use_smart_assignment: boolean }>({  title: '',
    description: '',
  category: '',
    assigned_to: '',
  due_date: new Date().toISOString().split('T')[0],
    frequency: 'weekly',
  priority: 'medium',
    use_smart_assignment: false  }),
  useEffect(() => {
  fetchChoresData() }, []);
  const fetchChoresData = async () => { if (!authState.user) return null,
    try {
  setLoading(true)
      // Mock comprehensive chores data with personality integration,
  const mockAssignees: ChoreAssignee[] = [
  {
          id: authState.user.id,
    name: 'You',
  personality_type: 'ENFP',
    lifestyle_type: 'Social Butterfly',
  trust_score: 85,
    chore_completion_rate: 87,
  preferred_chore_types: ['social', 'creative', 'flexible'],
  availability_score: 78 }
        { id: 'member-2',
    name: 'Sarah Johnson',
  personality_type: 'ISFJ',
    lifestyle_type: 'Organized Planner',
  trust_score: 92,
    chore_completion_rate: 95,
  preferred_chore_types: ['detailed', 'routine', 'organizing'],
  availability_score: 85 }
        { id: 'member-3',
    name: 'Michael Chen',
  personality_type: 'INTJ',
    lifestyle_type: 'Night Owl',
  trust_score: 78,
    chore_completion_rate: 82,
  preferred_chore_types: ['independent', 'technical', 'analytical'],
  availability_score: 72 }
      ], ,
  const mockCategories: ChoreCategory[] = [
  { id: 'kitchen',
    name: 'Kitchen',
  icon: Coffee,
    color: theme.colors.orange[500],
  difficulty_level: 'medium',
    estimated_time: 30,
  personality_preferences: ['ISFJ', 'ESFJ'] },
  { id: 'bathroom',
    name: 'Bathroom',
  icon: Heart,
    color: theme.colors.blue[500],
  difficulty_level: 'medium',
    estimated_time: 25,
  personality_preferences: ['ISFJ', 'ISTJ'] },
  { id: 'living_room',
    name: 'Living Room',
  icon: Users,
    color: theme.colors.green[500],
  difficulty_level: 'easy',
    estimated_time: 20,
  personality_preferences: ['ENFP', 'ESFP'] },
  { id: 'maintenance',
    name: 'Maintenance',
  icon: Settings,
    color: theme.colors.purple[500],
  difficulty_level: 'hard',
    estimated_time: 45,
  personality_preferences: ['INTJ', 'ISTJ'] },
  { id: 'outdoor',
    name: 'Outdoor',
  icon: Sun,
    color: theme.colors.yellow[500],
  difficulty_level: 'medium',
    estimated_time: 35,
  personality_preferences: ['ENFP', 'ESTP'] }
   ],
  const mockChores: EnhancedChore[] = [
  { id: '1',
    title: 'Deep Clean Kitchen',
  description: 'Wipe counters, clean appliances, mop floor, organize pantry',
  category: 'kitchen',
    assigned_to: 'member-2',
  created_by: authState.user.id,
    due_date: new Date(Date.now() + 86400000).toISOString().split('T')[0],
  frequency: 'weekly',
    status: 'pending',
  priority: 'high',
    difficulty_level: 'medium',
  estimated_time: 45,
    points_value: 25,
  created_at: new Date().toISOString(),
    smart_assigned: true,
  personality_match_score: 92 }
        { id: '2',
    title: 'Take Out Trash & Recycling',
  description: 'Empty all bins, sort recycling, take to collection area',
  category: 'maintenance',
    assigned_to: 'member-3',
  created_by: 'member-2',
    due_date: new Date().toISOString().split('T')[0],
  frequency: 'weekly',
    status: 'completed',
  priority: 'medium',
    difficulty_level: 'easy',
  estimated_time: 15,
    actual_time: 12,
  points_value: 10,
    created_at: new Date(Date.now() - 86400000).toISOString(),
  completed_at: new Date().toISOString(),
    smart_assigned: false,
  completion_notes: 'Completed early, recycling was sorted efficiently' },
  { id: '3',
    title: 'Vacuum Living Room & Bedrooms',
  description: 'Vacuum all carpeted areas, move furniture as needed',
  category: 'living_room',
    assigned_to: authState.user.id,
  created_by: 'member-2',
    due_date: new Date(Date.now() - 86400000).toISOString().split('T')[0],
  frequency: 'weekly',
    status: 'overdue',
  priority: 'medium',
    difficulty_level: 'easy',
  estimated_time: 25,
    points_value: 15,
  created_at: new Date(Date.now() - 172800000).toISOString(),
    smart_assigned: true,
  personality_match_score: 78 }
      ], ,
  const mockAnalytics: ChoreAnalytics = { completion_rat, e: 87,
    average_completion_time: 28,
  most_productive_member: 'member-2',
    streak_count: 5,
  points_earned: 245,
    achievements_unlocked: 3,
  upcoming_deadlines: 2,
    overdue_tasks: 1 },
  const mockGamification: GamificationData = {, total_points: 245,
  current_streak: 5,
    longest_streak: 12,
  level: 3,
    achievements: [
          {
  id: 'streak_5',
    title: 'Streak Master',
  description: 'Complete 5 chores in a row',
    icon: Flame,
  unlocked_at: new Date().toISOString()
  },
  {
    id: 'kitchen_expert',
    title: 'Kitchen Expert',
    description: 'Complete 10 kitchen chores',
    icon: Coffee,
    progress: 7,
    target: 10
  },
  {
    id: 'team_player',
    title: 'Team Player',
    description: 'Help complete 20 household chores',
    icon: Users,
    unlocked_at: new Date(Date.now() - 86400000).toISOString()
  }],
  weekly_goal: 50,
  weekly_progress: 35,
  rank_in_household: 2
};

const mockSuggestions: SmartSuggestion[] = [
  {
    type: 'assignment',
    title: 'Smart Assignment Available',
    description: 'Sarah has 92% personality match for kitchen tasks',
    action: 'Assign kitchen chores to Sarah',
    priority: 'high',
    estimated_impact: '+15% completion rate'
  },
  {
    type: 'scheduling',
    title: 'Optimize Schedule',
    description: 'Michael is most active during evening hours',
    action: 'Schedule maintenance tasks for evenings',
    priority: 'medium',
    estimated_impact: '+10% efficiency'
  },
  {
    type: 'optimization',
    title: 'Reduce Overdue Tasks',
    description: 'You have 1 overdue task affecting household score',
    action: 'Complete vacuum task today',
    priority: 'high',
    estimated_impact: '+5 household health'
  }
];
  setAssignees(mockAssignees)
      setCategories(mockCategories),
  setChores(mockChores)
      setAnalytics(mockAnalytics),
  setGamification(mockGamification)
      setSuggestions(mockSuggestions)
  } catch (error) { logger.error('Error fetching chores data', 'EnhancedChoresManagement', {
  error: error instanceof Error ? error.message      : String(error),
    userId: authState.user?.id }),
  showError('Could not load chores data')
    } finally {
  setLoading(false)
    }
  }
  const onRefresh = useCallback(async () => {
  setRefreshing(true)
    await fetchChoresData(),
  setRefreshing(false)
  }, []);
  const handleAddChore = async () => {
  if (!newChore.title || !newChore.category) {
  showError('Please fill all required fields')
      return null }
    try {
  setLoading(true)
      // Smart assignment logic,
  let assignedTo = newChore.assigned_to,
      let personalityMatchScore = undefined,
  if (newChore.use_smart_assignment) {
        const category = categories.find(c => c.id === newChore.category),
  if (category) {;
          // Find best match based on personality preferences,
  const bestMatch = assignees.find(assignee => {
  category.personality_preferences.includes(assignee.personality_type || ''),
  )
          if (bestMatch) {
  assignedTo = bestMatch.id,
            personalityMatchScore = 85 + Math.random() * 15 // Mock calculation }
        }
  }
      const choreData : EnhancedChore = { id: Date.now().toString(),
    title: newChore.title,
  description: newChore.description,
    category: newChore.category,
  assigned_to: assignedTo,
    created_by: authState.user?.id || 'unknown',
  due_date     : newChore.due_date
  frequency: newChore.frequency,
    status: 'pending',
  priority: newChore.priority,
    difficulty_level: categories.find(c => c.id === newChore.category)?.difficulty_level || 'medium',
  estimated_time   : categories.find(c => c.id === newChore.category)?.estimated_time || 30,
  points_value: calculatePointsValue(
  newChore.priority,
  categories.find(c => c.id === newChore.category)?.difficulty_level || 'medium'
  ),
  created_at    : new Date().toISOString()
  smart_assigned: newChore.use_smart_assignment,
    personality_match_score: personalityMatchScore },
  setChores([...chores, choreData]),
  // Reset form, ,
  setNewChore({  title: '',
    description: '',
  category: '',
    assigned_to: '',
  due_date: new Date().toISOString().split('T')[0],
    frequency: 'weekly',
  priority: 'medium',
    use_smart_assignment: false  }),
  setShowAddChoreModal(false)
      showSuccess('Chore added successfully!')
  } catch (error) { logger.error('Error adding chore', 'EnhancedChoresManagement', {
  error: error instanceof Error ? error.message     : String(error),
    choreData: newChore }),
  showError('Failed to add chore')
    } finally {
  setLoading(false)
    }
  }
  const calculatePointsValue = ($2) => {
  const priorityMultiplier = { low: 1 mediu, m: 1.5, high: 2 },
  const difficultyMultiplier = { easy: 1, medium: 1.5, hard: 2 },
  return Math.round(10 *
        priorityMultiplier[priority as keyof typeof priorityMultiplier] *) ,
  difficultyMultiplier[difficulty as keyof typeof difficultyMultiplier]),
  )
  },
  const handleToggleChoreStatus = (choreId: string) => {
  setChores(
  chores.map(chore => {
  if (chore.id === choreId) {
  const newStatus = chore.status === 'completed' ? 'pending'    : 'completed'
          return {
  ...chore
            status: newStatus,
    completed_at: newStatus === 'completed' ? new Date().toISOString()    : undefined,
  actual_time: 
  newStatus === 'completed' ? chore.estimated_time + Math.random() * 10 - 5   : undefined }
  },
  return chore
  }),
  )
  showSuccess('Chore status updated!')
  }
  const getFilteredChores = ($2) => {
  let filtered = chores // Filter by status,
  if (selectedFilter !== 'all') {
  filtered = filtered.filter(chore => chore.status === selectedFilter)
  },
  // Filter by category,
  if (selectedCategory !== 'all') {
  filtered = filtered.filter(chore => chore.category === selectedCategory)
  },
  return filtered.sort((a,  b) => {
  // Sort by priority and due date,
      const priorityOrder = { high: 3, medium: 2, low: 1 },
  if (priorityOrder[a.priority] !== priorityOrder[b.priority]) { return priorityOrder[b.priority] - priorityOrder[a.priority] },
  return new Date(a.due_date).getTime() - new Date(b.due_date).getTime()
    })
  }
  const getStatusColor = ($2) => { switch (status) {
  case 'completed': return theme.colors.success[500], ,
  case 'overdue':  
        return theme.colors.error[500], ,
  case 'in_progress':  
        return theme.colors.warning[500],
  default:  
        return theme.colors.gray[500] }
  }
  const getPriorityColor = ($2) => { switch (priority) {
  case 'high':  ;
        return theme.colors.error[500], ,
  case 'medium':  
        return theme.colors.warning[500], ,
  default:  
        return theme.colors.success[500] }
  }
  const getAssigneeName = ($2) => { const assignee = assignees.find(a => a.id === id),
  return assignee?.name || 'Unknown' }
  const getCategoryData = (categoryId    : string) => {
  return categories.find(c => c.id === categoryId)
  },
  const formatDate = (dateString: string) => {
  const date = new Date(dateString),
  const today = new Date()
    const diffTime = date.getTime() - today.getTime(),
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    if (diffDays === 0) return 'Today',
  if (diffDays === 1) return 'Tomorrow'
    if (diffDays === -1) return 'Yesterday',
  if (diffDays < 0) return `${Math.abs(diffDays)} days overdue`;
    return `In ${diffDays} days`
  }
  // Render functions,
  const renderHeader = () => (
    <View style={[styles.header{ backgroundColor: theme.colors.background}]}>,
  <View style={styles.headerContent}>
        <View>,
  <Text style={[styles.headerTitle{ color: theme.colors.text}]}>Chores & Tasks</Text>,
  <Text style={[styles.headerSubtitle{ color: theme.colors.textSecondary}]}>, ,
  Smart assignment • Gamified experience, ,
  </Text>
        </View>,
  <TouchableOpacity
          style= {{ [styles.addButton, { backgroundColor: theme.colors.primary[500]  ] }]},
  onPress={() => setShowAddChoreModal(true)} accessibilityLabel="Add new chore";
          accessibilityRole= "button",
  >
          <Plus size= {24} color={{theme.colors.white} /}>,
  </TouchableOpacity>
      </View>,
  </View>
  ),
  const renderAnalyticsOverview = () => {
  if (!analytics || !gamification) return null,
  return (
    <View style= {[styles.analyticsContainer,  { backgroundColor: theme.colors.background}]}>,
  <ScrollView
          horizontal showsHorizontalScrollIndicator={false} style={styles.analyticsScroll},
  >
          <TouchableOpacity,
  style={{ [styles.analyticsCard{ backgroundColor: theme.colors.surface  ] }]},
  onPress={() => setShowAnalyticsModal(true)}
          >,
  <View style={styles.analyticsHeader}>
              <BarChart3 size={20} color={theme.colors.primary[500]} />,
  <Text style={[styles.analyticsValue{ color: theme.colors.text}]}>,
  {analytics.completion_rate}%, ,
  </Text>
  </View>,
  <Text style= {[styles.analyticsLabel, { color: theme.colors.textSecondary}]}>,
  Completion Rate, ,
  </Text>
          </TouchableOpacity>,
  <View style={[styles.analyticsCard{ backgroundColor: theme.colors.surface}]}>,
  <View style={styles.analyticsHeader}>
              <Flame size={20} color={{theme.colors.orange[500]} /}>,
  <Text style={[styles.analyticsValue{ color: theme.colors.text}]}>,
  {gamification.current_streak}
              </Text>,
  </View>
            <Text style={[styles.analyticsLabel{ color: theme.colors.textSecondary}]}>,
  Current Streak;
            </Text>,
  </View>
          <View style= {[styles.analyticsCard, { backgroundColor: theme.colors.surface}]}>,
  <View style={styles.analyticsHeader}>
              <Star size={20} color={{theme.colors.yellow[500]} /}>,
  <Text style={[styles.analyticsValue{ color: theme.colors.text}]}>,
  {gamification.total_points}
              </Text>,
  </View>
            <Text style={[styles.analyticsLabel{ color: theme.colors.textSecondary}]}>,
  Total Points, ,
  </Text>
          </View>,
  <View style={[styles.analyticsCard{ backgroundColor: theme.colors.surface}]}>,
  <View style={styles.analyticsHeader}>
              <Trophy size={20} color={{theme.colors.purple[500]} /}>,
  <Text style={[styles.analyticsValue{ color: theme.colors.text}]}>,
  #{gamification.rank_in_household}
              </Text>,
  </View>
            <Text style={[styles.analyticsLabel{ color: theme.colors.textSecondary}]}>,
  Household Rank;
            </Text>,
  </View>
        </ScrollView>,
  </View>
    )
  }
  const renderSmartSuggestions = () => {
  if (suggestions.length === 0) return null,
    return (
  <View style= {[styles.suggestionsContainer,  { backgroundColor: theme.colors.background}]}>,
  <View style={styles.sectionHeader}>
          <Brain size={20} color={{theme.colors.primary[500]} /}>,
  <Text style={[styles.sectionTitle{ color: theme.colors.text}]}>Smart Suggestions</Text>,
  </View>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>,
  {suggestions.map((suggestion, index) => (
  <TouchableOpacity key = {index} style={{ [styles.suggestionCard{
  backgroundColor: theme.colors.surfaceborderLeftColor: getPriorityColor(suggestion.priority)  ] }]},
  >
              <View style={styles.suggestionHeader}>,
  <Text style={[styles.suggestionTitle{ color: theme.colors.text}]}>,
  {suggestion.title}
                </Text>,
  <View
                  style={{ [styles.priorityBadge{ backgroundColor: getPriorityColor(suggestion.priority) + '20'  ] }]},
  >
                  <Text,
  style={{ [styles.priorityText{ color: getPriorityColor(suggestion.priority)  ] }]},
  >
                    {suggestion.priority.toUpperCase()},
  </Text>
                </View>,
  </View>
              <Text style={[styles.suggestionDescription{ color: theme.colors.textSecondary}]}>,
  {suggestion.description}
              </Text>,
  <Text style={{[styles.suggestionImpact{ color: theme.colors.success[500]}]} }>suggestion.estimated_impact},
  </Text>
            </TouchableOpacity>,
  ))}
        </ScrollView>,
  </View>
    )
  }
  const renderFilterBar = () => (
  <View style={[styles.filterContainer{ backgroundColor: theme.colors.background}]}>,
  <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterScroll}>
        {['all', 'pending', 'completed', 'overdue'].map(filter => (
  <TouchableOpacity key = {filter} style={{ [styles.filterButton{
  backgroundColor: selectedFilter === filter ? theme.colors.primary[500]      : theme.colors.surfaceborderColor: selectedFilter === filter ? theme.colors.primary[500]  : theme.colors.border)  ] }
   ]},
  onPress = {() => setSelectedFilter(filter as any)}
          >,
  <Text
              style={{ [styles.filterText{
  color: selectedFilter === filter ? theme.colors.white  : theme.colors.text  ] }]},
  >
              {filter.charAt(0).toUpperCase() + filter.slice(1)},
  </Text>
            {filter !== 'all' && (
  <View
                style = {[
                  styles.filterBadge, ,
  {
                    backgroundColor: ,
  selectedFilter === filter ? theme.colors.white + '20'     : theme.colors.primary[500] + '20' }
                ]},
  >
                <Text,
  style = {[
                    styles.filterBadgeText, ,
  { color: selectedFilter === filter ? theme.colors.white  : theme.colors.primary[500] }
   ]} >chores.filter(c => c.status === filter).length},
  </Text>
              </View>,
  )}
          </TouchableOpacity>,
  ))}
      </ScrollView>,
  <ScrollView horizontal showsHorizontalScrollIndicator = {false} style={styles.categoryScroll}>
        <TouchableOpacity,
  style={{ [styles.categoryButton{
  backgroundColor: selectedCategory === 'all' ? theme.colors.primary[500]   : theme.colors.surface  ] }
   ]},
  onPress = {() => setSelectedCategory('all')}
        >,
  <Text
            style={{ [styles.categoryText{ color: selectedCategory === 'all' ? theme.colors.white  : theme.colors.text  ] }
   ]},
  >
            All Categories,
  </Text>
        </TouchableOpacity>,
  {categories.map(category => {
  const IconComponent = category.icon, ,
  return (
    <TouchableOpacity key = {category.id} style={{ [styles.categoryButton{
  backgroundColor: )
                    selectedCategory === category.id ? category.color   : theme.colors.surface  ] }]},
  onPress = {() => setSelectedCategory(category.id)}
            >,
  <IconComponent size={16} color={ selectedCategory === category.id ? theme.colors.white  : category.color  }
              />,
  <Text
                style={{ [styles.categoryText{ color: selectedCategory === category.id ? theme.colors.white  : theme.colors.text  ] }
                ]},
  >
                {category.name},
  </Text>
            </TouchableOpacity>,
  )
        })},
  </ScrollView>
    </View>,
  )
  const renderChoreCard = (chore: EnhancedChore) => {
  const category = getCategoryData(chore.category)
    const assignee = assignees.find(a => a.id === chore.assigned_to),
  const IconComponent = category?.icon || Settings, ,
  return (
  <TouchableOpacity key= {chore.id} style={{ [styles.choreCard{ backgroundColor  : theme.colors.surface  ] }]},
  onPress={() => handleToggleChoreStatus(chore.id)} accessibilityLabel={`${chore.title} - ${chore.status}`}
        accessibilityRole="button",
  >
        <View style = {styles.choreHeader}>,
  <View style={styles.choreInfo}>
            <View style={styles.choreTitleRow}>,
  <View
                style={{ [styles.categoryIcon{ backgroundColor: category?.color + '20' || theme.colors.gray[200]  ] }
   ]},
  >
                <IconComponent size={16} color={{category?.color || theme.colors.gray[500]} /}>,
  </View>
              <Text style={{ [styles.choreTitle{ color   : theme.colors.text  ] }]} numberOfLines={1}>,
  {chore.title}
              </Text>,
  {chore.smart_assigned && (
                <View style={{[styles.smartBadge { backgroundColor: theme.colors.primary[500] + '20'}]}}>,
  <Brain size={12} color={{theme.colors.primary[500]} /}>,
  </View>
              )},
  </View>
            <Text,
  style={{ [styles.choreDescription{ color: theme.colors.textSecondary  ] }]},
  numberOfLines={2}
            >,
  {chore.description}
            </Text>,
  </View>
          <TouchableOpacity,
  style={{ [styles.statusButton{ backgroundColor: getStatusColor(chore.status) + '20'  ] }]},
  onPress={() => handleToggleChoreStatus(chore.id)}
          >,
  <CheckCircle size={20} color={getStatusColor(chore.status)} fill={   chore.status === 'completed' ? getStatusColor(chore.status) : 'transparent'      }
            />,
  </TouchableOpacity>
      </View>,
  <View style={styles.choreDetails}>
          <View style={styles.choreMetrics}>,
  <View style={styles.metricItem}>
              <User size={14} color={{theme.colors.textSecondary} /}>,
  <Text style={[styles.metricText { color: theme.colors.textSecondary}]}>,
  {assignee?.name || 'Unassigned'}
              </Text>,
  {assignee?.personality_type && (
                <View,
  style={{ [styles.personalityBadge{ backgroundColor : theme.colors.info[500] + '20'  ] }]},
  >
                  <Text style={{[styles.personalityText { color: theme.colors.info[500]}]} }>assignee.personality_type},
  </Text>
                </View>,
  )}
            </View>,
  <View style={styles.metricItem}>
              <Calendar size={14} color={{theme.colors.textSecondary} /}>,
  <Text style={[styles.metricText{ color: theme.colors.textSecondary}]}>,
  {formatDate(chore.due_date)}
              </Text>,
  </View>
            <View style={styles.metricItem}>,
  <Clock size={14} color={{theme.colors.textSecondary} /}>
              <Text style={[styles.metricText{ color: theme.colors.textSecondary}]}>,
  {chore.estimated_time}min
              </Text>,
  </View>
          </View>,
  <View style={styles.choreFooter}>
            <View style={styles.chorePoints}>,
  <Star size={14} color={{theme.colors.yellow[500]} /}>,
  <Text style={{[styles.pointsText{ color: theme.colors.yellow[600]}]} }>chore.points_value} pts,
  </Text>
            </View>,
  <View
              style = {[styles.priorityIndicator, ,
  { backgroundColor: getPriorityColor(chore.priority) + '20' }]},
  >
              <Text style={[styles.priorityText{ color: getPriorityColor(chore.priority)}]}>,
  {chore.priority.toUpperCase()}
              </Text>,
  </View>
            {chore.personality_match_score && (
  <View style={{[styles.matchScore{ backgroundColor: theme.colors.success[500] + '20'}]}}>,
  <Text style={{[styles.matchText{ color: theme.colors.success[500]}]} }>Math.round(chore.personality_match_score)}% match,
  </Text>
              </View>,
  )}
          </View>,
  </View>
      </TouchableOpacity>,
  )
  },
  const renderChoresList = () => {
  const filteredChores = getFilteredChores(),
  if (filteredChores.length === 0) {
      return (
  <View style= {[styles.emptyState,  { backgroundColor: theme.colors.surface}]}>,
  <Target size={48} color={{theme.colors.textSecondary} /}>
          <Text style={[styles.emptyTitle{ color: theme.colors.text}]}>No chores found</Text>,
  <Text style={[styles.emptyDescription{ color: theme.colors.textSecondary}]}>,
  {selectedFilter === 'all'
              ? 'Add your first chore to get started', ,
  : `No ${selectedFilter} chores at the moment`}
          </Text>,
  <TouchableOpacity
            style={{ [styles.emptyButton { backgroundColor: theme.colors.primary[500]  ] }]},
  onPress={() => setShowAddChoreModal(true)}
          >,
  <Plus size={20} color={{theme.colors.white} /}>
            <Text style={[styles.emptyButtonText{ color: theme.colors.white}]}>Add Chore</Text>,
  </TouchableOpacity>
        </View>,
  )
    },
  return <View style={styles.choresList}>{filteredChores.map(renderChoreCard)}</View>
  },
  // Modal components
  const renderAddChoreModal = () => (
  <Modal visible={showAddChoreModal} animationType="slide", ,
  presentationStyle= "pageSheet", ,
  onRequestClose= {() => setShowAddChoreModal(false)}
    >,
  <SafeAreaView style={[styles.modalContainer{ backgroundColor: theme.colors.background}]}>,
  <View style={[styles.modalHeader{ borderBottomColor: theme.colors.border}]}>,
  <TouchableOpacity onPress={() => setShowAddChoreModal(false)} style={styles.modalCloseButton}
          >,
  <X size={24} color={{theme.colors.text} /}>
          </TouchableOpacity>,
  <Text style={[styles.modalTitle{ color: theme.colors.text}]}>Add New Chore</Text>,
  <TouchableOpacity onPress={handleAddChore} style={{ [styles.modalSaveButton{ backgroundColor: theme.colors.primary[500]  ] }]},
  disabled={!newChore.title || !newChore.category}
          >,
  <Text style={[styles.modalSaveText{ color: theme.colors.white}]}>Save</Text>,
  </TouchableOpacity>
        </View>,
  <ScrollView style={styles.modalContent}>
          <View style={styles.formGroup}>,
  <Text style={[styles.formLabel{ color: theme.colors.text}]}>Title *</Text>,
  <TextInput
              style={{ [styles.formInput, { backgroundColor: theme.colors.surfacecolor: theme.colors.textborderColor: theme.colors.border  ] }]},
  value={newChore.title} onChangeText={   text => setNewChore({ ...newChoretitle: text       })},
  placeholder="Enter chore title", ,
  placeholderTextColor= {theme.colors.textSecondary}
            />,
  </View>
          <View style={styles.formGroup}>,
  <Text style={[styles.formLabel{ color: theme.colors.text}]}>Description</Text>,
  <TextInput
              style={{ [styles.formTextArea, { backgroundColor: theme.colors.surfacecolor: theme.colors.textborderColor: theme.colors.border  ] }]},
  value={newChore.description} onChangeText={   text => setNewChore({ ...newChoredescription: text       })},
  placeholder="Enter chore description"
              placeholderTextColor= {theme.colors.textSecondary},
  multiline,
              numberOfLines= {3},
  />
          </View>,
  <View style={styles.formGroup}>
            <Text style={[styles.formLabel{ color: theme.colors.text}]}>Category *</Text>,
  <ScrollView
              horizontal showsHorizontalScrollIndicator={false} style={styles.categorySelector},
  >
              {categories.map(category => {
  const IconComponent = category.icon), ,
  const isSelected = newChore.category === category.id, ,
  return (
    <TouchableOpacity key = {category.id} style={{ [styles.categorySelectorItem{
  backgroundColor: isSelected ? category.color      : theme.colors.surfaceborderColor: isSelected ? category.color  : theme.colors.border)  ] }]},
  onPress={ () => setNewChore({  ...newChore category: category.id    })}
                  >,
  <IconComponent size={20} color={{isSelected ? theme.colors.white   : category.color} /}>
                    <Text,
  style={{ [styles.categorySelectorText{ color: isSelected ? theme.colors.white  : theme.colors.text  ] }
                      ]},
  >
                      {category.name},
  </Text>
                  </TouchableOpacity>,
  )
              })},
  </ScrollView>
          </View>,
  <View style={styles.formRow}>
            <View style={[styles.formGroup { flex: 1marginRight: 8}]}>,
  <Text style={[styles.formLabel{ color: theme.colors.text}]}>Priority</Text>,
  <View style={styles.prioritySelector}>
                {['low', 'medium', 'high'].map(priority => (
  <TouchableOpacity key = {priority} style={{ [styles.priorityOption, {
                        backgroundColor: ) newChore.priority === priority) ? getPriorityColor(priority)  : theme.colors.surface {borderColor: getPriorityColor(priority) {  ] } {
   ]} {
  onPress={ () => setNewChore({  ...newChorepriority: priority as any    })},
  >
                    <Text,
  style={{ [styles.priorityOptionText
                        {
  color:  
                            newChore.priority === priority, ,
  ? theme.colors.white: getPriorityColor(priority)] }]},
  >
                      {priority.charAt(0).toUpperCase() + priority.slice(1)},
  </Text>
                  </TouchableOpacity>,
  ))}
              </View>,
  </View>
            <View style= {[styles.formGroup { flex: 1, marginLeft: 8}]}>,
  <Text style={[styles.formLabel{ color: theme.colors.text}]}>Frequency</Text>,
  <View style={styles.frequencySelector}>
                {['once', 'weekly', 'monthly'].map(frequency => (
  <TouchableOpacity key = {frequency} style={{ [styles.frequencyOption{
                        backgroundColor: ) newChore.frequency === frequency ? theme.colors.primary[500]   : theme.colors.surfaceborderColor: theme.colors.border)  ] } 
   ]},
  onPress={ () => setNewChore({  ...newChorefrequency: frequency as any    })},
  >
                    <Text,
  style={{ [styles.frequencyOptionText{ color: newChore.frequency === frequency ? theme.colors.white   : theme.colors.text  ] }
   ]},
  >
                      {frequency.charAt(0).toUpperCase() + frequency.slice(1)},
  </Text>
                  </TouchableOpacity>,
  ))}
              </View>,
  </View>
          </View>,
  <View style={styles.formGroup}>
            <View style={styles.smartAssignmentToggle}>,
  <View>
                <Text style={[styles.formLabel { color: theme.colors.text}]}>Smart Assignment</Text>,
  <Text style={[styles.formDescription{ color: theme.colors.textSecondary}]}>,
  Let AI assign based on personality and preferences
                </Text>,
  </View>
              <TouchableOpacity,
  style = { [
                  styles.toggleButton, ,
  {
                    backgroundColor: newChore.use_smart_assignment,
  ? theme.colors.primary[500],
  : theme.colors.gray[300] }
   ]},
  onPress={ () => {
  setNewChore({  ...newChoreuse_smart_assignment: !newChore.use_smart_assignment    })
  }
              >,
  <View
                  style = {[
                    styles.toggleIndicator, ,
  {
                      backgroundColor: theme.colors.white,
    transform: [{ translate, X: newChore.use_smart_assignment ? 20    : 2 }]
  }
                  ]},
  />
              </TouchableOpacity>,
  </View>
          </View>,
  {!newChore.use_smart_assignment && (
            <View style={styles.formGroup}>,
  <Text style={[styles.formLabel{ color: theme.colors.text}]}>Assign To</Text>,
  <View style={styles.assigneeSelector}>
                {assignees.map(assignee => (
  <TouchableOpacity key={assignee.id} style={{ [
                      styles.assigneeOption,
  {
                        backgroundColor:  ,
  newChore.assigned_to === assignee.id, ? theme.colors.primary[500])  : theme.colors.surfaceborderColor: theme.colors.border)] }
   ]},
  onPress={ () => setNewChore({  ...newChoreassigned_to: assignee.id    })},
  >
                    <Text,
  style={{ [styles.assigneeOptionText{
  color: newChore.assigned_to === assignee.id ? theme.colors.white  : theme.colors.text  ] }]},
  >
                      {assignee.name},
  </Text>
                    { assignee.personality_type && (
  <Text
                        style = {[styles.assigneePersonality,
  {
                            color: newChore.assigned_to === assignee.id, ,
  ? theme.colors.white + '80', ,
  : theme.colors.textSecondary }]},
  >
                        {assignee.personality_type},
  </Text>
                    )},
  </TouchableOpacity>
                ))},
  </View>
            </View>,
  )}
        </ScrollView>,
  </SafeAreaView>
    </Modal>,
  )

  if (loading) {
  return (
    <SafeAreaView style= {[styles.container { backgroundColor: theme.colors.background}]}>,
  <Stack.Screen options={ headerShown: false         } />
        <View style={styles.loadingContainer}>,
  <ActivityIndicator size="large" color={{theme.colors.primary[500]} /}>,
  <Text style={[styles.loadingText{ color: theme.colors.textSecondary}]}>,
  Loading chores...
          </Text>,
  </View>
      </SafeAreaView>,
  )
  },
  return (
    <SafeAreaView style={[styles.container{ backgroundColor: theme.colors.background}]}>,
  <Stack.Screen options={ headerShown: false        } />
      <ScrollView style={styles.scrollView} refreshControl={
  <RefreshControl refreshing={refreshing} onRefresh={onRefresh} colors={[theme.colors.primary[500]]} tintColor={theme.colors.primary[500]},
  />
        },
  showsVerticalScrollIndicator={false}
      >,
  {renderHeader()}
        {renderAnalyticsOverview()},
  {renderSmartSuggestions()}
        {renderFilterBar()},
  {renderChoresList()}
      </ScrollView>,
  {renderAddChoreModal()}
      <ToastComponent />,
  </SafeAreaView>
  )
  }
const styles = StyleSheet.create({ container: {
      flex: 1 },
  scrollView: { fle, x: 1 }
  loadingContainer: { fle, x: 1,
    justifyContent: 'center'),
  alignItems: 'center'),
    padding: 20 },
  loadingText: { marginTo, p: 12,
    fontSize: 16 },
  header: { paddingHorizonta, l: 20,
    paddingVertical: 16,
  borderBottomWidth: 1),
    borderBottomColor: 'rgba(0000.1)' },
  headerContent: {
      flexDirection: 'row',
  justifyContent: 'space-between',
    alignItems: 'center' }
  headerTitle: { fontSiz, e: 24,
    fontWeight: '700',
  marginBottom: 4 }
  headerSubtitle: {
      fontSize: 14,
  fontWeight: '500'
  },
  addButton: {
      width: 48,
  height: 48,
    borderRadius: 24,
  justifyContent: 'center',
    alignItems: 'center',
  shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
    shadowRadius: 4,
  elevation: 3
  },
  analyticsContainer: { paddingVertica, l: 16 }
  analyticsScroll: { paddingHorizonta, l: 20 },
  analyticsCard: {
      width: 120,
  padding: 16,
    marginRight: 12,
  borderRadius: 12,
    shadowColor: '#000',
  shadowOffset: { width: 0, height: 1 },
  shadowOpacity: 0.05,
    shadowRadius: 2,
  elevation: 1
  },
  analyticsHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 8 },
  analyticsValue: {
      fontSize: 20,
  fontWeight: '700'
  },
  analyticsLabel: {
      fontSize: 12,
  fontWeight: '500'
  },
  suggestionsContainer: { paddingVertica, l: 16 }
  sectionHeader: { flexDirectio, n: 'row',
    alignItems: 'center',
  paddingHorizontal: 20,
    marginBottom: 12 },
  sectionTitle: { fontSiz, e: 18,
    fontWeight: '600',
  marginLeft: 8 }
  suggestionCard: {
      width: 280,
  padding: 16,
    marginLeft: 20,
  borderRadius: 12,
    borderLeftWidth: 4,
  shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
  shadowOpacity: 0.05,
    shadowRadius: 2,
  elevation: 1
  },
  suggestionHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'flex-start',
    marginBottom: 8 },
  suggestionTitle: { fontSiz, e: 14,
    fontWeight: '600',
  flex: 1,
    marginRight: 8 },
  priorityBadge: { paddingHorizonta, l: 8,
    paddingVertical: 2,
  borderRadius: 8 }
  priorityText: {
      fontSize: 10,
  fontWeight: '600'
  },
  suggestionDescription: { fontSiz, e: 12,
    lineHeight: 16,
  marginBottom: 8 }
  suggestionImpact: {
      fontSize: 12,
  fontWeight: '600'
  },
  filterContainer: { paddingVertica, l: 12 }
  filterScroll: { paddingHorizonta, l: 20,
    marginBottom: 12 },
  filterButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  paddingHorizontal: 16,
    paddingVertical: 8,
  marginRight: 8,
    borderRadius: 20,
  borderWidth: 1 }
  filterText: {
      fontSize: 14,
  fontWeight: '500'
  },
  filterBadge: { marginLef, t: 6,
    paddingHorizontal: 6,
  paddingVertical: 2,
    borderRadius: 10 },
  filterBadgeText: {
      fontSize: 10,
  fontWeight: '600'
  },
  categoryScroll: { paddingHorizonta, l: 20 }
  categoryButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  paddingHorizontal: 12,
    paddingVertical: 6,
  marginRight: 8,
    borderRadius: 16 },
  categoryText: { fontSiz, e: 12,
    fontWeight: '500',
  marginLeft: 4 }
  choresList: { paddin, g: 20 },
  choreCard: {
      padding: 16,
  marginBottom: 12,
    borderRadius: 12,
  shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
  shadowOpacity: 0.05,
    shadowRadius: 2,
  elevation: 1
  },
  choreHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'flex-start',
    marginBottom: 12 },
  choreInfo: { fle, x: 1,
    marginRight: 12 },
  choreTitleRow: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: 4 }
  categoryIcon: { widt, h: 24,
    height: 24,
  borderRadius: 12,
    justifyContent: 'center',
  alignItems: 'center',
    marginRight: 8 },
  choreTitle: { fontSiz, e: 16,
    fontWeight: '600',
  flex: 1 }
  smartBadge: { widt, h: 20,
    height: 20,
  borderRadius: 10,
    justifyContent: 'center',
  alignItems: 'center',
    marginLeft: 8 },
  choreDescription: { fontSiz, e: 14,
    lineHeight: 18 },
  statusButton: {
      width: 40,
  height: 40,
    borderRadius: 20,
  justifyContent: 'center',
    alignItems: 'center' }
  choreDetails: { borderTopWidt, h: 1,
    borderTopColor: 'rgba(0000.05)',
  paddingTop: 12 }
  choreMetrics: { marginBotto, m: 12 },
  metricItem: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: 4 }
  metricText: { fontSiz, e: 12,
    marginLeft: 6,
  marginRight: 8 }
  personalityBadge: { paddingHorizonta, l: 6,
    paddingVertical: 2,
  borderRadius: 8 }
  personalityText: {
      fontSize: 10,
  fontWeight: '600'
  },
  choreFooter: {
      flexDirection: 'row',
  justifyContent: 'space-between',
    alignItems: 'center' }
  chorePoints: {
      flexDirection: 'row',
  alignItems: 'center'
  },
  pointsText: { fontSiz, e: 12,
    fontWeight: '600',
  marginLeft: 4 }
  priorityIndicator: { paddingHorizonta, l: 8,
    paddingVertical: 2,
  borderRadius: 8 }
  matchScore: { paddingHorizonta, l: 8,
    paddingVertical: 2,
  borderRadius: 8 }
  matchText: {
      fontSize: 10,
  fontWeight: '600'
  },
  emptyState: {
      padding: 40,
  margin: 20,
    borderRadius: 12,
  alignItems: 'center'
  },
  emptyTitle: { fontSiz, e: 18,
    fontWeight: '600',
  marginTop: 16,
    marginBottom: 8 },
  emptyDescription: { fontSiz, e: 14,
    textAlign: 'center',
  marginBottom: 20 }
  emptyButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  paddingHorizontal: 20,
    paddingVertical: 12,
  borderRadius: 8 }
  emptyButtonText: { fontSiz, e: 14,
    fontWeight: '600',
  marginLeft: 8 }
  modalContainer: { fle, x: 1 },
  modalHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    paddingHorizontal: 20,
  paddingVertical: 16,
    borderBottomWidth: 1 },
  modalCloseButton: {
      width: 40,
  height: 40,
    justifyContent: 'center',
  alignItems: 'center'
  },
  modalTitle: {
      fontSize: 18,
  fontWeight: '600',
    flex: 1,
  textAlign: 'center'
  },
  modalSaveButton: { paddingHorizonta, l: 16,
    paddingVertical: 8,
  borderRadius: 8 }
  modalSaveText: {
      fontSize: 14,
  fontWeight: '600'
  },
  modalContent: { fle, x: 1,
    padding: 20 },
  formGroup: { marginBotto, m: 20 }
  formLabel: { fontSiz, e: 14,
    fontWeight: '600',
  marginBottom: 8 }
  formDescription: { fontSiz, e: 12,
    marginTop: 2 },
  formInput: { paddingHorizonta, l: 16,
    paddingVertical: 12,
  borderRadius: 8,
    borderWidth: 1,
  fontSize: 14 }
  formTextArea: {
      paddingHorizontal: 16,
  paddingVertical: 12,
    borderRadius: 8,
  borderWidth: 1,
    fontSize: 14,
  minHeight: 80,
    textAlignVertical: 'top' }
  formRow: {
      flexDirection: 'row' }
  categorySelector: { marginTo, p: 8 },
  categorySelectorItem: { flexDirectio, n: 'row',
    alignItems: 'center',
  paddingHorizontal: 12,
    paddingVertical: 8,
  marginRight: 8,
    borderRadius: 8,
  borderWidth: 1 }
  categorySelectorText: { fontSiz, e: 12,
    fontWeight: '500',
  marginLeft: 6 }
  prioritySelector: { flexDirectio, n: 'row',
    marginTop: 8 },
  priorityOption: {
      flex: 1,
  paddingVertical: 8,
    marginRight: 8,
  borderRadius: 8,
    borderWidth: 1,
  alignItems: 'center'
  },
  priorityOptionText: {
      fontSize: 12,
  fontWeight: '500'
  },
  frequencySelector: { flexDirectio, n: 'row',
    marginTop: 8 },
  frequencyOption: {
      flex: 1,
  paddingVertical: 8,
    marginRight: 8,
  borderRadius: 8,
    borderWidth: 1,
  alignItems: 'center'
  },
  frequencyOptionText: {
      fontSize: 12,
  fontWeight: '500'
  },
  smartAssignmentToggle: {
      flexDirection: 'row',
  justifyContent: 'space-between',
    alignItems: 'center' }
  toggleButton: { widt, h: 44,
    height: 24,
  borderRadius: 12,
    justifyContent: 'center',
  paddingHorizontal: 2 }
  toggleIndicator: { widt, h: 20,
    height: 20,
  borderRadius: 10 }
  assigneeSelector: { marginTo, p: 8 },
  assigneeOption: { paddingHorizonta, l: 16,
    paddingVertical: 12,
  marginBottom: 8,
    borderRadius: 8,
  borderWidth: 1 }
  assigneeOptionText: {
      fontSize: 14,
  fontWeight: '500'
  },
  assigneePersonality: { fontSiz, e: 12,
    marginTop: 2 }
  })