import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  RefreshControl,
  Alert,
  ActivityIndicator,
  TouchableOpacity,
  Modal
} from 'react-native';
import {
  useRouter
} from 'expo-router';
import {
  Ionicons
} from '@expo/vector-icons';
import {
  useTheme
} from '@design-system';
import {
  StyleSheet
} from 'react-native';
import {
  SafeAreaView
} from 'react-native-safe-area-context';
import {
  Clock, CheckCircle, XCircle, User, FileText, Phone, Mail
} from 'lucide-react-native';
import {
  useAuth
} from '@context/AuthContext';
import {
  logger
} from '@services/loggerService';
// Components
import {
  Button
} from '@design-system';
import {
  Card
} from '@components/ui';
import Input from '@components/ui/form/Input';
// Services
import {
  manualVerificationService,
  type VerificationSubmission as ServiceVerificationSubmission,
  VerificationMetrics,
  type VerificationReview
} from '@services/verification/ManualVerificationService';
interface VerificationQueueProps {}
// Local interface that extends the service interface with additional UI properties
interface UIVerificationSubmission extends ServiceVerificationSubmission {
  userEmail: string;, type: 'identity' | 'background' | 'reference',
  createdAt: string;
}
export default function VerificationQueue({}: VerificationQueueProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const router = useRouter();
  const { user } = useAuth();
  // State
  const [submissions, setSubmissions] = useState<UIVerificationSubmission[]>([]);
  const [metrics, setMetrics] = useState<VerificationMetrics | null>(null);
  const [selectedSubmission, setSelectedSubmission] = useState<UIVerificationSubmission | null>(
    null
  );
  const [reviewNotes, setReviewNotes] = useState('');
  const [rejectionReason, setRejectionReason] = useState('');
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [adminNotes, setAdminNotes] = useState('');
  // Load data on mount
  useEffect(() => {
    loadVerificationData();
  }, []);

  const loadVerificationData = async () => {
    try {
      const [submissionsResult, metricsResult] = await Promise.all([
        manualVerificationService.getPendingVerifications(),
        manualVerificationService.getVerificationMetrics()
      ]);
      if (submissionsResult.success && submissionsResult.submissions) {
        setSubmissions(submissionsResult.submissions);
      }
      if (metricsResult.success && metricsResult.metrics) {
        setMetrics(metricsResult.metrics);
      }
    } catch (error) {
      logger.error('Failed to load verification data', 'VerificationQueue', {
        error: (error as Error).message
      });
      Alert.alert('Error', 'Failed to load verification data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };
  // Helper function to get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return theme.colors.warning;
      case 'approved': return theme.colors.success;
      case 'rejected': return theme.colors.error;
      case 'resubmission_requested': return theme.colors.info;, default: return theme.colors.textSecondary;
    }
  };
  const handleRefresh = () => {
    setRefreshing(true);
    loadVerificationData();
  };
  const handleSelectSubmission = (submission: UIVerificationSubmission) => {
    setSelectedSubmission(submission);
    setReviewNotes('');
    setRejectionReason('');
    setAdminNotes('');
  };
  const handleApprove = async (submissionId: string) => {
    if (!selectedSubmission || !user) return;
    setProcessing(true);

    try {
      const review: VerificationReview = {
        submissionId,
        decision: 'approve',
        notes: reviewNotes,
        reviewerId: user.id
      };

      const result = await manualVerificationService.reviewSubmission(review);

      if (result.success) {
        Alert.alert('Success', 'Verification approved successfully');
        setSelectedSubmission(null);
        loadVerificationData(); // Refresh the list
      } else {
        Alert.alert('Error', result.error || 'Failed to approve verification');
      }
    } catch (error) {
      logger.error('Failed to approve verification', 'VerificationQueue', {
        error: (error as Error).message
      });
      Alert.alert('Error', 'Failed to approve verification');
    } finally {
      setProcessing(false);
    }
  };
  const handleReject = async (submissionId: string) => {
    if (!selectedSubmission || !user || !rejectionReason.trim()) {
      Alert.alert('Error', 'Please provide a rejection reason');
      return;
    }

    setProcessing(true);

    try {
      const review: VerificationReview = {
        submissionId,
        decision: 'reject',
        notes: reviewNotes,
        rejectionReason: rejectionReason,
        reviewerId: user.id
      };

      const result = await manualVerificationService.reviewSubmission(review);

      if (result.success) {
        Alert.alert('Success', 'Verification rejected');
        setSelectedSubmission(null);
        loadVerificationData(); // Refresh the list
      } else {
        Alert.alert('Error', result.error || 'Failed to reject verification');
      }
    } catch (error) {
      logger.error('Failed to reject verification', 'VerificationQueue', {
        error: (error as Error).message
      });
      Alert.alert('Error', 'Failed to reject verification');
    } finally {
      setProcessing(false);
    }
  };
  const handleRequestResubmission = async () => {
    if (!selectedSubmission || !user || !rejectionReason.trim()) {
      Alert.alert('Error', 'Please provide a reason for resubmission');
      return;
    }

    setProcessing(true);

    try {
      const review: VerificationReview = {, submissionId: selectedSubmission.id,
        decision: 'request_resubmission',
        notes: reviewNotes,
        rejectionReason: rejectionReason,
        reviewerId: user.id
      };

      const result = await manualVerificationService.reviewSubmission(review);

      if (result.success) {
        Alert.alert('Success', 'Resubmission requested');
        setSelectedSubmission(null);
        loadVerificationData(); // Refresh the list
      } else {
        Alert.alert('Error', result.error || 'Failed to request resubmission');
      }
    } catch (error) {
      logger.error('Failed to request resubmission', 'VerificationQueue', {
        error: (error as Error).message
      });
      Alert.alert('Error', 'Failed to request resubmission');
    } finally {
      setProcessing(false);
    }
  };
  const handleDelete = async (submissionId: string) => {
    if (!selectedSubmission || !user) return;

    setProcessing(true);

    try {
      const result = await manualVerificationService.deleteSubmission(submissionId);

      if (result.success) {
        Alert.alert('Success', 'Verification deleted successfully');
        setSelectedSubmission(null);
        loadVerificationData(); // Refresh the list
      } else {
        Alert.alert('Error', result.error || 'Failed to delete verification');
      }
    } catch (error) {
      logger.error('Failed to delete verification', 'VerificationQueue', {
        error: (error as Error).message
      });
      Alert.alert('Error', 'Failed to delete verification');
    } finally {
      setProcessing(false);
    }
  };
  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return 'Less than 1 hour ago';
    if (diffInHours === 1) return '1 hour ago';
    if (diffInHours < 24) return `${diffInHours} hours ago`;

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays === 1) return '1 day ago';
    return `${diffInDays} days ago`;
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size='large' color={theme.colors.primary} />
        <Text style={styles.loadingText}>Loading verification queue...</Text>
      </View>
    );
  }
  return (
    <ScrollView
      style={styles.container}
      refreshControl={<RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />}
    >
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>ID Verification Queue</Text>
        <Text style={styles.subtitle}>Manual document review dashboard</Text>
      </View>

      {/* Metrics Cards */}
      {metrics && (
        <View style={styles.metricsContainer}>
          <Card style={styles.metricCard}>
            <Text style={styles.metricNumber}>{metrics.pendingReviews}</Text>
            <Text style={styles.metricLabel}>Pending Reviews</Text>
          </Card>
          <Card style={styles.metricCard}>
            <Text style={styles.metricNumber}>{metrics.approvedToday}</Text>
            <Text style={styles.metricLabel}>Approved Today</Text>
          </Card>
          <Card style={styles.metricCard}>
            <Text style={styles.metricNumber}>{metrics.averageReviewTime.toFixed(1)}h</Text>
            <Text style={styles.metricLabel}>Avg Review Time</Text>
          </Card>
          <Card style={styles.metricCard}>
            <Text style={styles.metricNumber}>{metrics.approvalRate.toFixed(1)}%</Text>
            <Text style={styles.metricLabel}>Approval Rate</Text>
          </Card>
        </View>
      )}
      {/* Submissions List */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Pending Submissions ({submissions.length})</Text>
        {submissions.length === 0 ? (
          <Card style={styles.emptyCard}>
            <Ionicons name='checkmark-circle' size={48} color={theme.colors.success} />
            <Text style={styles.emptyTitle}>All caught up!</Text>
            <Text style={styles.emptySubtitle}>No pending verifications to review</Text>
          </Card>
        ) : (
          submissions.map(submission => (
            <TouchableOpacity
              key={submission.id}
              onPress={() => handleSelectSubmission(submission)}
            >
              <Card
                style={[
                  styles.submissionCard,
                  selectedSubmission?.id === submission.id && {
                    borderColor: theme.colors.primaryborderWidth: 2
                  }
                ]}
              >
                <View style={styles.submissionHeader}>
                  <Text style={styles.submissionTitle}>
                    {submission.userEmail || submission.userId || 'Unknown User'}
                  </Text>
                  <View
                    style={[
                      styles.statusBadge{ backgroundColor: getStatusColor(submission.status) }
                    ]}
                  >
                    <Text style={styles.statusText}>{submission.status}</Text>
                  </View>
                </View>
                <View style={styles.submissionInfo}>
                  <Text style={styles.infoText}>Type: {submission.type || 'ID Verification'}</Text>
                  <Text style={styles.infoText}>
                    Submitted:{' '}
                    {new Date(submission.submittedAt || submission.createdAt).toLocaleDateString()}
                  </Text>
                </View>
              </Card>
            </TouchableOpacity>
          ))
        )}
      </View>
      {/* Review Panel */}
      {selectedSubmission && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Review Submission</Text>
          <Card style={styles.reviewCard}>
            <Text style={styles.reviewTitle}>#{selectedSubmission.id}</Text>
            <Text style={styles.reviewSubtitle}>
              Submitted {formatTimeAgo(selectedSubmission.submittedAt)}
            </Text>

            {/* Document Preview Placeholders */}
            <View style={styles.documentsPreview}>
              <View style={styles.documentPreview}>
                <Ionicons name='card' size={32} color={theme.colors.textSecondary} />
                <Text style={styles.documentLabel}>ID Document</Text>
                <Button
                  title='View Full Size'
                  variant='outlined'
                  size='small'
                  onPress={() => {
                    // TODO: Open full-size document viewer
                    Alert.alert('Document Viewer''Full-size document viewer would open here');
                  }}
                >
                  View Full Size
                </Button>
              </View>
              <View style={styles.documentPreview}>
                <Ionicons name='camera' size={32} color={theme.colors.textSecondary} />
                <Text style={styles.documentLabel}>Selfie Photo</Text>
                <Button
                  title='View Full Size'
                  variant='outlined'
                  size='small'
                  onPress={() => {
                    // TODO: Open full-size photo viewer
                    Alert.alert('Photo Viewer''Full-size photo viewer would open here');
                  }}
                >
                  View Full Size
                </Button>
              </View>
            </View>
            {/* Review Notes */}
            <Input
              label='Review Notes (Optional)'
              value={reviewNotes}
              onChangeText={setReviewNotes}
              placeholder='Add any notes about this verification...'
              multiline
              numberOfLines={3}
              containerStyle={styles.notesInput}
            />

            {/* Admin Notes */}
            <Input
              label='Admin Notes'
              value={adminNotes}
              onChangeText={setAdminNotes}
              placeholder='Add notes about this verification...'
              multiline
              numberOfLines={3}
              containerStyle={{ marginBottom: 16 }}
            />

            {/* Rejection Reason (for reject/resubmit) */}
            <Input
              label='Rejection Reason'
              value={rejectionReason}
              onChangeText={setRejectionReason}
              placeholder='Provide reason for rejection...'
              multiline
              numberOfLines={3}
              containerStyle={{ marginBottom: 16 }}
            />
            {/* Action Buttons */}
            <View style={styles.actionButtons}>
              <Button
                variant='filled'
                color='success'
                onPress={() => handleApprove(selectedSubmission.id)}
                disabled={processing}
                style={styles.approveButton}
              >
                Approve
              </Button>
              <Button
                variant='outlined'
                color='error'
                onPress={() => handleReject(selectedSubmission.id)}
                disabled={processing}
                style={styles.rejectButton}
              >
                Reject
              </Button>
              <Button
                variant='outlined'
                color='error'
                onPress={() => handleDelete(selectedSubmission.id)}
                disabled={processing}
                style={styles.deleteButton}
              >
                Delete
              </Button>
            </View>

            {processing && (
              <View style={styles.processingOverlay}>
                <ActivityIndicator size='small' color={theme.colors.primary} />
                <Text style={styles.processingText}>Processing...</Text>
              </View>
            )}
          </Card>
        </View>
      )}

      {/* Cost Savings Info */}
      <Card style={styles.costSavingsCard}>
        <View style={styles.costSavingsHeader}>
          <Ionicons name='trending-down' size={24} color={theme.colors.success} />
          <Text style={styles.costSavingsTitle}>Cost Savings</Text>
        </View>
        <Text style={styles.costSavingsText}>
          Manual verification saves $7-35 per check vs automated services
        </Text>
        {metrics && (
          <Text style={styles.costSavingsAmount}>
            Total saved this month: ${(metrics.totalSubmissions * 21).toLocaleString()}
          </Text>
        )}
      </Card>
    </ScrollView>
  );
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: theme.colors.background
    },
    loadingText: {
      marginTop: theme.spacing.md,
      fontSize: theme.typography.body.fontSize,
      color: theme.colors.textSecondary
    },
    header: {
      padding: theme.spacing.lg,
      backgroundColor: theme.colors.surface
    },
    title: {
      fontSize: theme.typography.h1.fontSize,
      fontWeight: theme.typography.h1.fontWeight,
      color: theme.colors.text,
      marginBottom: theme.spacing.xs
    },
    subtitle: {
      fontSize: theme.typography.body.fontSize,
      color: theme.colors.textSecondary
    },
    metricsContainer: {
      flexDirection: 'row',
      paddingHorizontal: theme.spacing.lg,
      marginBottom: theme.spacing.lg,
      gap: theme.spacing.md
    },
    metricCard: {
      flex: 1,
      backgroundColor: theme.colors.surface,
      padding: theme.spacing.md,
      borderRadius: theme.borderRadius.md,
      alignItems: 'center'
    },
    metricNumber: {
      fontSize: theme.typography.h2.fontSize,
      fontWeight: theme.typography.h2.fontWeight,
      color: theme.colors.text,
      marginBottom: theme.spacing.xs
    },
    metricLabel: {
      fontSize: theme.typography.caption.fontSize,
      color: theme.colors.textSecondary,
      textAlign: 'center'
    },
    section: {
      padding: theme.spacing.lg
    },
    sectionTitle: {
      fontSize: theme.typography.h3.fontSize,
      fontWeight: theme.typography.h3.fontWeight,
      color: theme.colors.text,
      marginBottom: theme.spacing.md
    },
    emptyCard: {
      alignItems: 'center',
      padding: theme.spacing.xl,
      marginHorizontal: theme.spacing.lg
    },
    emptyTitle: {
      fontSize: theme.typography.h3.fontSize,
      fontWeight: theme.typography.h3.fontWeight,
      color: theme.colors.text,
      marginTop: theme.spacing.md,
      marginBottom: theme.spacing.xs
    },
    emptySubtitle: {
      fontSize: theme.typography.body.fontSize,
      color: theme.colors.textSecondary,
      textAlign: 'center'
    },
    submissionCard: {
      backgroundColor: theme.colors.surface,
      marginBottom: theme.spacing.md,
      padding: theme.spacing.md,
      borderRadius: theme.borderRadius.md
    },
    selectedCard: {
      borderColor: theme.colors.primary,
      borderWidth: 2
    },
    submissionHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: theme.spacing.sm
    },
    submissionTitle: {
      fontSize: theme.typography.body.fontSize,
      fontWeight: '600',
      color: theme.colors.text,
      flex: 1
    },
    statusBadge: {
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.xs,
      borderRadius: theme.borderRadius.sm
    },
    statusText: {
      fontSize: theme.typography.caption.fontSize,
      fontWeight: '600',
      color: theme.colors.textInverse
    },
    submissionInfo: {
      gap: theme.spacing.xs
    },
    infoText: {
      fontSize: theme.typography.caption.fontSize,
      color: theme.colors.textSecondary
    },
    reviewCard: {
      backgroundColor: theme.colors.surface,
      padding: theme.spacing.lg,
      borderRadius: theme.borderRadius.md
    },
    reviewTitle: {
      fontSize: theme.typography.h3.fontSize,
      fontWeight: theme.typography.h3.fontWeight,
      color: theme.colors.text,
      marginBottom: theme.spacing.xs
    },
    reviewSubtitle: {
      fontSize: theme.typography.body.fontSize,
      color: theme.colors.textSecondary,
      marginBottom: theme.spacing.lg
    },
    documentsPreview: {
      flexDirection: 'row',
      gap: theme.spacing.md,
      marginBottom: theme.spacing.lg
    },
    documentPreview: {
      flex: 1,
      alignItems: 'center',
      padding: theme.spacing.md,
      backgroundColor: theme.colors.background,
      borderRadius: theme.borderRadius.md,
      gap: theme.spacing.sm
    },
    documentLabel: {
      fontSize: theme.typography.caption.fontSize,
      color: theme.colors.textSecondary,
      textAlign: 'center'
    },
    notesInput: {
      marginBottom: theme.spacing.md
    },
    actionButtons: {
      flexDirection: 'row',
      gap: theme.spacing.md,
      marginTop: theme.spacing.md
    },
    approveButton: {
      flex: 1
    },
    rejectButton: {
      flex: 1
    },
    deleteButton: {
      flex: 1
    },
    processingOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: theme.colors.overlay,
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: theme.borderRadius.md,
      flexDirection: 'row',
      gap: theme.spacing.sm
    },
    processingText: {
      fontSize: theme.typography.body.fontSize,
      color: theme.colors.text
    },
    costSavingsCard: {
      backgroundColor: theme.colors.surface,
      margin: theme.spacing.lg,
      padding: theme.spacing.lg,
      borderRadius: theme.borderRadius.md
    },
    costSavingsHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: theme.spacing.sm,
      gap: theme.spacing.sm
    },
    costSavingsTitle: {
      fontSize: theme.typography.h3.fontSize,
      fontWeight: theme.typography.h3.fontWeight,
      color: theme.colors.text
    },
    costSavingsText: {
      fontSize: theme.typography.body.fontSize,
      color: theme.colors.textSecondary,
      marginBottom: theme.spacing.xs
    },
    costSavingsAmount: {
      fontSize: theme.typography.h3.fontSize,
      fontWeight: '600',
      color: theme.colors.success
    }
  });