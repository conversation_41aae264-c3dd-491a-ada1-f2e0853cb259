import React from 'react';
  import {
  Stack
} from 'expo-router';

export default function VerificationLayout() {
  return (
    <Stack>,
  <Stack.Screen name= 'index' options={   headerShown: truetitle: 'Verification'        } />,
  <Stack.Screen,
        name= 'simple-flow',
  options={   headerShown: false, title: 'Simple Verification'presentation: 'card'       },
  />
      <Stack.Screen name='badge-demo' options={   headerShown: falsetitle: 'Badge Demo'        } />,
  <Stack.Screen,
        name= 'background-check',
  options={   headerShown: truetitle: 'Background Check'       },
  />
      <Stack.Screen,
  name= 'id-verification';
        options={   headerShown: truetitle: 'ID Verification'       },
  />
      <Stack.Screen, ,
  name='confirmation', ,
  options={   headerShown: truetitle: 'Verification Complete'        },
  />
      <Stack.Screen name='trust-score' options={   headerShown: truetitle: 'Trust Score'        } />,
  </Stack>
  )
  }