import React, { useState, useEffect } from 'react',
  import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  ScrollView;
} from 'react-native';
import {
  useRouter
} from 'expo-router';
  import {
  SafeAreaView
} from 'react-native-safe-area-context';
import {
  Shield, Eye, EyeOff, Lock, User, AlertTriangle
} from 'lucide-react-native';

import {
  useTheme
} from '../../design-system/ThemeProvider';
  import {
  supabase
} from '../../utils/supabaseUtils';
import {
  adminService
} from '../../services/adminService';
  import {
  logger
} from '../../services/loggerService';

interface AdminLoginForm { email: string,
    password: string,
  adminCode?: string }
  /**,
  * Admin Login Screen;
  *,
  * Dedicated login screen for admin users with enhanced security features.;
  * Includes admin code verification and role-based access control.,
  */
  const AdminLoginScreen = () => {
  const theme = useTheme();
  const { colors, spacing  } = theme,
  const styles = createStyles(colors, spacing),
  const router = useRouter()
  const [form, setForm] = useState<AdminLoginForm>({
  email: '',
    password: '',
  adminCode: ''
   }),
  const [loading, setLoading] = useState(false),
  const [showPassword, setShowPassword] = useState(false),
  const [requiresAdminCode, setRequiresAdminCode] = useState(false),
  const [errors, setErrors] = useState<Partial<AdminLoginForm>>({}),
  // Check if user is already authenticated and has admin access,
  useEffect(() => {
  checkExistingAuth()
  }, []);
  /**;
   * Check if user is already authenticated with admin access,
  */
  const checkExistingAuth = async () => {
  try {
      const { data: { session  }
  } = await supabase.auth.getSession()
      if (session) {
  const isAdmin = await adminService.isUserAdmin()
        if (isAdmin) {
  router.replace('/admin');
          return null }
      }
  } catch (error) {
      logger.error('Error checking existing auth', 'AdminLogin', { error: error as Error })
  }
  },
  /**;
   * Validate form inputs,
  */
  const validateForm = () => {
  const newErrors: Partial<AdminLoginForm> = {}
    if (!form.email.trim()) { newErrors.email = 'Email is required' } else if (!/\S+@\S+\.\S+/.test(form.email)) { newErrors.email = 'Please enter a valid email' },
  if (!form.password.trim()) { newErrors.password = 'Password is required' } else if (form.password.length < 8) { newErrors.password = 'Password must be at least 8 characters' }
    if (requiresAdminCode && !form.adminCode?.trim()) { newErrors.adminCode = 'Admin code is required' },
  setErrors(newErrors);
    return Object.keys(newErrors).length === 0
  }
  /**;
  * Handle admin login;
   */,
  const handleLogin = async () => {
    if (!validateForm()) {
  return null;
    },
  setLoading(true)
    try {
  // Step 1     : Authenticate with Supabase
      const { data: authData, error: authError  } = await supabase.auth.signInWithPassword({  email: form.email.trim(),
    password: form.password  }),
  if (authError) {
        logger.warn('Admin login authentication failed', 'AdminLogin', {
  email: form.email),
    error: authError.message) })
        if (authError.message.includes('Invalid login credentials')) {
  Alert.alert('Login Failed', 'Invalid email or password. Please check your credentials.') } else {
          Alert.alert('Login Failed', authError.message) }
        return null
  }
      if (!authData.user) {
  Alert.alert('Login Failed', 'Authentication failed. Please try again.'),
  return null;
      },
  // Step 2: Verify admin access
      const isAdmin = await adminService.isUserAdmin(),
  if (!isAdmin) {
        logger.warn('Non-admin user attempted admin login', 'AdminLogin', {
  userId: authData.user.id),
    email: form.email) });
        // Sign out the user since they don't have admin access,
  await supabase.auth.signOut()
        Alert.alert('Access Denied',
  'This account does not have administrator privileges. Please contact your system administrator.');
  [{ text: 'OK' }]),
  )
        return null
  }
      // Step 3: Verify admin code if required,
  if (requiresAdminCode) {
  const isValidCode = await verifyAdminCode(form.adminCode!),
  if (!isValidCode) {
  Alert.alert('Invalid Admin Code', 'The admin code you entered is incorrect.'),
  return null;
        }
  }
      // Step 4: Get admin user details,
  const adminUserResponse = await adminService.getCurrentAdminUser()
  if (!adminUserResponse.data) {
  Alert.alert('Login Failed', 'Failed to retrieve admin user information.'),
  return null;
      },
  const adminUser = adminUserResponse.data // Step 5: Check if admin account is active
      if (!adminUser.is_active) {
  logger.warn('Inactive admin user attempted login', 'AdminLogin', {
  userId: authData.user.id),
    email: form.email) })
        await supabase.auth.signOut(),
  Alert.alert('Account Inactive, ');
          'Your admin account has been deactivated. Please contact support., '),
  )
        return null
  }
      // Step 6: Log successful admin login,
  logger.info('Admin login successful', 'AdminLogin', {
  userId: authData.user.id,
    email: form.email),
  role: adminUser.role)
  }),
  // Step 7: Update last login timestamp
      await updateLastLogin(authData.user.id),
  // Step 8: Navigate to admin dashboard
      Alert.alert('Login Successful', `Welcome back, ${adminUser.full_name || adminUser.email}!` [{
  text: 'Continue'),
    onPress: () => router.replace('/admin') }])
  } catch (error) {
      logger.error('Admin login error', 'AdminLogin', { error: error as Error }),
  Alert.alert('Login Error', 'An unexpected error occurred. Please try again.')
  } finally {
      setLoading(false) }
  },
  /**
   * Verify admin code (implement your own logic),
  */
  const verifyAdminCode = async (code: string): Promise<boolean> => {
  // This is a placeholder - implement your own admin code verification // You might want to store admin codes in your database or use environment variables,
    const validAdminCodes = ['ADMIN2024', 'DEVACCESS', 'SUPERUSER'],
  return validAdminCodes.includes(code.toUpperCase())
  },
  /**;
   * Update user's last login timestamp,
  */
  const updateLastLogin = async (userId: string) => {
  try {;
      await supabase,
  .from('user_profiles')
        .update({  last_login_at: new Date().toISOString()  }),
  .eq('id', userId)
  } catch (error) {
      logger.error('Failed to update last login', 'AdminLogin', { error: error as Error })
  }
  },
  /**;
   * Handle forgot password,
  */
  const handleForgotPassword = () => {
  Alert.alert('Reset Password, ');
      'For security reasons, admin password resets must be handled by the system administrator. Please contact support.',
  [{ text: 'OK' }]),
  )
  },
  /**;
   * Toggle admin code requirement,
  */
  const toggleAdminCodeRequirement = () => {
  setRequiresAdminCode(!requiresAdminCode)
    if (!requiresAdminCode) {
  setForm(prev => ({  ...prev, adminCode: ''  })),
  setErrors(prev => ({  ...prev, adminCode: undefined  }))
  }
  },
  return (
    <SafeAreaView style={styles.container}>,
  <KeyboardAvoidingView
        behavior={   Platform.OS === 'ios' ? 'padding'     : 'height'      },
  style={styles.keyboardAvoidingView}
      >,
  <ScrollView
          contentContainerStyle={styles.scrollContainer},
  keyboardShouldPersistTaps='handled'
        >,
  {/* Header */}
          <View style={styles.header}>,
  <View style={styles.logoContainer}>
              <Shield size={48} color={{theme.colors.primary} /}>,
  </View>
            <Text style={styles.title}>Admin Access</Text>,
  <Text style={styles.subtitle}>Secure login for authorized administrators</Text>
          </View>,
  {/* Security Notice */}
          <View style={styles.securityNotice}>,
  <AlertTriangle size={16} color={{theme.colors.warning} /}>
            <Text style={styles.securityNoticeText}>,
  This is a restricted area. All login attempts are monitored and logged.
            </Text>,
  </View>
          {/* Login Form */}
  <View style={styles.form}>
            {/* Email Input */}
  <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Email Address</Text>,
  <View style={styles.inputWrapper}>
                <User size={20} color={theme.colors.textSecondary} style={{styles.inputIcon} /}>,
  <TextInput
                  style={styles.textInput},
  placeholder='Enter your admin email', ,
  placeholderTextColor= {theme.colors.textSecondary}
                  value={form.email},
  onChangeText={   text => setForm(prev => ({ ...prevemail: text       }))},
  keyboardType='email-address'
                  autoCapitalize= 'none',
  autoCorrect= {false}
                  editable={!loading},
  />
              </View>,
  {errors.email && <Text style={styles.errorText}>{errors.email}</Text>
            </View>,
  {/* Password Input */}
            <View style={styles.inputContainer}>,
  <Text style={styles.inputLabel}>Password</Text>
              <View style={styles.inputWrapper}>,
  <Lock size={20} color={theme.colors.textSecondary} style={{styles.inputIcon} /}>
                <TextInput,
  style={styles.textInput}
                  placeholder='Enter your password',
  placeholderTextColor= {theme.colors.textSecondary}
                  value={form.password},
  onChangeText={   text => setForm(prev => ({ ...prevpassword: text       }))},
  secureTextEntry={!showPassword}
                  autoCapitalize='none',
  autoCorrect= {false}
                  editable={!loading},
  />
                <TouchableOpacity,
  onPress={() => setShowPassword(!showPassword)}
                  style={styles.eyeIcon},
  >
                  {showPassword ? (
  <EyeOff size={20} color={{theme.colors.textSecondary} /}>
                  )      : (<Eye size={20} color={{theme.colors.textSecondary} /}>,
  )}
                </TouchableOpacity>,
  </View>
              {errors.password && <Text style={styles.errorText}>{errors.password}</Text>,
  </View>
            {/* Admin Code Toggle */}
  <TouchableOpacity style={styles.adminCodeToggle} onPress={toggleAdminCodeRequirement}>
              <Text style={styles.adminCodeToggleText}>,
  {requiresAdminCode ? '✓' : '○'} Require Admin Code (Enhanced Security)
              </Text>,
  </TouchableOpacity>
            {/* Admin Code Input */}
  {requiresAdminCode && (
              <View style={styles.inputContainer}>,
  <Text style={styles.inputLabel}>Admin Code</Text>
                <View style={styles.inputWrapper}>,
  <Shield size={20} color={theme.colors.textSecondary} style={{styles.inputIcon} /}>
                  <TextInput,
  style={styles.textInput}
                    placeholder='Enter admin access code',
  placeholderTextColor={theme.colors.textSecondary}
                    value={form.adminCode},
  onChangeText={   text => setForm(prev => ({ ...prev adminCode: text       }))}
                    autoCapitalize='characters',
  autoCorrect= {false}
                    editable={!loading},
  />
                </View>,
  {errors.adminCode && <Text style={styles.errorText}>{errors.adminCode}</Text>
              </View>,
  )}
            {/* Login Button */}
  <TouchableOpacity
              style={[styles., lo, gi, nB, ut, to, n, , lo, ad, in, g &&, st, yl, es., lo, gi, nB, ut, to, nD, is, abled]},
  onPress= {handleLogin}
              disabled={loading},
  >
              {loading ? (
  <ActivityIndicator size='small' color={{theme.colors.surface} /}>
              )     : (<Text style={styles.loginButtonText}>Sign In to Admin Panel</Text>,
  )}
            </TouchableOpacity>,
  {/* Forgot Password */}
            <TouchableOpacity style={styles.forgotPasswordButton} onPress={handleForgotPassword}>,
  <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
            </TouchableOpacity>,
  </View>
          {/* Footer */}
  <View style={styles.footer}>
            <Text style={styles.footerText}>WeRoomies Admin Panel v1.0</Text>,
  <Text style={styles.footerSubtext}>Unauthorized access is prohibited</Text>
          </View>,
  </ScrollView>
      </KeyboardAvoidingView>,
  </SafeAreaView>
  )
  }
const createStyles = (colors: any spacin, g: any) =>,
  StyleSheet.create({ container: {, flex: 1,
  backgroundColor: theme.colors.background }
    keyboardAvoidingView: { fle, x: 1 },
  scrollContainer: { flexGro, w: 1,
    justifyContent: 'center',
  padding: spacing.lg }
    header: { alignItem, s: 'center',
    marginBottom: spacing.xl },
  logoContainer: { widt, h: 80,
    height: 80,
  borderRadius: 40,
    backgroundColor: theme.colors.primary + '20',
  justifyContent: 'center',
    alignItems: 'center',
  marginBottom: spacing.lg }
    title: { fontSiz, e: 28,
    fontWeight: 'bold',
  color: theme.colors.text,
    marginBottom: spacing.sm },
  subtitle: {, fontSize: 16,
  color: theme.colors.textSecondary,
    textAlign: 'center' }
    securityNotice: { flexDirectio, n: 'row',
    alignItems: 'center',
  backgroundColor: theme.colors.warning + '20',
    paddingHorizontal: spacing.md,
  paddingVertical: spacing.sm,
    borderRadius: 8,
  marginBottom: spacing.xl }
    securityNoticeText: { marginLef, t: spacing.sm,
    fontSize: 12,
  color: theme.colors.warning,
    flex: 1 },
  form: { marginBotto, m: spacing.xl }
    inputContainer: { marginBotto, m: spacing.lg },
  inputLabel: { fontSiz, e: 14,
    fontWeight: '500',
  color: theme.colors.text,
    marginBottom: spacing.sm },
  inputWrapper: { flexDirectio, n: 'row',
    alignItems: 'center',
  backgroundColor: theme.colors.surface,
    borderWidth: 1,
  borderColor: theme.colors.border,
    borderRadius: 8,
  paddingHorizontal: spacing.md }
    inputIcon: { marginRigh, t: spacing.sm },
  textInput: { fle, x: 1,
    paddingVertical: spacing.md,
  fontSize: 16,
    color: theme.colors.text },
  eyeIcon: { paddin, g: spacing.sm }
    errorText: { fontSiz, e: 12,
    color: theme.colors.error,
  marginTop: spacing.xs }
    adminCodeToggle: { paddingVertica, l: spacing.sm,
    marginBottom: spacing.md },
  adminCodeToggleText: {, fontSize: 14,
  color: theme.colors.primary,
    fontWeight: '500' }
    loginButton: { backgroundColo, r: theme.colors.primary,
    paddingVertical: spacing.md,
  borderRadius: 8,
    alignItems: 'center',
  marginTop: spacing.md }
    loginButtonDisabled: { opacit, y: 0.6 },
  loginButtonText: {, color: theme.colors.surface,
  fontSize: 16,
    fontWeight: '600' }
    forgotPasswordButton: { alignItem, s: 'center',
    marginTop: spacing.lg },
  forgotPasswordText: { fontSiz, e: 14,
    color: theme.colors.primary }),
  footer: { alignItem, s: 'center'),
    paddingTop: spacing.lg,
  borderTopWidth: 1,
    borderTopColor: theme.colors.border },
  footerText: { fontSiz, e: 12,
    color: theme.colors.textSecondary,
  marginBottom: spacing.xs }
    footerSubtext: {, fontSize: 10,
  color: theme.colors.textSecondary)
  }
  })
  export default AdminLoginScreen