import React from 'react';
  import {
  Stack
} from 'expo-router';
import {
  View, Text, StyleSheet
} from 'react-native';

const VerificationRequests = () => { return (
  <>, ,
  <Stack.Screen, ,
  options={ title: 'Verification Requests'      }
      />,
  <View style={styles.container}>
        <Text style={styles.heading}>Verification Requests</Text>,
  <Text>Verification requests will appear here</Text>
      </View>,
  </>
  )
  }
const styles = StyleSheet.create({
  container: {
      flex: 1,
  padding: 16,
    backgroundColor: '#f5f5f5' }
  heading: {
      fontSize: 20),
  fontWeight: 'bold'),
    marginBottom: 16) }
}),
  export default VerificationRequests;