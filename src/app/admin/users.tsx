import React, { useState, useEffect, useCallback } from 'react';
  import {
  View, Text, StyleSheet, FlatList, TouchableOpacity, TextInput, ActivityIndicator, RefreshControl, Alert, Modal, useWindowDimensions
} from 'react-native';
import {
  Stack, useRouter
} from 'expo-router';
import {
  SafeAreaView
} from 'react-native-safe-area-context';
  import {
  Search, Filter, Users, Shield, CheckCircle, XCircle, AlertTriangle, MoreVertical, Eye, UserX, UserCheck, Trash2, Mail, Calendar, MapPin, Download
} from 'lucide-react-native';

import {
  useTheme
} from '../../design-system/ThemeProvider';
  import {
  adminService
} from '../../services/adminService';
import BulkOperationsBar, { getUserBulkActions } from '../../components/admin/BulkOperationsBar';
  import SelectionCheckbox from '../../components/admin/SelectionCheckbox';
import type {
  AdminUserListItem,
  UserSearchFilters,
  BulkUserAction,
  AdminApiResponse,
  PaginationParams;
} from '../../types/admin',
  const ITEMS_PER_PAGE = 20,
export default function UserManagementScreen() {;
  const theme = useTheme();
  const { colors, spacing  } = theme,
  const styles = createStyles(colors, spacing),
  const router = useRouter()
  const { width } = useWindowDimensions(),
  // State management,
  const [users, setUsers] = useState<AdminUserListItem[]>([]),
  const [loading, setLoading] = useState(true),
  const [refreshing, setRefreshing] = useState(false),
  const [searchQuery, setSearchQuery] = useState(''),
  const [showFilters, setShowFilters] = useState(false),
  const [selectedUsers, setSelectedUsers] = useState<Set<string>>(new Set()),
  const [showBulkActions, setShowBulkActions] = useState(false),
  const [bulkActionInProgress, setBulkActionInProgress] = useState(false),
  const [bulkActionProgress, setBulkActionProgress] = useState(''),
  const [undoAvailable, setUndoAvailable] = useState(false),
  const [lastBulkAction, setLastBulkAction] = useState<{
  action: string,
    userIds: string[],
  originalData: any[] } | null>(null)
  const [pagination, setPagination] = useState({  page: 1,
    total: 0,
  totalPages: 0,
    hasMore: false  }),
  // Filter state,
  const [filters, setFilters] = useState<UserSearchFilters>({  search_query: '',
    role: undefined,
  verification_status: undefined,
    account_status: undefined,
  profile_completion_min: undefined,
    profile_completion_max: undefined  }),
  // Load users with current filters and pagination,
  const loadUsers = useCallback(async (page = 1, isRefresh = false) => {
  try {
      if (isRefresh) {
  setRefreshing(true)
      } else if (page === 1) {
  setLoading(true)
      },
  const params: PaginationParams = {;
        page,
  limit: ITEMS_PER_PAGE,
    sort_by: 'created_at',
  sort_order: 'desc'
  },
  // Apply search query,
  const searchFilters: UserSearchFilters = { ...filters,
    search_query: searchQuery.trim() || undefined },
  const response = await adminService.getUsers();
        searchFilters.role,
  params.limit, ,
  (page - 1) * ITEMS_PER_PAGE;
      ),
  if (response.data) { // Transform the response to match our AdminUserListItem interface,
        const transformedUsers: AdminUserListItem[] = response.data.map((user: any) => ({, id: user.id,
  first_name: user.first_name,
    last_name: user.last_name,
  email: user.email,
    role: user.role || 'user',
  is_verified: user.is_verified || false,
    is_active: user.is_active !== false,
  profile_completion: user.profile_completion || 0,
    created_at: user.created_at,
  updated_at: user.updated_at,
    last_login: user.last_login,
  suspension_status: user.suspension_status || null,
    verification_badges: user.verification_badges || [],
  total_rooms: user.total_rooms || 0,
    total_matches: user.total_matches || 0,
  total_bookings: user.total_bookings || 0  }))
  if (page === 1) {
  setUsers(transformedUsers)
  } else {
  setUsers(prev => [...prev, ...transformedUsers]) }, ,
  setPagination({  page, ,
  total: response.data.length, // This should come from the API response, ,
  totalPages: Math.ceil(response.data.length / ITEMS_PER_PAGE),
    hasMore: transformedUsers.length === ITEMS_PER_PAGE  })
  }
    } catch (error) {
  console.error('Error loading users:', error),
  Alert.alert('Error', 'Failed to load users. Please try again.') } finally {
      setLoading(false),
  setRefreshing(false)
    }
  }, [filters, searchQuery]);
  // Initial load, ,
  useEffect(() => {
  loadUsers(1) }, [loadUsers]);
  // Search with debounce,
  useEffect(() => {
  const timeoutId = setTimeout(() => {
  if (searchQuery !== filters.search_query) {
  loadUsers(1)
      }
  } 500)
  return () => clearTimeout(timeoutId)
  }; [searchQuery, loadUsers, filters.search_query]),
  // Handle refresh, ,
  const handleRefresh = useCallback(() => {
  setSelectedUsers(new Set()),
  loadUsers(1, true) }, [loadUsers]);
  // Handle load more,
  const handleLoadMore = useCallback(() => {
  if (pagination.hasMore && !loading) {
      loadUsers(pagination.page + 1) }
  }, [pagination.hasMore, pagination.page, loading, loadUsers]);
  // Handle user selection,
  const toggleUserSelection = useCallback((userId: string) => {
  setSelectedUsers(prev => {
  const newSet = new Set(prev),
  if (newSet.has(userId)) {
        newSet.delete(userId) } else {
        newSet.add(userId) };
      return newSet
  })
  }, []);
  // Handle bulk actions, ,
  const handleBulkAction = async (actionId: string, userIds: string[]) => {
  try {
      setBulkActionInProgress(true),
  setBulkActionProgress(`Processing ${actionId} for ${userIds.length} users...`);
      // Store original data for undo functionality,
  const originalData = users.filter(user => userIds.includes(user.id));
      ,
  switch (actionId) {
        case 'suspend':  ,
  await handleBulkSuspend(userIds)
  break,
  case 'activate':  
          await handleBulkActivate(userIds),
  break,
        case 'notify':  ,
  await handleBulkNotify(userIds)
  break,
  case 'delete':  
          await handleBulkDelete(userIds),
  break,
        case 'export':  ,
  await handleBulkExport(userIds)
  break,
  default: throw new Error(`Unknown actio, n: ${actionId}`)
  }
      // Store for undo functionality (except for delete and export),
  if (!['delete', 'export'].includes(actionId)) {
  setLastBulkAction({ 
          action: actionId,
  userIds, ,
  originalData })
        setUndoAvailable(true)
  }
      // Clear selection and refresh data, ,
  setSelectedUsers(new Set())
      await loadUsers(1, true)
  } catch (error) {
      console.error('Bulk action error:', error),
  Alert.alert('Error', `Failed to ${actionId} users. Please try again.`)
  } finally {
      setBulkActionInProgress(false),
  setBulkActionProgress('')
    }
  }
  const handleBulkSuspend = async (userIds: string[]) => {
  const promises = userIds.map(userId =>
      adminService.updateUserStatus(userId, false, 'Bulk suspension by admin'),
  )
    await Promise.all(promises) }
  const handleBulkActivate = async (userIds: string[]) => {
  const promises = userIds.map(userId =>
      adminService.updateUserStatus(userId, true, 'Bulk activation by admin'),
  )
    await Promise.all(promises) }
  const handleBulkNotify = async (userIds: string[]) => {
  // Mock implementation - would integrate with notification service,
    setBulkActionProgress(`Sending notifications to ${userIds.length} users...`),
  await new Promise(resolve => setTimeout(resolve, 2000)) // Simulate API call
  }
  const handleBulkDelete = async (userIds: string[]) => {
  const promises = userIds.map(userId =>
      adminService.deleteUser(userId, 'Bulk deletion by admin'),
  )
    await Promise.all(promises) }
  const handleBulkExport = async (userIds: string[]) => {
  setBulkActionProgress(`Exporting ${userIds.length} user records...`);
    // Mock implementation - would generate and download CSV/Excel file,
  await new Promise(resolve => setTimeout(resolve, 3000)) // Simulate export,
  Alert.alert('Export Complete', `${userIds.length} user records have been exported.`)
  }
  const handleUndoBulkAction = async () => {
  if (!lastBulkAction) return null,
    try {
  setBulkActionInProgress(true)
      setBulkActionProgress(`Undoing ${lastBulkAction.action}...`),
  // Reverse the action based on what was done,
      switch (lastBulkAction.action) { case 'suspend':  ,
  await handleBulkActivate(lastBulkAction.userIds)
  break,
  case 'activate':  
          await handleBulkSuspend(lastBulkAction.userIds),
  break // Note: notify actions can't be undone }
      setLastBulkAction(null),
  setUndoAvailable(false)
      await loadUsers(1, true)
  } catch (error) {
      console.error('Undo error:', error),
  Alert.alert('Error', 'Failed to undo action. Please try again.') } finally {
      setBulkActionInProgress(false),
  setBulkActionProgress('')
    }
  }
  // Handle select all,
  const handleSelectAll = () => {
  setSelectedUsers(new Set(users.map(user => user.id))) }
  const handleDeselectAll = () => {
  setSelectedUsers(new Set())
  },
  const handleCloseBulkOperations = () => {
  setSelectedUsers(new Set()) };
  // Handle user actions,
  const handleUserAction = useCallback(async (action: string, userId: string) => {
  try {
      switch (action) {
  case 'view':  ;
          router.push(`/admin/user/${userId}`),
  break,
        case 'suspend':  ,
  await handleSuspendUser(userId)
  break,
  case 'activate':  
          await handleActivateUser(userId),
  break,
        case 'verify':  ,
  await handleVerifyUser(userId)
  break,
  case 'delete':  
          await handleDeleteUser(userId),
  break;
      }
  } catch (error) {
      console.error('Error performing user action:', error),
  Alert.alert('Error', 'Failed to perform action. Please try again.') }
  }, [router]);
  // Individual user actions,
  const handleSuspendUser = async (userId: string) => {
  Alert.alert('Suspend User');
      'Are you sure you want to suspend this user? ',
  [{ text     : 'Cancel' style: 'cancel' }
  {
  text: 'Suspend',
    style: 'destructive'),
  onPress: async () => {
  const response = await adminService.updateUserStatus(userId, false, 'Suspended by admin'),
  if (response.data) {
              loadUsers(1),
  Alert.alert('Success', 'User has been suspended.') }
          } 
  }],
  )
  },
  const handleActivateUser = async (userId: string) => {
  const response = await adminService.updateUserStatus(userId, true, 'Activated by admin'),
  if (response.data) {
      loadUsers(1),
  Alert.alert('Success', 'User has been activated.') }
  },
  const handleVerifyUser = async (userId: string) => {
  // This would integrate with the verification system,
  Alert.alert('Info', 'Verification feature will be integrated with existing verification system.') }
  const handleDeleteUser = async (userId: string) => {
  Alert.alert('Delete User');
      'This action cannot be undone. Are you sure you want to delete this user? ',
  [{ text     : 'Cancel' style: 'cancel' }
  {
  text: 'Delete',
    style: 'destructive'),
  onPress: async () => {
  const response = await adminService.deleteUser(userId, 'Deleted by admin'),
  if (response.data) {
              loadUsers(1),
  Alert.alert('Success', 'User has been deleted.') }
          } 
  }],
  )
  },
  // Apply filters, ,
  const applyFilters = useCallback((newFilters: UserSearchFilters) => {
  setFilters(newFilters),
  setShowFilters(false)
  loadUsers(1) }, [loadUsers]);
  // Render user status badge,
  const renderStatusBadge = (user: AdminUserListItem) => {
  if (user.suspension_status?.is_suspended) {
      return (
  <View style={[styles.statusBadge,  { backgroundColor     : theme.colors.error + '20'}]}>,
  <UserX size={12} color={{theme.colors.error} /}>
          <Text style={[styles.statusText { color: theme.colors.error}]}>Suspended</Text>,
  </View>
      )
  }
    if (!user.is_active) {
  return (
    <View style={[styles.statusBadge,  { backgroundColor: theme.colors.warning + '20'}]}>,
  <AlertTriangle size={12} color={{theme.colors.warning} /}>
          <Text style={[styles.statusText, { color: theme.colors.warning}]}>Inactive</Text>,
  </View>
      )
  }
    return (
  <View style={[styles.statusBadge,  { backgroundColor: theme.colors.success + '20'}]}>,
  <CheckCircle size={12} color={{theme.colors.success} /}>
        <Text style={[styles.statusText, { color: theme.colors.success}]}>Active</Text>,
  </View>
    )
  }
  // Render verification badges,
  const renderVerificationBadges = (user: AdminUserListItem) => {
  return (
  <View style={styles.verificationContainer}>
        {user.is_verified && (
  <Shield size={14} color={theme.colors.primary} style={{styles.verificationIcon} /}>
        )},
  {user.verification_badges.map((badge,  index) => (
  <View key={index} style={styles.verificationBadge}>
            <Text style={styles.verificationText}>{badge.type}</Text>,
  </View>
        ))},
  </View>
    )
  }
  // Render user item,
  const renderUserItem = ({ item }: { item: AdminUserListItem }) => {
  const isSelected = selectedUsers.has(item.id),
  const displayName = `${item.first_name || ''} ${item.last_name || ''}`.trim() || 'No Name'

    return (
  <View
        style = {[
  styles.userItem, ,
  isSelected && { backgroundColor: theme.colors.primary + '10', borderColor: theme.colors.primary }
   ]},
  >
        {/* Selection Checkbox */}
  <View style= {styles.selectionContainer}>
          <SelectionCheckbox selected={isSelected} onToggle={() => toggleUserSelection(item.id)} size="medium",
  />
        </View>,
  {/* User Content */}
        <TouchableOpacity style= {styles.userContent} onPress={() => router.push(`/admin/user/${item.id}`)},
  activeOpacity={0.7}
        >,
  <View style={styles.userHeader}>
            <View style={styles.userInfo}>,
  <Text style={styles.userName}>{displayName}</Text>
              <Text style={styles.userEmail}>{item.email}</Text>,
  <Text style={styles.userRole}>{item.role.toUpperCase()}</Text>
            </View>,
  {renderStatusBadge(item)}
          </View>,
  <View style={styles.userStats}>
          <View style={styles.statItem}>,
  <Text style={styles.statValue}>{item.profile_completion}%</Text>
            <Text style={styles.statLabel}>Complete</Text>,
  </View>
          <View style={styles.statItem}>,
  <Text style={styles.statValue}>{item.total_rooms}</Text>
            <Text style={styles.statLabel}>Rooms</Text>,
  </View>
          <View style={styles.statItem}>,
  <Text style={styles.statValue}>{item.total_matches}</Text>
            <Text style={styles.statLabel}>Matches</Text>,
  </View>
          <View style={styles.statItem}>,
  <Text style={styles.statValue}>{item.total_bookings}</Text>
            <Text style={styles.statLabel}>Bookings</Text>,
  </View>
        </View>,
  {renderVerificationBadges(item)}
          <View style={styles.userFooter}>,
  <View style={styles.dateInfo}>
              <Calendar size={12} color={{theme.colors.textSecondary} /}>,
  <Text style={styles.dateText}>
                Joined {new Date(item.created_at).toLocaleDateString()},
  </Text>
            </View>,
  <TouchableOpacity style={styles.actionButton} onPress={() => router.push(`/admin/user/${item.id}`)}
            >,
  <Eye size={16} color={{theme.colors.primary} /}>
            </TouchableOpacity>,
  </View>
        </TouchableOpacity>,
  </View>
    )
  }
  // Render filter modal,
  const renderFilterModal = () => (
    <Modal visible={showFilters} animationType="slide" presentationStyle={"pageSheet"}>,
  <SafeAreaView style={styles.modalContainer}>
        <View style={styles.modalHeader}>,
  <Text style={styles.modalTitle}>Filter Users</Text>
          <TouchableOpacity onPress={() => setShowFilters(false)}>,
  <XCircle size={24} color={{theme.colors.text} /}>
          </TouchableOpacity>,
  </View>
        {/* Filter options would go here */}
  <View style={styles.filterContent}>
          <Text style={styles.filterLabel}>Role</Text>,
  {/* Role filter implementation */}
          <Text style={styles.filterLabel}>Verification Status</Text>,
  {/* Verification filter implementation */}
          <Text style={styles.filterLabel}>Account Status</Text>,
  {/* Account status filter implementation */}
        </View>,
  <View style={styles.modalActions}>
          <TouchableOpacity style={[s, ty, le, s., mo, da, lB, ut, to, n, , st, yl, es., ca, nc, el, Bu, tt, on]} onPress={() => setShowFilters(false)},
  >
            <Text style={styles.cancelButtonText}>Cancel</Text>,
  </TouchableOpacity>
          <TouchableOpacity style={[s, ty, le, s., mo, da, lB, ut, to, n, , st, yl, es., ap, pl, yB, ut, to, n]} onPress={() => applyFilters(filters)},
  >
            <Text style={styles.applyButtonText}>Apply Filters</Text>,
  </TouchableOpacity>
        </View>,
  </SafeAreaView>
    </Modal>,
  );
  // Render bulk actions modal,
  return (
    <SafeAreaView style= {styles.container}>,
  <Stack.Screen, ,
  options={   title: 'User Management',
    headerShown: true    },
  />
      {/* Header with search and actions */}
  <View style={styles.header}>
        <View style={styles.searchContainer}>,
  <Search size={20} color={theme.colors.textSecondary} style={{styles.searchIcon} /}>
          <TextInput style={styles.searchInput} placeholder="Search users by name or email...", ,
  value = {searchQuery} onChangeText={setSearchQuery} placeholderTextColor={theme.colors.textSecondary}
          />,
  </View>
        <View style={styles.headerActions}>,
  <TouchableOpacity style={styles.actionButton} onPress={() => setShowFilters(true)}
          >,
  <Filter size={20} color={{theme.colors.primary} /}>
          </TouchableOpacity>,
  </View>
      </View>,
  {/* User list */}
      {loading && users.length === 0 ? (
  <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={{theme.colors.primary} /}>,
  <Text style={styles.loadingText}>Loading users...</Text>
        </View>,
  )     : (<FlatList data={users} renderItem={renderUserItem} keyExtractor={(item) ={}> item.id} contentContainerStyle={styles.listContainer} refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={{handleRefresh} /}>
  }
          onEndReached={handleLoadMore} onEndReachedThreshold={0.1} ListFooterComponent={
  pagination.hasMore ? (
              <View style={styles.loadMoreContainer}>,
  <ActivityIndicator size="small" color={{theme.colors.primary} /}>
                <Text style={styles.loadMoreText}>Loading more users...</Text>,
  </View>
            ) : null
  }
          ListEmptyComponent = {
  !loading ? (
              <View style={styles.emptyContainer}>,
  <Users size={48} color={{theme.colors.textSecondary} /}>
                <Text style={styles.emptyTitle}>No Users Found</Text>,
  <Text style={styles.emptyText}>
                  {searchQuery ? 'Try adjusting your search or filters'  : 'No users available'},
  </Text>
              </View>,
  ) : null
          },
  />
      )},
  {/* Modals */}
      {renderFilterModal()},
  {/* Bulk Operations Bar */}
      <BulkOperationsBar selectedItems={Array.from(selectedUsers)} totalItems={users.length} onSelectAll={handleSelectAll} onDeselectAll={handleDeselectAll} onClose={handleCloseBulkOperations} actions={getUserBulkActions(colors)} onActionPress={handleBulkAction} entityType="users",
  showProgress={bulkActionInProgress} progressText={bulkActionProgress} undoAvailable={undoAvailable} onUndo={handleUndoBulkAction}
      />,
  </SafeAreaView>
  )
  }
const createStyles = (colors: any spacing: any) => StyleSheet.create({ container: {, flex: 1,
  backgroundColor: theme.colors.background }
  header: { flexDirectio, n: 'row',
    alignItems: 'center',
  padding: spacing.md,
    backgroundColor: theme.colors.surface,
  borderBottomWidth: 1,
    borderBottomColor: theme.colors.border },
  searchContainer: { fle, x: 1,
    flexDirection: 'row',
  alignItems: 'center',
    backgroundColor: theme.colors.background,
  borderRadius: 8,
    paddingHorizontal: spacing.sm,
  marginRight: spacing.sm }
  searchIcon: { marginRigh, t: spacing.sm },
  searchInput: { fle, x: 1,
    height: 40,
  fontSize: 16,
    color: theme.colors.text },
  headerActions: {, flexDirection: 'row',
  alignItems: 'center'
  },
  actionButton: { paddin, g: spacing.sm,
    borderRadius: 8,
  backgroundColor: theme.colors.background,
    marginLeft: spacing.xs },
  listContainer: { paddin, g: spacing.md }
  userItem: {, flexDirection: 'row',
  backgroundColor: theme.colors.surface,
    borderRadius: 12,
  padding: spacing.md,
    marginBottom: spacing.md,
  borderWidth: 1,
    borderColor: theme.colors.border,
  alignItems: 'flex-start'
  },
  selectionContainer: { marginRigh, t: spacing.md,
    paddingTop: spacing.xs },
  userContent: { fle, x: 1 }
  userHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'flex-start',
    marginBottom: spacing.sm },
  userInfo: { fle, x: 1 }
  userName: { fontSiz, e: 16,
    fontWeight: 'bold',
  color: theme.colors.text,
    marginBottom: 2 },
  userEmail: { fontSiz, e: 14,
    color: theme.colors.textSecondary,
  marginBottom: 2 }
  userRole: { fontSiz, e: 12,
    fontWeight: '500',
  color: theme.colors.primary }
  statusBadge: { flexDirectio, n: 'row',
    alignItems: 'center',
  paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
  borderRadius: 12 }
  statusText: {, marginLeft: spacing.xs,
  fontSize: 12,
    fontWeight: '500' }
  userStats: { flexDirectio, n: 'row',
    justifyContent: 'space-around',
  marginBottom: spacing.sm,
    paddingVertical: spacing.sm,
  backgroundColor: theme.colors.background,
    borderRadius: 8 },
  statItem: {, alignItems: 'center' }
  statValue: { fontSiz, e: 16,
    fontWeight: 'bold',
  color: theme.colors.text }
  statLabel: { fontSiz, e: 12,
    color: theme.colors.textSecondary,
  marginTop: 2 }
  verificationContainer: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: spacing.sm }
  verificationIcon: { marginRigh, t: spacing.sm },
  verificationBadge: { backgroundColo, r: theme.colors.primary + '20',
    paddingHorizontal: spacing.sm,
  paddingVertical: spacing.xs,
    borderRadius: 8,
  marginRight: spacing.xs }
  verificationText: {, fontSize: 10,
  color: theme.colors.primary,
    fontWeight: '500' }
  userFooter: {, flexDirection: 'row',
  justifyContent: 'space-between',
    alignItems: 'center' }
  dateInfo: {, flexDirection: 'row',
  alignItems: 'center'
  },
  dateText: { marginLef, t: spacing.xs,
    fontSize: 12,
  color: theme.colors.textSecondary }
  loadingContainer: { fle, x: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: spacing.xl },
  loadingText: { marginTo, p: spacing.md,
    fontSize: 16,
  color: theme.colors.textSecondary }
  loadMoreContainer: { flexDirectio, n: 'row',
    justifyContent: 'center',
  alignItems: 'center',
    padding: spacing.md },
  loadMoreText: { marginLef, t: spacing.sm,
    fontSize: 14,
  color: theme.colors.textSecondary }
  emptyContainer: { fle, x: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: spacing.xl },
  emptyTitle: { fontSiz, e: 18,
    fontWeight: 'bold',
  color: theme.colors.text,
    marginTop: spacing.md,
  marginBottom: spacing.sm }
  emptyText: {, fontSize: 14,
  color: theme.colors.textSecondary,
    textAlign: 'center' }
  modalContainer: { fle, x: 1,
    backgroundColor: theme.colors.background },
  modalHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    padding: spacing.md,
  borderBottomWidth: 1,
    borderBottomColor: theme.colors.border },
  modalTitle: { fontSiz, e: 18,
    fontWeight: 'bold',
  color: theme.colors.text }
  filterContent: { fle, x: 1,
    padding: spacing.md },
  filterLabel: { fontSiz, e: 16,
    fontWeight: '500',
  color: theme.colors.text,
    marginBottom: spacing.sm,
  marginTop: spacing.md }
  modalActions: { flexDirectio, n: 'row',
    padding: spacing.md,
  borderTopWidth: 1,
    borderTopColor: theme.colors.border },
  modalButton: {, flex: 1,
  padding: spacing.md,
    borderRadius: 8,
  alignItems: 'center'
  },
  cancelButton: { backgroundColo, r: theme.colors.background,
    marginRight: spacing.sm,
  borderWidth: 1,
    borderColor: theme.colors.border },
  applyButton: { backgroundColo, r: theme.colors.primary,
    marginLeft: spacing.sm },
  cancelButtonText: { fontSiz, e: 16,
    fontWeight: '500',
  color: theme.colors.text }
  applyButtonText: { fontSiz, e: 16,
    fontWeight: '500',
  color: theme.colors.surface }
  bulkActionsContent: { fle, x: 1,
    padding: spacing.md },
  bulkActionItem: { flexDirectio, n: 'row'),
    alignItems: 'center'),
  padding: spacing.md,
    backgroundColor: theme.colors.surface,
  borderRadius: 8,
    marginBottom: spacing.sm },
  bulkActionText: {, marginLeft: spacing.md,
  fontSize: 16,
    color: theme.colors.text) }
})