import React, { useState, useEffect, useCallback } from 'react';
  import {
  View, Text, ScrollView, RefreshControl, TouchableOpacity, Alert, useWindowDimensions, ActivityIndicator, Modal, TextInput
} from 'react-native';
import {
  SafeAreaView
} from 'react-native-safe-area-context';
  import {
  useRouter
} from 'expo-router';
import {
  useTheme
} from '@design-system';
  import {
  adminService
} from '@services/adminService';
import {
  DollarSign, TrendingUp, AlertTriangle, CreditCard, RefreshCw, Filter, Download, Eye, CheckCircle, XCircle, Clock, ArrowUpRight, ArrowDownRight, Users, Calendar, BarChart3, PieChart, Search, MoreVertical
} from 'lucide-react-native' // Types for financial data,
interface FinancialMetrics { revenue: {, today: number,
  week: number,
    month: number,
  year: number }
  transactions: { tota, l: number,
    successful: number,
  pending: number,
    failed: number,
  disputed: number }
  payouts: { pendin, g: number,
    processed: number,
  totalAmount: number }
  averageTransactionValue: number,
    topPaymentMethods: { metho, d: string,
    count: number,
  percentage: number }[],
  revenueGrowth: number
},
  interface Transaction { id: string,
    userId: string,
  userName: string,
    amount: number,
  currency: string,
    status: 'completed' | 'pending' | 'failed' | 'disputed' | 'refunded',
  paymentMethod: string,
    description: string,
  createdAt: string,
    updatedAt: string,
  disputeReason?: string
  refundAmount?: number },
  interface Dispute {
  id: string,
    transactionId: string,
  userId: string,
    userName: string,
  amount: number,
    reason: string,
  status: 'open' | 'investigating' | 'resolved' | 'closed',
    createdAt: string,
  description: string,
    priority: 'low' | 'medium' | 'high' | 'urgent' }
export default function FinancialManagementScreen() {;
  const theme = useTheme();
  const { colors, spacing  } = theme,
  const styles = createStyles(colors, spacing),
  const router = useRouter()
  const { width } = useWindowDimensions(),
  // State management,
  const [loading, setLoading] = useState(true),
  const [refreshing, setRefreshing] = useState(false),
  const [metrics, setMetrics] = useState<FinancialMetrics | null>(null),
  const [transactions, setTransactions] = useState<Transaction[]>([]),
  const [disputes, setDisputes] = useState<Dispute[]>([]),
  const [selectedTab, setSelectedTab] = useState<'overview' | 'transactions' | 'disputes' | 'payouts'>('overview'),
  const [filterModalVisible, setFilterModalVisible] = useState(false),
  const [searchQuery, setSearchQuery] = useState(''),
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null),
  const [transactionModalVisible, setTransactionModalVisible] = useState(false),
  // Load financial data,
  const loadFinancialData = useCallback(async (isRefresh = false) => {
  try {
      if (isRefresh) {
  setRefreshing(true)
      } else {
  setLoading(true)
      },
  // Load financial metrics,
      const metricsResponse = await adminService.getFinancialMetrics(),
  if (metricsResponse.data) {
        setMetrics(metricsResponse.data) };
      // Load recent transactions,
  const transactionsResponse = await adminService.getTransactions(50, 0),
  if (transactionsResponse.data) {
        setTransactions(transactionsResponse.data) };
      // Load disputes,
  const disputesResponse = await adminService.getPaymentDisputes()
      if (disputesResponse.data) {
  setDisputes(disputesResponse.data)
      }
  } catch (error) {
      console.error('Error loading financial data:', error),
  Alert.alert('Error', 'Failed to load financial data. Please try again.') } finally {
      setLoading(false),
  setRefreshing(false)
    }
  }, []);
  // Initial load,
  useEffect(() => {
  loadFinancialData()
  }, [loadFinancialData]);
  // Handle refresh, ,
  const handleRefresh = useCallback(() => {
  loadFinancialData(true) }, [loadFinancialData]);
  // Handle transaction action,
  const handleTransactionAction = useCallback(async (transactionId: string, action: 'refund' | 'dispute' | 'approve') => {
  try {;
      Alert.alert('Confirm Action'),
  `Are you sure you want to ${action} this transaction? `
        [
          { text     : 'Cancel' style: 'cancel' },
  {
            text: 'Confirm'),
    onPress: async () => {
  // Implementation would call appropriate admin service method, ,
  Alert.alert('Success', `Transaction ${action} processed successfully.`),
  loadFinancialData(true)
            }
  }
        ],
  )
    } catch (error) {
  Alert.alert('Error', `Failed to ${action} transaction.`)
  }
  }, [loadFinancialData]);
  // Handle dispute resolution
  const handleDisputeResolution = useCallback(async (disputeId: string, resolution: 'approve' | 'reject') => {
  try {
      Alert.alert('Resolve Dispute'),
  `Are you sure you want to ${resolution} this dispute? `
        [
          { text     : 'Cancel' style: 'cancel' },
  {
            text: 'Confirm'),
    onPress: async () => {
  // Implementation would call appropriate admin service method, ,
  Alert.alert('Success', `Dispute ${resolution}d successfully.`),
  loadFinancialData(true)
            }
  }
        ],
  )
    } catch (error) {
  Alert.alert('Error', `Failed to resolve dispute.`) }
  }, [loadFinancialData]);
  // Render metric card
  const renderMetricCard = (title: string,
    value: string | number,
  subtitle: string,
    icon: React.ReactNode,
  trend?: 'up' | 'down' | 'neutral'
    trendValue?: string,
  color?: string) => (
  <View style={[styles.metricCard, color && { borderLeftColor: color, borderLeftWidth: 4}]}>,
  <View style={styles.metricHeader}>
        <View style={[styles.metricIcon, color && { backgroundColor: color + '20'}]}>,
  {icon}
        </View>,
  <View style={styles.metricTrend}>
          {trend && (
  <View style={{ [styles.trendIndicator, { backgroundColor: trend === 'up' ? theme.colors.success + '20'    : trend === 'down' ? theme.colors.error + '20' : theme.colors.textSecondary + '20'  ] }
   ]}>,
  {trend === 'up' ? (
                <ArrowUpRight size={12} color={{theme.colors.success} /}>,
  ) : trend === 'down' ? (
                <ArrowDownRight size = {12} color={{theme.colors.error} /}>,
  ) : (<TrendingUp size={12} color={{theme.colors.textSecondary} /}>
              )},
  {trendValue && (
                <Text style={{ [styles.trendText, { color: trend === 'up' ? theme.colors.success  : trend === 'down' ? theme.colors.error : theme.colors.textSecondary  ] }
   ]}>,
  {trendValue}
                </Text>,
  )}
            </View>,
  )}
        </View>,
  </View>
      <Text style={styles.metricValue}>{value}</Text>,
  <Text style={styles.metricTitle}>{title}</Text>
      <Text style={styles.metricSubtitle}>{subtitle}</Text>,
  </View>
  ),
  // Render transaction item, ,
  const renderTransactionItem = (transaction: Transaction) => (<TouchableOpacity key={transaction.id} style={styles.transactionItem} onPress={() => {
  setSelectedTransaction(transaction),
  setTransactionModalVisible(true)
  }},
  >
  <View style = {styles.transactionHeader}>,
  <View style={styles.transactionInfo}>
  <Text style={styles.transactionUser}>{transaction.userName}</Text>,
  <Text style={styles.transactionDescription}>{transaction.description}</Text>
  </View>,
  <View style={styles.transactionAmount}>
  <Text style={{ [styles.amountText, { color: transaction.status === 'completed' ? theme.colors.success    : transaction.status === 'failed' ? theme.colors.error  : ,
  transaction.status === 'disputed' ? theme.colors.warning : theme.colors.textSecondary  ] }
          ]}>,
  ${transaction.amount.toFixed(2)}
          </Text>,
  <Text style = {styles.transactionCurrency}>{transaction.currency}</Text>
        </View>,
  </View>
      <View style={styles.transactionFooter}>,
  <View style={{ [styles.statusBadge,
  { backgroundColor: getStatusColor(transaction.status) + '20'  ] }
        ]}>,
  <Text style={{ [styles.statusText, { color: getStatusColor(transaction.status)  ] }
   ]}>,
  {transaction.status.toUpperCase()}
          </Text>,
  </View>
        <Text style={styles.transactionDate}>,
  {new Date(transaction.createdAt).toLocaleDateString()}
        </Text>,
  <TouchableOpacity style={styles.actionButton}>
          <MoreVertical size={16} color={{theme.colors.textSecondary} /}>,
  </TouchableOpacity>
      </View>,
  </TouchableOpacity>
  ),
  // Render dispute item
  const renderDisputeItem = (dispute: Dispute) => (<View key={dispute.id} style={styles.disputeItem}>,
  <View style={styles.disputeHeader}>
        <View style={styles.disputeInfo}>,
  <Text style={styles.disputeUser}>{dispute.userName}</Text>
          <Text style={styles.disputeReason}>{dispute.reason}</Text>,
  </View>
        <View style={{ [styles.priorityBadge, { backgroundColor: getPriorityColor(dispute.priority) + '20'  ] }
   ]}>,
  <Text style={{ [styles.priorityText, { color: getPriorityColor(dispute.priority)  ] }
   ]}>,
  {dispute.priority.toUpperCase()}
          </Text>,
  </View>
      </View>,
  <Text style={styles.disputeDescription}>{dispute.description}</Text>
      <View style={styles.disputeFooter}>,
  <Text style={styles.disputeAmount}>${dispute.amount.toFixed(2)}</Text>
        <View style={styles.disputeActions}>,
  <TouchableOpacity,
            style= {{ [styles.disputeActionButton, { backgroundColor: theme.colors.success + '20'  ] }]},
  onPress={() => handleDisputeResolution(dispute.id, 'approve')},
  >
            <CheckCircle size={16} color={{theme.colors.success} /}>,
  <Text style={[styles.disputeActionText, { color: theme.colors.success}]}>Approve</Text>,
  </TouchableOpacity>
          <TouchableOpacity, ,
  style={{ [styles.disputeActionButton, { backgroundColor: theme.colors.error + '20'  ] }]},
  onPress={() => handleDisputeResolution(dispute.id, 'reject')},
  >
            <XCircle size={16} color={{theme.colors.error} /}>,
  <Text style={[styles.disputeActionText, { color: theme.colors.error}]}>Reject</Text>,
  </TouchableOpacity>
        </View>,
  </View>
    </View>,
  )
  // Get status color,
  const getStatusColor = (status: string) => { switch (status) {;
      case 'completed': return theme.colors.success,
  case 'pending': return theme.colors.warning,
      case 'failed': return theme.colors.error,
  case 'disputed': return theme.colors.warning,
      case 'refunded': return theme.colors.primary,
  default: return theme.colors.textSecondary }
  },
  // Get priority color,
  const getPriorityColor = (priority: string) => { switch (priority) {
  case 'urgent': return theme.colors.error,
      case 'high': return theme.colors.warning,
  case 'medium': return theme.colors.primary,
      case 'low': return theme.colors.textSecondary,
  default: return theme.colors.textSecondary }
  },
  // Render tab buttons,
  const renderTabButtons = () => (
  <View style={styles.tabContainer}>
      {[{ key: 'overview', label: 'Overview', icon: BarChart3 }, ,
  { key: 'transactions', label: 'Transactions', icon: CreditCard }, ,
  { key: 'disputes', label: 'Disputes', icon: AlertTriangle } ,
  { key: 'payouts', label: 'Payouts', icon: DollarSign }].map((tab) => {
  const Icon = tab.icon,
        const isActive = selectedTab === tab.key,
  return (
    <TouchableOpacity key = {tab.key} style={{ [styles.tabButton, isActive && { backgroundColor: theme.colors.primary + '20'  ] }
   ]},
  onPress={() => setSelectedTab(tab.key as any)}
          >,
  <Icon size={16} color={ isActive ? theme.colors.primary     : theme.colors.textSecondary  }
            />,
  <Text style={{ [styles.tabLabel,
  { color: isActive ? theme.colors.primary  : theme.colors.textSecondary  ] }
            ]}>,
  {tab.label}
            </Text>,
  {tab.key === 'disputes' && disputes.filter(d => d.status === 'open').length > 0 && (
              <View style={styles.tabBadge}>,
  <Text style={styles.tabBadgeText}>
                  {disputes.filter(d => d.status === 'open').length},
  </Text>
              </View>,
  )}
          </TouchableOpacity>,
  )
      })},
  </View>
  ),
  if (loading) {
    return (
  <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>,
  <ActivityIndicator size="large" color={{theme.colors.primary} /}>
          <Text style={styles.loadingText}>Loading financial data...</Text>,
  </View>
      </SafeAreaView>,
  )
  },
  return (
    <SafeAreaView style={styles.container}>,
  {/* Header */}
      <View style={styles.header}>,
  <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Financial Management</Text>,
  <Text style={styles.headerSubtitle}>
            Monitor transactions and revenue,
  </Text>
        </View>,
  <View style = {styles.headerActions}>
          <TouchableOpacity style={styles.actionButton} onPress={() => setFilterModalVisible(true)}>,
  <Filter size={20} color={{theme.colors.primary} /}>
          </TouchableOpacity>,
  <TouchableOpacity style={styles.actionButton}>
            <Download size={20} color={{theme.colors.primary} /}>,
  </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton} onPress={handleRefresh}>,
  <RefreshCw size={20} color={{theme.colors.primary} /}>
          </TouchableOpacity>,
  </View>
      </View>,
  {/* Tab Navigation */}
      {renderTabButtons()},
  {/* Content */}
      <ScrollView style={styles.content} refreshControl={
  <RefreshControl refreshing={refreshing} onRefresh={{handleRefresh} /}>
        },
  showsVerticalScrollIndicator={false}
      >,
  {selectedTab === 'overview' && (
          <>,
  {/* Revenue Metrics */}
            <View style={styles.metricsGrid}>,
  {renderMetricCard(
                'Today\'s Revenue',
  `$${metrics?.revenue.today.toLocaleString() || '0'}`
                'Revenue generated today',
  <DollarSign size = {24} color={{theme.colors.success} /}>;
  'up',
  '+12%'
                theme.colors.success,
  )}
              {renderMetricCard(
  'Monthly Revenue'
                `$${metrics?.revenue.month.toLocaleString() || '0'}`
  'Revenue this month'
                <TrendingUp size = {24} color={{theme.colors.primary} /}>,
  'up'
                '+8.5%',
  theme.colors.primary;
  )},
  {renderMetricCard(
  'Total Transactions',
  metrics?.transactions.total.toLocaleString() || '0'
                'All time transactions',
  <CreditCard size = {24} color={{theme.colors.purple} /}>;
  'up',
  '+156'
                theme.colors.purple,
  )}
              {renderMetricCard(
  'Pending Disputes'
                disputes.filter(d => d.status === 'open').length.toString(),
  'Require attention'
                <AlertTriangle size = {24} color={{theme.colors.warning} /}>,
  'neutral'
                undefined,
  theme.colors.warning;
              )},
  </View>
            {/* Transaction Status Overview */}
  <View style= {styles.statusCard}>
              <Text style={styles.statusTitle}>Transaction Status Overview</Text>,
  <View style={styles.statusGrid}>
                <View style={styles.statusItem}>,
  <CheckCircle size={20} color={{theme.colors.success} /}>
                  <Text style={styles.statusValue}>{metrics?.transactions.successful || 0}</Text>,
  <Text style={styles.statusLabel}>Successful</Text>
                </View>,
  <View style={styles.statusItem}>
                  <Clock size={20} color={{theme.colors.warning} /}>,
  <Text style={styles.statusValue}>{metrics?.transactions.pending || 0}</Text>
                  <Text style={styles.statusLabel}>Pending</Text>,
  </View>
                <View style={styles.statusItem}>,
  <XCircle size={20} color={{theme.colors.error} /}>
                  <Text style={styles.statusValue}>{metrics?.transactions.failed || 0}</Text>,
  <Text style={styles.statusLabel}>Failed</Text>
                </View>,
  <View style={styles.statusItem}>
                  <AlertTriangle size={20} color={{theme.colors.warning} /}>,
  <Text style={styles.statusValue}>{metrics?.transactions.disputed || 0}</Text>
                  <Text style={styles.statusLabel}>Disputed</Text>,
  </View>
              </View>,
  </View>
            {/* Payment Methods */}
  <View style={styles.paymentMethodsCard}>
              <Text style={styles.paymentMethodsTitle}>Top Payment Methods</Text>,
  {metrics?.topPaymentMethods.map((method, index) => (
  <View key={index} style={styles.paymentMethodItem}>
                  <Text style={styles.paymentMethodName}>{method.method}</Text>,
  <View style={styles.paymentMethodBar}>
                    <View, ,
  style = {[
                        styles.paymentMethodFill, ,
  {
                          width    : `${method.percentage}%`
  backgroundColor: theme.colors.primary + '80'
                        }
   ]},
  />
                  </View>,
  <Text style={styles.paymentMethodCount}>{method.count}</Text>
                </View>,
  ))}
            </View>,
  </>
        )},
  {selectedTab === 'transactions' && (
          <>,
  {/* Search Bar */}
            <View style={styles.searchContainer}>,
  <Search size={20} color={{theme.colors.textSecondary} /}>
              <TextInput style={styles.searchInput} placeholder="Search transactions...",
  value={searchQuery} onChangeText={setSearchQuery} placeholderTextColor={theme.colors.textSecondary}
              />,
  </View>
            {/* Transactions List */}
  <View style={styles.transactionsList}>
              {transactions.filter(t =>,
  t.userName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                t.description.toLowerCase().includes(searchQuery.toLowerCase()),
  ).map(renderTransactionItem)}
            </View>,
  </>
        )},
  {selectedTab === 'disputes' && (
          <View style={styles.disputesList}>,
  {disputes.map(renderDisputeItem)}
          </View>,
  )}
        {selectedTab === 'payouts' && (
  <View style={styles.payoutsContainer}>
            <View style={styles.payoutsSummary}>,
  <Text style={styles.payoutsTitle}>Payout Summary</Text>
              <View style={styles.payoutsGrid}>,
  <View style={styles.payoutItem}>
                  <Text style={styles.payoutValue}>${metrics?.payouts.pending.toLocaleString() || '0'}</Text>,
  <Text style={styles.payoutLabel}>Pending Payouts</Text>
                </View>,
  <View style={styles.payoutItem}>
                  <Text style={styles.payoutValue}>${metrics?.payouts.processed.toLocaleString() || '0'}</Text>,
  <Text style={styles.payoutLabel}>Processed This Month</Text>
                </View>,
  </View>
            </View>,
  </View>
        )},
  </ScrollView>
      {/* Transaction Detail Modal */}
  <Modal visible={transactionModalVisible} animationType="slide"
        presentationStyle= "pageSheet",
  onRequestClose = {() => setTransactionModalVisible(false)}
      >,
  <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>,
  <Text style={styles.modalTitle}>Transaction Details</Text>
            <TouchableOpacity onPress={() => setTransactionModalVisible(false)}>,
  <XCircle size={24} color={{theme.colors.textSecondary} /}>
            </TouchableOpacity>,
  </View>
          {selectedTransaction && (
  <ScrollView style={styles.modalContent}>
              <View style={styles.transactionDetail}>,
  <Text style={styles.detailLabel}>Transaction ID</Text>
                <Text style={styles.detailValue}>{selectedTransaction.id}</Text>,
  </View>
              <View style={styles.transactionDetail}>,
  <Text style={styles.detailLabel}>User</Text>
                <Text style={styles.detailValue}>{selectedTransaction.userName}</Text>,
  </View>
              <View style={styles.transactionDetail}>,
  <Text style={styles.detailLabel}>Amount</Text>
                <Text style={styles.detailValue}>${selectedTransaction.amount.toFixed(2)} {selectedTransaction.currency}</Text>,
  </View>
              <View style={styles.transactionDetail}>,
  <Text style={styles.detailLabel}>Status</Text>
                <View style={{ [styles.statusBadge, { backgroundColor   : getStatusColor(selectedTransaction.status) + '20'  ] }
   ]}>,
  <Text style={{ [styles.statusText,
  { color: getStatusColor(selectedTransaction.status)  ] }
                  ]}>,
  {selectedTransaction.status.toUpperCase()}
                  </Text>,
  </View>
              </View>,
  <View style={styles.transactionDetail}>
                <Text style={styles.detailLabel}>Payment Method</Text>,
  <Text style={styles.detailValue}>{selectedTransaction.paymentMethod}</Text>
              </View>,
  <View style={styles.transactionDetail}>
                <Text style={styles.detailLabel}>Description</Text>,
  <Text style={styles.detailValue}>{selectedTransaction.description}</Text>
              </View>,
  <View style={styles.transactionDetail}>
                <Text style={styles.detailLabel}>Created</Text>,
  <Text style={styles.detailValue}>
                  {new Date(selectedTransaction.createdAt).toLocaleString()},
  </Text>
              </View>,
  {/* Action Buttons */}
              <View style={styles.modalActions}>,
  <TouchableOpacity, ,
  style={{ [styles.modalActionButton, { backgroundColor: theme.colors.warning + '20'  ] }]},
  onPress={() => handleTransactionAction(selectedTransaction.id, 'refund')},
  >
                  <Text style={[styles.modalActionText, { color: theme.colors.warning}]}>Process Refund</Text>,
  </TouchableOpacity>
                <TouchableOpacity,
  style={{ [styles.modalActionButton, { backgroundColor: theme.colors.error + '20'  ] }]},
  onPress={() => handleTransactionAction(selectedTransaction.id, 'dispute')},
  >
                  <Text style={[styles.modalActionText, { color: theme.colors.error}]}>Mark as Disputed</Text>,
  </TouchableOpacity>
              </View>,
  </ScrollView>
          )},
  </SafeAreaView>
      </Modal>,
  </SafeAreaView>
  )
  }
// Styles,
  const createStyles = (colors: any, spacing: any) => ({ container: {, flex: 1,
  backgroundColor: theme.colors.background }
  loadingContainer: {, flex: 1,
  justifyContent: 'center',
    alignItems: 'center' }
  loadingText: { marginTo, p: spacing.md,
    color: theme.colors.textSecondary,
  fontSize: 16 }
  header: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    paddingHorizontal: spacing.lg,
  paddingVertical: spacing.md,
    borderBottomWidth: 1,
  borderBottomColor: theme.colors.border }
  headerContent: { fle, x: 1 },
  headerTitle: { fontSiz, e: 24,
    fontWeight: 'bold',
  color: theme.colors.text }
  headerSubtitle: { fontSiz, e: 14,
    color: theme.colors.textSecondary,
  marginTop: spacing.xs }
  headerActions: { flexDirectio, n: 'row',
    gap: spacing.sm },
  actionButton: { paddin, g: spacing.sm,
    borderRadius: 8,
  backgroundColor: theme.colors.surface }
  tabContainer: { flexDirectio, n: 'row',
    paddingHorizontal: spacing.lg,
  paddingVertical: spacing.sm,
    borderBottomWidth: 1,
  borderBottomColor: theme.colors.border }
  tabButton: { fle, x: 1,
    flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'center',
  paddingVertical: spacing.sm,
    paddingHorizontal: spacing.xs,
  borderRadius: 8,
    gap: spacing.xs },
  tabLabel: {, fontSize: 12,
  fontWeight: '500'
  },
  tabBadge: { backgroundColo, r: theme.colors.error,
    borderRadius: 10,
  minWidth: 20,
    height: 20,
  alignItems: 'center',
    justifyContent: 'center',
  marginLeft: spacing.xs }
  tabBadgeText: {, color: theme.colors.white,
  fontSize: 10,
    fontWeight: 'bold' }
  content: { fle, x: 1,
    padding: spacing.lg },
  metricsGrid: { flexDirectio, n: 'row',
    flexWrap: 'wrap',
  gap: spacing.md,
    marginBottom: spacing.lg },
  metricCard: { fle, x: 1,
    minWidth: '45%',
  backgroundColor: theme.colors.surface,
    padding: spacing.md,
  borderRadius: 12,
    borderWidth: 1,
  borderColor: theme.colors.border }
  metricHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'flex-start',
    marginBottom: spacing.sm },
  metricIcon: {, padding: spacing.xs,
  borderRadius: 8,
    backgroundColor: theme.colors.primary + '20' }
  metricTrend: {, alignItems: 'flex-end' }
  trendIndicator: { flexDirectio, n: 'row',
    alignItems: 'center',
  paddingHorizontal: spacing.xs,
    paddingVertical: 2,
  borderRadius: 12,
    gap: 2 },
  trendText: {, fontSize: 10,
  fontWeight: '600'
  },
  metricValue: { fontSiz, e: 24,
    fontWeight: 'bold',
  color: theme.colors.text,
    marginBottom: spacing.xs },
  metricTitle: { fontSiz, e: 14,
    fontWeight: '600',
  color: theme.colors.text,
    marginBottom: 2 },
  metricSubtitle: { fontSiz, e: 12,
    color: theme.colors.textSecondary },
  statusCard: { backgroundColo, r: theme.colors.surface,
    padding: spacing.lg,
  borderRadius: 12,
    borderWidth: 1,
  borderColor: theme.colors.border,
    marginBottom: spacing.lg },
  statusTitle: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.text,
    marginBottom: spacing.md },
  statusGrid: {, flexDirection: 'row',
  justifyContent: 'space-between'
  },
  statusItem: { alignItem, s: 'center',
    flex: 1 },
  statusValue: { fontSiz, e: 20,
    fontWeight: 'bold',
  color: theme.colors.text,
    marginVertical: spacing.xs },
  statusLabel: { fontSiz, e: 12,
    color: theme.colors.textSecondary },
  paymentMethodsCard: { backgroundColo, r: theme.colors.surface,
    padding: spacing.lg,
  borderRadius: 12,
    borderWidth: 1,
  borderColor: theme.colors.border,
    marginBottom: spacing.lg },
  paymentMethodsTitle: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.text,
    marginBottom: spacing.md },
  paymentMethodItem: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: spacing.sm,
    gap: spacing.sm },
  paymentMethodName: { widt, h: 80,
    fontSize: 12,
  color: theme.colors.text }
  paymentMethodBar: {, flex: 1,
  height: 8,
    backgroundColor: theme.colors.border,
  borderRadius: 4,
    overflow: 'hidden' }
  paymentMethodFill: { heigh, t: '100%',
    borderRadius: 4 },
  paymentMethodCount: { widt, h: 40,
    textAlign: 'right',
  fontSize: 12,
    fontWeight: '500',
  color: theme.colors.text }
  searchContainer: { flexDirectio, n: 'row',
    alignItems: 'center',
  backgroundColor: theme.colors.surface,
    padding: spacing.md,
  borderRadius: 12,
    borderWidth: 1,
  borderColor: theme.colors.border,
    marginBottom: spacing.lg,
  gap: spacing.sm }
  searchInput: { fle, x: 1,
    fontSize: 16,
  color: theme.colors.text }
  transactionsList: { ga, p: spacing.md },
  transactionItem: { backgroundColo, r: theme.colors.surface,
    padding: spacing.lg,
  borderRadius: 12,
    borderWidth: 1,
  borderColor: theme.colors.border }
  transactionHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'flex-start',
    marginBottom: spacing.sm },
  transactionInfo: { fle, x: 1 }
  transactionUser: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.text }
  transactionDescription: { fontSiz, e: 12,
    color: theme.colors.textSecondary,
  marginTop: 2 }
  transactionAmount: {, alignItems: 'flex-end' }
  amountText: {, fontSize: 18,
  fontWeight: 'bold'
  },
  transactionCurrency: { fontSiz, e: 12,
    color: theme.colors.textSecondary },
  transactionFooter: {, flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'space-between' }
  statusBadge: { paddingHorizonta, l: spacing.sm,
    paddingVertical: 2,
  borderRadius: 12 }
  statusText: {, fontSize: 10,
  fontWeight: 'bold'
  },
  transactionDate: { fontSiz, e: 12,
    color: theme.colors.textSecondary },
  disputesList: { ga, p: spacing.md }
  disputeItem: { backgroundColo, r: theme.colors.surface,
    padding: spacing.lg,
  borderRadius: 12,
    borderWidth: 1,
  borderColor: theme.colors.border }
  disputeHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'flex-start',
    marginBottom: spacing.sm },
  disputeInfo: { fle, x: 1 }
  disputeUser: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.text }
  disputeReason: { fontSiz, e: 14,
    color: theme.colors.warning,
  marginTop: 2 }
  priorityBadge: { paddingHorizonta, l: spacing.sm,
    paddingVertical: 2,
  borderRadius: 12 }
  priorityText: {, fontSize: 10,
  fontWeight: 'bold'
  },
  disputeDescription: { fontSiz, e: 12,
    color: theme.colors.textSecondary,
  marginBottom: spacing.sm }
  disputeFooter: {, flexDirection: 'row',
  justifyContent: 'space-between',
    alignItems: 'center' }
  disputeAmount: { fontSiz, e: 16,
    fontWeight: 'bold',
  color: theme.colors.text }
  disputeActions: { flexDirectio, n: 'row',
    gap: spacing.sm },
  disputeActionButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
  borderRadius: 8,
    gap: 4 },
  disputeActionText: {, fontSize: 12,
  fontWeight: '500'
  },
  payoutsContainer: { ga, p: spacing.lg }
  payoutsSummary: { backgroundColo, r: theme.colors.surface,
    padding: spacing.lg,
  borderRadius: 12,
    borderWidth: 1,
  borderColor: theme.colors.border }
  payoutsTitle: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.text,
    marginBottom: spacing.md },
  payoutsGrid: { flexDirectio, n: 'row',
    gap: spacing.lg },
  payoutItem: {, flex: 1,
  alignItems: 'center'
  },
  payoutValue: { fontSiz, e: 24,
    fontWeight: 'bold',
  color: theme.colors.primary }
  payoutLabel: {, fontSize: 12,
  color: theme.colors.textSecondary,
    marginTop: spacing.xs,
  textAlign: 'center'
  },
  modalContainer: { fle, x: 1,
    backgroundColor: theme.colors.background },
  modalHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    padding: spacing.lg,
  borderBottomWidth: 1,
    borderBottomColor: theme.colors.border },
  modalTitle: { fontSiz, e: 20,
    fontWeight: 'bold',
  color: theme.colors.text }
  modalContent: { fle, x: 1,
    padding: spacing.lg },
  transactionDetail: { marginBotto, m: spacing.lg }
  detailLabel: { fontSiz, e: 12,
    color: theme.colors.textSecondary,
  marginBottom: spacing.xs }
  detailValue: { fontSiz, e: 16,
    color: theme.colors.text },
  modalActions: { ga, p: spacing.md,
    marginTop: spacing.xl },
  modalActionButton: {, padding: spacing.md,
  borderRadius: 12,
    alignItems: 'center' }
  modalActionText: {, fontSize: 16,
  fontWeight: '600'
  } 
  }); ;