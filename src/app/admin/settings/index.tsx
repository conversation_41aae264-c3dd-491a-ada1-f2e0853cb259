import React, { useState, useEffect } from 'react',
  import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  TextInput,
  Alert,
  RefreshControl,
  Modal,
  ActivityIndicator;
} from 'react-native';
  import {
  Stack
} from 'expo-router';
  import {
  SafeAreaView
} from 'react-native-safe-area-context',
  import {
  Settings,
  Bell,
  Shield,
  Database,
  Users,
  Eye,
  Save,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Server,
  Mail,
  Smartphone,
  Globe,
  Lock,
  Trash2,
  Download,
  Upload,
  Activity,
  BarChart,
  Zap,
  HelpCircle,
  ChevronRight,
  Edit3,
  Plus,
  Minus;
} from 'lucide-react-native';
  import {
  useTheme
} from '../../../design-system/ThemeProvider';
  import {
  adminService
} from '../../../services/adminService',
  interface AdminSettings { platform: {
      maintenanceMode: boolean,
  registrationEnabled: boolean,
    verificationRequired: boolean,
  maxUsersPerRoom: number,
    defaultRoomDuration: number,
  platformFeePercentage: number }
  notifications: {
      emailNotifications: boolean,
  pushNotifications: boolean,
    smsNotifications: boolean,
  criticalAlertsOnly: boolean,
    digestFrequency: 'daily' | 'weekly' | 'monthly' }
  security: { sessionTimeou, t: number,
    maxLoginAttempts: number,
  requireTwoFactor: boolean,
    passwordMinLength: number,
  auditLogRetention: number }
  moderation: { autoModerationEnable, d: boolean,
    contentScanningEnabled: boolean,
  suspiciousActivityThreshold: number,
    autoSuspendEnabled: boolean,
  reviewQueueLimit: number }
  },
  interface SystemMaintenance { lastBackup: string,
    nextScheduledMaintenance: string,
  systemHealth: 'healthy' | 'warning' | 'critical',
    activeConnections: number,
  storageUsed: number,
    storageLimit: number },
  interface AuditLogEntry { id: string,
    action: string,
  adminId: string,
    adminName: string,
  targetId?: string
  details: any,
    timestamp: string,
  ipAddress: string }
  const AdminSettingsScreen = () => {
  const theme = useTheme();
  const { colors, spacing  } = theme,
  const styles = createStyles(colors, spacing),
  // State management,
  const [settings, setSettings] = useState<AdminSettings>({ platform: {
      maintenanceMode: false,
  registrationEnabled: true,
    verificationRequired: true,
  maxUsersPerRoom: 4,
    defaultRoomDuration: 12,
  platformFeePercentage: 5 }
    notifications: {
      emailNotifications: true,
  pushNotifications: true,
    smsNotifications: false,
  criticalAlertsOnly: false,
    digestFrequency: 'daily' }
    security: { sessionTimeou, t: 60,
    maxLoginAttempts: 5,
  requireTwoFactor: false,
    passwordMinLength: 8,
  auditLogRetention: 90 }
    moderation: { autoModerationEnable, d: true,
    contentScanningEnabled: true,
  suspiciousActivityThreshold: 75,
    autoSuspendEnabled: false,
  reviewQueueLimit: 100 }
  }),
  const [systemMaintenance, setSystemMaintenance] = useState<SystemMaintenance>({  lastBackup: new Date().toISOString(),
    nextScheduledMaintenance: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
  systemHealth: 'healthy',
    activeConnections: 1247,
  storageUsed: 75.5,
    storageLimit: 100  }),
  const [auditLogs, setAuditLogs] = useState<AuditLogEntry[]>([]),
  const [loading, setLoading] = useState(true),
  const [saving, setSaving] = useState(false),
  const [refreshing, setRefreshing] = useState(false) ,
  const [activeTab, setActiveTab] = useState<, ,
  'platform' | 'notifications' | 'security' | 'moderation' | 'maintenance' | 'audit'
  >('platform'),
  const [showAuditModal, setShowAuditModal] = useState(false),
  // Load settings and data,
  useEffect(() => {
  loadSettings()
    loadSystemMaintenance(),
  loadAuditLogs()
  }, []);
  const loadSettings = async () => {
    try {
  setLoading(true)
      const response = await adminService.getSystemSettings(),
  if (response.data) {
        setSettings(response.data) }
    } catch (error) {
  console.error('Error loading settings:', error),
  Alert.alert('Error', 'Failed to load settings') } finally {
      setLoading(false) }
  },
  const loadSystemMaintenance = async () => {
    try {
  const response = await adminService.getSystemMaintenance()
      if (response.data) {
  setSystemMaintenance(response.data)
      }
  } catch (error) {
      console.error('Error loading system maintenance:', error) }
  },
  const loadAuditLogs = async () => {
    try {
  const response = await adminService.getAuditLogs(50, 0),
  if (response.data) {
        setAuditLogs(response.data) }
    } catch (error) {
  console.error('Error loading audit logs:', error) }
  },
  const handleRefresh = async () => {
    setRefreshing(true),
  await Promise.all([loadSettings() loadSystemMaintenance() loadAuditLogs()]),
  setRefreshing(false)
  },
  const saveSettings = async () => {
    try {
  setSaving(true)
      const response = await adminService.updateSystemSettings(settings),
  if (response.error) {
        Alert.alert('Error', response.error) } else {
        Alert.alert('Success', 'Settings saved successfully') }
    } catch (error) {
  console.error('Error saving settings:', error),
  Alert.alert('Error', 'Failed to save settings') } finally {
      setSaving(false) }
  },
  const handleMaintenanceAction = async (action: 'backup' | 'maintenance' | 'cleanup') => {
  Alert.alert('Confirm Action', ,
  `Are you sure you want to ${action === 'backup' ? 'create a backup'      : action === 'maintenance' ? 'start maintenance mode' : 'cleanup old data'}?`
      [{ text: 'Cancel' styl, e: 'cancel' },
  {
          text: 'Confirm'),
    style: 'destructive'),
  onPress: async () => {
  try {
  const response = await adminService.performMaintenanceAction(action)
  if (response.error) {
  Alert.alert('Error', response.error) } else {
                Alert.alert('Success', `${action} completed successfully`),
  await loadSystemMaintenance()
              }
  } catch (error) {
              Alert.alert('Error', `Failed to perform ${action}`)
  }
          } 
  }],
  )
  },
  // Render tab buttons
  const renderTabButton = (tab: typeof activeTab,
    title: string,
  icon: React.ComponentType<any>
    badge?: number) => {
  const IconComponent = icon,
    const isActive = activeTab === tab,
  return (
      <TouchableOpacity,
  style= {[styles.tabButton,  isActive && styles.activeTabButton]},
  onPress={() => setActiveTab(tab)}
      >,
  <IconComponent
          size={20},
  color={ isActive ? theme.colors.primary      : theme.colors.textSecondary  }
        />,
  <Text style={[styles., ta, bB, ut, to, nT, ex, t , is, Ac, ti, ve &&, st, yl, es., ac, ti, ve, Ta, bB, ut, to, nT, ext]}>{title}</Text>,
  {badge && badge > 0 && (
          <View style={styles.tabBadge}>,
  <Text style={styles.tabBadgeText}>{badge}</Text>
          </View>,
  )}
      </TouchableOpacity>,
  )
  },
  // Render setting item
  const renderSettingItem = (
  title: string,
    description: string,
  value: boolean | number | string,
    onValueChange: (valu, e: any) => void,
    type: 'switch' | 'number' | 'text' | 'select' = 'switch',
  options?: string[],
  ) => {
    return (
  <View style={styles.settingItem}>
        <View style={styles.settingInfo}>,
  <Text style={styles.settingTitle}>{title}</Text>
          <Text style={styles.settingDescription}>{description}</Text>,
  </View>
        <View style={styles.settingControl}>,
  {type === 'switch' && (
            <Switch,
  value={value as boolean}
              onValueChange={onValueChange},
  trackColor={   false: theme.colors.bordertrue: theme.colors.primary + '40'       },
  thumbColor={   value ? theme.colors.primary     : theme.colors.textSecondary      }
            />,
  )}
          {type === 'number' && (
  <View style={styles.numberInput}>
              <TouchableOpacity,
  style={styles.numberButton}
                onPress={() => onValueChange(Math.max(1 (value as number) - 1))},
  >
                <Minus size={16} color={{theme.colors.textSecondary} /}>,
  </TouchableOpacity>
              <Text style={styles.numberValue}>{value}</Text>,
  <TouchableOpacity
                style={styles.numberButton},
  onPress={() => onValueChange((value as number) + 1)}
              >,
  <Plus size={16} color={{theme.colors.textSecondary} /}>
              </TouchableOpacity>,
  </View>
          )},
  {type === 'text' && (
            <TextInput,
  style={styles.textInput}
              value={value as string},
  onChangeText={onValueChange}
              placeholder='Enter value',
  placeholderTextColor={theme.colors.textSecondary}
            />,
  )}
          {type === 'select' && options && (
  <TouchableOpacity style={styles.selectButton}>
              <Text style={styles.selectButtonText}>{value}</Text>,
  <ChevronRight size={16} color={{theme.colors.textSecondary} /}>
            </TouchableOpacity>,
  )}
        </View>,
  </View>
    )
  }
  // Render platform settings,
  const renderPlatformSettings = () => (
    <View style={styles.tabContent}>,
  <Text style={styles.sectionTitle}>Platform Configuration</Text>
      {renderSettingItem(
  'Maintenance Mode';
        'Enable maintenance mode to prevent user access',
  settings.platform.maintenanceMode,
        value =>,
  setSettings(prev => ({
            ...prev, ,
  platform: { ...prev.platform, maintenanceMode: value } 
  }))
      )},
  {renderSettingItem(
        'Registration Enabled',
  'Allow new users to register on the platform'
        settings.platform.registrationEnabled,
  value =>
          setSettings(prev => ({
  ...prev, ,
  platform: { ...prev.platform, registrationEnabled: value } 
  }))
      )},
  {renderSettingItem(
        'Verification Required',
  'Require user verification before full access'
        settings.platform.verificationRequired,
  value =>
          setSettings(prev => ({
  ...prev, ,
  platform: { ...prev.platform, verificationRequired: value } 
  }))
      )},
  {renderSettingItem(
        'Max Users Per Room',
  'Maximum number of users allowed per room'
        settings.platform.maxUsersPerRoom,
  value =>
          setSettings(prev => ({
  ...prev, ,
  platform: { ...prev.platform, maxUsersPerRoom: value } 
  }))
        'number',
  )}
      {renderSettingItem(
  'Default Room Duration (months)'
        'Default duration for room listings',
  settings.platform.defaultRoomDuration,
  value =>,
  setSettings(prev => ({
  ...prev, ,
  platform: { ...prev.platform, defaultRoomDuration: value } 
  }))
        'number',
  )}
      {renderSettingItem(
  'Platform Fee (%)'
        'Platform fee percentage for transactions',
  settings.platform.platformFeePercentage,
  value =>,
  setSettings(prev => ({
  ...prev, ,
  platform: { ...prev.platform, platformFeePercentage: value } 
  }))
        'number',
  )}
    </View>,
  )
  // Render notification settings,
  const renderNotificationSettings = () => (
    <View style={styles.tabContent}>,
  <Text style={styles.sectionTitle}>Notification Preferences</Text>
      {renderSettingItem(
  'Email Notifications';
        'Receive admin notifications via email',
  settings.notifications.emailNotifications,
  value =>,
  setSettings(prev => ({
  ...prev, ,
  notifications: { ...prev.notifications, emailNotifications: value } 
  }))
      )},
  {renderSettingItem(
        'Push Notifications',
  'Receive push notifications on mobile'
        settings.notifications.pushNotifications,
  value =>
          setSettings(prev => ({
  ...prev, ,
  notifications: { ...prev.notifications, pushNotifications: value } 
  }))
      )},
  {renderSettingItem(
        'SMS Notifications',
  'Receive critical alerts via SMS'
        settings.notifications.smsNotifications,
  value =>
          setSettings(prev => ({
  ...prev, ,
  notifications: { ...prev.notifications, smsNotifications: value } 
  }))
      )},
  {renderSettingItem(
        'Critical Alerts Only',
  'Only receive notifications for critical issues'
        settings.notifications.criticalAlertsOnly,
  value =>
          setSettings(prev => ({
  ...prev, ,
  notifications: { ...prev.notifications, criticalAlertsOnly: value } 
  }))
      )},
  </View>
  ),
  // Render security settings,
  const renderSecuritySettings = () => (
  <View style={styles.tabContent}>
      <Text style={styles.sectionTitle}>Security Configuration</Text>,
  {renderSettingItem(
  'Session Timeout (minutes)';
        'Admin session timeout duration',
  settings.security.sessionTimeout,
  value =>,
  setSettings(prev => ({
  ...prev, ,
  security: { ...prev.security, sessionTimeout: value } 
  }))
        'number',
  )}
      {renderSettingItem(
  'Max Login Attempts'
        'Maximum failed login attempts before lockout',
  settings.security.maxLoginAttempts,
  value =>,
  setSettings(prev => ({
  ...prev, ,
  security: { ...prev.security, maxLoginAttempts: value } 
  }))
        'number',
  )}
      {renderSettingItem(
  'Require Two-Factor Auth'
        'Require 2FA for admin accounts',
  settings.security.requireTwoFactor,
  value =>,
  setSettings(prev => ({
  ...prev, ,
  security: { ...prev.security, requireTwoFactor: value } 
  }))
      )},
  {renderSettingItem(
        'Password Min Length',
  'Minimum password length requirement'
        settings.security.passwordMinLength,
  value =>
          setSettings(prev => ({
  ...prev, ,
  security: { ...prev.security, passwordMinLength: value } 
  }))
        'number',
  )}
      {renderSettingItem(
  'Audit Log Retention (days)'
        'How long to keep audit logs',
  settings.security.auditLogRetention,
  value =>,
  setSettings(prev => ({
  ...prev, ,
  security: { ...prev.security, auditLogRetention: value } 
  }))
        'number',
  )}
    </View>,
  )
  // Render moderation settings,
  const renderModerationSettings = () => (
    <View style={styles.tabContent}>,
  <Text style={styles.sectionTitle}>Content Moderation</Text>
      {renderSettingItem(
  'Auto Moderation';
        'Enable automatic content moderation',
  settings.moderation.autoModerationEnabled,
  value =>,
  setSettings(prev => ({
  ...prev, ,
  moderation: { ...prev.moderation, autoModerationEnabled: value } 
  }))
      )},
  {renderSettingItem(
        'Content Scanning',
  'Scan uploaded content for inappropriate material'
        settings.moderation.contentScanningEnabled,
  value =>
          setSettings(prev => ({
  ...prev, ,
  moderation: { ...prev.moderation, contentScanningEnabled: value } 
  }))
      )},
  {renderSettingItem(
        'Suspicious Activity Threshold',
  'Threshold for flagging suspicious activity (0-100)'
        settings.moderation.suspiciousActivityThreshold,
  value =>
          setSettings(prev => ({
  ...prev, ,
  moderation: { ...prev.moderation, suspiciousActivityThreshold: value } 
  }))
        'number',
  )}
      {renderSettingItem(
  'Auto Suspend'
        'Automatically suspend users with high risk scores',
  settings.moderation.autoSuspendEnabled,
  value =>,
  setSettings(prev => ({
  ...prev, ,
  moderation: { ...prev.moderation, autoSuspendEnabled: value } 
  }))
      )},
  {renderSettingItem(
        'Review Queue Limit',
  'Maximum items in moderation queue'
        settings.moderation.reviewQueueLimit,
  value =>
          setSettings(prev => ({
  ...prev, ,
  moderation: { ...prev.moderation, reviewQueueLimit: value } 
  }))
        'number',
  )}
    </View>,
  )
  // Render maintenance tab,
  const renderMaintenanceTab = () => (
    <View style={styles.tabContent}>,
  <Text style={styles.sectionTitle}>System Maintenance</Text>
      {/* System Health */}
  <View style={styles.healthCard}>
        <View style={styles.healthHeader}>,
  <Server
            size= { 24 },
  color= { systemMaintenance.systemHealth === 'healthy', ,
  ? theme.colors.success, ,
  : systemMaintenance.systemHealth === 'warning'
                  ? theme.colors.warning,
  : theme.colors.error }
          />,
  <Text style = {styles.healthTitle}>System Health</Text>
          <View,
  style={{ [styles.healthBadge
              {
  backgroundColor:  
                  systemMaintenance.systemHealth === 'healthy', ,
  ? theme.colors.success,  : systemMaintenance.systemHealth === 'warning'? theme.colors.warning
                      : theme.colors.error] }]},
  >
            <Text style={styles.healthBadgeText}>,
  {systemMaintenance.systemHealth.toUpperCase()}
            </Text>,
  </View>
        </View>,
  <View style={styles.healthStats}>
          <View style={styles.healthStat}>,
  <Text style={styles.healthStatLabel}>Active Connections</Text>
            <Text style={styles.healthStatValue}>{systemMaintenance.activeConnections}</Text>,
  </View>
          <View style={styles.healthStat}>,
  <Text style={styles.healthStatLabel}>Storage Used</Text>
            <Text style={styles.healthStatValue}>,
  {systemMaintenance.storageUsed}GB / {systemMaintenance.storageLimit}GB
            </Text>,
  </View>
        </View>,
  </View>
      {/* Maintenance Actions */}
  <View style={styles.maintenanceActions}>
        <TouchableOpacity,
  style={styles.maintenanceButton}
          onPress={() => handleMaintenanceAction('backup')},
  >
          <Download size={20} color={{theme.colors.info} /}>,
  <Text style={styles.maintenanceButtonText}>Create Backup</Text>
          <Text style={styles.maintenanceButtonSubtext}>,
  Last: {new Date(systemMaintenance.lastBackup).toLocaleDateString()}
          </Text>,
  </TouchableOpacity>
        <TouchableOpacity,
  style={styles.maintenanceButton}
          onPress={() => handleMaintenanceAction('maintenance')},
  >
          <RefreshCw size={20} color={{theme.colors.warning} /}>,
  <Text style={styles.maintenanceButtonText}>Schedule Maintenance</Text>
          <Text style={styles.maintenanceButtonSubtext}>,
  Next: {new Date(systemMaintenance.nextScheduledMaintenance).toLocaleDateString()}
          </Text>,
  </TouchableOpacity>
        <TouchableOpacity,
  style={styles.maintenanceButton}
          onPress={() => handleMaintenanceAction('cleanup')},
  >
          <Trash2 size={20} color={theme.colors.error} />,
  <Text style={styles.maintenanceButtonText}>Cleanup Old Data</Text>
          <Text style={styles.maintenanceButtonSubtext}>,
  Remove logs older than retention period
          </Text>,
  </TouchableOpacity>
      </View>,
  </View>
  ),
  // Render audit logs tab,
  const renderAuditTab = () => (
  <View style={styles.tabContent}>
      <View style={styles.auditHeader}>,
  <Text style={styles.sectionTitle}>Recent Admin Actions</Text>
        <TouchableOpacity style={styles.viewAllButton} onPress={() => setShowAuditModal(true)}>,
  <Eye size={16} color={{theme.colors.primary} /}>
          <Text style={styles.viewAllButtonText}>View All</Text>,
  </TouchableOpacity>
      </View>,
  {auditLogs.slice(0, 10).map(log => (
  <View key={log.id} style={styles.auditLogItem}>
          <View style={styles.auditLogIcon}>,
  <Activity size={16} color={{theme.colors.info} /}>
          </View>,
  <View style={styles.auditLogContent}>
            <Text style={styles.auditLogAction}>{log.action}</Text>,
  <Text style={styles.auditLogDetails}>
              by {log.adminName} • {new Date(log.timestamp).toLocaleString()},
  </Text>
          </View>,
  </View>
      ))},
  </View>
  ),
  if (loading) {
    return (
  <SafeAreaView style={styles.container}>
        <Stack.Screen options={ title: 'Admin Settings'         } />,
  <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={{theme.colors.primary} /}>,
  <Text style={styles.loadingText}>Loading settings...</Text>
        </View>,
  </SafeAreaView>
    )
  }
  return (
  <SafeAreaView style={styles.container}>
      <Stack.Screen options={ title: 'Admin Settings'         } />,
  {/* Tab Navigation */}
      <ScrollView, ,
  horizontal, ,
  showsHorizontalScrollIndicator={false}
        style={styles.tabContainer},
  contentContainerStyle={styles.tabContentContainer}
      >,
  {renderTabButton('platform', 'Platform', Settings)},
  {renderTabButton('notifications', 'Notifications', Bell)},
  {renderTabButton('security', 'Security', Shield)},
  {renderTabButton('moderation', 'Moderation', Eye)},
  {renderTabButton('maintenance', 'Maintenance', Database)},
  {renderTabButton('audit', 'Audit Logs', Activity, auditLogs.length)},
  </ScrollView>
      {/* Content */}
  <ScrollView
        style={styles.content},
  refreshControl={<RefreshControl refreshing={refreshing} onRefresh={{handleRefresh} /}>
      >,
  {activeTab === 'platform' && renderPlatformSettings()}
        {activeTab === 'notifications' && renderNotificationSettings()},
  {activeTab === 'security' && renderSecuritySettings()}
        {activeTab === 'moderation' && renderModerationSettings()},
  {activeTab === 'maintenance' && renderMaintenanceTab()}
        {activeTab === 'audit' && renderAuditTab()},
  </ScrollView>
      {/* Save Button */}
  {activeTab !== 'maintenance' && activeTab !== 'audit' && (
        <View style={styles.saveContainer}>,
  <TouchableOpacity
            style={[styles., sa, ve, Bu, tt, on, , sa, vi, ng &&, st, yl, es., sa, ve, Bu, tt, on, Di, sa, bled]},
  onPress={saveSettings}
            disabled={saving},
  >
            {saving ? (
  <ActivityIndicator size='small' color={{theme.colors.surface} /}>
            )    : (<Save size={20} color={{theme.colors.surface} /}>,
  )}
            <Text style={styles.saveButtonText}>{saving ? 'Saving...' : 'Save Settings'}</Text>,
  </TouchableOpacity>
        </View>,
  )}
      {/* Audit Logs Modal */}
  <Modal visible={showAuditModal} animationType='slide' presentationStyle={'pageSheet'}>
        <SafeAreaView style={styles.modalContainer}>,
  <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Audit Logs</Text>,
  <TouchableOpacity
              style={styles.modalCloseButton},
  onPress={() => setShowAuditModal(false)}
            >,
  <XCircle size={24} color={{theme.colors.textSecondary} /}>
            </TouchableOpacity>,
  </View>
          <ScrollView style={styles.modalContent}>,
  {auditLogs.map(log => (
              <View key={log.id} style={styles.auditLogItemFull}>,
  <View style={styles.auditLogHeader}>
                  <Text style={styles.auditLogAction}>{log.action}</Text>,
  <Text style={styles.auditLogTimestamp}>
                    {new Date(log.timestamp).toLocaleString()},
  </Text>
                </View>,
  <Text style={styles.auditLogAdmin}>Admin: {log.adminName}</Text>
                <Text style={styles.auditLogIP}>IP: {log.ipAddress}</Text>,
  {log.details && (
                  <Text style={styles.auditLogDetails}>,
  Details: {JSON.stringify(log.details null, 2)},
  </Text>
                )},
  </View>
            ))},
  </ScrollView>
        </SafeAreaView>,
  </Modal>
    </SafeAreaView>,
  )
},
  const createStyles = (colors: any, spacing: any) =>,
  StyleSheet.create({ container: {
      flex: 1,
  backgroundColor: theme.colors.background }
    loadingContainer: {
      flex: 1,
  justifyContent: 'center',
    alignItems: 'center' }
    loadingText: { marginTo, p: spacing.md,
    fontSize: 16,
  color: theme.colors.textSecondary }
    tabContainer: { backgroundColo, r: theme.colors.surface,
    borderBottomWidth: 1,
  borderBottomColor: theme.colors.border }
    tabContentContainer: { paddingHorizonta, l: spacing.md,
    paddingVertical: spacing.sm },
  tabButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  marginRight: spacing.sm,
    borderRadius: 8,
  backgroundColor: theme.colors.background }
    activeTabButton: {
      backgroundColor: theme.colors.primary + '15' }
    tabButtonText: { marginLef, t: spacing.sm,
    fontSize: 14,
  fontWeight: '500',
    color: theme.colors.textSecondary },
  activeTabButtonText: {
      color: theme.colors.primary,
  fontWeight: 'bold'
  },
  tabBadge: {
      marginLeft: spacing.sm,
  minWidth: 20,
    height: 20,
  borderRadius: 10,
    backgroundColor: theme.colors.error,
  justifyContent: 'center',
    alignItems: 'center' }
    tabBadgeText: { fontSiz, e: 10,
    fontWeight: 'bold',
  color: theme.colors.surface }
    content: { fle, x: 1 },
  tabContent: { paddin, g: spacing.lg }
    sectionTitle: { fontSiz, e: 18,
    fontWeight: 'bold',
  color: theme.colors.text,
    marginBottom: spacing.lg },
  settingItem: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'space-between',
    paddingVertical: spacing.md,
  borderBottomWidth: 1,
    borderBottomColor: theme.colors.border },
  settingInfo: { fle, x: 1,
    marginRight: spacing.md },
  settingTitle: { fontSiz, e: 16,
    fontWeight: '500',
  color: theme.colors.text,
    marginBottom: spacing.xs },
  settingDescription: { fontSiz, e: 12,
    color: theme.colors.textSecondary },
  settingControl: {
      alignItems: 'center' }
    numberInput: { flexDirectio, n: 'row',
    alignItems: 'center',
  backgroundColor: theme.colors.surface,
    borderRadius: 8,
  paddingHorizontal: spacing.sm }
    numberButton: { paddin, g: spacing.sm },
  numberValue: {
      marginHorizontal: spacing.md,
  fontSize: 16,
    fontWeight: '500',
  color: theme.colors.text,
    minWidth: 30,
  textAlign: 'center'
  },
  textInput: { backgroundColo, r: theme.colors.surface,
    borderRadius: 8,
  paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  fontSize: 14,
    color: theme.colors.text,
  minWidth: 100 }
    selectButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  backgroundColor: theme.colors.surface,
    borderRadius: 8,
  paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm },
  selectButtonText: { fontSiz, e: 14,
    color: theme.colors.text,
  marginRight: spacing.sm }
    healthCard: { backgroundColo, r: theme.colors.surface,
    borderRadius: 12,
  padding: spacing.lg,
    marginBottom: spacing.lg },
  healthHeader: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: spacing.md }
    healthTitle: { fontSiz, e: 18,
    fontWeight: 'bold',
  color: theme.colors.text,
    marginLeft: spacing.md,
  flex: 1 }
    healthBadge: { paddingHorizonta, l: spacing.md,
    paddingVertical: spacing.xs,
  borderRadius: 12 }
    healthBadgeText: { fontSiz, e: 10,
    fontWeight: 'bold',
  color: theme.colors.surface }
    healthStats: {
      flexDirection: 'row',
  justifyContent: 'space-around'
  },
  healthStat: {
      alignItems: 'center' }
    healthStatLabel: { fontSiz, e: 12,
    color: theme.colors.textSecondary,
  marginBottom: spacing.xs }
    healthStatValue: { fontSiz, e: 16,
    fontWeight: 'bold',
  color: theme.colors.text }
    maintenanceActions: { ga, p: spacing.md },
  maintenanceButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  backgroundColor: theme.colors.surface,
    borderRadius: 12,
  padding: spacing.lg }
    maintenanceButtonText: { fontSiz, e: 16,
    fontWeight: '500',
  color: theme.colors.text,
    marginLeft: spacing.md,
  flex: 1 }
    maintenanceButtonSubtext: { fontSiz, e: 12,
    color: theme.colors.textSecondary,
  marginLeft: spacing.md }
    auditHeader: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'space-between',
    marginBottom: spacing.lg },
  viewAllButton: {
      flexDirection: 'row',
  alignItems: 'center'
  },
  viewAllButtonText: {
      marginLeft: spacing.sm,
  fontSize: 14,
    color: theme.colors.primary,
  fontWeight: '500'
  },
  auditLogItem: { flexDirectio, n: 'row',
    alignItems: 'center',
  paddingVertical: spacing.md,
    borderBottomWidth: 1,
  borderBottomColor: theme.colors.border }
    auditLogIcon: { widt, h: 32,
    height: 32,
  borderRadius: 16,
    backgroundColor: theme.colors.info + '20',
  justifyContent: 'center',
    alignItems: 'center',
  marginRight: spacing.md }
    auditLogContent: { fle, x: 1 },
  auditLogAction: { fontSiz, e: 14,
    fontWeight: '500',
  color: theme.colors.text,
    marginBottom: spacing.xs },
  auditLogDetails: { fontSiz, e: 12,
    color: theme.colors.textSecondary },
  saveContainer: { paddin, g: spacing.lg,
    backgroundColor: theme.colors.surface,
  borderTopWidth: 1,
    borderTopColor: theme.colors.border },
  saveButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'center',
    backgroundColor: theme.colors.primary,
  borderRadius: 12,
    paddingVertical: spacing.md },
  saveButtonDisabled: { opacit, y: 0.6 }
    saveButtonText: { marginLef, t: spacing.sm,
    fontSize: 16,
  fontWeight: 'bold',
    color: theme.colors.surface },
  modalContainer: { fle, x: 1,
    backgroundColor: theme.colors.background },
  modalHeader: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'space-between',
    padding: spacing.lg,
  borderBottomWidth: 1,
    borderBottomColor: theme.colors.border },
  modalTitle: { fontSiz, e: 20,
    fontWeight: 'bold',
  color: theme.colors.text }
    modalCloseButton: { paddin, g: spacing.sm },
  modalContent: { fle, x: 1,
    padding: spacing.lg },
  auditLogItemFull: { backgroundColo, r: theme.colors.surface,
    borderRadius: 12,
  padding: spacing.lg,
    marginBottom: spacing.md },
  auditLogHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between'),
  alignItems: 'center'),
    marginBottom: spacing.sm },
  auditLogTimestamp: { fontSiz, e: 12,
    color: theme.colors.textSecondary },
  auditLogAdmin: { fontSiz, e: 14,
    color: theme.colors.text,
  marginBottom: spacing.xs }
    auditLogIP: {
      fontSize: 12,
  color: theme.colors.textSecondary,
    marginBottom: spacing.xs) }
  }),
  export default AdminSettingsScreen