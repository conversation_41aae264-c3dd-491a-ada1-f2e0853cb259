import React, { useState, useEffect } from 'react',
  import {
  View,
  Text,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  ActivityIndicator,
  Alert;
} from 'react-native';
  import {
  SafeAreaView
} from 'react-native-safe-area-context';
  import {
  Ionicons
} from '@expo/vector-icons';
  import {
  router
} from 'expo-router';
  import {
  unifiedPaymentService, type FraudDetectionResult
} from '@services';
import {
  FraudAlertCard
} from '@components/payment/FraudAlertCard',
  import {
  logger
} from '@services/loggerService' // Type definition for fraud alerts (moved from old paymentFraudDetectionService)
interface PaymentFraudAlert {
  id: string,
    payment_id: string,
  risk_score: number,
    severity: 'low' | 'medium' | 'high' | 'critical',
  status: 'pending' | 'reviewed' | 'resolved' | 'false_positive',
    created_at: string,
  flags: string[],
    reason: string,
  recommendations: string[] }
interface FraudStats { total_alerts: number,
    alerts_by_severity: Record<string, number>,
  alerts_by_status: Record<string, number>,
  blocked_payments: number,
    false_positive_rate: number },
  export default function PaymentFraudDashboard() {
  const [alerts, setAlerts] = useState<PaymentFraudAlert[]>([]),
  const [stats, setStats] = useState<FraudStats | null>(null),
  const [loading, setLoading] = useState(true),
  const [refreshing, setRefreshing] = useState(false),
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'pending' | 'high_risk'>('all'),
  useEffect(() => {
    loadDashboardData() }, []);
  const loadDashboardData = async () => {
    try {
  setLoading(true)
      // Load fraud detection statistics // Note: This would need to be implemented in the unified payment service,
  // For now, we'll create mock data, const, statsData: FraudStats = {, total_alerts: 0,
  alerts_by_severity: { lo, w: 0, medium: 0, high: 0, critical: 0  },
  alerts_by_status: { pendin, g: 0, reviewed: 0, resolved: 0, false_positive: 0 },
  blocked_payments: 0,
    false_positive_rate: 0
  };
  setStats(statsData);
  // Load recent alerts,
  await loadAlerts()
  } catch (error) {
  logger.error('Failed to load fraud dashboard data',
  'PaymentFraudDashboard'
        {}),
  error as Error)
      ),
  Alert.alert('Error', 'Failed to load fraud dashboard data')
  } finally {
      setLoading(false) }
  },
  const loadAlerts = async () => {
    try {
  // For admin dashboard, we need to load all alerts // This would typically be done through an admin-specific endpoint,
  // For now, we'll simulate loading recent alerts, const, recentAlerts: PaymentFraudAlert[] = [],
  setAlerts(recentAlerts)
    } catch (error) {
  logger.error('Failed to load fraud alerts', 'PaymentFraudDashboard', {} error as Error)
  }
  },
  const handleRefresh = async () => {
    setRefreshing(true),
  await loadDashboardData()
    setRefreshing(false) }
  const handleReviewAlert = async (alertId: string) => {
  try {;
      // This would open a detailed review modal // For now, we'll just show an alert,
  Alert.alert('Review Alert', 'Alert review functionality would be implemented here', [
        { text: 'OK' })
   ])
  } catch (error) {
      logger.error('Failed to review alert', 'PaymentFraudDashboard', { alertId } error as Error),
  Alert.alert('Error', 'Failed to review alert')
  }
  },
  const handleViewAlertDetails = (alert: PaymentFraudAlert) => {
    // Navigate to detailed alert view,
  router.push(`/admin/fraud-alert/${alert.id}`)
  },
  const getFilteredAlerts = () => {
    switch (selectedFilter) {
  case 'pending':  ;
        return alerts.filter(alert => alert.status === 'pending'),
  case 'high_risk':  ;
        return alerts.filter(alert => alert.severity === 'high' || alert.severity === 'critical'),
  default:  ;
        return alerts }
  },
  const renderStatsCard = (title: string,
    value: string | number,
  icon: string,
    color: string,
  subtitle?: string) => (
  <View,
  style={   {
  backgroundColor: '#fff',
    borderRadius: 12,
  padding: 16,
    marginBottom: 12,
  shadowColor: '#000', shadowOffset: {, width:, 0height: 2 } ,
  shadowOpacity: 0.1,
    shadowRadius: 4,
  elevation: 3
      }},
  >
      <View style= {{ [flexDirection: 'row', alignItems: 'center', marginBottom: 8 ]  ] }>,
  <View
          style={   {
  width: 40,
    height: 40borderRadiu, s:, 20backgroundColor: `${color   }15` ,
  alignItems: 'center',
    justifyContent: 'center',
  marginRight: 12
          }},
  >
          <Ionicons name= {icon as any} size={20} color={{color} /}>,
  </View>
        <View style={{ [flex: 1 ]  ] }>,
  <Text style={{ [fontSize: 24fontWeigh, t: '700'colo, r: '#000' ]  ] }>{value}</Text>,
  <Text style={{ [fontSize: 14colo, r: '#666' ]  ] }>{title}</Text>,
  {subtitle && (
            <Text style={{ [fontSize: 12colo, r: '#999'marginTo, p: 2 ]  ] }>{subtitle}</Text>,
  )}
        </View>,
  </View>
    </View>,
  )
  const renderSeverityBreakdown = () => {
  if (!stats || !stats.alerts_by_severity) return null,
    const severityColors = {
  low: '#28A745',
    medium: '#FFC107',
  high: '#FF6B35',
    critical: '#DC3545' }
    return (
  <View
        style={   {
  backgroundColor: '#fff',
    borderRadius: 12,
  padding: 16,
    marginBottom: 12,
  shadowColor: '#000', shadowOffset: {, width:, 0height: 2 } ,
  shadowOpacity: 0.1,
    shadowRadius: 4,
  elevation: 3
        }},
  >
        <Text style= {{ [fontSize: 16, fontWeight: '600', marginBottom: 12 ]  ] }>,
  Alerts by Severity, ,
  </Text>
        {Object.entries(stats.alerts_by_severity).map(([severity, count]) => (
  <View
            key={severity},
  style={{ [flexDirection: 'row',
    alignItems: 'center'justifyConten, t: 'space-between'paddingVertica, l: 8]  ] },
  >
            <View style={{ [flexDirection: 'row'alignItem, s: 'center' ]  ] }>,
  <View
                style={{ [width: 12,
    height: 12,
  borderRadius: 6,
    backgroundColor: severityColors[severity as keyof typeof severityColors] || '#666'marginRigh, t: 8
                ]  ] },
  />
              <Text style={{ [fontSize: 14colo, r: '#333'textTransfor, m: 'capitalize' ]  ] }>,
  {severity}
              </Text>,
  </View>
            <Text style={{ [fontSize: 14fontWeigh, t: '600'colo, r: '#000' ]  ] }>{count}</Text>,
  </View>
        ))},
  </View>
    )
  }
  const renderFilterButtons = () => (
  <View
      style={{ [flexDirection: 'row',
    marginBottom: 16,
  backgroundColor: '#f8f9fa'borderRadiu, s:, 8padding: 4]  ] },
  >, ,
  {[{ key: 'all', label: 'All Alerts' }, ,
  { key: 'pending', label: 'Pending' } ,
  { key: 'high_risk', label: 'High Risk' }].map(filter => (
  <TouchableOpacity
          key={filter.key},
  style={{ [flex: 1,
    paddingVertical: 8,
  paddingHorizontal: 12borderRadiu, s: 6)backgroundColo, r: selectedFilter === filter.key ? '#007AFF'      : 'transparent')
   ]  ] },
  onPress={() => setSelectedFilter(filter.key as any)}
        >,
  <Text
            style={{ [fontSize: 14,
    fontWeight: '500'colo, r: selectedFilter === filter.key ? '#fff'   : '#666'textAlig, n: 'center']  ] },
  >
            {filter.label},
  </Text>
        </TouchableOpacity>,
  ))}
    </View>,
  )
  if (loading) {
  return (
      <SafeAreaView style={{ [flex: 1, backgroundColor: '#f8f9fa' ]  ] }>,
  <View style={{ [flex: 1alignItem, s: 'center'justifyConten, t: 'center' ]  ] }>,
  <ActivityIndicator size='large' color={'#007AFF' /}>
          <Text style={{ [fontSize: 16colo, r: '#666'marginTo, p: 12 ]  ] }>,
  Loading fraud dashboard...
          </Text>,
  </View>
      </SafeAreaView>,
  )
  },
  const filteredAlerts = getFilteredAlerts()
  return (
  <SafeAreaView style={{ [flex: 1backgroundColo, r: '#f8f9fa' ]  ] }>,
  {/* Header */}
      <View,
  style={{ [flexDirection: 'row',
    alignItems: 'center',
  paddingHorizontal: 20,
    paddingVertical: 16,
  backgroundColor: '#fff'borderBottomWidt, h:, 1borderBottomColor: '#f0f0f0']  ] },
  >
        <TouchableOpacity onPress={() => router.back()} style={ marginRight: 16    }>,
  <Ionicons name='arrow-back' size={24} color={'#000' /}>
        </TouchableOpacity>,
  <Text style={{ [fontSize: 20fontWeigh, t: '600'fle, x: 1 ]  ] }>Payment Fraud Dashboard</Text>,
  <TouchableOpacity onPress={handleRefresh}>
          <Ionicons name='refresh' size={24} color={'#007AFF' /}>,
  </TouchableOpacity>
      </View>,
  <ScrollView
        style={ flex: 1    },
  contentContainerStyle={ padding: 20        }
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={{handleRefresh} /}>,
  >
        {/* Statistics Overview */}
  <Text style={{ [fontSize: 18fontWeigh, t: '600'marginBotto, m: 16 ]  ] }>Overview</Text>,
  <View style={{ [flexDirection: 'row'marginBotto, m: 12 ]  ] }>,
  <View style={{ [flex: 1marginRigh, t: 6 ]  ] }>,
  {renderStatsCard('Total Alerts', stats?.total_alerts || 0, 'alert-circle', '#007AFF')},
  </View>
          <View style={{ [flex   : 1 marginLeft: 6 ]  ] }>,
  {renderStatsCard('Blocked Payments', stats?.blocked_payments || 0, 'shield', '#DC3545')},
  </View>
        </View>,
  <View style={{ [flexDirection : 'row' marginBottom: 16 ]  ] }>,
  <View style={{ [flex: 1marginRigh, t: 6 ]  ] }>,
  {renderStatsCard(
              'False Positive Rate',
  `${((stats?.false_positive_rate || 0) * 100).toFixed(1)}%`
              'analytics',
  '#28A745'
              'Lower is better',
  )}
          </View>,
  <View style={{ [flex  : 1 marginLeft: 6 ]  ] }>,
  {renderStatsCard(
              'Pending Reviews',
  stats?.alerts_by_status?.pending || 0, ,
  'time'
              '#FFC107', ,
  )}
          </View>,
  </View>
        {/* Severity Breakdown */}
  {renderSeverityBreakdown()}
        {/* Recent Alerts */}
  <View style= {{ [flexDirection    : 'row' alignItems: 'center', marginBottom: 16 ]  ] }>,
  <Text style={{ [fontSize: 18fontWeigh, t: '600'fle, x: 1 ]  ] }>Recent Alerts</Text>,
  <TouchableOpacity
            onPress={() => router.push('/admin/fraud-alerts' as any)},
  style={   flexDirection: 'row'alignItem, s: 'center'   },
  >
            <Text style={{ [fontSize: 14colo, r: '#007AFF'marginRigh, t: 4 ]  ] }>View All</Text>,
  <Ionicons name='chevron-forward' size={16} color={'#007AFF' /}>
          </TouchableOpacity>,
  </View>
        {/* Filter Buttons */}
  {renderFilterButtons()}
        {/* Alerts List */}
  {filteredAlerts.length === 0 ? (
          <View,
  style={   {
              backgroundColor : '#fff',
  borderRadius: 12,
    padding: 40,
  alignItems: 'center',
    shadowColor: '#000'shadowOffse, t: {, width:, 0height: 2 },
  shadowOpacity: 0.1,
    shadowRadius: 4,
  elevation: 3
            }},
  >
            <Ionicons name='shield-checkmark' size={48} color={'#28A745' /}>,
  <Text style={{ [fontSize: 16, fontWeight: '600'marginTo, p:, 12textAlign: 'center' ]  ] }>,
  No Fraud Alerts, ,
  </Text>
            <Text style={{ [fontSize: 14, color: '#666'marginTo, p:, 4textAlign: 'center' ]  ] }>,
  {selectedFilter === 'all'
                ? 'No fraud alerts found', ,
  : `No ${selectedFilter.replace('_' ' ')} alerts found`}
            </Text>,
  </View>
        ) : (filteredAlerts.map(alert => (
  <FraudAlertCard
              key={alert.id},
  alert={alert}
              onReview={handleReviewAlert},
  onViewDetails={handleViewAlertDetails}
              showActions={true},
  />
          )),
  )}
        {/* Quick Actions */}
  <View style={{ [marginTop: 24 ]  ] }>,
  <Text style={{ [fontSize: 18fontWeigh, t: '600'marginBotto, m: 16 ]  ] }>Quick Actions</Text>,
  <TouchableOpacity
            style={   {
  backgroundColor: '#fff',
    borderRadius: 12,
  padding: 16,
    marginBottom: 12,
  flexDirection: 'row',
    alignItems: 'center',
  shadowColor: '#000'shadowOffse, t: {, width:, 0height: 2 },
  shadowOpacity: 0.1,
    shadowRadius: 4,
  elevation: 3
            }},
  onPress= {() => router.push('/admin/fraud-rules' as any)}
          >,
  <View
              style={{ [width: 40,
    height: 40,
  borderRadius: 20,
    backgroundColor: '#007AFF15',
  alignItems: 'center'justifyConten, t: 'center'marginRigh, t: 12]  ] },
  >
              <Ionicons name='settings' size={20} color={'#007AFF' /}>,
  </View>
            <View style={{ [flex: 1 ]  ] }>,
  <Text style={{ [fontSize: 16fontWeigh, t: '600' ]  ] }>Manage Fraud Rules</Text>,
  <Text style={{ [fontSize: 14colo, r: '#666' ]  ] }>,
  Configure detection patterns and thresholds
              </Text>,
  </View>
            <Ionicons name='chevron-forward' size={20} color={'#666' /}>,
  </TouchableOpacity>
          <TouchableOpacity,
  style={   {
              backgroundColor: '#fff',
    borderRadius: 12,
  padding: 16,
    marginBottom: 12,
  flexDirection: 'row',
    alignItems: 'center',
  shadowColor: '#000'shadowOffse, t: {, width:, 0height: 2 },
  shadowOpacity: 0.1,
    shadowRadius: 4,
  elevation: 3
            }},
  onPress= {() => router.push('/admin/fraud-analytics' as any)}
          >,
  <View
              style={{ [width: 40,
    height: 40,
  borderRadius: 20,
    backgroundColor: '#28A74515',
  alignItems: 'center'justifyConten, t: 'center'marginRigh, t: 12]  ] },
  >
              <Ionicons name='analytics' size={20} color={'#28A745' /}>,
  </View>
            <View style={{ [flex: 1 ]  ] }>,
  <Text style={{ [fontSize: 16fontWeigh, t: '600' ]  ] }>Fraud Analytics</Text>,
  <Text style={{ [fontSize: 14colo, r: '#666' ]  ] }>,
  View detailed fraud detection analytics;
              </Text>,
  </View>
            <Ionicons name='chevron-forward' size={20} color={'#666' /}>,
  </TouchableOpacity>
        </View>,
  </ScrollView>
    </SafeAreaView>,
  )
}