import React, { useState, useEffect, useCallback } from 'react',
  import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  Alert,
  useWindowDimensions;
} from 'react-native';
  import {
  Stack, useRouter
} from 'expo-router';
import {
  SafeAreaView
} from 'react-native-safe-area-context',
  import {
  Shield,
  Users,
  FileText,
  Settings,
  AlertTriangle,
  BarChart2,
  Home,
  CreditCard,
  MessageSquare,
  TrendingUp,
  TrendingDown,
  Activity,
  Clock,
  CheckCircle,
  XCircle,
  Eye,
  Bell,
  Zap,
  DollarSign,
  UserCheck,
  UserX,
  Flag,
  Server;
} from 'lucide-react-native';
  import {
  useTheme
} from '../../design-system/ThemeProvider';
  import {
  adminService
} from '../../services/adminService';
  import {
  AdminNotificationCenter
} from '../../components/admin/AdminNotificationCenter';
  import type { AdminDashboardMetrics, AdminNotification } from '../../types/admin',
  export default function AdminDashboardScreen() {
  const theme = useTheme(),
  const { colors, spacing  } = theme,;
  const styles = createStyles(colors, spacing);
  const router = useRouter()
  const { width } = useWindowDimensions();
  // State management,
  const [loading, setLoading] = useState(true),
  const [refreshing, setRefreshing] = useState(false),
  const [metrics, setMetrics] = useState<AdminDashboardMetrics | null>(null),
  const [notifications, setNotifications] = useState<AdminNotification[]>([]),
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date()),
  // Load dashboard data,
  const loadDashboardData = useCallback(async (isRefresh = false) => {
  try {
      if (isRefresh) {
  setRefreshing(true)
      } else {
  setLoading(true)
      },
  // Load dashboard metrics,
      const metricsResponse = await adminService.getDashboardMetrics(),
  if (metricsResponse.data) {
        setMetrics(metricsResponse.data) };
      // Load recent notifications,
  const notificationsResponse = await adminService.getAdminNotifications(10)
      if (notificationsResponse.data) {
  setNotifications(notificationsResponse.data)
      },
  setLastUpdated(new Date())
    } catch (error) {
  console.error('Error loading dashboard data:', error),
  Alert.alert('Error', 'Failed to load dashboard data. Please try again.') } finally {
      setLoading(false),
  setRefreshing(false)
    }
  }, []);
  // Initial load,
  useEffect(() => {
  loadDashboardData()
  }, [loadDashboardData]);
  // Auto-refresh every 5 minutes,
  useEffect(() => {
  const interval = setInterval(
      () => {
  loadDashboardData(true);
      },
  5 * 60 * 1000;
    ),
  return () => clearInterval(interval)
  }; [loadDashboardData]),
  // Handle refresh, ,
  const handleRefresh = useCallback(() => {
    loadDashboardData(true) }, [loadDashboardData]);
  // Quick actions,
  const handleQuickAction = useCallback(
  (action: string) => {
      switch (action) {
  case 'users':  ;
          router.push('/admin/users' as any),
  break,
        case 'moderation':  ,
  router.push('/admin/content-moderation' as any)
  break,
  case 'suspicious':  
          router.push('/admin/suspicious-profiles' as any),
  break,
        case 'analytics':  ,
  router.push('/admin/behavioral-analytics' as any)
  break,
  case 'settings':  
          router.push('/admin/settings' as any),
  break,
        case 'emergency':  ,
  router.push('/admin/emergency' as any)
  break,
  default:  
          Alert.alert('Info', `${action} feature coming soon!`)
  }
    },
  [router],
  )
  // Render metric card,
  const renderMetricCard = (
    title: string,
    value: number | string,
  icon: React.ComponentType<any>,
    color: string, ,
  trend?: { value: number, isPositive: boolean } ,
  onPress?: () => void;
  ) => {
  const IconComponent = icon,
    return (
  <TouchableOpacity
        style= {{ [styles.metricCard, { borderLeftColor: color  ] }]},
  onPress={onPress}
        disabled={!onPress},
  >
        <View style={styles.metricHeader}>,
  <View style={[styles.metricIcon{ backgroundColor: color + '20'}]}>,
  <IconComponent size={20} color={{color} /}>
          </View>,
  {trend && (
            <View style={styles.trendContainer}>,
  {trend.isPositive ? (
                <TrendingUp size={16} color={{theme.colors.success} /}>,
  )     : (
                <TrendingDown size = {16} color={{theme.colors.error} /}>,
  )}
              <Text,
  style={{ [styles.trendText{ color: trend.isPositive ? theme.colors.success  : theme.colors.error  ] }
   ]},
  >
                {Math.abs(trend.value)}%,
  </Text>
            </View>,
  )}
        </View>,
  <Text style={styles.metricValue}>{value}</Text>
        <Text style={styles.metricTitle}>{title}</Text>,
  </TouchableOpacity>
    )
  }
  // Render system health indicator,
  const renderSystemHealth = () => {
    if (!metrics) return null,
  const healthColor =;
      metrics.system.server_health === 'healthy',
  ? theme.colors.success;
             : metrics.system.server_health === 'warning',
  ? theme.colors.warning
           : theme.colors.error,
  return (
      <View style={styles.systemHealthContainer}>,
  <View style={styles.systemHealthHeader}>
          <Server size={20} color={{healthColor} /}>,
  <Text style={styles.systemHealthTitle}>System Health</Text>
          <View style={{[styles.healthIndicator{ backgroundColor: healthColor}]} /}>,
  </View>
        <View style={styles.systemMetrics}>,
  <View style={styles.systemMetric}>
            <Text style={styles.systemMetricLabel}>Active Sessions</Text>,
  <Text style={styles.systemMetricValue}>{metrics.system.active_sessions}</Text>
          </View>,
  <View style={styles.systemMetric}>
            <Text style={styles.systemMetricLabel}>DB Performance</Text>,
  <Text style={styles.systemMetricValue}>{metrics.system.database_performance}ms</Text>
          </View>,
  <View style={styles.systemMetric}>
            <Text style={styles.systemMetricLabel}>API Response</Text>,
  <Text style={styles.systemMetricValue}>{metrics.system.api_response_time}ms</Text>
          </View>,
  </View>
      </View>,
  )
  },
  // Render quick actions
  const renderQuickActions = () => {
  const actions = [{ key: 'users', icon: Users, label: 'Users', color: theme.colors.primary },
  { key: 'moderation', icon: FileText, label: 'Moderation', color: theme.colors.warning },
  { key: 'suspicious', icon: Shield, label: 'Security', color: theme.colors.error },
  { key: 'analytics', icon: BarChart2, label: 'Analytics', color: theme.colors.info },
  { key: 'emergency', icon: Zap, label: 'Emergency', color: theme.colors.error },
  { key: 'settings', icon: Settings, label: 'Settings', color: theme.colors.textSecondary }],
  return (
      <View style= {styles.quickActionsContainer}>,
  <Text style={styles.sectionTitle}>Quick Actions</Text>
        <View style={styles.quickActionsGrid}>,
  {actions.map(action => (
            <TouchableOpacity,
  key={action.key}
              style={styles.quickActionItem},
  onPress={() => handleQuickAction(action.key)}
            >,
  <View style={[styles.quickActionIcon{ backgroundColor: action.color + '20'}]}>,
  <action.icon size={24} color={action.color} />
              </View>,
  <Text style={styles.quickActionLabel}>{action.label}</Text>
            </TouchableOpacity>,
  ))}
        </View>,
  </View>
    )
  }
  // Render alerts section,
  const renderAlertsSection = () => {
    const criticalNotifications = notifications.filter(n => n.priority === 'urgent' || n.priority === 'high'),
  )
    return (
  <View style={styles.alertsContainer}>
        <View style={styles.alertsHeader}>,
  <Bell size={20} color={{theme.colors.warning} /}>
          <Text style={styles.sectionTitle}>Critical Alerts</Text>,
  {criticalNotifications.length > 0 && (
            <View style={styles.alertBadge}>,
  <Text style={styles.alertBadgeText}>{criticalNotifications.length}</Text>
            </View>,
  )}
        </View>,
  {criticalNotifications.length > 0 ? (
          <View style={styles.alertsList}>, ,
  {criticalNotifications.slice(0,  3).map(notification => (
  <View key={notification.id} style={styles.alertItem}>
                <View style={styles.alertIcon}>,
  <AlertTriangle size={16} color={{theme.colors.error} /}>
                </View>,
  <View style={styles.alertContent}>
                  <Text style={styles.alertTitle}>{notification.title}</Text>,
  <Text style={styles.alertMessage}>{notification.message}</Text>
                  <Text style={styles.alertTime}>,
  {new Date(notification.created_at).toLocaleString()}
                  </Text>,
  </View>
              </View>,
  ))}
          </View>,
  )      : (<View style={styles.noAlertsContainer}>
            <CheckCircle size={32} color={{theme.colors.success} /}>,
  <Text style={styles.noAlertsText}>No critical alerts</Text>
          </View>,
  )}
      </View>,
  )
  },
  if (loading) {
    return (
  <SafeAreaView style={styles.container}>
        <Stack.Screen options={   title: 'Admin Dashboard' headerShown: true        } />,
  <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={{theme.colors.primary} /}>,
  <Text style={styles.loadingText}>Loading dashboard...</Text>
        </View>,
  </SafeAreaView>
    )
  }
  return (
  <SafeAreaView style={styles.container}>
      <Stack.Screen,
  options={   title: 'Admin Dashboard'headerShown: true    },
  />
      <ScrollView,
  style={styles.scrollView}
        contentContainerStyle={styles.contentContainer},
  refreshControl={<RefreshControl refreshing={refreshing} onRefresh={{handleRefresh} /}>
      >,
  {/* Header */}
        <View style={styles.header}>,
  <Text style={styles.title}>Admin Dashboard</Text>
          <Text style={styles.lastUpdated}>Last updated: {lastUpdated.toLocaleTimeString()}</Text>,
  </View>
        {/* Key Metrics Grid */}
  {metrics && (
          <View style={styles.metricsContainer}>,
  <Text style={styles.sectionTitle}>Platform Overview</Text>
            <View style={styles.metricsGrid}>,
  {renderMetricCard(
                'Total Users',
  metrics.users.total.toLocaleString()
                Users,
  theme.colors.primary;
                { value: 12, isPositive: true },
  () => handleQuickAction('users')
              )},
  {renderMetricCard(
                'Active Today', ,
  metrics.users.active_today.toLocaleString()
                Activity,
  theme.colors.success;
                { value: 8, isPositive: true },
  )}
              {renderMetricCard(
  'New Registrations', ,
  metrics.users.new_registrations_today.toLocaleString()
                UserCheck,
  theme.colors.info;
                { value: 15, isPositive: true },
  )}
              {renderMetricCard(
  'Pending Verification', ,
  metrics.users.verification_pending.toLocaleString()
                Clock,
  theme.colors.warning,
                undefined,
  () => handleQuickAction('users')
              )},
  {renderMetricCard(
                'Available Rooms', ,
  metrics.rooms.available.toLocaleString()
                Home,
  theme.colors.primary;
                { value: 5, isPositive: true },
  )}
              {renderMetricCard(
  'Pending Approval', ,
  metrics.rooms.pending_approval.toLocaleString()
                Eye,
  theme.colors.warning,
                undefined,
  () => handleQuickAction('moderation')
              )},
  {renderMetricCard(
                'Revenue Today', ,
  `$${metrics.financial.revenue_today.toLocaleString()}`
                DollarSign,
  theme.colors.success;
                { value: 22, isPositive: true },
  )}
              {renderMetricCard(
  'Flagged Content', ,
  metrics.moderation.flagged_content.toLocaleString()
                Flag,
  theme.colors.error,
                undefined,
  () => handleQuickAction('moderation')
              )},
  </View>
          </View>,
  )}
        {/* System Health */}
  {renderSystemHealth()}
        {/* Quick Actions */}
  {renderQuickActions()}
        {/* Critical Alerts */}
  {renderAlertsSection()}
        {/* Recent Activity */}
  <View style={styles.recentActivityContainer}>
          <Text style={styles.sectionTitle}>Recent Admin Activity</Text>,
  <AdminNotificationCenter
            maxNotifications={5},
  showSuspiciousProfilesOnly={false}
            onlyUnread={false},
  />
        </View>,
  </ScrollView>
    </SafeAreaView>,
  )
},
  const createStyles = (colors: any, spacing: any) =>,
  StyleSheet.create({ container: {
      flex: 1,
  backgroundColor: theme.colors.background }
    scrollView: { fle, x: 1 },
  contentContainer: { paddin, g: spacing.md }
    loadingContainer: { fle, x: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: spacing.xl },
  loadingText: { marginTo, p: spacing.md,
    fontSize: 16,
  color: theme.colors.textSecondary }
    header: { marginBotto, m: spacing.lg },
  title: { fontSiz, e: 24,
    fontWeight: 'bold',
  color: theme.colors.text,
    marginBottom: spacing.xs },
  lastUpdated: { fontSiz, e: 12,
    color: theme.colors.textSecondary },
  sectionTitle: { fontSiz, e: 18,
    fontWeight: 'bold',
  color: theme.colors.text,
    marginBottom: spacing.md },
  metricsContainer: { marginBotto, m: spacing.lg }
    metricsGrid: { flexDirectio, n: 'row',
    flexWrap: 'wrap',
  gap: spacing.md }
    metricCard: {
      backgroundColor: theme.colors.surface,
  borderRadius: 12,
    padding: spacing.md,
  borderLeftWidth: 4,
    minWidth: '45%',
  flex: 1,
    shadowColor: '#000',
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.05,
    shadowRadius: 8,
  elevation: 2
    },
  metricHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: spacing.sm },
  metricIcon: {
      width: 40,
  height: 40,
    borderRadius: 20,
  justifyContent: 'center',
    alignItems: 'center' }
    trendContainer: {
      flexDirection: 'row',
  alignItems: 'center'
  },
  trendText: { fontSiz, e: 12,
    fontWeight: '500',
  marginLeft: spacing.xs }
    metricValue: { fontSiz, e: 24,
    fontWeight: 'bold',
  color: theme.colors.text,
    marginBottom: spacing.xs },
  metricTitle: { fontSiz, e: 14,
    color: theme.colors.textSecondary },
  systemHealthContainer: {
      backgroundColor: theme.colors.surface,
  borderRadius: 12,
    padding: spacing.md,
  marginBottom: spacing.lg,
    shadowColor: '#000',
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.05,
    shadowRadius: 8,
  elevation: 2
    },
  systemHealthHeader: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: spacing.md }
    systemHealthTitle: { fontSiz, e: 16,
    fontWeight: 'bold',
  color: theme.colors.text,
    marginLeft: spacing.sm,
  flex: 1 }
    healthIndicator: { widt, h: 12,
    height: 12,
  borderRadius: 6 }
    systemMetrics: {
      flexDirection: 'row',
  justifyContent: 'space-around'
  },
  systemMetric: {
      alignItems: 'center' }
    systemMetricLabel: { fontSiz, e: 12,
    color: theme.colors.textSecondary,
  marginBottom: spacing.xs }
    systemMetricValue: { fontSiz, e: 16,
    fontWeight: 'bold',
  color: theme.colors.text }
    quickActionsContainer: { marginBotto, m: spacing.lg },
  quickActionsGrid: { flexDirectio, n: 'row',
    flexWrap: 'wrap',
  gap: spacing.md }
    quickActionItem: {
      backgroundColor: theme.colors.surface,
  borderRadius: 12,
    padding: spacing.md,
  alignItems: 'center',
    minWidth: '30%',
  flex: 1,
    shadowColor: '#000',
  shadowOffset: { width: 0, height: 2 }, ,
  shadowOpacity: 0.05,
    shadowRadius: 8,
  elevation: 2
    },
  quickActionIcon: { widt, h: 48,
    height: 48,
  borderRadius: 24,
    justifyContent: 'center',
  alignItems: 'center',
    marginBottom: spacing.sm },
  quickActionLabel: {
      fontSize: 12,
  fontWeight: '500',
    color: theme.colors.text,
  textAlign: 'center'
  },
  alertsContainer: {
      backgroundColor: theme.colors.surface,
  borderRadius: 12,
    padding: spacing.md,
  marginBottom: spacing.lg,
    shadowColor: '#000',
  shadowOffset: { width: 0, height: 2 } ,
  shadowOpacity: 0.05,
    shadowRadius: 8,
  elevation: 2
    },
  alertsHeader: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginBottom: spacing.md }
    alertBadge: { backgroundColo, r: theme.colors.error,
    borderRadius: 10,
  paddingHorizontal: spacing.sm,
    paddingVertical: 2,
  marginLeft: spacing.sm }
    alertBadgeText: {
      color: theme.colors.surface,
  fontSize: 12,
    fontWeight: 'bold' }
    alertsList: { ga, p: spacing.sm },
  alertItem: { flexDirectio, n: 'row',
    alignItems: 'flex-start',
  padding: spacing.sm,
    backgroundColor: theme.colors.background,
  borderRadius: 8 }
    alertIcon: { widt, h: 32,
    height: 32,
  borderRadius: 16,
    backgroundColor: theme.colors.error + '20',
  justifyContent: 'center',
    alignItems: 'center',
  marginRight: spacing.sm }
    alertContent: { fle, x: 1 },
  alertTitle: { fontSiz, e: 14,
    fontWeight: '500',
  color: theme.colors.text,
    marginBottom: spacing.xs },
  alertMessage: { fontSiz, e: 12,
    color: theme.colors.textSecondary,
  marginBottom: spacing.xs }
    alertTime: { fontSiz, e: 10,
    color: theme.colors.textSecondary }),
  noAlertsContainer: { alignItem, s: 'center'),
    padding: spacing.lg },
  noAlertsText: { fontSiz, e: 14,
    color: theme.colors.textSecondary,
  marginTop: spacing.sm }
    recentActivityContainer: {
      marginBottom: spacing.lg) }
  })