import React, { useState, useEffect, useCallback } from 'react';
  import {
  View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Alert, ActivityIndicator, RefreshControl, Modal, Image, useWindowDimensions
} from 'react-native';
import {
  Stack, useRouter
} from 'expo-router';
import {
  SafeAreaView
} from 'react-native-safe-area-context';
  import {
  Search, Filter, Eye, CheckCircle, XCircle, MoreVertical, MapPin, Calendar, DollarSign, Users, Home, Flag, Trash2, Edit, Camera, AlertTriangle, TrendingUp, Clock, Star, MessageSquare, ChevronDown, X, Check, Ban, RefreshCw
} from 'lucide-react-native';

import {
  useTheme
} from '../../../design-system/ThemeProvider';
  import {
  adminService
} from '../../../services/adminService';
import BulkOperationsBar, { getRoomBulkActions } from '../../../components/admin/BulkOperationsBar';
  import SelectionCheckbox from '../../../components/admin/SelectionCheckbox';
import type {
  AdminRoomListItem,
  RoomStatus,
  RoomApprovalAction,
  AdminBulkAction } from '../../../types/admin';

type FilterStatus = 'all' | 'active' | 'pending' | 'rejected' | 'flagged' | 'suspended',
  type SortOption = 'newest' | 'oldest' | 'price_high' | 'price_low' | 'most_viewed';

export default function RoomListingsManagementScreen() {
  const theme = useTheme();
  const { colors, spacing  } = theme,
  const styles = createStyles(colors, spacing),
  const router = useRouter()
  const { width } = useWindowDimensions(),
  // State management,
  const [loading, setLoading] = useState(true),
  const [refreshing, setRefreshing] = useState(false),
  const [rooms, setRooms] = useState<AdminRoomListItem[]>([]),
  const [filteredRooms, setFilteredRooms] = useState<AdminRoomListItem[]>([]),
  const [searchQuery, setSearchQuery] = useState(''),
  const [filterStatus, setFilterStatus] = useState<FilterStatus>('all'),
  const [sortOption, setSortOption] = useState<SortOption>('newest'),
  const [selectedRooms, setSelectedRooms] = useState<Set<string>>(new Set()),
  const [showFilters, setShowFilters] = useState(false),
  const [showBulkActions, setShowBulkActions] = useState(false),
  const [actionLoading, setActionLoading] = useState(false),
  const [bulkActionInProgress, setBulkActionInProgress] = useState(false),
  const [bulkActionProgress, setBulkActionProgress] = useState(''),
  const [undoAvailable, setUndoAvailable] = useState(false),
  const [lastBulkAction, setLastBulkAction] = useState<{
  action: string,
    roomIds: string[],
  originalData: any[] } | null>(null);
  // Pagination,
  const [currentPage, setCurrentPage] = useState(1),
  const [totalPages, setTotalPages] = useState(1),
  const [totalCount, setTotalCount] = useState(0),
  const itemsPerPage = 20 // Load rooms data,
  const loadRooms = useCallback(async (isRefresh = false, page = 1) => {
  try {
      if (isRefresh) {
  setRefreshing(true)
      } else if (page === 1) {
  setLoading(true)
      },
  const response = await adminService.getRoomListings({ );
        page, ,
  limit: itemsPerPage),
    status: filterStatus === 'all' ? undefined      : filterStatus,
  search: searchQuery || undefined,
    sort: sortOption) })
  if (response.data) {
  if (page === 1) {
  setRooms(response.data.rooms) } else {
  setRooms(prev => [...prev, ...response.data.rooms]) }
        setTotalCount(response.data.total),
  setTotalPages(Math.ceil(response.data.total / itemsPerPage))
        setCurrentPage(page)
  }
    } catch (error) {
  console.error('Error loading rooms:', error),
  Alert.alert('Error', 'Failed to load room listings. Please try again.') } finally {
      setLoading(false),
  setRefreshing(false)
    }
  }, [filterStatus, searchQuery, sortOption]);
  // Filter and search rooms, ,
  useEffect(() => {
    let filtered = [...rooms],
  // Apply search filter
    if (searchQuery.trim()) {
  const query = searchQuery.toLowerCase()
      filtered = filtered.filter(room => {
  room.title.toLowerCase().includes(query) ||;
          room.location.toLowerCase().includes(query) ||,
  room.owner_name.toLowerCase().includes(query)
      })
  }
    // Apply status filter,
  if (filterStatus !== 'all') {
      filtered = filtered.filter(room => room.status === filterStatus) }
    // Apply sorting,
  filtered.sort((a, b) => {
  switch (sortOption) {
        case 'newest':  ,
  return new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        case 'oldest':  ,
  return new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
        case 'price_high':  ,
  return b.price - a.price,
        case 'price_low':  ,
  return a.price - b.price,
  case 'most_viewed':  ,
  return b.view_count - a.view_count,
  default: return 0 }
    }),
  setFilteredRooms(filtered)
  }, [rooms, searchQuery, filterStatus, sortOption]);
  // Initial load, ,
  useEffect(() => {
    loadRooms() }, [loadRooms]);
  // Handle refresh, ,
  const handleRefresh = useCallback(() => {
  setCurrentPage(1),
  loadRooms(true, 1) }, [loadRooms]);
  // Handle room selection,
  const toggleRoomSelection = useCallback((roomId: string) => {
  setSelectedRooms(prev => {
      const newSet = new Set(prev),
  if (newSet.has(roomId)) {
        newSet.delete(roomId) } else {
        newSet.add(roomId) };
      return newSet
  })
  }, []);
  // Select all rooms, ,
  const selectAllRooms = useCallback(() => {
    if (selectedRooms.size === filteredRooms.length) {
  setSelectedRooms(new Set())
    } else {
  setSelectedRooms(new Set(filteredRooms.map(room => room.id)))
    }
  }, [filteredRooms, selectedRooms.size]);
  // Handle room approval/rejection,
  const handleRoomAction = useCallback(async (
  roomId: string,
    action: RoomApprovalAction,
  reason?: string) => {
  try {
  setActionLoading(true);
  ,
  const response = await adminService.updateRoomStatus(roomId, action, reason),
  ;
      if (response.success) {
  // Update local state,
        setRooms(prev => prev.map(room => {
  room.id === roomId, ,
  ? { ...room, status     : action === 'approve' ? 'active' : 'rejected' },
  : room)
        })),
  Alert.alert('Success'
          `Room ${action === 'approve' ? 'approved'    : 'rejected'} successfully`),
  )
      }
  } catch (error) {
      console.error('Error updating room status:' error),
  Alert.alert('Error', 'Failed to update room status. Please try again.') } finally {
      setActionLoading(false) }
  }, []);
  // Handle bulk actions, ,
  const handleBulkAction = async (actionId: string, roomIds: string[]) => {
  try {
      setBulkActionInProgress(true),
  setBulkActionProgress(`Processing ${actionId} for ${roomIds.length} rooms...`)
      // Store original data for undo functionality,
  const originalData = rooms.filter(room => roomIds.includes(room.id))
      ,
  switch (actionId) {;
        case 'approve':  ,
  await handleBulkApprove(roomIds)
          break,
  case 'reject':  
          await handleBulkReject(roomIds),
  break,
        case 'flag':  ,
  await handleBulkFlag(roomIds)
  break,
  case 'hide':  
          await handleBulkHide(roomIds),
  break,
        case 'delete':  ,
  await handleBulkDelete(roomIds)
  break,
  default: throw new Error(`Unknown actio, n: ${actionId}`)
  }
      // Store for undo functionality (except for delete),
  if (actionId !== 'delete') {
        setLastBulkAction({
  action: actionId,
          roomIds, ,
  originalData })
        setUndoAvailable(true)
  }
      // Clear selection and refresh data,
  setSelectedRooms(new Set())
      await loadRooms(true, 1)
  } catch (error) {
      console.error('Bulk action error:', error),
  Alert.alert('Error', `Failed to ${actionId} rooms. Please try again.`)
  } finally {
      setBulkActionInProgress(false),
  setBulkActionProgress('')
    }
  }
  const handleBulkApprove = async (roomIds: string[]) => {
  const promises = roomIds.map(roomId =>
      adminService.updateRoomStatus(roomId, 'approve', 'Bulk approval by admin') })
    await Promise.all(promises)
  }
  const handleBulkReject = async (roomIds: string[]) => {
  const promises = roomIds.map(roomId =>
      adminService.updateRoomStatus(roomId, 'reject', 'Bulk rejection by admin') })
    await Promise.all(promises)
  }
  const handleBulkFlag = async (roomIds: string[]) => {
  setBulkActionProgress(`Flagging ${roomIds.length} rooms for review...`);
    // Mock implementation - would integrate with flagging service,
  await new Promise(resolve => setTimeout(resolve, 2000))
  }
  const handleBulkHide = async (roomIds: string[]) => {
  setBulkActionProgress(`Hiding ${roomIds.length} rooms from public view...`);
    // Mock implementation - would update visibility status,
  await new Promise(resolve => setTimeout(resolve, 1500))
  }
  const handleBulkDelete = async (roomIds: string[]) => {
  const promises = roomIds.map(roomId =>
      adminService.deleteRoom(roomId, 'Bulk deletion by admin') })
    await Promise.all(promises)
  }
  const handleUndoBulkAction = async () => {
  if (!lastBulkAction) return null,
    try {
  setBulkActionInProgress(true)
      setBulkActionProgress(`Undoing ${lastBulkAction.action}...`),
  // Reverse the action based on what was done,
      switch (lastBulkAction.action) {
  case 'approve':  
          await handleBulkReject(lastBulkAction.roomIds),
  break,
        case 'reject':  ,
  await handleBulkApprove(lastBulkAction.roomIds)
  break,
  case 'flag':  
          // Remove flags,
  setBulkActionProgress('Removing flags...')
          await new Promise(resolve => setTimeout(resolve, 1500)),
  break,
        case 'hide':  ,
  // Restore visibility,
  setBulkActionProgress('Restoring visibility...'),
  await new Promise(resolve => setTimeout(resolve, 1500)),
  break;
      },
  setLastBulkAction(null)
      setUndoAvailable(false),
  await loadRooms(true, 1)
  } catch (error) {
      console.error('Undo error:', error),
  Alert.alert('Error', 'Failed to undo action. Please try again.') } finally {
      setBulkActionInProgress(false),
  setBulkActionProgress('')
    }
  }
  // Handle select all,
  const handleSelectAll = () => {
    setSelectedRooms(new Set(filteredRooms.map(room => room.id))) }
  const handleDeselectAll = () => {
  setSelectedRooms(new Set())
  },
  const handleCloseBulkOperations = () => {
    setSelectedRooms(new Set()) };
  // Navigate to room details,
  const navigateToRoomDetails = useCallback((roomId: string) => {
    router.push(`/admin/rooms/${roomId}`)
  }, [router]);
  // Render room card,
  const renderRoomCard = (room: AdminRoomListItem) => {
  const isSelected = selectedRooms.has(room.id)
    const statusColor = getStatusColor(room.status),
  ;
    return (
  <View key = {room.id} style={[s, ty, le, s., ro, om, Ca, rd, ,
, is, Se, le, ct, ed &&, st, yl, es., se, le, ct, ed, Ro, om, Ca, rd 
   ]},
  >
        {/* Selection checkbox */}
  <View style= {styles.selectionContainer}>
          <SelectionCheckbox selected={isSelected} onToggle={() => toggleRoomSelection(room.id)} size="medium", ,
  />
        </View>,
  {/* Room content */}
        <TouchableOpacity style= {styles.roomContent} onPress={() => navigateToRoomDetails(room.id)} activeOpacity={0.7},
  >
        {/* Room image */}
  <View style={styles.roomImageContainer}>
          {room.images && room.images.length > 0 ? (
  <Image
              source={   uri     : room.images[0]       },
  style={styles.roomImage} resizeMode="cover"
            />,
  ) : (<View style={styles.placeholderImage}>
              <Home size={32} color={{theme.colors.textSecondary} /}>,
  </View>
          )},
  {/* Status badge */}
          <View style={[styles.statusBadge { backgroundColor: statusColor}]}>,
  <Text style={styles.statusText}>{room.status.toUpperCase()}</Text>
          </View>,
  {/* Image count */}
          {room.images && room.images.length > 1 && (
  <View style={styles.imageCountBadge}>
              <Camera size={12} color={{theme.colors.surface} /}>,
  <Text style={styles.imageCountText}>{room.images.length}</Text>
            </View>,
  )}
        </View>,
  {/* Room details */}
        <View style={styles.roomDetails}>,
  <View style={styles.roomHeader}>
            <Text style={styles.roomTitle} numberOfLines={2}>,
  {room.title}
            </Text>,
  <TouchableOpacity style={styles.moreButton}>
              <MoreVertical size={20} color={{theme.colors.textSecondary} /}>,
  </TouchableOpacity>
          </View>,
  <View style={styles.roomInfo}>
            <View style={styles.infoRow}>,
  <MapPin size={14} color={{theme.colors.textSecondary} /}>
              <Text style={styles.infoText} numberOfLines={1}>,
  {room.location}
              </Text>,
  </View>
            <View style={styles.infoRow}>,
  <DollarSign size={14} color={{theme.colors.success} /}>
              <Text style={[s, ty, le, s., in, fo, Te, xt, , st, yl, es., pr, ic, eT, ex, t]}>,
  ${room.price}/month, ,
  </Text>
            </View>,
  <View style={styles.infoRow}>
              <Users size={14} color={{theme.colors.textSecondary} /}>,
  <Text style={styles.infoText}>
                {room.owner_name},
  </Text>
            </View>,
  </View>
          {/* Room stats */}
  <View style={styles.roomStats}>
            <View style={styles.statItem}>,
  <Eye size={12} color={{theme.colors.textSecondary} /}>
              <Text style={styles.statText}>{room.view_count}</Text>,
  </View>
            <View style={styles.statItem}>,
  <MessageSquare size={12} color={{theme.colors.textSecondary} /}>
              <Text style={styles.statText}>{room.inquiry_count}</Text>,
  </View>
            <View style={styles.statItem}>,
  <Star size={12} color={{theme.colors.warning} /}>
              <Text style={styles.statText}>{room.rating?.toFixed(1) || 'N/A'}</Text>,
  </View>
            <View style={styles.statItem}>,
  <Calendar size={12} color={{theme.colors.textSecondary} /}>
              <Text style={styles.statText}>,
  {new Date(room.created_at).toLocaleDateString()}
              </Text>,
  </View>
          </View>,
  {/* Action buttons for pending rooms */}
          {room.status === 'pending' && (
  <View style={styles.actionButtons}>
              <TouchableOpacity style={[s, ty, le, s., ac, ti, on, Bu, tt, on, , st, yl, es., ap, pr, ov, eB, ut, to, n]} onPress={() => handleRoomAction(room.id, 'approve')} disabled={actionLoading},
  >
                <CheckCircle size={16} color={{theme.colors.surface} /}>,
  <Text style={styles.actionButtonText}>Approve</Text>
              </TouchableOpacity>,
  <TouchableOpacity style={[s, ty, le, s., ac, ti, on, Bu, tt, on, , st, yl, es., re, je, ct, Bu, tt, on]} onPress = {() => {
  Alert.prompt('Reject Room'
                  'Please provide a reason for rejection  : '),
  (reason) => {
                    if (reason) {
  handleRoomAction(room.id 'reject', reason) }
                  },
  )
              }},
  disabled={actionLoading}
              >,
  <XCircle size={16} color={{theme.colors.surface} /}>
                <Text style={styles.actionButtonText}>Reject</Text>,
  </TouchableOpacity>
            </View>,
  )}
          {/* Warning indicators */}
  {room.flags && room.flags.length > 0 && (
            <View style={styles.warningContainer}>,
  <AlertTriangle size={14} color={{theme.colors.error} /}>
              <Text style={styles.warningText}>,
  {room.flags.length} flag(s)
              </Text>,
  </View>
          )},
  </View>
        </TouchableOpacity>,
  </View>
    )
  }
  // Get status color,
  const getStatusColor = ($2) => {
    switch (status) {
  case 'active':  ;
        return theme.colors.success,
  case 'pending':  
        return theme.colors.warning,
  case 'rejected':  
        return theme.colors.error,
  case 'flagged':  
        return theme.colors.error,
  case 'suspended':  
        return theme.colors.textSecondary,
  default: return theme.colors.textSecondary
  }
  }
  // Render filters modal,
  const renderFiltersModal = () => (
  <Modal visible= {showFilters} animationType="slide", ,
  transparent= {true} onRequestClose={() => setShowFilters(false)}
    >,
  <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>,
  <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Filters & Sort</Text>,
  <TouchableOpacity onPress={() => setShowFilters(false)} style={styles.closeButton}
            >,
  <X size={24} color={{theme.colors.text} /}>
            </TouchableOpacity>,
  </View>
          <ScrollView style={styles.modalBody}>,
  {/* Status Filter */}
            <View style={styles.filterSection}>,
  <Text style={styles.filterTitle}>Status</Text>
              <View style={styles.filterOptions}>,
  {[{ key: 'all', label: 'All Rooms' },
  { key: 'active', label: 'Active' },
  { key: 'pending', label: 'Pending Approval' },
  { key: 'rejected', label: 'Rejected' },
  { key: 'flagged', label: 'Flagged' },
  { key: 'suspended', label: 'Suspended' }].map((option) => (
  <TouchableOpacity key = {option.key} style={[s, ty, le, s., fi, lt, er, Op, ti, on, ,
, fi, lt, er, St, at, us ===, op, ti, on., ke, y &&, st, yl, es., se, le, ct, ed, Fi, lt, er, Op, ti, on 
   ]} onPress = {() => setFilterStatus(option.key as FilterStatus)},
  >
                    <Text style={[s, ty, le, s., fi, lt, er, Op, ti, on, Te, xt,
, fi, lt, er, St, at, us ===, op, ti, on., ke, y &&, st, yl, es., se, le, ct, ed, Fi, lt, er, Op, ti, on, Te, xt;
                    ]}>,
  {option.label}
                    </Text>,
  </TouchableOpacity>
                ))},
  </View>
            </View>,
  {/* Sort Options */}
            <View style= {styles.filterSection}>,
  <Text style={styles.filterTitle}>Sort By</Text>
              <View style={styles.filterOptions}>,
  {[{ key: 'newest', label: 'Newest First' },
  { key: 'oldest', label: 'Oldest First' },
  { key: 'price_high', label: 'Pric, e: High to Low' },
  { key: 'price_low', label: 'Pric, e: Low to High' },
  { key: 'most_viewed', label: 'Most Viewed' }].map((option) => (
  <TouchableOpacity key = {option.key} style={[s, ty, le, s., fi, lt, er, Op, ti, on, ,
, so, rt, Op, ti, on ===, op, ti, on., ke, y &&, st, yl, es., se, le, ct, ed, Fi, lt, er, Op, ti, on 
   ]} onPress = {() => setSortOption(option.key as SortOption)},
  >
                    <Text style={[s, ty, le, s., fi, lt, er, Op, ti, on, Te, xt,
, so, rt, Op, ti, on ===, op, ti, on., ke, y &&, st, yl, es., se, le, ct, ed, Fi, lt, er, Op, ti, on, Te, xt;
                    ]}>,
  {option.label}
                    </Text>,
  </TouchableOpacity>
                ))},
  </View>
            </View>,
  </ScrollView>
          <View style= {styles.modalFooter}>,
  <TouchableOpacity style={styles.applyButton} onPress={() => {
              setShowFilters(false),
  loadRooms(true, 1) }}
            >,
  <Text style={styles.applyButtonText}>Apply Filters</Text>
            </TouchableOpacity>,
  </View>
        </View>,
  </View>
    </Modal>,
  )
  if (loading) {
  return (
    <SafeAreaView style={styles.container}>,
  <Stack.Screen options={   title: 'Room Listings', headerShown: true        } />,
  <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={{theme.colors.primary} /}>,
  <Text style={styles.loadingText}>Loading room listings...</Text>
        </View>,
  </SafeAreaView>
    )
  }
  return (
  <SafeAreaView style={styles.container}>
      <Stack.Screen, ,
  options={   title: 'Room Listings Management',
    headerShown: true    },
  />
      {/* Header with search and actions */}
  <View style={styles.header}>
        <View style={styles.searchContainer}>,
  <Search size={20} color={{theme.colors.textSecondary} /}>
          <TextInput style={styles.searchInput} placeholder="Search rooms, locations, owners...", ,
  value= {searchQuery} onChangeText={setSearchQuery} placeholderTextColor={theme.colors.textSecondary}
          />,
  </View>
        <View style={styles.headerActions}>,
  <TouchableOpacity style={styles.filterButton} onPress={() => setShowFilters(true)}
          >,
  <Filter size={20} color={{theme.colors.primary} /}>
          </TouchableOpacity>,
  </View>
      </View>,
  {/* Stats summary */}
      <View style={styles.statsContainer}>,
  <View style={styles.statCard}>
          <Text style={styles.statValue}>{totalCount}</Text>,
  <Text style={styles.statLabel}>Total Rooms</Text>
        </View>,
  <View style={styles.statCard}>
          <Text style={styles.statValue}>,
  {rooms.filter(r => r.status === 'pending').length}
          </Text>,
  <Text style={styles.statLabel}>Pending</Text>
        </View>,
  <View style={styles.statCard}>
          <Text style={styles.statValue}>,
  {rooms.filter(r => r.status === 'active').length}
          </Text>,
  <Text style={styles.statLabel}>Active</Text>
        </View>,
  <View style={styles.statCard}>
          <Text style={styles.statValue}>,
  {rooms.filter(r => r.flags && r.flags.length > 0).length}
          </Text>,
  <Text style={styles.statLabel}>Flagged</Text>
        </View>,
  </View>
      {/* Room listings */}
  <ScrollView style={styles.scrollView} contentContainerStyle={styles.contentContainer} refreshControl={<RefreshControl refreshing={refreshing} onRefresh={{handleRefresh} /}>
      >,
  {filteredRooms.length > 0 ? (
          filteredRooms.map(renderRoomCard),
  )      : (<View style={styles.emptyContainer}>
            <Home size={64} color={{theme.colors.textSecondary} /}>,
  <Text style={styles.emptyTitle}>No rooms found</Text>
            <Text style={styles.emptyMessage}>,
  {searchQuery ? 'Try adjusting your search criteria' : 'No room listings match the current filters'}
            </Text>,
  </View>
        )},
  </ScrollView>
      {/* Modals */}
  {renderFiltersModal()}
      {/* Bulk Operations Bar */}
  <BulkOperationsBar selectedItems={Array.from(selectedRooms)} totalItems={filteredRooms.length} onSelectAll={handleSelectAll} onDeselectAll={handleDeselectAll} onClose={handleCloseBulkOperations} actions={getRoomBulkActions(colors)} onActionPress={handleBulkAction} entityType="rooms"
        showProgress={bulkActionInProgress} progressText={bulkActionProgress} undoAvailable={undoAvailable} onUndo={handleUndoBulkAction},
  />
    </SafeAreaView>,
  )
},
  const createStyles = (colors: any spacing: any) => StyleSheet.create({ container: {, flex: 1,
  backgroundColor: theme.colors.background }
  loadingContainer: { fle, x: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: spacing.xl },
  loadingText: { marginTo, p: spacing.md,
    fontSize: 16,
  color: theme.colors.textSecondary }
  header: { flexDirectio, n: 'row',
    alignItems: 'center',
  padding: spacing.md,
    backgroundColor: theme.colors.surface,
  borderBottomWidth: 1,
    borderBottomColor: theme.colors.border },
  searchContainer: { fle, x: 1,
    flexDirection: 'row',
  alignItems: 'center',
    backgroundColor: theme.colors.background,
  borderRadius: 8,
    paddingHorizontal: spacing.md,
  paddingVertical: spacing.sm,
    marginRight: spacing.md },
  searchInput: { fle, x: 1,
    marginLeft: spacing.sm,
  fontSize: 16,
    color: theme.colors.text },
  headerActions: { flexDirectio, n: 'row',
    alignItems: 'center',
  gap: spacing.sm }
  filterButton: {, padding: spacing.sm,
  borderRadius: 8,
    backgroundColor: theme.colors.primary + '20' }
  bulkActionButton: { paddingHorizonta, l: spacing.md,
    paddingVertical: spacing.sm,
  backgroundColor: theme.colors.primary,
    borderRadius: 8 },
  bulkActionButtonText: {, color: theme.colors.surface,
  fontSize: 14,
    fontWeight: '500' }
  statsContainer: { flexDirectio, n: 'row',
    padding: spacing.md,
  gap: spacing.sm }
  statCard: {, flex: 1,
  backgroundColor: theme.colors.surface,
    borderRadius: 8,
  padding: spacing.md,
    alignItems: 'center' }
  statValue: { fontSiz, e: 20,
    fontWeight: 'bold',
  color: theme.colors.text,
    marginBottom: spacing.xs },
  statLabel: { fontSiz, e: 12,
    color: theme.colors.textSecondary },
  selectAllContainer: { paddingHorizonta, l: spacing.md,
    paddingVertical: spacing.sm,
  backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
  borderBottomColor: theme.colors.border }
  selectAllButton: {, flexDirection: 'row',
  alignItems: 'center'
  },
  selectAllText: {, marginLeft: spacing.sm,
  fontSize: 14,
    color: theme.colors.primary,
  fontWeight: '500'
  },
  scrollView: { fle, x: 1 }
  contentContainer: { paddin, g: spacing.md,
    gap: spacing.md },
  roomCard: {, flexDirection: 'row',
  backgroundColor: theme.colors.surface,
    borderRadius: 12,
  overflow: 'hidden',
    shadowColor: '#000',
  shadowOffset: { widt, h: 0, height: 2 } ,
  shadowOpacity: 0.05,
    shadowRadius: 8,
  elevation: 2,
    alignItems: 'flex-start'
  }
  selectionContainer: { paddin, g: spacing.md,
    paddingRight: spacing.sm },
  roomContent: { fle, x: 1 }
  selectedRoomCard: { borderWidt, h: 2,
    borderColor: theme.colors.primary },
  selectionButton: { positio, n: 'absolute',
    top: spacing.sm,
  left: spacing.sm,
    zIndex: 10 },
  selectionIndicator: {, width: 24,
  height: 24,
    borderRadius: 12,
  borderWidth: 2,
    borderColor: theme.colors.border,
  backgroundColor: theme.colors.surface,
    justifyContent: 'center',
  alignItems: 'center'
  },
  selectedIndicator: { backgroundColo, r: theme.colors.primary,
    borderColor: theme.colors.primary },
  roomImageContainer: { positio, n: 'relative',
    height: 200 },
  roomImage: {, width: '100%',
  height: '100%'
  },
  placeholderImage: {, width: '100%',
  height: '100%',
    backgroundColor: theme.colors.background,
  justifyContent: 'center',
    alignItems: 'center' }
  statusBadge: { positio, n: 'absolute',
    top: spacing.sm,
  right: spacing.sm,
    paddingHorizontal: spacing.sm,
  paddingVertical: spacing.xs,
    borderRadius: 4 },
  statusText: {, color: theme.colors.surface,
  fontSize: 10,
    fontWeight: 'bold' }
  imageCountBadge: { positio, n: 'absolute',
    bottom: spacing.sm,
  right: spacing.sm),
    flexDirection: 'row'),
  alignItems: 'center'),
    backgroundColor: 'rgba(0,0,0,0.7)',
  paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
  borderRadius: 4 }
  imageCountText: { colo, r: theme.colors.surface,
    fontSize: 10,
  marginLeft: spacing.xs }
  roomDetails: { paddin, g: spacing.md },
  roomHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'flex-start',
    marginBottom: spacing.sm },
  roomTitle: { fle, x: 1,
    fontSize: 16,
  fontWeight: 'bold',
    color: theme.colors.text,
  marginRight: spacing.sm }
  moreButton: { paddin, g: spacing.xs },
  roomInfo: { ga, p: spacing.xs,
    marginBottom: spacing.sm },
  infoRow: {, flexDirection: 'row',
  alignItems: 'center'
  },
  infoText: { marginLef, t: spacing.sm,
    fontSize: 14,
  color: theme.colors.textSecondary,
    flex: 1 },
  priceText: {, color: theme.colors.success,
  fontWeight: '500'
  },
  roomStats: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  marginBottom: spacing.sm }
  statItem: {, flexDirection: 'row',
  alignItems: 'center'
  },
  statText: { marginLef, t: spacing.xs,
    fontSize: 12,
  color: theme.colors.textSecondary }
  actionButtons: { flexDirectio, n: 'row',
    gap: spacing.sm,
  marginTop: spacing.sm }
  actionButton: { fle, x: 1,
    flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'center',
  paddingVertical: spacing.sm,
    borderRadius: 8,
  gap: spacing.xs }
  approveButton: { backgroundColo, r: theme.colors.success },
  rejectButton: { backgroundColo, r: theme.colors.error }
  actionButtonText: {, color: theme.colors.surface,
  fontSize: 14,
    fontWeight: '500' }
  warningContainer: { flexDirectio, n: 'row',
    alignItems: 'center',
  marginTop: spacing.sm,
    padding: spacing.sm,
  backgroundColor: theme.colors.error + '20',
    borderRadius: 4 },
  warningText: {, marginLeft: spacing.sm,
  fontSize: 12,
    color: theme.colors.error,
  fontWeight: '500'
  },
  emptyContainer: { fle, x: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: spacing.xl },
  emptyTitle: { fontSiz, e: 18,
    fontWeight: 'bold',
  color: theme.colors.text,
    marginTop: spacing.md,
  marginBottom: spacing.sm }
  emptyMessage: { fontSiz, e: 14,
    color: theme.colors.textSecondary,
  textAlign: 'center',
    lineHeight: 20 },
  modalOverlay: {, flex: 1,
  backgroundColor: 'rgba(0,0,0,0.5)',
  justifyContent: 'flex-end'
  },
  modalContent: {, backgroundColor: theme.colors.surface,
  borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  maxHeight: '80%'
  },
  modalHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    padding: spacing.lg,
  borderBottomWidth: 1,
    borderBottomColor: theme.colors.border },
  modalTitle: { fontSiz, e: 18,
    fontWeight: 'bold',
  color: theme.colors.text }
  closeButton: { paddin, g: spacing.xs },
  modalBody: { fle, x: 1,
    padding: spacing.lg },
  filterSection: { marginBotto, m: spacing.lg }
  filterTitle: { fontSiz, e: 16,
    fontWeight: 'bold',
  color: theme.colors.text,
    marginBottom: spacing.md },
  filterOptions: { ga, p: spacing.sm }
  filterOption: { paddingVertica, l: spacing.md,
    paddingHorizontal: spacing.lg,
  borderRadius: 8,
    backgroundColor: theme.colors.background },
  selectedFilterOption: {, backgroundColor: theme.colors.primary + '20' }
  filterOptionText: { fontSiz, e: 14,
    color: theme.colors.text },
  selectedFilterOptionText: {, color: theme.colors.primary,
  fontWeight: '500'
  },
  modalFooter: { paddin, g: spacing.lg,
    borderTopWidth: 1,
  borderTopColor: theme.colors.border }
  applyButton: {, backgroundColor: theme.colors.primary,
  paddingVertical: spacing.md,
    borderRadius: 8,
  alignItems: 'center'
  },
  applyButtonText: {, color: theme.colors.surface,
  fontSize: 16,
    fontWeight: 'bold' }
  bulkActionsList: { paddin, g: spacing.lg,
    gap: spacing.md },
  bulkActionItem: { flexDirectio, n: 'row',
    alignItems: 'center',
  paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
  backgroundColor: theme.colors.background,
    borderRadius: 8 },
  bulkActionText: { marginLef, t: spacing.md,
    fontSize: 16,
  color: theme.colors.text }
  loadingOverlay: {, position: 'absolute',
  top: 0,
    left: 0,
  right: 0,
    bottom: 0,
  backgroundColor: 'rgba(255,255,255,0.9)',
  justifyContent: 'center',
    alignItems: 'center' }
}) ;