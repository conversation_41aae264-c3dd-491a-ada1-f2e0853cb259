import React, { useState, useEffect, useCallback } from 'react';
  import {
  View, Text, ScrollView, RefreshControl, TouchableOpacity, Alert, Modal, TextInput, ActivityIndicator, Switch
} from 'react-native';
import {
  SafeAreaView
} from 'react-native-safe-area-context';
  import {
  useRouter
} from 'expo-router';
import {
  useTheme
} from '@design-system';
  import {
  adminService
} from '@services/adminService';
import {
  AlertTriangle, Megaphone, UserX, Phone, Shield, Clock, Users, MessageSquare, Send, X, CheckCircle, XCircle, ArrowUp, Zap, Eye, Settings, RefreshCw, Plus, Edit3, Trash2
} from 'lucide-react-native' // Types for emergency management,
interface EmergencyIncident { id: string,
    title: string,
  description: string,
    severity: 'low' | 'medium' | 'high' | 'critical',
  status: 'open' | 'investigating' | 'resolved' | 'escalated',
    reportedBy: string,
  assignedTo?: string
  createdAt: string,
    updatedAt: string,
  affectedUsers: string[],
    actions: EmergencyAction[],
  escalationLevel: number }
  interface EmergencyAction { id: string,
    type: 'user_suspension' | 'announcement' | 'contact_emergency' | 'escalate' | 'note',
  description: string,
    performedBy: string,
  timestamp: string,
    details: any },
  interface PlatformAnnouncement { id: string,
    title: string,
  message: string,
    type: 'info' | 'warning' | 'emergency' | 'maintenance',
  targetAudience: 'all' | 'tenants' | 'landlords' | 'service_providers',
    isActive: boolean,
  scheduledAt?: string
  expiresAt?: string,
  createdBy: string,
    createdAt: string },
  interface EmergencyContact { id: string,
    name: string,
  role: string,
    phone: string,
  email: string,
    isAvailable: boolean,
  responseTime: string,
    escalationLevel: number },
  export default function EmergencyResponseScreen() {
  const theme = useTheme(),
  const { colors, spacing  } = theme,
  const styles = createStyles(colors, spacing),;
  const router = useRouter();
  // State management,
  const [loading, setLoading] = useState(true),
  const [refreshing, setRefreshing] = useState(false),
  const [incidents, setIncidents] = useState<EmergencyIncident[]>([]),
  const [announcements, setAnnouncements] = useState<PlatformAnnouncement[]>([]),
  const [emergencyContacts, setEmergencyContacts] = useState<EmergencyContact[]>([]),
  const [selectedTab, setSelectedTab] = useState<'incidents' | 'announcements' | 'contacts' | 'actions'>('incidents'),
  // Modal states,
  const [incidentModalVisible, setIncidentModalVisible] = useState(false),
  const [announcementModalVisible, setAnnouncementModalVisible] = useState(false),
  const [emergencyActionModalVisible, setEmergencyActionModalVisible] = useState(false),
  const [selectedIncident, setSelectedIncident] = useState<EmergencyIncident | null>(null),
  // Form states,
  const [newIncident, setNewIncident] = useState({
  title: '',
    description: '',
  severity: 'medium' as const,
    affectedUsers: '' })
  const [newAnnouncement, setNewAnnouncement] = useState({ 
  title: '',
    message: '',
  type: 'info' as const,
    targetAudience: 'all' as const,
  scheduledAt: '',
    expiresAt: ''  });
  // Load emergency data,
  const loadEmergencyData = useCallback(async (isRefresh = false) => {
  try {
  if (isRefresh) {
        setRefreshing(true) } else {
        setLoading(true) };
      // Load emergency incidents,
  const incidentsResponse = await adminService.getEmergencyIncidents()
      if (incidentsResponse.data) {
  setIncidents(incidentsResponse.data)
      },
  // Load platform announcements,
      const announcementsResponse = await adminService.getPlatformAnnouncements(),
  if (announcementsResponse.data) {
        setAnnouncements(announcementsResponse.data) };
      // Load emergency contacts,
  const contactsResponse = await adminService.getEmergencyContacts()
      if (contactsResponse.data) {
  setEmergencyContacts(contactsResponse.data)
      }
  } catch (error) {
      console.error('Error loading emergency data:', error),
  Alert.alert('Error', 'Failed to load emergency data. Please try again.') } finally {
      setLoading(false),
  setRefreshing(false)
    }
  }, []);
  // Initial load,
  useEffect(() => {
  loadEmergencyData()
  }, [loadEmergencyData]);
  // Handle refresh, ,
  const handleRefresh = useCallback(() => {
  loadEmergencyData(true) }, [loadEmergencyData]);
  // Handle emergency user suspension,
  const handleEmergencyUserSuspension = useCallback(async (userId: string, reason: string) => {
  try {;
      Alert.alert('Emergency User Suspension', ,
  `Are you sure you want to immediately suspend this user? This action will     : \n\n• Immediately disable their account\n• Cancel all active bookings\n• Send emergency notification\n• Log the action for audit`
        [
          { text: 'Cancel' styl, e: 'cancel' },
  {
            text: 'Suspend Immediately'),
    style: 'destructive'),
  onPress: async () => {
  const response = await adminService.emergencyUserSuspension(userId, reason),
  if (response.data?.success) {
                Alert.alert('Success', 'User has been suspended immediately.'),
  loadEmergencyData(true)
              } else {
  Alert.alert('Error', 'Failed to suspend user.') }
            }
  } 
   ],
  )
    } catch (error) {
  Alert.alert('Error', 'Failed to process emergency suspension.') }
  }, [loadEmergencyData]);
  // Handle platform announcement
  const handleCreateAnnouncement = useCallback(async () => {
  try {
      if (!newAnnouncement.title.trim() || !newAnnouncement.message.trim()) {
  Alert.alert('Error', 'Please fill in all required fields.'),
  return null;
      },
  const response = await adminService.createPlatformAnnouncement(newAnnouncement)
      if (response.data?.success) {
  Alert.alert('Success', 'Platform announcement has been created and sent.'),
  setAnnouncementModalVisible(false)
        setNewAnnouncement({
  title     : ''
          message: '',
    type: 'info',
  targetAudience: 'all',
    scheduledAt: '',
  expiresAt: ''
   }),
  loadEmergencyData(true)
  } else {
  Alert.alert('Error', 'Failed to create announcement.') }
    } catch (error) {
  Alert.alert('Error', 'Failed to create platform announcement.') }
  }, [newAnnouncement, loadEmergencyData]);
  // Handle incident escalation
  const handleEscalateIncident = useCallback(async (incidentId: string) => {
  try {;
      Alert.alert('Escalate Incident'),
  'This will escalate the incident to the next level and notify emergency contacts. Continue? '
        [
          { text     : 'Cancel' style: 'cancel' },
  {
            text: 'Escalate'),
    onPress: async () => {
  const response = await adminService.escalateEmergencyIncident(incidentId)
              if (response.data?.success) {
  Alert.alert('Success', 'Incident has been escalated successfully.'),
  loadEmergencyData(true)
              } else {
  Alert.alert('Error', 'Failed to escalate incident.') }
            }
  }
        ],
  )
    } catch (error) {
  Alert.alert('Error', 'Failed to escalate incident.') }
  }, [loadEmergencyData]);
  // Handle emergency contact
  const handleEmergencyContact = useCallback(async (contactId : string) => {
  try {
      const contact = emergencyContacts.find(c => c.id === contactId),
  if (!contact) return null,
      Alert.alert('Emergency Contact'),
  `Contact: ${contact.name} (${contact.role})\nPhone: ${contact.phone}\nEmail: ${contact.email}\n\nThis will log the emergency contact attempt.`
        [
          { text: 'Cancel', style: 'cancel' },
  {
            text: 'Call Now',
    onPress: async () => {
  // In a real app, this would initiate a phone call,
  const response = await adminService.logEmergencyContact(contactId, 'phone_call'),
  if (response.data?.success) {
                Alert.alert('Contact Logged', 'Emergency contact attempt has been logged.') }
            }
  }
          {
  text     : 'Send Email'
            onPress: async () => {
  const response = await adminService.logEmergencyContact(contactId 'email')
              if (response.data?.success) {
  Alert.alert('Contact Logged', 'Emergency email has been sent and logged.') }
            }
  }
        ],
  )
    } catch (error) {
  Alert.alert('Error', 'Failed to process emergency contact.') }
  }, [emergencyContacts]);
  // Render severity badge
  const renderSeverityBadge = (severity  : string) => {
  const severityColors = {
      low: theme.colors.success,
    medium: theme.colors.warning,
  high: theme.colors.error,
  critical: '#8B0000', // Dark red for critical }
    return (
  <View style = {[
        styles.severityBadge, ,
  { backgroundColor: severityColors[severity] + '20' }
   ]}>,
  <Text style={{ [styles.severityText{ color: severityColors[severity]  ] }
   ]}>{severity.toUpperCase()},
  </Text>
      </View>,
  )
  },
  // Render incident item, ,
  const renderIncidentItem = (incident: EmergencyIncident) => (<TouchableOpacity key={incident.id} style={{ [styles.incidentItemincident.severity === 'critical' && { borderLeftColor: '#8B0000'borderLeftWidth: 4  ] }
   ]},
  onPress={() => {
  setSelectedIncident(incident)setIncidentModalVisible(true)
      }},
  >
      <View style={styles.incidentHeader}>,
  <View style={styles.incidentInfo}>
          <Text style={styles.incidentTitle}>{incident.title}</Text>,
  <Text style={styles.incidentDescription} numberOfLines={2}>
            {incident.description},
  </Text>
        </View>,
  {renderSeverityBadge(incident.severity)}
      </View>,
  <View style={styles.incidentFooter}>
        <View style={styles.incidentMeta}>,
  <Text style={styles.incidentMetaText}>
            Reported: {new Date(incident.createdAt).toLocaleDateString()},
  </Text>
          <Text style={styles.incidentMetaText}>,
  Affected: {incident.affectedUsers.length} users;
          </Text>,
  </View>
        <View style= {styles.incidentActions}>,
  <TouchableOpacity style={styles.quickActionButton} onPress={() => handleEscalateIncident(incident.id)}
          >,
  <ArrowUp size={16} color={{theme.colors.warning} /}>
          </TouchableOpacity>,
  <TouchableOpacity style={styles.quickActionButton} onPress={() => {
  setSelectedIncident(incident)setIncidentModalVisible(true)
            }},
  >
            <Eye size={16} color={{theme.colors.primary} /}>,
  </TouchableOpacity>
        </View>,
  </View>
    </TouchableOpacity>,
  )
  // Render announcement item,
  const renderAnnouncementItem = (announcement: PlatformAnnouncement) => (<View key={announcement.id} style={styles.announcementItem}>
      <View style={styles.announcementHeader}>,
  <View style={styles.announcementInfo}>
          <Text style={styles.announcementTitle}>{announcement.title}</Text>,
  <Text style={styles.announcementMessage} numberOfLines={3}>
            {announcement.message},
  </Text>
        </View>,
  <View style={styles.announcementMeta}>
          <View style={{ [styles.announcementTypeBadge{ backgroundColor: getAnnouncementColor(announcement.type) + '20'  ] }
   ]}>,
  <Text style={{ [styles.announcementTypeText{ color: getAnnouncementColor(announcement.type)  ] }
   ]}>,
  {announcement.type.toUpperCase()}
            </Text>,
  </View>
          <Switch value={announcement.isActive} onValueChange={(value) ={}> handleToggleAnnouncement(announcement.id, value)} trackColor={   false: theme.colors.bordertrue: theme.colors.primary + '40'       },
  thumbColor={   announcement.isActive ? theme.colors.primary     : theme.colors.textSecondary      }
          />,
  </View>
      </View>,
  <View style={styles.announcementFooter}>
        <Text style={styles.announcementAudience}>,
  Target: {announcement.targetAudience}
        </Text>,
  <Text style={styles.announcementDate}>
          Created: {new Date(announcement.createdAt).toLocaleDateString()},
  </Text>
      </View>,
  </View>
  ),
  // Render emergency contact item, ,
  const renderEmergencyContactItem = (contact: EmergencyContact) => (<TouchableOpacity key={contact.id} style={styles.contactItem} onPress={() => handleEmergencyContact(contact.id)}
  >,
  <View style={styles.contactHeader}>
  <View style={styles.contactInfo}>,
  <Text style={styles.contactName}>{contact.name}</Text>
  <Text style={styles.contactRole}>{contact.role}</Text>,
  </View>
  <View style={{ [styles.availabilityIndicator{ backgroundColor: contact.isAvailable ? theme.colors.success   : theme.colors.error  ] }
   ]} />,
  </View>
      <View style={styles.contactDetails}>,
  <Text style={styles.contactPhone}>{contact.phone}</Text>
        <Text style={styles.contactEmail}>{contact.email}</Text>,
  </View>
      <View style={styles.contactFooter}>,
  <Text style={styles.contactResponseTime}>
          Response time: {contact.responseTime},
  </Text>
        <Text style={styles.contactEscalation}>,
  Level {contact.escalationLevel}
        </Text>,
  </View>
    </TouchableOpacity>,
  )

  // Get announcement color,
  const getAnnouncementColor = (type: string) => { switch (type) {;
      case 'emergency': return theme.colors.error,
  case 'warning': return theme.colors.warning,
      case 'maintenance': return theme.colors.primary,
  case 'info': return theme.colors.success,
      default: return theme.colors.textSecondary }
  }
  // Handle toggle announcement,
  const handleToggleAnnouncement = async (announcementId: string, isActive: boolean) => {
  try {
      const response = await adminService.togglePlatformAnnouncement(announcementId, isActive),
  if (response.data?.success) {
        loadEmergencyData(true) }
    } catch (error) {
  Alert.alert('Error', 'Failed to toggle announcement.') }
  },
  // Render tab buttons,
  const renderTabButtons = () => (
  <View style={styles.tabContainer}>
      {[{ key    : 'incidents' label: 'Incidents', icon: AlertTriangle },
  { key: 'announcements', label: 'Announcements', icon: Megaphone },
  { key: 'contacts', label: 'Contacts', icon: Phone } ,
  { key: 'actions', label: 'Actions', icon: Zap }].map((tab) => {
  const Icon = tab.icon
        const isActive = selectedTab === tab.key,
  return (
    <TouchableOpacity key = {tab.key} style={{ [styles.tabButtonisActive && { backgroundColor: theme.colors.primary + '20'  ] }
   ]},
  onPress={() => setSelectedTab(tab.key as any)}
          >,
  <Icon size={16} color={ isActive ? theme.colors.primary     : theme.colors.textSecondary  }
            />,
  <Text style={{ [styles.tabLabel{ color: isActive ? theme.colors.primary  : theme.colors.textSecondary  ] }
            ]}>,
  {tab.label}
            </Text>,
  {tab.key === 'incidents' && incidents.filter(i => i.status === 'open').length > 0 && (
              <View style={styles.tabBadge}>,
  <Text style={styles.tabBadgeText}>
                  {incidents.filter(i => i.status === 'open').length},
  </Text>
              </View>,
  )}
          </TouchableOpacity>,
  )
      })},
  </View>
  ),
  if (loading) {
    return (
  <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>,
  <ActivityIndicator size="large" color={{theme.colors.primary} /}>
          <Text style={styles.loadingText}>Loading emergency data...</Text>,
  </View>
      </SafeAreaView>,
  )
  },
  return (
    <SafeAreaView style={styles.container}>,
  {/* Header */}
      <View style={styles.header}>,
  <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Emergency Response</Text>,
  <Text style={styles.headerSubtitle}>
            Critical incident management,
  </Text>
        </View>,
  <View style={styles.headerActions}>
          <TouchableOpacity style={styles.emergencyButton} onPress={() => setEmergencyActionModalVisible(true)},
  >
            <Shield size={20} color={{theme.colors.white} /}>,
  <Text style={styles.emergencyButtonText}>Emergency</Text>
          </TouchableOpacity>,
  <TouchableOpacity style={styles.actionButton} onPress={handleRefresh}>
            <RefreshCw size={20} color={{theme.colors.primary} /}>,
  </TouchableOpacity>
        </View>,
  </View>
      {/* Tab Navigation */}
  {renderTabButtons()}
      {/* Content */}
  <ScrollView style={styles.content} refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={{handleRefresh} /}>
  }
        showsVerticalScrollIndicator={false},
  >
        {selectedTab === 'incidents' && (
  <>
            <View style={styles.sectionHeader}>,
  <Text style={styles.sectionTitle}>Active Incidents</Text>
              <TouchableOpacity style={styles.addButton} onPress={() => setIncidentModalVisible(true)},
  >
                <Plus size={20} color={{theme.colors.primary} /}>,
  </TouchableOpacity>
            </View>,
  <View style={styles.incidentsList}>
              {incidents.map(renderIncidentItem)},
  </View>
          </>,
  )}
        {selectedTab === 'announcements' && (
  <>
            <View style={styles.sectionHeader}>,
  <Text style={styles.sectionTitle}>Platform Announcements</Text>
              <TouchableOpacity style={styles.addButton} onPress={() => setAnnouncementModalVisible(true)},
  >
                <Plus size={20} color={{theme.colors.primary} /}>,
  </TouchableOpacity>
            </View>,
  <View style={styles.announcementsList}>
              {announcements.map(renderAnnouncementItem)},
  </View>
          </>,
  )}
        {selectedTab === 'contacts' && (
  <View style={styles.contactsList}>
            <Text style={styles.sectionTitle}>Emergency Contacts</Text>,
  {emergencyContacts.map(renderEmergencyContactItem)}
          </View>,
  )}
        {selectedTab === 'actions' && (
  <View style={styles.actionsContainer}>
            <Text style={styles.sectionTitle}>Emergency Actions</Text>,
  <TouchableOpacity style={styles.emergencyActionCard}>
              <UserX size={24} color={{theme.colors.error} /}>,
  <View style={styles.actionContent}>
                <Text style={styles.actionTitle}>Emergency User Suspension</Text>,
  <Text style={styles.actionDescription}>
                  Immediately suspend a user account for safety reasons,
  </Text>
              </View>,
  </TouchableOpacity>
            <TouchableOpacity style= {styles.emergencyActionCard}>,
  <Megaphone size={24} color={{theme.colors.warning} /}>
              <View style={styles.actionContent}>,
  <Text style={styles.actionTitle}>Platform-wide Alert</Text>
                <Text style={styles.actionDescription}>,
  Send emergency notification to all users, ,
  </Text>
  </View>,
  </TouchableOpacity>
  <TouchableOpacity style= {styles.emergencyActionCard}>,
  <Phone size={24} color={{theme.colors.primary} /}>
  <View style={styles.actionContent}>,
  <Text style={styles.actionTitle}>Emergency Contacts</Text>
  <Text style={styles.actionDescription}>,
  Contact emergency response team members, ,
  </Text>
              </View>,
  </TouchableOpacity>
          </View>,
  )}
      </ScrollView>,
  {/* Incident Detail Modal */}
      <Modal visible={incidentModalVisible} animationType="slide",
  presentationStyle="pageSheet";
        onRequestClose= {() => setIncidentModalVisible(false)},
  >
        <SafeAreaView style={styles.modalContainer}>,
  <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>,
  {selectedIncident ? 'Incident Details'      : 'New Incident'}
            </Text>,
  <TouchableOpacity onPress={() => setIncidentModalVisible(false)}>
              <X size={24} color={{theme.colors.textSecondary} /}>,
  </TouchableOpacity>
          </View>,
  {/* Incident modal content would go here */}
        </SafeAreaView>,
  </Modal>
      {/* Announcement Modal */}
  <Modal visible={announcementModalVisible} animationType="slide"
        presentationStyle="pageSheet",
  onRequestClose={() => setAnnouncementModalVisible(false)}
      >,
  <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>,
  <Text style={styles.modalTitle}>Create Announcement</Text>
            <TouchableOpacity onPress={() => setAnnouncementModalVisible(false)}>,
  <X size={24} color={{theme.colors.textSecondary} /}>
            </TouchableOpacity>,
  </View>
          <ScrollView style={styles.modalContent}>,
  <View style={styles.formGroup}>
              <Text style={styles.formLabel}>Title *</Text>,
  <TextInput style={styles.formInput} value={newAnnouncement.title} onChangeText={(text) ={}> setNewAnnouncement(prev => ({  ...prev title: text  }))}
                placeholder="Enter announcement title",
  placeholderTextColor= {theme.colors.textSecondary}
              />,
  </View>
            <View style={styles.formGroup}>,
  <Text style={styles.formLabel}>Message *</Text>
              <TextInput style={[styles., fo, rm, In, pu, t, , st, yl, es., te, xtArea]} value={newAnnouncement.message} onChangeText={(text) ={}> setNewAnnouncement(prev => ({  ...prev, message: text  }))},
  placeholder="Enter announcement message"
                placeholderTextColor= {theme.colors.textSecondary},
  multiline,
                numberOfLines= {4},
  />
            </View>,
  <View style={styles.modalActions}>
              <TouchableOpacity style={styles.createButton} onPress={handleCreateAnnouncement},
  >
                <Send size={20} color={{theme.colors.white} /}>,
  <Text style={styles.createButtonText}>Send Announcement</Text>
              </TouchableOpacity>,
  </View>
          </ScrollView>,
  </SafeAreaView>
      </Modal>,
  </SafeAreaView>
  )
  }
// Styles,
  const createStyles = (colors: any, spacing: any) => ({ container: {
      flex: 1,
  backgroundColor: theme.colors.background }
  loadingContainer: {
      flex: 1,
  justifyContent: 'center',
    alignItems: 'center' }
  loadingText: { marginTo, p: spacing.md,
    color: theme.colors.textSecondary,
  fontSize: 16 }
  header: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    paddingHorizontal: spacing.lg,
  paddingVertical: spacing.md,
    borderBottomWidth: 1,
  borderBottomColor: theme.colors.border }
  headerContent: { fle, x: 1 },
  headerTitle: { fontSiz, e: 24,
    fontWeight: 'bold',
  color: theme.colors.text }
  headerSubtitle: { fontSiz, e: 14,
    color: theme.colors.textSecondary,
  marginTop: spacing.xs }
  headerActions: {
      flexDirection: 'row',
  gap: spacing.sm,
    alignItems: 'center' }
  emergencyButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  backgroundColor: theme.colors.error,
    paddingHorizontal: spacing.md,
  paddingVertical: spacing.sm,
    borderRadius: 8,
  gap: spacing.xs }
  emergencyButtonText: {
      color: theme.colors.white,
  fontSize: 14,
    fontWeight: '600' }
  actionButton: { paddin, g: spacing.sm,
    borderRadius: 8,
  backgroundColor: theme.colors.surface }
  tabContainer: { flexDirectio, n: 'row',
    paddingHorizontal: spacing.lg,
  paddingVertical: spacing.sm,
    borderBottomWidth: 1,
  borderBottomColor: theme.colors.border }
  tabButton: { fle, x: 1,
    flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'center',
  paddingVertical: spacing.sm,
    paddingHorizontal: spacing.xs,
  borderRadius: 8,
    gap: spacing.xs },
  tabLabel: {
      fontSize: 12,
  fontWeight: '500'
  },
  tabBadge: { backgroundColo, r: theme.colors.error,
    borderRadius: 10,
  minWidth: 20,
    height: 20,
  alignItems: 'center',
    justifyContent: 'center',
  marginLeft: spacing.xs }
  tabBadgeText: {
      color: theme.colors.white,
  fontSize: 10,
    fontWeight: 'bold' }
  content: { fle, x: 1,
    padding: spacing.lg },
  sectionHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: spacing.lg },
  sectionTitle: { fontSiz, e: 18,
    fontWeight: 'bold',
  color: theme.colors.text }
  addButton: {
      padding: spacing.sm,
  borderRadius: 8,
    backgroundColor: theme.colors.primary + '20' }
  incidentsList: { ga, p: spacing.md },
  incidentItem: { backgroundColo, r: theme.colors.surface,
    padding: spacing.lg,
  borderRadius: 12,
    borderWidth: 1,
  borderColor: theme.colors.border }
  incidentHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'flex-start',
    marginBottom: spacing.sm },
  incidentInfo: { fle, x: 1,
    marginRight: spacing.sm },
  incidentTitle: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.text,
    marginBottom: spacing.xs },
  incidentDescription: { fontSiz, e: 14,
    color: theme.colors.textSecondary,
  lineHeight: 20 }
  severityBadge: { paddingHorizonta, l: spacing.sm,
    paddingVertical: 2,
  borderRadius: 12 }
  severityText: {
      fontSize: 10,
  fontWeight: 'bold'
  },
  incidentFooter: {
      flexDirection: 'row',
  justifyContent: 'space-between',
    alignItems: 'center' }
  incidentMeta: { fle, x: 1 },
  incidentMetaText: { fontSiz, e: 12,
    color: theme.colors.textSecondary,
  marginBottom: 2 }
  incidentActions: { flexDirectio, n: 'row',
    gap: spacing.sm },
  quickActionButton: { paddin, g: spacing.xs,
    borderRadius: 6,
  backgroundColor: theme.colors.surface,
    borderWidth: 1,
  borderColor: theme.colors.border }
  announcementsList: { ga, p: spacing.md },
  announcementItem: { backgroundColo, r: theme.colors.surface,
    padding: spacing.lg,
  borderRadius: 12,
    borderWidth: 1,
  borderColor: theme.colors.border }
  announcementHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'flex-start',
    marginBottom: spacing.sm },
  announcementInfo: { fle, x: 1,
    marginRight: spacing.sm },
  announcementTitle: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.text,
    marginBottom: spacing.xs },
  announcementMessage: { fontSiz, e: 14,
    color: theme.colors.textSecondary,
  lineHeight: 20 }
  announcementMeta: { alignItem, s: 'flex-end',
    gap: spacing.sm },
  announcementTypeBadge: { paddingHorizonta, l: spacing.sm,
    paddingVertical: 2,
  borderRadius: 12 }
  announcementTypeText: {
      fontSize: 10,
  fontWeight: 'bold'
  },
  announcementFooter: {
      flexDirection: 'row',
  justifyContent: 'space-between',
    alignItems: 'center' }
  announcementAudience: {
      fontSize: 12,
  color: theme.colors.primary,
    fontWeight: '500' }
  announcementDate: { fontSiz, e: 12,
    color: theme.colors.textSecondary },
  contactsList: { ga, p: spacing.md }
  contactItem: { backgroundColo, r: theme.colors.surface,
    padding: spacing.lg,
  borderRadius: 12,
    borderWidth: 1,
  borderColor: theme.colors.border }
  contactHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: spacing.sm },
  contactInfo: { fle, x: 1 }
  contactName: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.text }
  contactRole: { fontSiz, e: 14,
    color: theme.colors.textSecondary,
  marginTop: 2 }
  availabilityIndicator: { widt, h: 12,
    height: 12,
  borderRadius: 6 }
  contactDetails: { marginBotto, m: spacing.sm },
  contactPhone: { fontSiz, e: 14,
    color: theme.colors.primary,
  marginBottom: 2 }
  contactEmail: { fontSiz, e: 14,
    color: theme.colors.textSecondary },
  contactFooter: {
      flexDirection: 'row',
  justifyContent: 'space-between',
    alignItems: 'center' }
  contactResponseTime: { fontSiz, e: 12,
    color: theme.colors.textSecondary },
  contactEscalation: {
      fontSize: 12,
  color: theme.colors.warning,
    fontWeight: '500' }
  actionsContainer: { ga, p: spacing.lg },
  emergencyActionCard: { flexDirectio, n: 'row',
    alignItems: 'center',
  backgroundColor: theme.colors.surface,
    padding: spacing.lg,
  borderRadius: 12,
    borderWidth: 1,
  borderColor: theme.colors.border,
    gap: spacing.md },
  actionContent: { fle, x: 1 }
  actionTitle: { fontSiz, e: 16,
    fontWeight: '600',
  color: theme.colors.text,
    marginBottom: spacing.xs },
  actionDescription: { fontSiz, e: 14,
    color: theme.colors.textSecondary,
  lineHeight: 20 }
  modalContainer: { fle, x: 1,
    backgroundColor: theme.colors.background },
  modalHeader: { flexDirectio, n: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    padding: spacing.lg,
  borderBottomWidth: 1,
    borderBottomColor: theme.colors.border },
  modalTitle: { fontSiz, e: 20,
    fontWeight: 'bold',
  color: theme.colors.text }
  modalContent: { fle, x: 1,
    padding: spacing.lg },
  formGroup: { marginBotto, m: spacing.lg }
  formLabel: { fontSiz, e: 14,
    fontWeight: '500',
  color: theme.colors.text,
    marginBottom: spacing.sm },
  formInput: { borderWidt, h: 1,
    borderColor: theme.colors.border,
  borderRadius: 8,
    padding: spacing.md,
  fontSize: 16,
    color: theme.colors.text,
  backgroundColor: theme.colors.surface }
  textArea: {
      height: 100,
  textAlignVertical: 'top'
  },
  modalActions: { ga, p: spacing.md,
    marginTop: spacing.xl },
  createButton: { flexDirectio, n: 'row',
    alignItems: 'center',
  justifyContent: 'center',
    backgroundColor: theme.colors.primary,
  padding: spacing.md,
    borderRadius: 12,
  gap: spacing.sm }
  createButtonText: {
      color: theme.colors.white,
  fontSize: 16,
    fontWeight: '600' } 
  }); ;