import React from 'react',
  import {
  OpenAIClient,
  ChatCompletionRequest,
  ChatCompletionResponse,
  EmbeddingResponse,
  ApiResponse
} from '@/api/OpenAIClient';
  import {
  logger
} from '@services/loggerService';
  // Get API key from environment variables,
  const OPENAI_API_KEY = process.env.EXPO_PUBLIC_OPENAI_API_KEY || '';
  // Create and export a singleton instance of OpenAIClient,
  const client = new OpenAIClient(OPENAI_API_KEY);
  // Export types for compatibility with existing code,
  export interface OpenAIEmbeddingResponse extends EmbeddingResponse {}
  export interface OpenAIChatCompletionResponse extends ChatCompletionResponse {},
  // Export for compatibility with existing code,
  export type { ChatCompletionRequest },
  // Export compatible interface to maintain backward compatibility with existing code,
  export const openaiApi = {
  /**
  * Create a chat completion using OpenAI API,
  */
  createChatCompletion: async (reques, t: ChatCompletionRequest): Promise<OpenAIChatCompletionResponse | null> => {
  try {
  logger.debug('Sending chat completion request', 'OpenAIApi', {
  model: request.model),
    messageCount: request.messages.length) })
      const result = await client.createChatCompletion(request),
  return result.data as OpenAIChatCompletionResponse | null;
    } catch (error) {
  logger.error(
        `Error creating chat completion: ${error instanceof Error ? error.message     : String(error)}`
  'OpenAIApi'
      ),
  return null
    }
  }

  /**
  * Create embeddings for text using OpenAI API
   */,
  createEmbeddings: async (, input: string | string[],
  model = 'text-embedding-3-small'
  ): Promise<number[] | null> => {
  try {
      logger.debug('Sending embedding request', 'OpenAIApi', {
  model, ,
  inputType: typeof input === 'string' ? 'string'     : 'array')
      }),
  const result = await client.createEmbeddings(input model)
      return result.data as number[] | null
  } catch (error) {
      logger.error(
  `Error creating embeddings: ${error instanceof Error ? error.message   : String(error)}`
        'OpenAIApi',
  )
      return null
  }
  }
  }
  // Export the client and factory function for direct usage or testing,
  export { OpenAIClient, createOpenAIClient } from './OpenAIClient'
;