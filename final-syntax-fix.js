#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Function to fix all syntax errors in verification-queue.tsx
function fixVerificationQueueFile() {
  const filePath = path.join(process.cwd(), 'src/app/(admin)/verification-queue.tsx');
  
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Fix 1: Interface property syntax
    content = content.replace(
      /userEmail: string;, type: 'identity' \| 'background' \| 'reference',/g,
      "userEmail: string;\n  type: 'identity' | 'background' | 'reference';"
    );
    
    // Fix 2: Switch statement default case
    content = content.replace(
      /return theme\.colors\.info;, default: return theme\.colors\.textSecondary;/g,
      'return theme.colors.info;\n      default: return theme.colors.textSecondary;'
    );
    
    // Fix 3: Object literal with leading comma
    content = content.replace(
      /const review: VerificationReview = \{, submissionId: selectedSubmission\.id,/g,
      'const review: VerificationReview = {\n        submissionId: selectedSubmission.id,'
    );
    
    // Fix 4: Style array with missing comma
    content = content.replace(
      /borderColor: theme\.colors\.primaryborderWidth: 2/g,
      'borderColor: theme.colors.primary,\n                    borderWidth: 2'
    );
    
    // Fix 5: Style array with missing comma
    content = content.replace(
      /styles\.statusBadge\{ backgroundColor: getStatusColor\(submission\.status\) \}/g,
      'styles.statusBadge,\n                      { backgroundColor: getStatusColor(submission.status) }'
    );
    
    // Fix 6: Alert.alert missing comma
    content = content.replace(
      /Alert\.alert\('Document Viewer''Full-size document viewer would open here'\);/g,
      "Alert.alert('Document Viewer', 'Full-size document viewer would open here');"
    );
    
    // Fix 7: Alert.alert missing comma
    content = content.replace(
      /Alert\.alert\('Photo Viewer''Full-size photo viewer would open here'\);/g,
      "Alert.alert('Photo Viewer', 'Full-size photo viewer would open here');"
    );
    
    fs.writeFileSync(filePath, content, 'utf8');
    console.log('✅ Fixed verification-queue.tsx');
    return true;
  } catch (error) {
    console.error('❌ Error fixing verification-queue.tsx:', error.message);
    return false;
  }
}

// Function to fix all remaining syntax errors across the project
function fixAllSyntaxErrors() {
  const srcDir = path.join(process.cwd(), 'src');
  let fixedCount = 0;
  
  function walkDir(dir) {
    const files = fs.readdirSync(dir);
    
    for (const file of files) {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        walkDir(filePath);
      } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
        if (processFile(filePath)) {
          fixedCount++;
        }
      }
    }
  }
  
  function processFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      let fixed = content;
      
      // Fix common syntax errors
      fixed = fixed.replace(/(\w+):\s*([^,}\n]+);,/g, '$1: $2;');
      fixed = fixed.replace(/Alert\.alert\('([^']+)'([^']+)'\)/g, "Alert.alert('$1', '$2')");
      fixed = fixed.replace(/(\w+):\s*([^,}\n]+)\s+(\w+):/g, '$1: $2, $3:');
      fixed = fixed.replace(/borderColor:\s*theme\.colors\.(\w+)borderWidth:/g, 'borderColor: theme.colors.$1, borderWidth:');
      fixed = fixed.replace(/styles\.(\w+)\{\s*backgroundColor:/g, 'styles.$1, { backgroundColor:');
      
      if (content !== fixed) {
        fs.writeFileSync(filePath, fixed, 'utf8');
        console.log(`✅ Fixed: ${filePath}`);
        return true;
      }
      return false;
    } catch (error) {
      console.error(`❌ Error processing ${filePath}:`, error.message);
      return false;
    }
  }
  
  if (fs.existsSync(srcDir)) {
    walkDir(srcDir);
    console.log(`\n🎉 Fixed ${fixedCount} additional files`);
  }
}

// Run the fixes
console.log('🔧 Starting final syntax error fixes...\n');
fixVerificationQueueFile();
fixAllSyntaxErrors();
console.log('\n✨ All syntax errors should now be fixed!');
