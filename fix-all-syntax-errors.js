#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Function to fix syntax errors in a file
function fixSyntaxErrors(content) {
  let fixed = content;
  
  // Fix 1: Import statements - missing commas
  fixed = fixed.replace(/import\s*\{\s*([^}]*)\s*\}\s*from/g, (match, imports) => {
    const cleanImports = imports
      .split(/[\n\r]+/)
      .map(line => line.trim())
      .filter(line => line && !line.startsWith('//'))
      .join(',\n  ');
    return `import {\n  ${cleanImports}\n} from`;
  });
  
  // Fix 2: Missing semicolons after import statements
  fixed = fixed.replace(/(\} from [^;]+)([,]?)$/gm, '$1;');
  
  // Fix 3: Fix malformed object literals in JSX
  fixed = fixed.replace(/style=\{\s*\[\s*([^}]*)\s*\]\s*\}/g, (match, content) => {
    const cleanContent = content.replace(/([^,])\s*([a-zA-Z])/g, '$1, $2');
    return `style={[${cleanContent}]}`;
  });
  
  // Fix 4: Fix missing commas in object literals
  fixed = fixed.replace(/([a-zA-Z0-9_'"]+):\s*([^,}\n]+)\s*([a-zA-Z0-9_'"]+):/g, '$1: $2, $3:');
  
  // Fix 5: Fix semicolons that should be commas in objects
  fixed = fixed.replace(/([a-zA-Z0-9_'"]+):\s*([^;}\n]+);(\s*[a-zA-Z0-9_'"]+:)/g, '$1: $2,$3');
  
  // Fix 6: Fix malformed function calls
  fixed = fixed.replace(/(\w+)\(\s*\{([^}]*)\}\s*\)\s*;/g, '$1({ $2 });');
  
  // Fix 7: Fix missing opening braces in objects
  fixed = fixed.replace(/=\s*\{\s*([a-zA-Z0-9_]+):\s*([^,}]+),?\s*\}/g, '={ $1: $2 }');
  
  // Fix 8: Fix useEffect dependency arrays
  fixed = fixed.replace(/\}\s*\[\s*([^\]]*)\s*\]\s*\),/g, '}, [$1]);');
  
  // Fix 9: Fix arrow functions
  fixed = fixed.replace(/=>\s*\{;/g, '=> {');
  
  // Fix 10: Fix JSX props
  fixed = fixed.replace(/(\w+)=\s*\{\s*([^}]*)\s*\};/g, '$1={$2}');
  
  return fixed;
}

// Function to process a single file
function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const fixed = fixSyntaxErrors(content);
    
    if (content !== fixed) {
      fs.writeFileSync(filePath, fixed, 'utf8');
      console.log(`✅ Fixed: ${filePath}`);
      return true;
    }
    return false;
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Function to find and process all TypeScript/React files
function processAllFiles() {
  const srcDir = path.join(process.cwd(), 'src');
  let fixedCount = 0;
  
  function walkDir(dir) {
    const files = fs.readdirSync(dir);
    
    for (const file of files) {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        walkDir(filePath);
      } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
        if (processFile(filePath)) {
          fixedCount++;
        }
      }
    }
  }
  
  if (fs.existsSync(srcDir)) {
    walkDir(srcDir);
    console.log(`\n🎉 Fixed ${fixedCount} files`);
  } else {
    console.error('❌ src directory not found');
  }
}

// Run the script
console.log('🔧 Starting syntax error fixes...\n');
processAllFiles();
