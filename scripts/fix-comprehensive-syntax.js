#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Comprehensive syntax error patterns and fixes
const syntaxFixes = [
  // Import/export fixes
  {
    pattern: /;,/g,
    replacement: ',',
    description: 'Fix semicolon followed by comma'
  },
  {
    pattern: /,;/g,
    replacement: ';',
    description: 'Fix comma followed by semicolon'
  },
  {
    pattern: /(\w+);(\s*\w+)(\s*}\s*from)/g,
    replacement: '$1,$2$3',
    description: 'Fix semicolons in import statements that should be commas'
  },
  {
    pattern: /import\s*\{,/g,
    replacement: 'import {',
    description: 'Fix malformed import statements with extra commas'
  },
  {
    pattern: /,(\s*}\s*from)/g,
    replacement: '$1',
    description: 'Fix trailing commas in import statements'
  },
  
  // Object and array fixes
  {
    pattern: /\{\s*,/g,
    replacement: '{',
    description: 'Fix malformed object literals with extra commas at start'
  },
  {
    pattern: /\(\s*,/g,
    replacement: '(',
    description: 'Fix malformed function calls with extra commas'
  },
  {
    pattern: /\[\s*,/g,
    replacement: '[',
    description: 'Fix malformed array literals with extra commas'
  },
  {
    pattern: /,,+/g,
    replacement: ',',
    description: 'Fix double commas'
  },
  {
    pattern: /;;+/g,
    replacement: ';',
    description: 'Fix double semicolons'
  },
  
  // JSX and React fixes
  {
    pattern: /=\s*\{\{([^}]+)\}\s*\}/g,
    replacement: '={$1}',
    description: 'Fix malformed JSX props with double braces'
  },
  {
    pattern: /(\w+)=\s*(\w+:\s*[^,}\s]+)(?!\})/g,
    replacement: '$1={{$2}}',
    description: 'Fix missing curly braces in JSX props'
  },
  
  // Function and operator fixes
  {
    pattern: /=\s*>/g,
    replacement: '=>',
    description: 'Fix malformed arrow functions'
  },
  {
    pattern: /=\s*=/g,
    replacement: '==',
    description: 'Fix malformed comparison operators'
  },
  {
    pattern: /!=\s*=/g,
    replacement: '!==',
    description: 'Fix malformed not equals'
  },
  {
    pattern: /\?\s*\./g,
    replacement: '?.',
    description: 'Fix space before question mark in optional chaining'
  },
  
  // Statement termination fixes
  {
    pattern: /(\w+)\s*,\s*$/gm,
    replacement: '$1;',
    description: 'Fix statements ending with comma instead of semicolon'
  },
  {
    pattern: /(\w+)\s*}\s*,\s*$/gm,
    replacement: '$1};',
    description: 'Fix function/object endings with comma instead of semicolon'
  },
  
  // Specific React Native and TypeScript fixes
  {
    pattern: /(\s+)(\w+):\s*([^,}\n]+),(\s*})/g,
    replacement: '$1$2: $3$4',
    description: 'Fix trailing commas in object properties'
  },
  {
    pattern: /(\w+)\s*}\s*([,;])\s*$/gm,
    replacement: '$1}$2',
    description: 'Fix spacing around closing braces'
  }
];

function fixSyntaxErrors(content) {
  let fixed = content;
  let totalFixes = 0;
  
  for (const fix of syntaxFixes) {
    const matches = fixed.match(fix.pattern);
    if (matches) {
      fixed = fixed.replace(fix.pattern, fix.replacement);
      totalFixes += matches.length;
      console.log(`  Applied ${matches.length} fixes for: ${fix.description}`);
    }
  }
  
  return { content: fixed, fixCount: totalFixes };
}

function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const result = fixSyntaxErrors(content);
    
    if (result.fixCount > 0) {
      fs.writeFileSync(filePath, result.content, 'utf8');
      console.log(`✅ Fixed ${result.fixCount} syntax errors in: ${filePath}`);
      return result.fixCount;
    }
    
    return 0;
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return 0;
  }
}

function findTypeScriptFiles(dir, files = []) {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      // Skip node_modules and other build directories
      if (!['node_modules', '.expo', '.git', 'dist', 'build'].includes(item)) {
        findTypeScriptFiles(fullPath, files);
      }
    } else if (item.match(/\.(ts|tsx|js|jsx)$/)) {
      files.push(fullPath);
    }
  }
  
  return files;
}

// Main execution
console.log('🔧 Starting comprehensive syntax error fixing...\n');

const projectRoot = process.cwd();
const files = findTypeScriptFiles(projectRoot);

console.log(`Found ${files.length} TypeScript/JavaScript files to process\n`);

let totalFilesFixed = 0;
let totalErrorsFixed = 0;

for (const file of files) {
  const fixCount = processFile(file);
  if (fixCount > 0) {
    totalFilesFixed++;
    totalErrorsFixed += fixCount;
  }
}

console.log('\n📊 Summary:');
console.log(`Total files processed: ${files.length}`);
console.log(`Files with fixes: ${totalFilesFixed}`);
console.log(`Total syntax errors fixed: ${totalErrorsFixed}`);
console.log('\n✨ Comprehensive syntax fixing completed!');
