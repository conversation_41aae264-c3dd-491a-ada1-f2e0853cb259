#!/usr/bin/env node

const fs = require('fs');

const filePath = 'src/app/(admin)/verification-queue.tsx';

try {
  let content = fs.readFileSync(filePath, 'utf8');
  
  // Fix all the syntax errors in this file
  content = content
    // Fix semicolons that should be commas in import statements
    .replace(/TouchableOpacity;/g, 'TouchableOpacity,')
    .replace(/ActivityIndicator;/g, 'ActivityIndicator,')
    
    // Fix function declarations and statements
    .replace(/const handleApprove = async \(submissionId: string\) => \{ if \(!selectedSubmission \|\| !user\) return null,/g, 
             'const handleApprove = async (submissionId: string) => {\n    if (!selectedSubmission || !user) return;\n')
    .replace(/setProcessing\(true\)/g, 'setProcessing(true);')
    .replace(/const review: VerificationReview = \{;/g, 'const review: VerificationReview = {')
    .replace(/reviewerId: user\.id \}/g, 'reviewerId: user.id\n    };')
    .replace(/const result = await manualVerificationService\.reviewSubmission\(review\),/g, 
             'const result = await manualVerificationService.reviewSubmission(review);')
    .replace(/if \(result\.success\) \{/g, 'if (result.success) {')
    .replace(/Alert\.alert\('Success', 'Verification approved successfully'\),/g, 
             "Alert.alert('Success', 'Verification approved successfully');")
    .replace(/setSelectedSubmission\(null\)/g, 'setSelectedSubmission(null);')
    .replace(/loadVerificationData\(\) \/\/ Refresh the list \} else \{/g, 
             'loadVerificationData(); // Refresh the list\n      } else {')
    .replace(/Alert\.alert\('Error', result\.error \|\| 'Failed to approve verification'\) \}/g, 
             "Alert.alert('Error', result.error || 'Failed to approve verification');\n      }")
    .replace(/\} catch \(error\) \{ logger\.error/g, '} catch (error) {\n      logger.error')
    .replace(/error: \(error as Error\)\.message \}\)/g, 'error: (error as Error).message\n      });')
    .replace(/Alert\.alert\('Error', 'Failed to approve verification'\)/g, 
             "Alert.alert('Error', 'Failed to approve verification');")
    .replace(/\} finally \{/g, '} finally {')
    .replace(/setProcessing\(false\) \}/g, 'setProcessing(false);\n  }')
    .replace(/\},/g, '};')
    
    // Fix variable declarations
    .replace(/const date = new Date\(dateString\),/g, 'const date = new Date(dateString);')
    .replace(/const now = new Date\(\)/g, 'const now = new Date();')
    .replace(/const diffInHours = Math\.floor\(\(now\.getTime\(\) - date\.getTime\(\)\) \/ \(1000 \* 60 \* 60\)\),/g, 
             'const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));')
    .replace(/if \(diffInHours === 1\) return '1 hour ago',/g, "if (diffInHours === 1) return '1 hour ago';")
    .replace(/const diffInDays = Math\.floor\(diffInHours \/ 24\),/g, 'const diffInDays = Math.floor(diffInHours / 24);')
    .replace(/return `\$\{diffInDays\} days ago`/g, 'return `${diffInDays} days ago`;')
    
    // Fix JSX syntax
    .replace(/<View style=\{styles\.loadingContainer\}>,/g, '<View style={styles.loadingContainer}>')
    .replace(/<Text style=\{styles\.loadingText\}>Loading verification queue\.\.\.<\/Text>,/g, 
             '<Text style={styles.loadingText}>Loading verification queue...</Text>')
    .replace(/<\/View>/g, '</View>')
    .replace(/\)/g, ')')
    .replace(/style = \{styles\.container\},/g, 'style={styles.container}')
    .replace(/>,/g, '>')
    .replace(/<View style=\{styles\.header\}>,/g, '<View style={styles.header}>')
    .replace(/<Text style=\{styles\.title\}>ID Verification Queue<\/Text>/g, 
             '<Text style={styles.title}>ID Verification Queue</Text>')
    .replace(/<Text style=\{styles\.subtitle\}>Manual document review dashboard<\/Text>,/g, 
             '<Text style={styles.subtitle}>Manual document review dashboard</Text>')
    
    // Fix more JSX issues
    .replace(/\{metrics && \(/g, '{metrics && (')
    .replace(/<View style=\{styles\.metricsContainer\}>,/g, '<View style={styles.metricsContainer}>')
    .replace(/<Text style=\{styles\.metricNumber\}>\{metrics\.pendingReviews\}<\/Text>,/g, 
             '<Text style={styles.metricNumber}>{metrics.pendingReviews}</Text>')
    .replace(/<Text style=\{styles\.metricLabel\}>Pending Reviews<\/Text>/g, 
             '<Text style={styles.metricLabel}>Pending Reviews</Text>')
    .replace(/<\/Card>,/g, '</Card>')
    .replace(/\)\},/g, ')},')
    
    // Fix more complex JSX patterns
    .replace(/\{submissions\.length === 0 \? \(/g, '{submissions.length === 0 ? (')
    .replace(/<Ionicons name='checkmark-circle' size=\{48\} color=\{theme\.colors\.success\} \/>,/g, 
             "<Ionicons name='checkmark-circle' size={48} color={theme.colors.success} />")
    .replace(/<Text style=\{styles\.emptyTitle\}>All caught up!<\/Text>/g, 
             '<Text style={styles.emptyTitle}>All caught up!</Text>')
    .replace(/<Text style=\{styles\.emptySubtitle\}>No pending verifications to review<\/Text>,/g, 
             '<Text style={styles.emptySubtitle}>No pending verifications to review</Text>')
    .replace(/\)    : \(/g, ') : (')
    .replace(/<TouchableOpacity,/g, '<TouchableOpacity')
    .replace(/onPress=\{\(\) => handleSelectSubmission\(submission\)\},/g, 
             'onPress={() => handleSelectSubmission(submission)}')
    .replace(/<Card,/g, '<Card')
    
    // Fix style objects
    .replace(/style=\{\{ \[styles\.submissionCard/g, 'style={[styles.submissionCard')
    .replace(/selectedSubmission\?\.id === submission\.id && \{/g, 
             'selectedSubmission?.id === submission.id && {')
    .replace(/borderColor : theme\.colors\.primary/g, 'borderColor: theme.colors.primary,')
    .replace(/borderWidth: 2  \] \}\]\},/g, 'borderWidth: 2\n                }]\n              }')
    .replace(/<View style = \{styles\.submissionHeader\}>,/g, '<View style={styles.submissionHeader}>')
    .replace(/\{submission\.userEmail \|\| submission\.userId \|\| 'Unknown User'\},/g, 
             "{submission.userEmail || submission.userId || 'Unknown User'}")
    .replace(/<View,/g, '<View')
    .replace(/style=\{\{ \[styles\.statusBadge, \{ backgroundColor: getStatusColor\(submission\.status\)  \] \}\]\},/g, 
             'style={[styles.statusBadge, { backgroundColor: getStatusColor(submission.status) }]}')
    .replace(/<Text style=\{styles\.statusText\}>\{submission\.status\}<\/Text>,/g, 
             '<Text style={styles.statusText}>{submission.status}</Text>')
    
    // Fix more JSX closing tags
    .replace(/<\/TouchableOpacity>/g, '</TouchableOpacity>')
    .replace(/\)\),/g, '))')
    .replace(/\)\}/g, ')}')
    
    // Fix StyleSheet
    .replace(/const createStyles = \(theme: any\) =>/g, 'const createStyles = (theme: any) =>')
    .replace(/StyleSheet\.create\(\{ container: \{/g, 'StyleSheet.create({\n    container: {')
    .replace(/backgroundColor: theme\.colors\.background \}/g, 'backgroundColor: theme.colors.background\n    },')
    .replace(/loadingContainer: \{ flex: 1,/g, 'loadingContainer: {\n      flex: 1,')
    .replace(/backgroundColor: theme\.colors\.background \},/g, 'backgroundColor: theme.colors.background\n    },')
    .replace(/loadingText: \{ marginTop: theme\.spacing\.md,/g, 'loadingText: {\n      marginTop: theme.spacing.md,')
    .replace(/color: theme\.colors\.textSecondary \}/g, 'color: theme.colors.textSecondary\n    }')
    
    // Fix remaining syntax issues
    .replace(/\},$/gm, '}')
    .replace(/;$/gm, ';')
    .replace(/,$/gm, '')
    
    // Final cleanup
    .replace(/\n\n+/g, '\n\n')
    .replace(/\s+$/gm, '');
  
  fs.writeFileSync(filePath, content, 'utf8');
  console.log('✅ Fixed verification-queue.tsx syntax errors');
  
} catch (error) {
  console.error('❌ Error fixing file:', error.message);
}
