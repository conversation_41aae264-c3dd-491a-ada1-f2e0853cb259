#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔧 Starting comprehensive syntax error fix...\n');

function findTsFiles(dir) {
  let results = [];
  try {
    const list = fs.readdirSync(dir);
    list.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      if (stat && stat.isDirectory()) {
        results = results.concat(findTsFiles(filePath));
      } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
        results.push(filePath);
      }
    });
  } catch (error) {
    console.log(`⚠️  Error reading directory ${dir}: ${error.message}`);
  }
  return results;
}

function fixSyntaxErrors(content) {
  let fixed = content;
  let fixCount = 0;

  // Fix 1: Remove semicolon followed by comma (;,)
  const semicolonCommaRegex = /;,/g;
  if (fixed.match(semicolonCommaRegex)) {
    fixed = fixed.replace(semicolonCommaRegex, ',');
    fixCount++;
  }

  // Fix 2: Remove comma followed by semicolon (,;)
  const commaSemicolonRegex = /,;/g;
  if (fixed.match(commaSemicolonRegex)) {
    fixed = fixed.replace(commaSemicolonRegex, ';');
    fixCount++;
  }

  // Fix 3: Fix import statements ending with comma instead of semicolon
  const importCommaRegex = /import\s+([^;]+),\s*$/gm;
  if (fixed.match(importCommaRegex)) {
    fixed = fixed.replace(importCommaRegex, 'import $1;');
    fixCount++;
  }

  // Fix 4: Fix malformed import statements with extra commas
  const malformedImportRegex = /import\s*\{,/g;
  if (fixed.match(malformedImportRegex)) {
    fixed = fixed.replace(malformedImportRegex, 'import {');
    fixCount++;
  }

  // Fix 5: Fix malformed object literals with extra commas at start
  const extraCommaRegex = /\{\s*,/g;
  if (fixed.match(extraCommaRegex)) {
    fixed = fixed.replace(extraCommaRegex, '{');
    fixCount++;
  }

  // Fix 6: Fix malformed function calls with extra commas
  const extraCommaFuncRegex = /\(\s*,/g;
  if (fixed.match(extraCommaFuncRegex)) {
    fixed = fixed.replace(extraCommaFuncRegex, '(');
    fixCount++;
  }

  // Fix 7: Fix malformed array literals with extra commas
  const extraCommaArrayRegex = /\[\s*,/g;
  if (fixed.match(extraCommaArrayRegex)) {
    fixed = fixed.replace(extraCommaArrayRegex, '[');
    fixCount++;
  }

  // Fix 8: Fix double commas
  const doubleCommaRegex = /,,+/g;
  if (fixed.match(doubleCommaRegex)) {
    fixed = fixed.replace(doubleCommaRegex, ',');
    fixCount++;
  }

  // Fix 9: Fix double semicolons
  const doubleSemicolonRegex = /;;+/g;
  if (fixed.match(doubleSemicolonRegex)) {
    fixed = fixed.replace(doubleSemicolonRegex, ';');
    fixCount++;
  }

  // Fix 10: Fix malformed arrow functions (= >)
  const malformedArrowRegex = /=\s*>/g;
  if (fixed.match(malformedArrowRegex)) {
    fixed = fixed.replace(malformedArrowRegex, '=>');
    fixCount++;
  }

  // Fix 11: Fix malformed comparison operators (= =)
  const malformedEqualsRegex = /=\s*=/g;
  if (fixed.match(malformedEqualsRegex)) {
    fixed = fixed.replace(malformedEqualsRegex, '==');
    fixCount++;
  }

  // Fix 12: Fix malformed not equals (!= =)
  const malformedNotEqualsRegex = /!=\s*=/g;
  if (fixed.match(malformedNotEqualsRegex)) {
    fixed = fixed.replace(malformedNotEqualsRegex, '!==');
    fixCount++;
  }

  // Fix 13: Fix trailing commas in object/array literals followed by closing bracket
  const trailingCommaRegex = /,(\s*[}\]])/g;
  if (fixed.match(trailingCommaRegex)) {
    fixed = fixed.replace(trailingCommaRegex, '$1');
    fixCount++;
  }

  // Fix 14: Fix malformed JSX props with double braces
  const malformedJsxRegex = /=\s*\{\{([^}]+)\}\s*\}/g;
  if (fixed.match(malformedJsxRegex)) {
    fixed = fixed.replace(malformedJsxRegex, '={$1}');
    fixCount++;
  }

  // Fix 15: Fix space before question mark in optional chaining
  const optionalChainingRegex = /\?\s*\./g;
  if (fixed.match(optionalChainingRegex)) {
    fixed = fixed.replace(optionalChainingRegex, '?.');
    fixCount++;
  }

  // Fix 16: Fix semicolons in import statements that should be commas
  const importSemicolonRegex = /(\w+);(\s*\w+)/g;
  if (fixed.match(importSemicolonRegex)) {
    fixed = fixed.replace(importSemicolonRegex, '$1,$2');
    fixCount++;
  }

  // Fix 17: Fix missing curly braces in JSX props
  const missingBracesRegex = /(\w+)=\s*(\w+:\s*[^,}\s]+)/g;
  if (fixed.match(missingBracesRegex)) {
    fixed = fixed.replace(missingBracesRegex, '$1={{$2}}');
    fixCount++;
  }

  return { content: fixed, fixCount };
}

function processFile(filePath) {
  try {
    const originalContent = fs.readFileSync(filePath, 'utf8');
    const { content: fixedContent, fixCount } = fixSyntaxErrors(originalContent);
    
    if (fixCount > 0) {
      fs.writeFileSync(filePath, fixedContent, 'utf8');
      console.log(`✅ Fixed ${fixCount} syntax errors in ${filePath}`);
      return true;
    } else {
      console.log(`✓ No syntax errors found in ${filePath}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Error processing ${filePath}: ${error.message}`);
    return false;
  }
}

// Main execution
const srcDir = path.join(__dirname, '../src');
const files = findTsFiles(srcDir);

console.log(`Found ${files.length} TypeScript files to process\n`);

let fixedCount = 0;
files.forEach(file => {
  if (processFile(file)) {
    fixedCount++;
  }
});

console.log(`\n🎉 Fixed syntax errors in ${fixedCount} files out of ${files.length} total files`);

// Run a quick check to see if there are remaining issues
console.log('\n🔍 Checking for remaining syntax issues...');
try {
  const { execSync } = require('child_process');
  execSync('npx tsc --noEmit --skipLibCheck', { stdio: 'inherit' });
  console.log('✅ All syntax errors fixed!');
} catch (error) {
  console.log('⚠️  Some TypeScript errors may remain, but major syntax issues should be resolved');
}
