#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Function to fix specific syntax errors
function fixSyntaxErrors(content) {
  let fixed = content;
  
  // Fix 1: Remove double commas
  fixed = fixed.replace(/,,/g, ',');
  
  // Fix 2: Fix corrupted object properties in StyleSheet
  fixed = fixed.replace(/(\w+):\s*\{,\s*/g, '$1: {\n      ');
  
  // Fix 3: Fix corrupted shadowOffset properties
  fixed = fixed.replace(/\{\s*widt,\s*h:\s*(\d+),\s*height:\s*(\d+)\s*\}/g, '{ width: $1, height: $2 }');
  
  // Fix 4: Fix corrupted style arrays
  fixed = fixed.replace(/\[s,\s*ty,\s*le,\s*s\./g, '[styles.');
  
  // Fix 5: Fix broken property chains
  fixed = fixed.replace(/(\w+),\s*(\w+),\s*(\w+),\s*(\w+)\./g, '$1$2$3$4.');
  
  // Fix 6: Fix broken conditional expressions
  fixed = fixed.replace(/(\w+)\s*\?\s*st,\s*yl,\s*es\./g, '$1 ? styles.');
  
  // Fix 7: Fix broken object property definitions
  fixed = fixed.replace(/(\w+):\s*\{,\s*([^}]+)\s*\}/g, '$1: {\n      $2\n    }');
  
  // Fix 8: Remove trailing commas before closing braces
  fixed = fixed.replace(/,(\s*\})/g, '$1');
  
  // Fix 9: Fix broken import statements
  fixed = fixed.replace(/(\w+),,/g, '$1,');
  
  // Fix 10: Fix broken JSX attributes
  fixed = fixed.replace(/(\w+)=\{([^}]*),\s*([^}]*)\}/g, '$1={$2$3}');
  
  return fixed;
}

// Function to process a single file
function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const fixed = fixSyntaxErrors(content);
    
    if (content !== fixed) {
      fs.writeFileSync(filePath, fixed, 'utf8');
      console.log(`✅ Fixed: ${filePath}`);
      return true;
    }
    return false;
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Function to find and process all TypeScript/React files
function processAllFiles() {
  const srcDir = path.join(process.cwd(), 'src');
  let fixedCount = 0;
  
  function walkDir(dir) {
    const files = fs.readdirSync(dir);
    
    for (const file of files) {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        walkDir(filePath);
      } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
        if (processFile(filePath)) {
          fixedCount++;
        }
      }
    }
  }
  
  if (fs.existsSync(srcDir)) {
    walkDir(srcDir);
    console.log(`\n🎉 Fixed ${fixedCount} files`);
  } else {
    console.error('❌ src directory not found');
  }
}

// Run the script
console.log('🔧 Starting remaining syntax error fixes...\n');
processAllFiles();
