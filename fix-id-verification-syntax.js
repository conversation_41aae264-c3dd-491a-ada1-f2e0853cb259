#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

function fixIdVerificationFile() {
  const filePath = path.join(process.cwd(), 'src/app/(auth)/id-verification.tsx');
  
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Fix all the syntax errors systematically
    
    // Fix import statements
    content = content.replace(/  import \{/g, 'import {');
    content = content.replace(/\} from 'expo-router';\s*import \{/g, '} from \'expo-router\';\nimport {');
    content = content.replace(/\} from 'react-native-safe-area-context';\s*import \{/g, '} from \'react-native-safe-area-context\';\nimport {');
    content = content.replace(/\} from '@design-system';\s*import \{/g, '} from \'@design-system\';\nimport {');
    content = content.replace(/\} from '@utils\/logger';\s*import/g, '} from \'@utils/logger\';\nimport');
    content = content.replace(/\} from 'expo-image-picker';\s*import/g, '} from \'expo-image-picker\';\nimport');
    content = content.replace(/\} from '@context\/SimpleAuthContext';\s*import \{/g, '} from \'@context/SimpleAuthContext\';\nimport {');
    
    // Fix variable declarations
    content = content.replace(/const \{ width, height  \} = Dimensions\.get\('window'\),/g, 'const { width, height } = Dimensions.get(\'window\');');
    
    // Fix interface declarations
    content = content.replace(/  \/\/ Types for Step 3,/g, '// Types for Step 3');
    content = content.replace(/interface Step3Data \{ idDocument: string \| null,/g, 'interface Step3Data {\n  idDocument: string | null;');
    content = content.replace(/    selfiePhoto: string \| null,/g, '  selfiePhoto: string | null;');
    content = content.replace(/  verificationType: 'automatic' \| 'manual'/g, '  verificationType: \'automatic\' | \'manual\';');
    content = content.replace(/  jumioSessionId\?: string \},/g, '  jumioSessionId?: string;\n}');
    
    // Fix more interface declarations
    content = content.replace(/  interface VerificationOption \{ id: string,/g, 'interface VerificationOption {\n  id: string;');
    content = content.replace(/    title: string,/g, '  title: string;');
    content = content.replace(/  description: string,/g, '  description: string;');
    content = content.replace(/    icon: string,/g, '  icon: string;');
    content = content.replace(/  recommended: boolean,/g, '  recommended: boolean;');
    content = content.replace(/    estimatedTime: string \},/g, '  estimatedTime: string;\n}');
    
    // Fix array declarations
    content = content.replace(/  const VERIFICATION_OPTIONS: VerificationOption\[\] = \[/g, 'const VERIFICATION_OPTIONS: VerificationOption[] = [');
    content = content.replace(/  estimatedTime: '< 1 minute' \}/g, '    estimatedTime: \'< 1 minute\'\n  },');
    content = content.replace(/  \{/g, '  {');
    content = content.replace(/  estimatedTime: '24-48 hours' \}\], ,/g, '    estimatedTime: \'24-48 hours\'\n  }\n];');
    
    // Fix benefits array
    content = content.replace(/  const VERIFICATION_BENEFITS = \['✅ Verified badge on your profile', ,/g, 'const VERIFICATION_BENEFITS = [\n  \'✅ Verified badge on your profile\',');
    content = content.replace(/  '🎯 Priority in search results'/g, '  \'🎯 Priority in search results\',');
    content = content.replace(/  '🔒 Access to verified-only features',/g, '  \'🔒 Access to verified-only features\',');
    content = content.replace(/  '💬 Increased trust from other users'/g, '  \'💬 Increased trust from other users\',');
    content = content.replace(/  '⭐ Higher match success rate'\],/g, '  \'⭐ Higher match success rate\'\n];');
    
    // Fix more interface declarations
    content = content.replace(/interface DocumentImages \{ front: string \| null,/g, 'interface DocumentImages {\n  front: string | null;');
    content = content.replace(/    back: string \| null \},/g, '  back: string | null;\n}');
    
    // Fix function declarations
    content = content.replace(/  export default function IDVerificationScreen\(\) \{/g, 'export default function IDVerificationScreen() {');
    content = content.replace(/  const theme = useTheme\(\),/g, '  const theme = useTheme();');
    content = content.replace(/  const router = useRouter\(\)/g, '  const router = useRouter();');
    content = content.replace(/  const params = useLocalSearchParams\(\),/g, '  const params = useLocalSearchParams();');
    content = content.replace(/  const styles = createStyles\(theme\)/g, '  const styles = createStyles(theme);');
    
    // Fix destructuring
    content = content.replace(/  const \{ completeStep3, isLoading, getCostSavings  \} = useSimpleAuth\(\),/g, '  const { completeStep3, isLoading, getCostSavings } = useSimpleAuth();');
    
    // Fix useState declarations
    content = content.replace(/  const \[selectedDocumentType, setSelectedDocumentType\] = useState<DocumentType>\('drivers_license'\),/g, '  const [selectedDocumentType, setSelectedDocumentType] = useState<DocumentType>(\'drivers_license\');');
    content = content.replace(/  const \[documentImages, setDocumentImages\] = useState<DocumentImages>\(\{  front: null,/g, '  const [documentImages, setDocumentImages] = useState<DocumentImages>({\n    front: null,');
    content = content.replace(/    back: null  \}\),/g, '    back: null\n  });');
    content = content.replace(/  const \[isSubmitting, setIsSubmitting\] = useState\(false\),/g, '  const [isSubmitting, setIsSubmitting] = useState(false);');
    
    // Fix form state
    content = content.replace(/  \/\/ Form state,/g, '  // Form state');
    content = content.replace(/  const \[formData, setFormData\] = useState<Step3Data>\(\{/g, '  const [formData, setFormData] = useState<Step3Data>({');
    content = content.replace(/  idDocument: null,/g, '    idDocument: null,');
    content = content.replace(/    selfiePhoto: null,/g, '    selfiePhoto: null,');
    content = content.replace(/  verificationType: 'automatic'/g, '    verificationType: \'automatic\'');
    content = content.replace(/   \}\),/g, '  });');
    
    // Fix more useState declarations
    content = content.replace(/  const \[loading, setLoading\] = useState\(false\),/g, '  const [loading, setLoading] = useState(false);');
    content = content.replace(/  const \[uploadingDocument, setUploadingDocument\] = useState\(false\),/g, '  const [uploadingDocument, setUploadingDocument] = useState(false);');
    content = content.replace(/  const \[uploadingSelfie, setUploadingSelfie\] = useState\(false\),/g, '  const [uploadingSelfie, setUploadingSelfie] = useState(false);');
    
    // Fix currentStep useState
    content = content.replace(/  const \[currentStep, setCurrentStep\] = useState<'method' \| 'documents' \| 'selfie' \| 'processing'>\(/g, '  const [currentStep, setCurrentStep] = useState<\'method\' | \'documents\' | \'selfie\' | \'processing\'>(');
    content = content.replace(/  'method', ,/g, '    \'method\'');
    content = content.replace(/  \)/g, '  );');
    
    fs.writeFileSync(filePath, content, 'utf8');
    console.log('✅ Fixed id-verification.tsx (part 1)');
    return true;
  } catch (error) {
    console.error('❌ Error fixing id-verification.tsx:', error.message);
    return false;
  }
}

console.log('🔧 Fixing id-verification.tsx syntax errors...');
fixIdVerificationFile();
